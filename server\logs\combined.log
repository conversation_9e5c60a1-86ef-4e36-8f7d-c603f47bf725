2025-03-22 17:03:49 [INFO]: Database configuration loaded from file 
2025-03-22 17:05:13 [INFO]: Database configuration loaded from file 
2025-03-22 17:05:24 [INFO]: Database configuration loaded from file 
2025-03-22 17:05:47 [INFO]: Database configuration loaded from file 
2025-03-22 17:05:48 [ERROR]: Error connecting to SQL Server: Port for SQLEXPRESS not found in localhost {
  "code": "EINSTLOOKUP",
  "originalError": {
    "code": "EINSTLOOKUP"
  },
  "name": "ConnectionError",
  "stack": "ConnectionError: Port for SQLEXPRESS not found in localhost\n    at C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:639:26)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:995:16\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"
}
2025-03-22 17:06:06 [INFO]: Database configuration loaded from file 
2025-03-22 17:06:06 [ERROR]: Error connecting to SQL Server: Port for SQLEXPRESS not found in localhost {
  "code": "EINSTLOOKUP",
  "originalError": {
    "code": "EINSTLOOKUP"
  },
  "name": "ConnectionError",
  "stack": "ConnectionError: Port for SQLEXPRESS not found in localhost\n    at C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:639:26)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:995:16\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"
}
2025-03-22 17:06:15 [INFO]: Database configuration loaded from file 
2025-03-22 17:06:15 [ERROR]: Error connecting to SQL Server: Port for SQLEXPRESS not found in localhost {
  "code": "EINSTLOOKUP",
  "originalError": {
    "code": "EINSTLOOKUP"
  },
  "name": "ConnectionError",
  "stack": "ConnectionError: Port for SQLEXPRESS not found in localhost\n    at C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:639:26)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:995:16\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"
}
2025-03-22 17:06:20 [INFO]: Database configuration loaded from file 
2025-03-22 17:06:20 [ERROR]: Error connecting to SQL Server: Port for SQLEXPRESS not found in localhost {
  "code": "EINSTLOOKUP",
  "originalError": {
    "code": "EINSTLOOKUP"
  },
  "name": "ConnectionError",
  "stack": "ConnectionError: Port for SQLEXPRESS not found in localhost\n    at C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:639:26)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:995:16\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"
}
2025-03-22 17:06:33 [INFO]: Database configuration loaded from file 
2025-03-22 17:06:33 [ERROR]: Error connecting to SQL Server: Port for SQLEXPRESS not found in localhost {
  "code": "EINSTLOOKUP",
  "originalError": {
    "code": "EINSTLOOKUP"
  },
  "name": "ConnectionError",
  "stack": "ConnectionError: Port for SQLEXPRESS not found in localhost\n    at C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:639:26)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:995:16\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"
}
2025-03-22 17:06:37 [INFO]: Database configuration loaded from file 
2025-03-22 17:06:37 [ERROR]: Error connecting to SQL Server: Port for SQLEXPRESS not found in localhost {
  "code": "EINSTLOOKUP",
  "originalError": {
    "code": "EINSTLOOKUP"
  },
  "name": "ConnectionError",
  "stack": "ConnectionError: Port for SQLEXPRESS not found in localhost\n    at C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:639:26)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:995:16\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"
}
2025-03-22 17:06:48 [INFO]: Database configuration loaded from file 
2025-03-22 17:06:48 [ERROR]: Error connecting to SQL Server: Port for SQLEXPRESS not found in localhost {
  "code": "EINSTLOOKUP",
  "originalError": {
    "code": "EINSTLOOKUP"
  },
  "name": "ConnectionError",
  "stack": "ConnectionError: Port for SQLEXPRESS not found in localhost\n    at C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:639:26)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:995:16\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"
}
2025-03-22 17:07:23 [INFO]: Database configuration loaded from file 
2025-03-22 17:07:23 [ERROR]: Error connecting to SQL Server: Port for SQLEXPRESS not found in localhost {
  "code": "EINSTLOOKUP",
  "originalError": {
    "code": "EINSTLOOKUP"
  },
  "name": "ConnectionError",
  "stack": "ConnectionError: Port for SQLEXPRESS not found in localhost\n    at C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:639:26)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:995:16\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"
}
2025-03-22 17:08:08 [INFO]: Database configuration loaded from file 
2025-03-22 17:08:08 [ERROR]: Error connecting to SQL Server: Port for SQLEXPRESS not found in 127.0.0.1 {
  "code": "EINSTLOOKUP",
  "originalError": {
    "code": "EINSTLOOKUP"
  },
  "name": "ConnectionError",
  "stack": "ConnectionError: Port for SQLEXPRESS not found in 127.0.0.1\n    at C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:639:26)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:995:16\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"
}
2025-03-22 17:28:34 [INFO]: Database configuration loaded from file 
2025-03-22 17:28:34 [INFO]: Using mock database for development 
2025-03-22 17:28:34 [INFO]: Server running on port 5000 
2025-03-22 17:28:34 [INFO]: Environment: development 
2025-03-22 17:29:05 [ERROR]: Not Found - /favicon.ico {
  "stack": "Error: Not Found - /favicon.ico\n    at notFound (C:\\claude\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\claude\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/favicon.ico",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-22 17:29:05 [ERROR]: Not Found - /logo192.png {
  "stack": "Error: Not Found - /logo192.png\n    at notFound (C:\\claude\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\claude\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/logo192.png",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-22 17:32:51 [INFO]: Database configuration loaded from file 
2025-03-22 17:32:51 [INFO]: Using mock database for development 
2025-03-22 17:32:51 [INFO]: Server running on port 5000 
2025-03-22 17:32:51 [INFO]: Environment: development 
2025-03-22 17:33:49 [INFO]: Database configuration loaded from file 
2025-03-22 17:33:49 [INFO]: Using mock database for development 
2025-03-22 17:33:49 [INFO]: Server running on port 5000 
2025-03-22 17:33:49 [INFO]: Environment: development 
2025-03-22 17:36:14 [INFO]: Database configuration loaded from file 
2025-03-22 17:36:14 [INFO]: Using mock database for development 
2025-03-22 17:36:14 [INFO]: Server running on port 5000 
2025-03-22 17:36:14 [INFO]: Environment: development 
2025-03-22 17:37:48 [INFO]: Database configuration loaded from file 
2025-03-22 17:37:48 [ERROR]: Error connecting to SQL Server: Port for SQLEXPRESS not found in localhost {
  "code": "EINSTLOOKUP",
  "originalError": {
    "code": "EINSTLOOKUP"
  },
  "name": "ConnectionError",
  "stack": "ConnectionError: Port for SQLEXPRESS not found in localhost\n    at C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:639:26)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:995:16\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"
}
2025-03-22 17:37:48 [INFO]: Falling back to mock database for development 
2025-03-22 17:37:48 [INFO]: Server running on port 5000 
2025-03-22 17:37:48 [INFO]: Environment: development 
2025-03-22 17:38:24 [INFO]: Database configuration loaded from file 
2025-03-22 17:38:25 [ERROR]: Error connecting to SQL Server: Port for SQLEXPRESS not found in localhost {
  "code": "EINSTLOOKUP",
  "originalError": {
    "code": "EINSTLOOKUP"
  },
  "name": "ConnectionError",
  "stack": "ConnectionError: Port for SQLEXPRESS not found in localhost\n    at C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:639:26)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:995:16\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"
}
2025-03-22 17:38:25 [INFO]: Falling back to mock database for development 
2025-03-22 17:38:25 [INFO]: Server running on port 5000 
2025-03-22 17:38:25 [INFO]: Environment: development 
2025-03-22 17:40:28 [INFO]: Database configuration loaded from file 
2025-03-22 17:40:28 [ERROR]: Error connecting to SQL Server: Port for SQLEXPRESS not found in localhost {
  "code": "EINSTLOOKUP",
  "originalError": {
    "code": "EINSTLOOKUP"
  },
  "name": "ConnectionError",
  "stack": "ConnectionError: Port for SQLEXPRESS not found in localhost\n    at C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:639:26)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:995:16\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"
}
2025-03-22 17:40:28 [INFO]: Falling back to mock database for development 
2025-03-22 17:40:28 [INFO]: Server running on port 5000 
2025-03-22 17:40:28 [INFO]: Environment: development 
2025-03-22 17:40:54 [ERROR]: Error getting user by username/email: Connection is closed. {
  "code": "ECONNCLOSED",
  "name": "ConnectionError",
  "stack": "ConnectionError: Connection is closed.\n    at Request._execute (C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\base\\request.js:583:37)\n    at Request._execute (C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:718:11)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\base\\request.js:545:12\n    at new Promise (<anonymous>)\n    at Request.execute (C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\base\\request.js:544:12)\n    at Object.getUserByUsername (C:\\claude\\burky_root_web\\server\\modules\\auth\\auth.service.js:32:34)\n    at login (C:\\claude\\burky_root_web\\server\\modules\\auth\\auth.controller.js:30:36)\n    at Layer.handle [as handle_request] (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (C:\\claude\\burky_root_web\\server\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)"
}
2025-03-22 17:40:54 [ERROR]: Login error: Connection is closed. {
  "code": "ECONNCLOSED",
  "name": "ConnectionError",
  "stack": "ConnectionError: Connection is closed.\n    at Request._execute (C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\base\\request.js:583:37)\n    at Request._execute (C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:718:11)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\base\\request.js:545:12\n    at new Promise (<anonymous>)\n    at Request.execute (C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\base\\request.js:544:12)\n    at Object.getUserByUsername (C:\\claude\\burky_root_web\\server\\modules\\auth\\auth.service.js:32:34)\n    at login (C:\\claude\\burky_root_web\\server\\modules\\auth\\auth.controller.js:30:36)\n    at Layer.handle [as handle_request] (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (C:\\claude\\burky_root_web\\server\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)"
}
2025-03-22 17:41:53 [INFO]: Database configuration loaded from file 
2025-03-22 17:41:53 [ERROR]: Error connecting to SQL Server: Port for SQLEXPRESS not found in localhost {
  "code": "EINSTLOOKUP",
  "originalError": {
    "code": "EINSTLOOKUP"
  },
  "name": "ConnectionError",
  "stack": "ConnectionError: Port for SQLEXPRESS not found in localhost\n    at C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:639:26)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:995:16\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"
}
2025-03-22 17:41:53 [INFO]: Falling back to mock database for development 
2025-03-22 17:41:53 [INFO]: Server running on port 5000 
2025-03-22 17:41:53 [INFO]: Environment: development 
2025-03-22 17:42:49 [INFO]: Database configuration loaded from file 
2025-03-22 17:42:49 [INFO]: Using mock database instead of real SQL Server 
2025-03-22 17:42:49 [INFO]: Server running on port 5000 
2025-03-22 17:42:49 [INFO]: Environment: development 
2025-03-22 17:43:03 [INFO]: Database configuration loaded from file 
2025-03-22 17:43:03 [INFO]: Using mock database instead of real SQL Server 
2025-03-22 17:43:03 [INFO]: Server running on port 5000 
2025-03-22 17:43:03 [INFO]: Environment: development 
2025-03-22 17:45:19 [INFO]: Database configuration loaded from file 
2025-03-22 17:45:19 [INFO]: Using mock database instead of real SQL Server 
2025-03-22 17:45:19 [INFO]: Server running on port 5000 
2025-03-22 17:45:19 [INFO]: Environment: development 
2025-03-22 18:00:44 [INFO]: Database configuration loaded from file 
2025-03-22 18:00:44 [INFO]: Connected to SQL Server 
2025-03-22 18:00:44 [INFO]: Server running on port 5000 
2025-03-22 18:00:44 [INFO]: Environment: development 
2025-03-22 18:04:05 [INFO]: Database configuration loaded from file 
2025-03-22 18:04:06 [INFO]: Connected to SQL Server 
2025-03-22 18:04:06 [INFO]: Server running on port 5000 
2025-03-22 18:04:06 [INFO]: Environment: development 
2025-03-22 18:04:27 [INFO]: Database configuration loaded from file 
2025-03-22 18:04:27 [INFO]: Connected to SQL Server 
2025-03-22 18:04:27 [INFO]: Server running on port 5000 
2025-03-22 18:04:27 [INFO]: Environment: development 
2025-03-22 18:04:45 [INFO]: Database configuration loaded from file 
2025-03-22 18:04:45 [INFO]: Connected to SQL Server 
2025-03-22 18:04:45 [INFO]: Server running on port 5000 
2025-03-22 18:04:45 [INFO]: Environment: development 
2025-03-22 18:05:15 [INFO]: Database configuration loaded from file 
2025-03-22 18:05:15 [INFO]: Connected to SQL Server 
2025-03-22 18:05:15 [INFO]: Server running on port 5000 
2025-03-22 18:05:15 [INFO]: Environment: development 
2025-03-22 18:05:30 [INFO]: Database configuration loaded from file 
2025-03-22 18:05:30 [INFO]: Connected to SQL Server 
2025-03-22 18:05:30 [INFO]: Server running on port 5000 
2025-03-22 18:05:30 [INFO]: Environment: development 
2025-03-22 18:05:40 [INFO]: Database configuration loaded from file 
2025-03-22 18:05:45 [INFO]: Database configuration loaded from file 
2025-03-22 18:05:46 [INFO]: Connected to SQL Server 
2025-03-22 18:05:46 [INFO]: Server running on port 5000 
2025-03-22 18:05:46 [INFO]: Environment: development 
2025-03-22 18:05:55 [INFO]: Database configuration loaded from file 
2025-03-22 18:05:55 [INFO]: Connected to SQL Server 
2025-03-22 18:05:55 [INFO]: Server running on port 5000 
2025-03-22 18:05:55 [INFO]: Environment: development 
2025-03-22 19:24:09 [INFO]: Database configuration loaded from file 
2025-03-22 19:24:10 [INFO]: Connected to SQL Server 
2025-03-22 19:24:10 [INFO]: Server running on port 5000 
2025-03-22 19:24:10 [INFO]: Environment: development 
2025-03-22 20:53:38 [INFO]: Database configuration loaded from file 
2025-03-22 20:53:38 [INFO]: Connected to SQL Server 
2025-03-22 20:53:38 [INFO]: Server running on port 5000 
2025-03-22 20:53:38 [INFO]: Environment: development 
2025-03-22 21:14:54 [INFO]: Database configuration loaded from file 
2025-03-22 21:14:54 [INFO]: Connected to SQL Server 
2025-03-22 21:14:54 [INFO]: Server running on port 5000 
2025-03-22 21:14:54 [INFO]: Environment: development 
2025-03-22 21:14:58 [INFO]: Database configuration loaded from file 
2025-03-22 21:14:58 [INFO]: Connected to SQL Server 
2025-03-22 21:14:58 [INFO]: Server running on port 5000 
2025-03-22 21:14:58 [INFO]: Environment: development 
2025-03-22 21:15:09 [INFO]: Database configuration loaded from file 
2025-03-22 21:15:09 [INFO]: Connected to SQL Server 
2025-03-22 21:15:09 [INFO]: Server running on port 5000 
2025-03-22 21:15:09 [INFO]: Environment: development 
2025-03-22 21:18:53 [ERROR]: Error getting permissions: Incorrect syntax near the keyword 'key'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 156,
      "state": 1,
      "class": 15,
      "message": "Incorrect syntax near the keyword 'key'.",
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": "",
      "lineNumber": 7
    }
  },
  "name": "RequestError",
  "number": 156,
  "lineNumber": 7,
  "state": 1,
  "class": 15,
  "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
  "procName": "",
  "precedingErrors": [],
  "stack": "RequestError: Incorrect syntax near the keyword 'key'.\n    at handleError (C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-03-22 21:18:54 [ERROR]: Error getting permissions: Incorrect syntax near the keyword 'key'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 156,
      "state": 1,
      "class": 15,
      "message": "Incorrect syntax near the keyword 'key'.",
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": "",
      "lineNumber": 7
    }
  },
  "name": "RequestError",
  "number": 156,
  "lineNumber": 7,
  "state": 1,
  "class": 15,
  "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
  "procName": "",
  "precedingErrors": [],
  "stack": "RequestError: Incorrect syntax near the keyword 'key'.\n    at handleError (C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-03-22 21:19:00 [ERROR]: Error getting permissions: Incorrect syntax near the keyword 'key'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 156,
      "state": 1,
      "class": 15,
      "message": "Incorrect syntax near the keyword 'key'.",
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": "",
      "lineNumber": 7
    }
  },
  "name": "RequestError",
  "number": 156,
  "lineNumber": 7,
  "state": 1,
  "class": 15,
  "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
  "procName": "",
  "precedingErrors": [],
  "stack": "RequestError: Incorrect syntax near the keyword 'key'.\n    at handleError (C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-03-22 21:19:00 [ERROR]: Error getting permissions: Incorrect syntax near the keyword 'key'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 156,
      "state": 1,
      "class": 15,
      "message": "Incorrect syntax near the keyword 'key'.",
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": "",
      "lineNumber": 7
    }
  },
  "name": "RequestError",
  "number": 156,
  "lineNumber": 7,
  "state": 1,
  "class": 15,
  "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
  "procName": "",
  "precedingErrors": [],
  "stack": "RequestError: Incorrect syntax near the keyword 'key'.\n    at handleError (C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-03-22 21:19:08 [ERROR]: Error getting permissions: Incorrect syntax near the keyword 'key'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 156,
      "state": 1,
      "class": 15,
      "message": "Incorrect syntax near the keyword 'key'.",
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": "",
      "lineNumber": 7
    }
  },
  "name": "RequestError",
  "number": 156,
  "lineNumber": 7,
  "state": 1,
  "class": 15,
  "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
  "procName": "",
  "precedingErrors": [],
  "stack": "RequestError: Incorrect syntax near the keyword 'key'.\n    at handleError (C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-03-22 21:19:08 [ERROR]: Error getting permissions: Incorrect syntax near the keyword 'key'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 156,
      "state": 1,
      "class": 15,
      "message": "Incorrect syntax near the keyword 'key'.",
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": "",
      "lineNumber": 7
    }
  },
  "name": "RequestError",
  "number": 156,
  "lineNumber": 7,
  "state": 1,
  "class": 15,
  "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
  "procName": "",
  "precedingErrors": [],
  "stack": "RequestError: Incorrect syntax near the keyword 'key'.\n    at handleError (C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-03-22 21:19:10 [ERROR]: Error getting permissions: Incorrect syntax near the keyword 'key'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 156,
      "state": 1,
      "class": 15,
      "message": "Incorrect syntax near the keyword 'key'.",
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": "",
      "lineNumber": 7
    }
  },
  "name": "RequestError",
  "number": 156,
  "lineNumber": 7,
  "state": 1,
  "class": 15,
  "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
  "procName": "",
  "precedingErrors": [],
  "stack": "RequestError: Incorrect syntax near the keyword 'key'.\n    at handleError (C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-03-22 21:19:10 [ERROR]: Error getting permissions: Incorrect syntax near the keyword 'key'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 156,
      "state": 1,
      "class": 15,
      "message": "Incorrect syntax near the keyword 'key'.",
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": "",
      "lineNumber": 7
    }
  },
  "name": "RequestError",
  "number": 156,
  "lineNumber": 7,
  "state": 1,
  "class": 15,
  "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
  "procName": "",
  "precedingErrors": [],
  "stack": "RequestError: Incorrect syntax near the keyword 'key'.\n    at handleError (C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-03-22 21:19:11 [ERROR]: Error getting permissions: Incorrect syntax near the keyword 'key'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 156,
      "state": 1,
      "class": 15,
      "message": "Incorrect syntax near the keyword 'key'.",
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": "",
      "lineNumber": 7
    }
  },
  "name": "RequestError",
  "number": 156,
  "lineNumber": 7,
  "state": 1,
  "class": 15,
  "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
  "procName": "",
  "precedingErrors": [],
  "stack": "RequestError: Incorrect syntax near the keyword 'key'.\n    at handleError (C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-03-22 21:19:11 [ERROR]: Error getting permissions: Incorrect syntax near the keyword 'key'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 156,
      "state": 1,
      "class": 15,
      "message": "Incorrect syntax near the keyword 'key'.",
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": "",
      "lineNumber": 7
    }
  },
  "name": "RequestError",
  "number": 156,
  "lineNumber": 7,
  "state": 1,
  "class": 15,
  "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
  "procName": "",
  "precedingErrors": [],
  "stack": "RequestError: Incorrect syntax near the keyword 'key'.\n    at handleError (C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-03-22 21:19:43 [INFO]: Database configuration loaded from file 
2025-03-22 21:19:43 [INFO]: Connected to SQL Server 
2025-03-22 21:19:43 [INFO]: Server running on port 5000 
2025-03-22 21:19:43 [INFO]: Environment: development 
2025-03-22 21:20:10 [INFO]: Database configuration loaded from file 
2025-03-22 21:20:10 [INFO]: Connected to SQL Server 
2025-03-22 21:20:10 [INFO]: Server running on port 5000 
2025-03-22 21:20:10 [INFO]: Environment: development 
2025-03-22 21:20:44 [ERROR]: Error getting permissions: Incorrect syntax near the keyword 'key'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 156,
      "state": 1,
      "class": 15,
      "message": "Incorrect syntax near the keyword 'key'.",
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": "",
      "lineNumber": 7
    }
  },
  "name": "RequestError",
  "number": 156,
  "lineNumber": 7,
  "state": 1,
  "class": 15,
  "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
  "procName": "",
  "precedingErrors": [],
  "stack": "RequestError: Incorrect syntax near the keyword 'key'.\n    at handleError (C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-03-22 21:20:44 [ERROR]: Error getting permissions: Incorrect syntax near the keyword 'key'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 156,
      "state": 1,
      "class": 15,
      "message": "Incorrect syntax near the keyword 'key'.",
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": "",
      "lineNumber": 7
    }
  },
  "name": "RequestError",
  "number": 156,
  "lineNumber": 7,
  "state": 1,
  "class": 15,
  "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
  "procName": "",
  "precedingErrors": [],
  "stack": "RequestError: Incorrect syntax near the keyword 'key'.\n    at handleError (C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-03-22 21:21:04 [ERROR]: Error getting permissions: Incorrect syntax near the keyword 'key'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 156,
      "state": 1,
      "class": 15,
      "message": "Incorrect syntax near the keyword 'key'.",
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": "",
      "lineNumber": 7
    }
  },
  "name": "RequestError",
  "number": 156,
  "lineNumber": 7,
  "state": 1,
  "class": 15,
  "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
  "procName": "",
  "precedingErrors": [],
  "stack": "RequestError: Incorrect syntax near the keyword 'key'.\n    at handleError (C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-03-22 21:21:04 [ERROR]: Error getting permissions: Incorrect syntax near the keyword 'key'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 156,
      "state": 1,
      "class": 15,
      "message": "Incorrect syntax near the keyword 'key'.",
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": "",
      "lineNumber": 7
    }
  },
  "name": "RequestError",
  "number": 156,
  "lineNumber": 7,
  "state": 1,
  "class": 15,
  "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
  "procName": "",
  "precedingErrors": [],
  "stack": "RequestError: Incorrect syntax near the keyword 'key'.\n    at handleError (C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-03-22 21:44:37 [INFO]: Database configuration loaded from file 
2025-03-22 21:44:37 [INFO]: Connected to SQL Server 
2025-03-22 21:44:37 [INFO]: Server running on port 5000 
2025-03-22 21:44:37 [INFO]: Environment: development 
2025-03-22 21:45:25 [ERROR]: Not Found - /api/api/roles/3 {
  "stack": "Error: Not Found - /api/api/roles/3\n    at notFound (C:\\claude\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\claude\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/api/api/roles/3",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-22 21:45:25 [ERROR]: Not Found - /api/api/roles/3 {
  "stack": "Error: Not Found - /api/api/roles/3\n    at notFound (C:\\claude\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\claude\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/api/api/roles/3",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-22 21:46:10 [ERROR]: Not Found - /api/api/roles/3 {
  "stack": "Error: Not Found - /api/api/roles/3\n    at notFound (C:\\claude\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\claude\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/api/api/roles/3",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-22 21:46:10 [ERROR]: Not Found - /api/api/roles/3 {
  "stack": "Error: Not Found - /api/api/roles/3\n    at notFound (C:\\claude\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\claude\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/api/api/roles/3",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-22 21:46:58 [ERROR]: Not Found - /api/api/roles/3 {
  "stack": "Error: Not Found - /api/api/roles/3\n    at notFound (C:\\claude\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\claude\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/api/api/roles/3",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-22 21:46:58 [ERROR]: Not Found - /api/api/roles/3 {
  "stack": "Error: Not Found - /api/api/roles/3\n    at notFound (C:\\claude\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\claude\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/api/api/roles/3",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-22 21:47:16 [INFO]: Database configuration loaded from file 
2025-03-22 21:47:17 [INFO]: Connected to SQL Server 
2025-03-22 21:47:17 [INFO]: Server running on port 5000 
2025-03-22 21:47:17 [INFO]: Environment: development 
2025-03-22 21:47:37 [WARN]: User (ID: 1, role: undefined) does not have permission: /admin/roles - READ 
2025-03-22 21:47:37 [WARN]: User (ID: 1, role: undefined) does not have permission: /admin/roles - READ 
2025-03-22 21:49:16 [INFO]: Database configuration loaded from file 
2025-03-22 21:49:17 [INFO]: Connected to SQL Server 
2025-03-22 21:49:17 [INFO]: Server running on port 5000 
2025-03-22 21:49:17 [INFO]: Environment: development 
2025-03-22 21:50:38 [ERROR]: Not Found - /api/api/roles/1 {
  "stack": "Error: Not Found - /api/api/roles/1\n    at notFound (C:\\claude\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\claude\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/api/api/roles/1",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-22 21:50:38 [ERROR]: Not Found - /api/api/roles/1 {
  "stack": "Error: Not Found - /api/api/roles/1\n    at notFound (C:\\claude\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\claude\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/api/api/roles/1",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-22 21:53:44 [INFO]: Database configuration loaded from file 
2025-03-22 21:53:44 [INFO]: Connected to SQL Server 
2025-03-22 21:53:44 [INFO]: Server running on port 5000 
2025-03-22 21:53:44 [INFO]: Environment: development 
2025-03-22 22:10:31 [INFO]: Database configuration loaded from file 
2025-03-22 22:10:31 [INFO]: Connected to SQL Server 
2025-03-22 22:10:31 [INFO]: Server running on port 5000 
2025-03-22 22:10:31 [INFO]: Environment: development 
2025-03-22 22:19:54 [INFO]: Database configuration loaded from file 
2025-03-22 22:19:54 [INFO]: Connected to SQL Server 
2025-03-22 22:19:54 [INFO]: Server running on port 5000 
2025-03-22 22:19:54 [INFO]: Environment: development 
2025-03-22 22:25:56 [INFO]: Database configuration loaded from file 
2025-03-22 22:25:56 [INFO]: Connected to SQL Server 
2025-03-22 22:25:56 [INFO]: Server running on port 5000 
2025-03-22 22:25:56 [INFO]: Environment: development 
2025-03-23 00:30:28 [INFO]: Database configuration loaded from file 
2025-03-23 00:30:28 [INFO]: Connected to SQL Server 
2025-03-23 00:30:28 [INFO]: Server running on port 5000 
2025-03-23 00:30:28 [INFO]: Environment: development 
2025-03-23 00:40:18 [INFO]: Database configuration loaded from file 
2025-03-23 00:40:19 [INFO]: Connected to SQL Server 
2025-03-23 00:40:19 [INFO]: Server running on port 5000 
2025-03-23 00:40:19 [INFO]: Environment: development 
2025-03-23 00:42:20 [INFO]: Database configuration loaded from file 
2025-03-23 00:42:20 [INFO]: Connected to SQL Server 
2025-03-23 00:42:20 [INFO]: Server running on port 5000 
2025-03-23 00:42:20 [INFO]: Environment: development 
2025-03-23 00:44:40 [INFO]: Database configuration loaded from file 
2025-03-23 00:44:41 [INFO]: Connected to SQL Server 
2025-03-23 00:44:41 [INFO]: Server running on port 5000 
2025-03-23 00:44:41 [INFO]: Environment: development 
2025-03-23 00:45:01 [INFO]: Database configuration loaded from file 
2025-03-23 00:45:01 [INFO]: Connected to SQL Server 
2025-03-23 00:45:01 [INFO]: Server running on port 5000 
2025-03-23 00:45:01 [INFO]: Environment: development 
2025-03-23 01:02:05 [INFO]: Database configuration loaded from file 
2025-03-23 01:02:06 [INFO]: Connected to SQL Server 
2025-03-23 01:02:06 [INFO]: Server running on port 5000 
2025-03-23 01:02:06 [INFO]: Environment: development 
2025-03-23 01:03:21 [INFO]: Database configuration loaded from file 
2025-03-23 01:03:21 [INFO]: Connected to SQL Server 
2025-03-23 01:03:21 [INFO]: Server running on port 5000 
2025-03-23 01:03:21 [INFO]: Environment: development 
2025-03-23 01:08:21 [INFO]: Database configuration loaded from file 
2025-03-23 01:08:21 [INFO]: Connected to SQL Server 
2025-03-23 01:08:21 [INFO]: Server running on port 5000 
2025-03-23 01:08:21 [INFO]: Environment: development 
2025-03-23 01:08:42 [INFO]: Database configuration loaded from file 
2025-03-23 01:08:42 [INFO]: Connected to SQL Server 
2025-03-23 01:08:42 [INFO]: Server running on port 5000 
2025-03-23 01:08:42 [INFO]: Environment: development 
2025-03-23 01:09:06 [INFO]: Database configuration loaded from file 
2025-03-23 01:09:06 [INFO]: Connected to SQL Server 
2025-03-23 01:09:06 [INFO]: Server running on port 5000 
2025-03-23 01:09:06 [INFO]: Environment: development 
2025-03-23 01:09:19 [INFO]: Database configuration loaded from file 
2025-03-23 01:09:19 [INFO]: Connected to SQL Server 
2025-03-23 01:09:19 [INFO]: Server running on port 5000 
2025-03-23 01:09:19 [INFO]: Environment: development 
2025-03-23 01:10:07 [ERROR]: Kullanıcı oluşturma hatası: Invalid column name 'Password'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 207,
      "state": 1,
      "class": 16,
      "message": "Invalid column name 'Password'.",
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": "",
      "lineNumber": 7
    }
  },
  "name": "RequestError",
  "number": 207,
  "lineNumber": 7,
  "state": 1,
  "class": 16,
  "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
  "procName": "",
  "precedingErrors": [],
  "stack": "RequestError: Invalid column name 'Password'.\n    at handleError (C:\\claude\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\claude\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-03-23 01:11:37 [INFO]: Database configuration loaded from file 
2025-03-23 01:11:38 [INFO]: Connected to SQL Server 
2025-03-23 01:11:38 [INFO]: Server running on port 5000 
2025-03-23 01:11:38 [INFO]: Environment: development 
2025-03-23 01:11:55 [INFO]: Database configuration loaded from file 
2025-03-23 01:11:56 [INFO]: Connected to SQL Server 
2025-03-23 01:11:56 [INFO]: Server running on port 5000 
2025-03-23 01:11:56 [INFO]: Environment: development 
2025-03-23 01:12:08 [INFO]: Database configuration loaded from file 
2025-03-23 01:12:09 [INFO]: Connected to SQL Server 
2025-03-23 01:12:09 [INFO]: Server running on port 5000 
2025-03-23 01:12:09 [INFO]: Environment: development 
2025-03-23 01:12:38 [INFO]: Yeni kullanıcı oluşturuldu. ID: 2, Kullanıcı Adı: burak 
2025-03-23 01:14:32 [INFO]: Database configuration loaded from file 
2025-03-23 01:14:32 [INFO]: Connected to SQL Server 
2025-03-23 01:14:32 [INFO]: Server running on port 5000 
2025-03-23 01:14:32 [INFO]: Environment: development 
2025-03-23 01:14:48 [INFO]: Database configuration loaded from file 
2025-03-23 01:14:49 [INFO]: Connected to SQL Server 
2025-03-23 01:14:49 [INFO]: Server running on port 5000 
2025-03-23 01:14:49 [INFO]: Environment: development 
2025-03-23 01:15:02 [INFO]: Database configuration loaded from file 
2025-03-23 01:15:02 [INFO]: Connected to SQL Server 
2025-03-23 01:15:02 [INFO]: Server running on port 5000 
2025-03-23 01:15:02 [INFO]: Environment: development 
2025-03-23 01:15:49 [INFO]: Database configuration loaded from file 
2025-03-23 01:15:49 [INFO]: Connected to SQL Server 
2025-03-23 01:15:49 [INFO]: Server running on port 5000 
2025-03-23 01:15:49 [INFO]: Environment: development 
2025-03-23 01:16:04 [INFO]: Database configuration loaded from file 
2025-03-23 01:16:04 [INFO]: Connected to SQL Server 
2025-03-23 01:16:04 [INFO]: Server running on port 5000 
2025-03-23 01:16:04 [INFO]: Environment: development 
2025-03-23 01:16:17 [INFO]: Database configuration loaded from file 
2025-03-23 01:16:17 [INFO]: Connected to SQL Server 
2025-03-23 01:16:17 [INFO]: Server running on port 5000 
2025-03-23 01:16:17 [INFO]: Environment: development 
2025-03-23 01:21:09 [INFO]: Database configuration loaded from file 
2025-03-23 01:21:09 [INFO]: Connected to SQL Server 
2025-03-23 01:21:09 [INFO]: Server running on port 5000 
2025-03-23 01:21:09 [INFO]: Environment: development 
2025-03-23 01:23:50 [INFO]: Database configuration loaded from file 
2025-03-23 01:23:50 [INFO]: Connected to SQL Server 
2025-03-23 01:23:50 [INFO]: Server running on port 5000 
2025-03-23 01:23:50 [INFO]: Environment: development 
2025-03-23 01:24:33 [INFO]: Database configuration loaded from file 
2025-03-23 01:24:33 [INFO]: Connected to SQL Server 
2025-03-23 01:24:33 [INFO]: Server running on port 5000 
2025-03-23 01:24:33 [INFO]: Environment: development 
2025-03-23 01:24:55 [INFO]: Database configuration loaded from file 
2025-03-23 01:24:56 [INFO]: Connected to SQL Server 
2025-03-23 01:24:56 [INFO]: Server running on port 5000 
2025-03-23 01:24:56 [INFO]: Environment: development 
2025-03-23 01:25:41 [INFO]: Database configuration loaded from file 
2025-03-23 01:25:41 [INFO]: Connected to SQL Server 
2025-03-23 01:25:41 [INFO]: Server running on port 5000 
2025-03-23 01:25:41 [INFO]: Environment: development 
2025-03-23 01:26:43 [INFO]: Database configuration loaded from file 
2025-03-23 01:26:43 [INFO]: Connected to SQL Server 
2025-03-23 01:26:43 [INFO]: Server running on port 5000 
2025-03-23 01:26:43 [INFO]: Environment: development 
2025-03-23 01:26:48 [INFO]: Database configuration loaded from file 
2025-03-23 01:26:49 [INFO]: Connected to SQL Server 
2025-03-23 01:26:49 [INFO]: Server running on port 5000 
2025-03-23 01:26:49 [INFO]: Environment: development 
2025-03-23 01:27:32 [INFO]: Database configuration loaded from file 
2025-03-23 01:27:32 [INFO]: Connected to SQL Server 
2025-03-23 01:27:32 [INFO]: Server running on port 5000 
2025-03-23 01:27:32 [INFO]: Environment: development 
2025-03-23 01:27:58 [INFO]: Database configuration loaded from file 
2025-03-23 01:27:58 [INFO]: Connected to SQL Server 
2025-03-23 01:27:58 [INFO]: Server running on port 5000 
2025-03-23 01:27:58 [INFO]: Environment: development 
2025-03-23 01:30:26 [INFO]: Database configuration loaded from file 
2025-03-23 01:30:26 [INFO]: Connected to SQL Server 
2025-03-23 01:30:26 [INFO]: Server running on port 5000 
2025-03-23 01:30:26 [INFO]: Environment: development 
2025-03-23 01:30:56 [INFO]: Database configuration loaded from file 
2025-03-23 01:30:56 [INFO]: Connected to SQL Server 
2025-03-23 01:30:56 [INFO]: Server running on port 5000 
2025-03-23 01:30:56 [INFO]: Environment: development 
2025-03-23 01:32:43 [INFO]: Database configuration loaded from file 
2025-03-23 01:32:43 [INFO]: Connected to SQL Server 
2025-03-23 01:32:43 [INFO]: Server running on port 5000 
2025-03-23 01:32:43 [INFO]: Environment: development 
2025-03-23 01:34:03 [INFO]: Database configuration loaded from file 
2025-03-23 01:34:03 [INFO]: Connected to SQL Server 
2025-03-23 01:34:03 [INFO]: Server running on port 5000 
2025-03-23 01:34:03 [INFO]: Environment: development 
2025-03-23 01:35:39 [INFO]: Database configuration loaded from file 
2025-03-23 01:35:40 [INFO]: Connected to SQL Server 
2025-03-23 01:35:40 [INFO]: Server running on port 5000 
2025-03-23 01:35:40 [INFO]: Environment: development 
2025-03-23 01:36:33 [INFO]: Database configuration loaded from file 
2025-03-23 01:36:33 [INFO]: Connected to SQL Server 
2025-03-23 01:36:33 [INFO]: Server running on port 5000 
2025-03-23 01:36:33 [INFO]: Environment: development 
2025-03-23 01:37:05 [INFO]: Database configuration loaded from file 
2025-03-23 01:37:05 [INFO]: Connected to SQL Server 
2025-03-23 01:37:05 [INFO]: Server running on port 5000 
2025-03-23 01:37:05 [INFO]: Environment: development 
2025-03-23 01:37:40 [INFO]: Database configuration loaded from file 
2025-03-23 01:37:40 [INFO]: Connected to SQL Server 
2025-03-23 01:37:40 [INFO]: Server running on port 5000 
2025-03-23 01:37:40 [INFO]: Environment: development 
2025-03-23 01:38:06 [INFO]: Database configuration loaded from file 
2025-03-23 01:38:07 [INFO]: Connected to SQL Server 
2025-03-23 01:38:07 [INFO]: Server running on port 5000 
2025-03-23 01:38:07 [INFO]: Environment: development 
2025-03-23 01:38:15 [INFO]: Login attempt for username: admin 
2025-03-23 01:38:15 [WARN]: Login failed: Invalid password for user: admin 
2025-03-23 01:38:43 [INFO]: Login attempt for username: admin 
2025-03-23 01:38:43 [WARN]: Login failed: Invalid password for user: admin 
2025-03-23 01:39:01 [INFO]: Database configuration loaded from file 
2025-03-23 01:39:01 [INFO]: Connected to SQL Server 
2025-03-23 01:39:01 [INFO]: Server running on port 5000 
2025-03-23 01:39:01 [INFO]: Environment: development 
2025-03-23 01:39:24 [INFO]: Login attempt for username: admin 
2025-03-23 01:39:24 [WARN]: Login failed: Invalid password for user: admin 
2025-03-23 01:39:37 [INFO]: Login attempt for username: burak 
2025-03-23 01:39:37 [INFO]: Login successful for user: burak (ID: 2, Role: Supervisor) 
2025-03-23 01:41:08 [WARN]: User (ID: 2, role: 2) does not have permission: /admin/roles - READ 
2025-03-23 01:41:08 [WARN]: User (ID: 2, role: 2) does not have permission: /admin/roles - READ 
2025-03-23 01:41:13 [WARN]: User (ID: 2, role: 2) does not have permission: /admin/roles - READ 
2025-03-23 01:41:13 [WARN]: User (ID: 2, role: 2) does not have permission: /admin/roles - READ 
2025-03-23 01:41:28 [INFO]: Database configuration loaded from file 
2025-03-23 01:41:28 [INFO]: Connected to SQL Server 
2025-03-23 01:41:28 [INFO]: Server running on port 5000 
2025-03-23 01:41:28 [INFO]: Environment: development 
2025-03-23 01:42:17 [INFO]: Database configuration loaded from file 
2025-03-23 01:42:17 [INFO]: Connected to SQL Server 
2025-03-23 01:42:17 [INFO]: Server running on port 5000 
2025-03-23 01:42:17 [INFO]: Environment: development 
2025-03-23 01:42:46 [INFO]: Database configuration loaded from file 
2025-03-23 01:42:47 [INFO]: Connected to SQL Server 
2025-03-23 01:42:47 [INFO]: Server running on port 5000 
2025-03-23 01:42:47 [INFO]: Environment: development 
2025-03-23 01:43:39 [INFO]: Database configuration loaded from file 
2025-03-23 01:45:49 [INFO]: Database configuration loaded from file 
2025-03-23 01:46:58 [INFO]: Database configuration loaded from file 
2025-03-23 01:47:53 [INFO]: Database configuration loaded from file 
2025-03-23 01:47:53 [INFO]: Connected to SQL Server 
2025-03-23 01:47:53 [INFO]: Server running on port 5000 
2025-03-23 01:47:53 [INFO]: Environment: development 
2025-03-23 01:48:19 [INFO]: Login attempt for username: burak 
2025-03-23 01:48:19 [INFO]: Login successful for user: burak (ID: 2, Role: Supervisor) 
2025-03-23 01:50:13 [INFO]: Database configuration loaded from file 
2025-03-23 01:50:13 [INFO]: Connected to SQL Server 
2025-03-23 01:50:13 [INFO]: Server running on port 5000 
2025-03-23 01:50:13 [INFO]: Environment: development 
2025-03-23 01:50:59 [INFO]: Database configuration loaded from file 
2025-03-23 01:51:00 [INFO]: Connected to SQL Server 
2025-03-23 01:51:00 [INFO]: Server running on port 5000 
2025-03-23 01:51:00 [INFO]: Environment: development 
2025-03-23 01:52:03 [INFO]: Database configuration loaded from file 
2025-03-23 01:52:04 [INFO]: Connected to SQL Server 
2025-03-23 01:52:04 [INFO]: Server running on port 5000 
2025-03-23 01:52:04 [INFO]: Environment: development 
2025-03-23 01:52:24 [INFO]: Database configuration loaded from file 
2025-03-23 01:52:25 [INFO]: Connected to SQL Server 
2025-03-23 01:52:25 [INFO]: Server running on port 5000 
2025-03-23 01:52:25 [INFO]: Environment: development 
2025-03-23 01:52:44 [INFO]: Database configuration loaded from file 
2025-03-23 01:52:44 [INFO]: Connected to SQL Server 
2025-03-23 01:52:44 [INFO]: Server running on port 5000 
2025-03-23 01:52:44 [INFO]: Environment: development 
2025-03-23 01:52:57 [INFO]: Login attempt for username: admin 
2025-03-23 01:52:58 [WARN]: Login failed: Invalid password for user: admin {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-23 01:53:10 [INFO]: Login attempt for username: burak 
2025-03-23 01:53:10 [WARN]: Login failed: Invalid password for user: burak {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-23 01:53:14 [INFO]: Login attempt for username: burak 
2025-03-23 01:53:14 [WARN]: Security alert: Common password attempt for user: burak 
2025-03-23 01:53:14 [INFO]: Login successful for user: burak (ID: 2, Role: Supervisor) 
2025-03-23 01:53:37 [INFO]: Database configuration loaded from file 
2025-03-23 01:53:37 [INFO]: Connected to SQL Server 
2025-03-23 01:53:37 [INFO]: Server running on port 5000 
2025-03-23 01:53:37 [INFO]: Environment: development 
2025-03-23 01:54:53 [INFO]: Database configuration loaded from file 
2025-03-23 01:54:53 [INFO]: Connected to SQL Server 
2025-03-23 01:54:53 [INFO]: Server running on port 5000 
2025-03-23 01:54:53 [INFO]: Environment: development 
2025-03-23 01:57:33 [INFO]: Login attempt for username: testuser 
2025-03-23 01:57:34 [WARN]: Login failed: Invalid password for user: testuser {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-23 01:57:41 [INFO]: Login attempt for username: testuser 
2025-03-23 01:57:41 [WARN]: Login failed: Invalid password for user: testuser {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-23 01:57:52 [INFO]: Login attempt for username: testuser 
2025-03-23 01:57:52 [WARN]: Security alert: Common password attempt for user: testuser 
2025-03-23 01:57:52 [WARN]: Login failed: Invalid password for user: testuser {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-23 01:57:58 [INFO]: Login attempt for username: testuser 
2025-03-23 01:57:58 [WARN]: Login failed: Invalid password for user: testuser {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-23 01:58:55 [INFO]: Login attempt for username: testuser 
2025-03-23 01:58:55 [WARN]: Login failed: Invalid password for user: testuser {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-23 01:59:23 [INFO]: Database configuration loaded from file 
2025-03-23 01:59:23 [INFO]: Connected to SQL Server 
2025-03-23 01:59:23 [INFO]: Server running on port 5000 
2025-03-23 01:59:23 [INFO]: Environment: development 
2025-03-23 02:00:23 [INFO]: Database configuration loaded from file 
2025-03-23 02:00:24 [INFO]: Connected to SQL Server 
2025-03-23 02:00:24 [INFO]: Server running on port 5000 
2025-03-23 02:00:24 [INFO]: Environment: development 
2025-03-23 02:01:15 [INFO]: Database configuration loaded from file 
2025-03-23 02:01:15 [INFO]: Connected to SQL Server 
2025-03-23 02:01:15 [INFO]: Server running on port 5000 
2025-03-23 02:01:15 [INFO]: Environment: development 
2025-03-23 02:05:19 [INFO]: Database configuration loaded from file 
2025-03-23 02:05:19 [INFO]: Connected to SQL Server 
2025-03-23 02:05:19 [INFO]: Server running on port 5000 
2025-03-23 02:05:19 [INFO]: Environment: development 
2025-03-23 02:05:44 [INFO]: Login attempt for username: admin 
2025-03-23 02:05:44 [WARN]: Login failed: Invalid password for user: admin {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-23 02:05:50 [INFO]: Login attempt for username: burak 
2025-03-23 02:05:50 [WARN]: Security alert: Common password attempt for user: burak 
2025-03-23 02:05:50 [INFO]: Login successful for user: burak (ID: 2, Role: Supervisor) 
2025-03-23 02:06:00 [INFO]: Login attempt for username: testuser 
2025-03-23 02:06:00 [WARN]: Login failed: Invalid password for user: testuser {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-23 02:09:45 [INFO]: Database configuration loaded from file 
2025-03-23 02:09:45 [INFO]: Connected to SQL Server 
2025-03-23 02:09:45 [INFO]: Server running on port 5000 
2025-03-23 02:09:45 [INFO]: Environment: development 
2025-03-23 02:10:58 [INFO]: Database configuration loaded from file 
2025-03-23 02:10:58 [INFO]: Connected to SQL Server 
2025-03-23 02:10:58 [INFO]: Server running on port 5000 
2025-03-23 02:10:58 [INFO]: Environment: development 
2025-03-23 02:11:26 [INFO]: Login attempt for username: admin 
2025-03-23 02:11:26 [WARN]: Login failed: Invalid password for user: admin {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-23 02:11:32 [INFO]: Login attempt for username: burak 
2025-03-23 02:11:32 [WARN]: Security alert: Common password attempt for user: burak 
2025-03-23 02:11:32 [INFO]: Login successful for user: burak (ID: 2, Role: Supervisor) 
2025-03-23 02:15:00 [INFO]: Database configuration loaded from file 
2025-03-23 02:15:01 [INFO]: Connected to SQL Server 
2025-03-23 02:15:01 [INFO]: Server running on port 5000 
2025-03-23 02:15:01 [INFO]: Environment: development 
2025-03-23 02:15:28 [INFO]: Database configuration loaded from file 
2025-03-23 02:15:28 [INFO]: Connected to SQL Server 
2025-03-23 02:15:28 [INFO]: Server running on port 5000 
2025-03-23 02:15:28 [INFO]: Environment: development 
2025-03-23 02:17:28 [INFO]: Login attempt for username: admin 
2025-03-23 02:17:28 [WARN]: Security alert: Common password attempt for user: admin 
2025-03-23 02:17:28 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-23 02:20:16 [INFO]: Database configuration loaded from file 
2025-03-23 02:20:16 [INFO]: Connected to SQL Server 
2025-03-23 02:20:16 [INFO]: Server running on port 5000 
2025-03-23 02:20:16 [INFO]: Environment: development 
2025-03-23 02:22:26 [INFO]: Login attempt for username: admin 
2025-03-23 02:22:26 [WARN]: Security alert: Common password attempt for user: admin 
2025-03-23 02:22:26 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-23 02:23:31 [INFO]: Login attempt for username: admin 
2025-03-23 02:23:31 [WARN]: Security alert: Common password attempt for user: admin 
2025-03-23 02:23:31 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-23 02:23:54 [INFO]: Login attempt for username: burak 
2025-03-23 02:23:54 [WARN]: Security alert: Common password attempt for user: burak 
2025-03-23 02:23:55 [INFO]: Login successful for user: burak (ID: 2, Role: Supervisor) 
2025-03-23 02:24:07 [INFO]: Login attempt for username: admin 
2025-03-23 02:24:07 [WARN]: Security alert: Common password attempt for user: admin 
2025-03-23 02:24:08 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-23 02:24:50 [INFO]: Database configuration loaded from file 
2025-03-23 02:24:50 [INFO]: Connected to SQL Server 
2025-03-23 02:24:50 [INFO]: Server running on port 5000 
2025-03-23 02:24:50 [INFO]: Environment: development 
2025-03-23 02:28:34 [INFO]: Yeni kullanıcı oluşturuldu. ID: 4, Kullanıcı Adı: baha 
2025-03-23 02:28:45 [INFO]: Login attempt for username: baha 
2025-03-23 02:28:45 [INFO]: Login successful for user: baha (ID: 4, Role: Supervisor) 
2025-03-23 02:28:57 [INFO]: Login attempt for username: admin 
2025-03-23 02:28:57 [WARN]: Security alert: Common password attempt for user: admin 
2025-03-23 02:28:58 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-23 02:29:30 [INFO]: Login attempt for username: admin 
2025-03-23 02:29:30 [WARN]: Security alert: Common password attempt for user: admin 
2025-03-23 02:29:30 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-23 02:36:31 [INFO]: Login attempt for username: burak 
2025-03-23 02:36:31 [WARN]: Security alert: Common password attempt for user: burak 
2025-03-23 02:36:31 [INFO]: Login successful for user: burak (ID: 2, Role: Supervisor) 
2025-03-23 02:36:38 [INFO]: Login attempt for username: admin 
2025-03-23 02:36:38 [WARN]: Security alert: Common password attempt for user: admin 
2025-03-23 02:36:38 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-23 02:43:12 [INFO]: Database configuration loaded from file 
2025-03-23 02:43:12 [INFO]: Connected to SQL Server 
2025-03-23 02:43:12 [INFO]: Server running on port 5000 
2025-03-23 02:43:12 [INFO]: Environment: development 
2025-03-23 02:43:27 [INFO]: Database configuration loaded from file 
2025-03-23 02:43:27 [INFO]: Connected to SQL Server 
2025-03-23 02:43:27 [INFO]: Server running on port 5000 
2025-03-23 02:43:27 [INFO]: Environment: development 
2025-03-23 02:45:57 [INFO]: Database configuration loaded from file 
2025-03-23 02:45:57 [INFO]: Connected to SQL Server 
2025-03-23 02:45:57 [INFO]: Server running on port 5000 
2025-03-23 02:45:57 [INFO]: Environment: development 
2025-03-23 02:46:20 [INFO]: Database configuration loaded from file 
2025-03-23 02:46:20 [INFO]: Connected to SQL Server 
2025-03-23 02:46:20 [INFO]: Server running on port 5000 
2025-03-23 02:46:20 [INFO]: Environment: development 
2025-03-23 02:47:36 [INFO]: Database configuration loaded from file 
2025-03-23 02:47:36 [INFO]: Connected to SQL Server 
2025-03-23 02:47:36 [INFO]: Server running on port 5000 
2025-03-23 02:47:36 [INFO]: Environment: development 
2025-03-23 02:48:25 [INFO]: Database configuration loaded from file 
2025-03-23 02:48:25 [INFO]: Connected to SQL Server 
2025-03-23 02:48:25 [INFO]: Server running on port 5000 
2025-03-23 02:48:25 [INFO]: Environment: development 
2025-03-23 02:55:26 [INFO]: Login attempt for username: admin 
2025-03-23 02:55:26 [WARN]: Security alert: Common password attempt for user: admin 
2025-03-23 02:55:27 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-23 02:58:06 [INFO]: Database configuration loaded from file 
2025-03-23 02:58:06 [INFO]: Connected to SQL Server 
2025-03-23 02:58:06 [INFO]: Server running on port 5000 
2025-03-23 02:58:06 [INFO]: Environment: development 
2025-03-23 02:59:05 [INFO]: Kullanıcının şifresi değiştirildi. ID: 1 
2025-03-23 02:59:13 [INFO]: Login attempt for username: admin 
2025-03-23 02:59:13 [WARN]: Security alert: Common password attempt for user: admin 
2025-03-23 02:59:13 [WARN]: Login failed: Invalid password for user: admin {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-23 02:59:15 [INFO]: Login attempt for username: admin 
2025-03-23 02:59:15 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-23 03:00:16 [INFO]: Database configuration loaded from file 
2025-03-23 03:00:16 [INFO]: Connected to SQL Server 
2025-03-23 03:00:16 [INFO]: Server running on port 5000 
2025-03-23 03:00:16 [INFO]: Environment: development 
2025-03-23 13:03:20 [INFO]: Database configuration loaded from file 
2025-03-23 13:03:20 [INFO]: Connected to SQL Server 
2025-03-23 13:03:20 [INFO]: Server running on port 5000 
2025-03-23 13:03:20 [INFO]: Environment: development 
2025-03-23 13:04:07 [INFO]: Login attempt for username: admin 
2025-03-23 13:04:07 [WARN]: Security alert: Common password attempt for user: admin 
2025-03-23 13:04:07 [WARN]: Login failed: Invalid password for user: admin {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-23 13:04:08 [INFO]: Login attempt for username: admin 
2025-03-23 13:04:09 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-23 13:12:37 [INFO]: Login attempt for username: admin 
2025-03-23 13:12:37 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-23 13:16:20 [INFO]: Login attempt for username: admin 
2025-03-23 13:16:20 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-23 13:19:10 [INFO]: Database configuration loaded from file 
2025-03-23 13:19:37 [INFO]: Database configuration loaded from file 
2025-03-23 13:19:37 [INFO]: Connected to SQL Server 
2025-03-23 13:19:37 [INFO]: Server running on port 5000 
2025-03-23 13:19:37 [INFO]: Environment: development 
2025-03-23 13:19:51 [INFO]: Database configuration loaded from file 
2025-03-23 13:19:51 [INFO]: Connected to SQL Server 
2025-03-23 13:19:51 [INFO]: Server running on port 5000 
2025-03-23 13:19:51 [INFO]: Environment: development 
2025-03-23 13:20:22 [INFO]: Login attempt for username: admin 
2025-03-23 13:20:22 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-23 13:22:13 [INFO]: Database configuration loaded from file 
2025-03-23 13:22:13 [INFO]: Connected to SQL Server 
2025-03-23 13:22:13 [INFO]: Server running on port 5000 
2025-03-23 13:22:13 [INFO]: Environment: development 
2025-03-23 13:23:02 [ERROR]: jwt malformed {
  "name": "JsonWebTokenError",
  "stack": "JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\claude\\burky_root_web\\server\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at module.exports (C:\\claude\\burky_root_web\\server\\middleware\\auth.js:23:25)\n    at Layer.handle [as handle_request] (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\claude\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\claude\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)"
}
2025-03-23 13:47:56 [INFO]: Login attempt for username: admin 
2025-03-23 13:47:56 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-23 13:54:31 [INFO]: Database configuration loaded from file 
2025-03-23 13:54:32 [INFO]: Connected to SQL Server 
2025-03-23 13:54:32 [INFO]: Server running on port 5000 
2025-03-23 13:54:32 [INFO]: Environment: development 
2025-03-23 14:07:45 [INFO]: Database configuration loaded from file 
2025-03-23 14:07:45 [INFO]: Connected to SQL Server 
2025-03-23 14:07:45 [INFO]: Server running on port 5000 
2025-03-23 14:07:45 [INFO]: Environment: development 
2025-03-23 14:29:40 [INFO]: Kullanıcı güncellendi. ID: 1 
2025-03-23 14:29:49 [INFO]: Login attempt for username: admin 
2025-03-23 14:29:49 [WARN]: Login failed: Invalid password for user: admin {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-23 14:29:51 [INFO]: Login attempt for username: admin 
2025-03-23 14:29:51 [WARN]: Security alert: Common password attempt for user: admin 
2025-03-23 14:29:51 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-23 14:30:19 [INFO]: Kullanıcının şifresi değiştirildi. ID: 1 
2025-03-23 14:34:00 [INFO]: Kullanıcı güncellendi. ID: 1 
2025-03-23 14:34:12 [INFO]: Login attempt for username: admin 
2025-03-23 14:34:12 [WARN]: Security alert: Common password attempt for user: admin 
2025-03-23 14:34:12 [WARN]: Login failed: Invalid password for user: admin {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-23 14:34:13 [INFO]: Login attempt for username: admin 
2025-03-23 14:34:13 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-23 14:34:21 [INFO]: Kullanıcı güncellendi. ID: 1 
2025-03-23 14:44:35 [INFO]: Login attempt for username: admin 
2025-03-23 14:44:35 [WARN]: Security alert: Common password attempt for user: admin 
2025-03-23 14:44:36 [WARN]: Login failed: Invalid password for user: admin {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-23 14:44:38 [INFO]: Login attempt for username: admin 
2025-03-23 14:44:38 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-23 14:45:04 [INFO]: Kullanıcı güncellendi. ID: 2 
2025-03-23 14:45:12 [INFO]: Login attempt for username: burak 
2025-03-23 14:45:12 [WARN]: Security alert: Common password attempt for user: burak 
2025-03-23 14:45:12 [INFO]: Login successful for user: burak (ID: 2, Role: burak rol1) 
2025-03-23 14:52:45 [INFO]: Login attempt for username: admin 
2025-03-23 14:52:45 [WARN]: Security alert: Common password attempt for user: admin 
2025-03-23 14:52:46 [WARN]: Login failed: Invalid password for user: admin {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-23 14:52:48 [INFO]: Login attempt for username: admin 
2025-03-23 14:52:48 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-24 02:13:59 [INFO]: Database configuration loaded from file 
2025-03-24 02:14:00 [INFO]: Connected to SQL Server 
2025-03-24 02:14:00 [INFO]: Server running on port 5000 
2025-03-24 02:14:00 [INFO]: Environment: development 
2025-03-24 02:15:14 [INFO]: Login attempt for username: admin 
2025-03-24 02:15:14 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-24 03:05:35 [INFO]: Database configuration loaded from file 
2025-03-24 03:06:08 [INFO]: Database configuration loaded from file 
2025-03-24 03:06:08 [INFO]: Connected to SQL Server 
2025-03-24 03:06:08 [INFO]: Server running on port 5000 
2025-03-24 03:06:08 [INFO]: Environment: development 
2025-03-24 03:06:55 [INFO]: Database configuration loaded from file 
2025-03-24 03:06:56 [INFO]: Connected to SQL Server 
2025-03-24 03:06:56 [INFO]: Server running on port 5000 
2025-03-24 03:06:56 [INFO]: Environment: development 
2025-03-24 03:08:18 [INFO]: Database configuration loaded from file 
2025-03-24 03:08:18 [INFO]: Connected to SQL Server 
2025-03-24 03:08:18 [INFO]: Server running on port 5000 
2025-03-24 03:08:18 [INFO]: Environment: development 
2025-03-24 03:08:57 [INFO]: Database configuration loaded from file 
2025-03-24 03:08:57 [INFO]: Connected to SQL Server 
2025-03-24 03:08:57 [INFO]: Server running on port 5000 
2025-03-24 03:08:57 [INFO]: Environment: development 
2025-03-24 03:09:41 [INFO]: Database configuration loaded from file 
2025-03-24 03:09:41 [INFO]: Connected to SQL Server 
2025-03-24 03:09:41 [INFO]: Server running on port 5000 
2025-03-24 03:09:41 [INFO]: Environment: development 
2025-03-24 03:10:05 [INFO]: Database configuration loaded from file 
2025-03-24 03:10:05 [INFO]: Connected to SQL Server 
2025-03-24 03:10:05 [INFO]: Server running on port 5000 
2025-03-24 03:10:05 [INFO]: Environment: development 
2025-03-24 03:10:11 [INFO]: Database configuration loaded from file 
2025-03-24 03:10:11 [INFO]: Connected to SQL Server 
2025-03-24 03:10:11 [INFO]: Server running on port 5000 
2025-03-24 03:10:11 [INFO]: Environment: development 
2025-03-24 03:10:51 [INFO]: Database configuration loaded from file 
2025-03-24 03:10:51 [INFO]: Connected to SQL Server 
2025-03-24 03:10:51 [INFO]: Server running on port 5000 
2025-03-24 03:10:51 [INFO]: Environment: development 
2025-03-24 03:11:28 [INFO]: Database configuration loaded from file 
2025-03-24 03:11:28 [INFO]: Connected to SQL Server 
2025-03-24 03:11:28 [INFO]: Server running on port 5000 
2025-03-24 03:11:28 [INFO]: Environment: development 
2025-03-24 03:11:46 [INFO]: Database configuration loaded from file 
2025-03-24 03:11:47 [INFO]: Connected to SQL Server 
2025-03-24 03:11:47 [INFO]: Server running on port 5000 
2025-03-24 03:11:47 [INFO]: Environment: development 
2025-03-24 03:11:56 [INFO]: Database configuration loaded from file 
2025-03-24 03:11:56 [INFO]: Connected to SQL Server 
2025-03-24 03:11:56 [INFO]: Server running on port 5000 
2025-03-24 03:11:56 [INFO]: Environment: development 
2025-03-24 03:12:09 [INFO]: Database configuration loaded from file 
2025-03-24 03:12:09 [INFO]: Connected to SQL Server 
2025-03-24 03:12:09 [INFO]: Server running on port 5000 
2025-03-24 03:12:09 [INFO]: Environment: development 
2025-03-24 03:14:47 [INFO]: Database configuration loaded from file 
2025-03-24 03:14:48 [INFO]: Connected to SQL Server 
2025-03-24 03:17:49 [INFO]: Database configuration loaded from file 
2025-03-24 03:17:49 [INFO]: Connected to SQL Server 
2025-03-24 03:17:49 [INFO]: Server running on port 5000 
2025-03-24 03:17:49 [INFO]: Environment: development 
2025-03-24 03:26:15 [INFO]: Database configuration loaded from file 
2025-03-24 03:26:15 [INFO]: Connected to SQL Server 
2025-03-24 03:26:15 [INFO]: Server running on port 5000 
2025-03-24 03:26:15 [INFO]: Environment: development 
2025-03-24 03:27:11 [INFO]: Login attempt for username: expert 
2025-03-24 03:27:11 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-24 03:28:10 [INFO]: Login attempt for username: admin 
2025-03-24 03:28:10 [WARN]: Security alert: Common password attempt for user: admin 
2025-03-24 03:28:10 [WARN]: Login failed: Invalid password for user: admin {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-24 03:28:13 [INFO]: Login attempt for username: admin 
2025-03-24 03:28:13 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-24 03:28:45 [INFO]: Login attempt for username: expert 
2025-03-24 03:28:46 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-24 03:40:01 [INFO]: Login attempt for username: admin 
2025-03-24 03:40:02 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-24 03:42:21 [INFO]: Login attempt for username: expert 
2025-03-24 03:42:21 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-24 03:42:30 [INFO]: Login attempt for username: admin 
2025-03-24 03:42:30 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-24 03:42:47 [INFO]: Login attempt for username: admin 
2025-03-24 03:42:47 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-24 03:44:08 [INFO]: Login attempt for username: expert 
2025-03-24 03:44:08 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-24 03:50:32 [INFO]: Login attempt for username: admin 
2025-03-24 03:50:32 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-24 03:51:09 [INFO]: Login attempt for username: expert 
2025-03-24 03:51:09 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-24 03:51:18 [INFO]: Login attempt for username: admin 
2025-03-24 03:51:19 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-24 03:51:48 [INFO]: Kullanıcı güncellendi. ID: 5 
2025-03-24 03:52:06 [INFO]: Login attempt for username: expert 
2025-03-24 03:52:06 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-24 04:16:37 [INFO]: Login attempt for username: expert 
2025-03-24 04:16:38 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-24 04:16:48 [INFO]: Login attempt for username: admin 
2025-03-24 04:16:49 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-24 04:17:24 [INFO]: Login attempt for username: admin 
2025-03-24 04:17:25 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-24 04:18:05 [INFO]: Login attempt for username: burak 
2025-03-24 04:18:05 [WARN]: Login failed: Invalid password for user: burak {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-24 04:18:07 [INFO]: Login attempt for username: burak 
2025-03-24 04:18:07 [WARN]: Security alert: Common password attempt for user: burak 
2025-03-24 04:18:08 [INFO]: Login successful for user: burak (ID: 2, Role: burak rol1) 
2025-03-24 04:18:21 [INFO]: Login attempt for username: expert 
2025-03-24 04:18:22 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-24 04:41:58 [INFO]: Login attempt for username: admin 
2025-03-24 04:41:59 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-24 04:42:54 [INFO]: Login attempt for username: expert 
2025-03-24 04:42:54 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-24 04:47:04 [INFO]: Login attempt for username: admin 
2025-03-24 04:47:04 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-24 04:51:30 [INFO]: Login attempt for username: expert 
2025-03-24 04:51:30 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-24 04:54:34 [INFO]: Database configuration loaded from file 
2025-03-24 04:54:34 [INFO]: Connected to SQL Server 
2025-03-24 04:54:34 [INFO]: Server running on port 5000 
2025-03-24 04:54:34 [INFO]: Environment: development 
2025-03-24 04:59:37 [INFO]: Login attempt for username: burak 
2025-03-24 04:59:37 [WARN]: Login failed: Invalid password for user: burak {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-24 04:59:40 [INFO]: Login attempt for username: burak 
2025-03-24 04:59:40 [WARN]: Security alert: Common password attempt for user: burak 
2025-03-24 04:59:40 [INFO]: Login successful for user: burak (ID: 2, Role: burak rol1) 
2025-03-24 04:59:48 [INFO]: Login attempt for username: admin 
2025-03-24 04:59:48 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-24 05:06:40 [INFO]: Login attempt for username: burak 
2025-03-24 05:06:40 [WARN]: Security alert: Common password attempt for user: burak 
2025-03-24 05:06:40 [INFO]: Login successful for user: burak (ID: 2, Role: burak rol1) 
2025-03-24 05:14:14 [INFO]: Login attempt for username: admin 
2025-03-24 05:14:14 [WARN]: Login failed: Invalid password for user: admin {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-24 05:14:17 [INFO]: Login attempt for username: admin 
2025-03-24 05:14:18 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-24 05:28:11 [INFO]: Login attempt for username: burak 
2025-03-24 05:28:11 [WARN]: Security alert: Common password attempt for user: burak 
2025-03-24 05:28:12 [INFO]: Login successful for user: burak (ID: 2, Role: burak rol1) 
2025-03-24 05:28:29 [INFO]: Login attempt for username: admin 
2025-03-24 05:28:29 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-24 05:29:10 [INFO]: Login attempt for username: burak 
2025-03-24 05:29:10 [WARN]: Security alert: Common password attempt for user: burak 
2025-03-24 05:29:10 [INFO]: Login successful for user: burak (ID: 2, Role: burak rol1) 
2025-03-24 05:29:15 [WARN]: User (ID: 2, role: 5) does not have permission: /admin/roles - READ 
2025-03-24 05:29:16 [WARN]: User (ID: 2, role: 5) does not have permission: /admin/roles - READ 
2025-03-24 05:29:16 [WARN]: User (ID: 2, role: 5) does not have permission: /admin/roles - READ 
2025-03-24 05:29:16 [WARN]: User (ID: 2, role: 5) does not have permission: /admin/roles - READ 
2025-03-24 05:29:23 [WARN]: User (ID: 2, role: 5) does not have permission: /admin/roles - READ 
2025-03-24 05:29:24 [WARN]: User (ID: 2, role: 5) does not have permission: /admin/roles - READ 
2025-03-24 05:29:24 [WARN]: User (ID: 2, role: 5) does not have permission: /admin/roles - READ 
2025-03-24 05:29:24 [WARN]: User (ID: 2, role: 5) does not have permission: /admin/roles - READ 
2025-03-24 05:29:43 [INFO]: Login attempt for username: admin 
2025-03-24 05:29:43 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-24 05:30:11 [INFO]: Login attempt for username: burak 
2025-03-24 05:30:11 [WARN]: Security alert: Common password attempt for user: burak 
2025-03-24 05:30:11 [INFO]: Login successful for user: burak (ID: 2, Role: burak rol1) 
2025-03-24 05:42:22 [INFO]: Login attempt for username: burak 
2025-03-24 05:42:22 [WARN]: Security alert: Common password attempt for user: burak 
2025-03-24 05:42:22 [INFO]: Login successful for user: burak (ID: 2, Role: burak rol1) 
2025-03-24 14:58:28 [INFO]: Database configuration loaded from file 
2025-03-24 14:58:28 [INFO]: Connected to SQL Server 
2025-03-24 14:58:28 [INFO]: Server running on port 5000 
2025-03-24 14:58:28 [INFO]: Environment: development 
2025-03-24 15:00:00 [INFO]: Login attempt for username: expert 
2025-03-24 15:00:00 [WARN]: Security alert: Common password attempt for user: expert 
2025-03-24 15:00:00 [WARN]: Login failed: Invalid password for user: expert {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-24 15:00:02 [INFO]: Login attempt for username: expert 
2025-03-24 15:00:02 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-24 15:00:19 [INFO]: Login attempt for username: burak 
2025-03-24 15:00:19 [WARN]: Login failed: Invalid password for user: burak {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-24 15:00:21 [INFO]: Login attempt for username: burak 
2025-03-24 15:00:21 [WARN]: Security alert: Common password attempt for user: burak 
2025-03-24 15:00:21 [INFO]: Login successful for user: burak (ID: 2, Role: burak rol1) 
2025-03-24 15:00:32 [INFO]: Login attempt for username: admin 
2025-03-24 15:00:32 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-24 15:01:09 [INFO]: Login attempt for username: burak 
2025-03-24 15:01:09 [WARN]: Security alert: Common password attempt for user: burak 
2025-03-24 15:01:10 [INFO]: Login successful for user: burak (ID: 2, Role: burak rol1) 
2025-03-24 15:02:09 [INFO]: Database configuration loaded from file 
2025-03-24 15:02:10 [INFO]: Connected to SQL Server 
2025-03-24 15:02:10 [INFO]: Server running on port 5000 
2025-03-24 15:02:10 [INFO]: Environment: development 
2025-03-24 15:44:06 [INFO]: Database configuration loaded from file 
2025-03-24 15:44:06 [INFO]: Connected to SQL Server 
2025-03-24 15:44:06 [INFO]: Server running on port 5000 
2025-03-24 15:44:06 [INFO]: Environment: development 
2025-03-24 15:57:03 [INFO]: Database configuration loaded from file 
2025-03-24 15:57:03 [INFO]: Connected to SQL Server 
2025-03-24 15:57:03 [INFO]: Server running on port 5000 
2025-03-24 15:57:03 [INFO]: Environment: development 
2025-03-24 16:00:13 [INFO]: Database configuration loaded from file 
2025-03-24 16:00:14 [INFO]: Connected to SQL Server 
2025-03-24 16:00:14 [INFO]: Server running on port 5000 
2025-03-24 16:00:14 [INFO]: Environment: development 
2025-03-24 20:43:16 [INFO]: Database configuration loaded from file 
2025-03-24 20:43:16 [INFO]: Connected to SQL Server 
2025-03-24 20:43:16 [INFO]: Server running on port 5000 
2025-03-24 20:43:16 [INFO]: Environment: development 
2025-03-24 20:54:05 [INFO]: Database configuration loaded from file 
2025-03-24 20:54:06 [INFO]: Connected to SQL Server 
2025-03-24 20:54:06 [INFO]: Server running on port 5000 
2025-03-24 20:54:06 [INFO]: Environment: development 
2025-03-24 20:55:12 [INFO]: Database configuration loaded from file 
2025-03-24 20:55:12 [INFO]: Connected to SQL Server 
2025-03-24 20:55:12 [INFO]: Server running on port 5000 
2025-03-24 20:55:12 [INFO]: Environment: development 
2025-03-24 21:08:27 [INFO]: Database configuration loaded from file 
2025-03-24 21:08:28 [INFO]: Connected to SQL Server 
2025-03-24 21:08:28 [INFO]: Server running on port 5000 
2025-03-24 21:08:28 [INFO]: Environment: development 
2025-03-24 21:08:43 [INFO]: Database configuration loaded from file 
2025-03-24 21:08:46 [INFO]: Connected to SQL Server 
2025-03-24 21:08:46 [INFO]: Server running on port 5000 
2025-03-24 21:08:46 [INFO]: Environment: development 
2025-03-24 21:09:26 [INFO]: Database configuration loaded from file 
2025-03-24 21:09:26 [INFO]: Connected to SQL Server 
2025-03-24 21:09:26 [INFO]: Server running on port 5000 
2025-03-24 21:09:26 [INFO]: Environment: development 
2025-03-24 21:10:30 [INFO]: Login attempt for username: admin 
2025-03-24 21:10:30 [WARN]: Security alert: Common password attempt for user: admin 
2025-03-24 21:10:30 [WARN]: Login failed: Invalid password for user: admin {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-24 21:10:30 [INFO]: Login attempt for username: admin 
2025-03-24 21:10:31 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-24 21:11:16 [INFO]: Login attempt for username: burak12 
2025-03-24 21:11:16 [WARN]: Login failed: User not found: burak12 
2025-03-24 21:11:18 [INFO]: Login attempt for username: burak 
2025-03-24 21:11:19 [WARN]: Login failed: Invalid password for user: burak {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-24 21:11:20 [INFO]: Login attempt for username: burak 
2025-03-24 21:11:20 [WARN]: Security alert: Common password attempt for user: burak 
2025-03-24 21:11:21 [INFO]: Login successful for user: burak (ID: 2, Role: burak rol1) 
2025-03-24 21:14:58 [INFO]: Login attempt for username: admin 
2025-03-24 21:14:58 [WARN]: Security alert: Common password attempt for user: admin 
2025-03-24 21:14:58 [WARN]: Login failed: Invalid password for user: admin {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-24 21:14:59 [INFO]: Login attempt for username: admin 
2025-03-24 21:14:59 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-24 21:17:50 [INFO]: Database configuration loaded from file 
2025-03-24 21:17:50 [INFO]: Connected to SQL Server 
2025-03-24 21:17:50 [INFO]: Server running on port 5000 
2025-03-24 21:17:50 [INFO]: Environment: development 
2025-03-24 21:18:43 [INFO]: Database configuration loaded from file 
2025-03-24 21:18:43 [INFO]: Connected to SQL Server 
2025-03-24 21:18:43 [INFO]: Server running on port 5000 
2025-03-24 21:18:43 [INFO]: Environment: development 
2025-03-24 21:46:30 [INFO]: Login attempt for username: admin 
2025-03-24 21:46:30 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-24 21:46:57 [INFO]: Login attempt for username: admin 
2025-03-24 21:46:58 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-24 21:53:21 [INFO]: Database configuration loaded from file 
2025-03-24 21:53:22 [INFO]: Connected to SQL Server 
2025-03-24 21:53:22 [INFO]: Server running on port 5000 
2025-03-24 21:53:22 [INFO]: Environment: development 
2025-03-24 21:53:44 [INFO]: Login attempt for username: admin 
2025-03-24 21:53:44 [WARN]: Security alert: Common password attempt for user: admin 
2025-03-24 21:53:44 [WARN]: Login failed: Invalid password for user: admin {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-24 21:53:46 [INFO]: Login attempt for username: admin 
2025-03-24 21:53:46 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-24 21:57:39 [INFO]: Database configuration loaded from file 
2025-03-24 21:57:39 [INFO]: Connected to SQL Server 
2025-03-24 21:57:39 [INFO]: Server running on port 5000 
2025-03-24 21:57:39 [INFO]: Environment: development 
2025-03-24 21:57:56 [ERROR]: Not Found - /main.5a49607b6b062d52a3de.hot-update.json {
  "stack": "Error: Not Found - /main.5a49607b6b062d52a3de.hot-update.json\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\burky root\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/main.5a49607b6b062d52a3de.hot-update.json",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-24 22:05:50 [INFO]: Database configuration loaded from file 
2025-03-24 22:05:50 [INFO]: Connected to SQL Server 
2025-03-24 22:05:50 [INFO]: Server running on port 5000 
2025-03-24 22:05:50 [INFO]: Environment: development 
2025-03-24 22:09:07 [INFO]: Database configuration loaded from file 
2025-03-24 22:09:08 [INFO]: Connected to SQL Server 
2025-03-24 22:09:08 [INFO]: Server running on port 5000 
2025-03-24 22:09:08 [INFO]: Environment: development 
2025-03-24 22:09:09 [INFO]: Database configuration loaded from file 
2025-03-24 22:31:00 [INFO]: Database configuration loaded from file 
2025-03-24 22:31:01 [INFO]: Connected to SQL Server 
2025-03-24 22:31:01 [INFO]: Server running on port 5000 
2025-03-24 22:31:01 [INFO]: Environment: development 
2025-03-24 22:43:51 [INFO]: Database configuration loaded from file 
2025-03-24 22:45:50 [INFO]: Login attempt for username: burak 
2025-03-24 22:45:50 [WARN]: Login failed: Invalid password for user: burak {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-24 22:45:51 [INFO]: Login attempt for username: burak 
2025-03-24 22:45:51 [WARN]: Security alert: Common password attempt for user: burak 
2025-03-24 22:45:52 [INFO]: Login successful for user: burak (ID: 2, Role: burak rol1) 
2025-03-24 22:46:06 [INFO]: Login attempt for username: expert 
2025-03-24 22:46:06 [WARN]: Security alert: Common password attempt for user: expert 
2025-03-24 22:46:06 [WARN]: Login failed: Invalid password for user: expert {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-24 22:46:07 [INFO]: Login attempt for username: expert 
2025-03-24 22:46:08 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-24 22:46:20 [INFO]: Login attempt for username: client 
2025-03-24 22:46:21 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-03-25 00:44:43 [INFO]: Login attempt for username: admin 
2025-03-25 00:44:43 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-25 00:46:48 [INFO]: Login attempt for username: expert 
2025-03-25 00:46:48 [WARN]: Security alert: Common password attempt for user: expert 
2025-03-25 00:46:48 [WARN]: Login failed: Invalid password for user: expert {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-25 00:46:49 [INFO]: Login attempt for username: expert 
2025-03-25 00:46:49 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-25 00:47:15 [INFO]: Login attempt for username: burak 
2025-03-25 00:47:15 [WARN]: Security alert: Common password attempt for user: burak 
2025-03-25 00:47:15 [INFO]: Login successful for user: burak (ID: 2, Role: burak rol1) 
2025-03-25 00:47:32 [INFO]: Login attempt for username: admin 
2025-03-25 00:47:32 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-25 00:47:44 [INFO]: Login attempt for username: burak 
2025-03-25 00:47:44 [WARN]: Security alert: Common password attempt for user: burak 
2025-03-25 00:47:44 [INFO]: Login successful for user: burak (ID: 2, Role: burak rol1) 
2025-03-25 00:47:58 [INFO]: Login attempt for username: admin 
2025-03-25 00:47:58 [WARN]: Security alert: Common password attempt for user: admin 
2025-03-25 00:47:58 [WARN]: Login failed: Invalid password for user: admin {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-25 00:47:59 [INFO]: Login attempt for username: admin 
2025-03-25 00:47:59 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-25 00:49:07 [INFO]: Login attempt for username: expert 
2025-03-25 00:49:07 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-25 16:20:11 [INFO]: Database configuration loaded from file 
2025-03-25 16:20:12 [INFO]: Connected to SQL Server 
2025-03-25 16:20:12 [INFO]: Server running on port 5000 
2025-03-25 16:20:12 [INFO]: Environment: development 
2025-03-25 16:57:44 [INFO]: Database configuration loaded from file 
2025-03-25 16:57:45 [INFO]: Connected to SQL Server 
2025-03-25 16:57:45 [INFO]: Server running on port 5000 
2025-03-25 16:57:45 [INFO]: Environment: development 
2025-03-25 17:23:52 [INFO]: Database configuration loaded from file 
2025-03-25 17:23:53 [INFO]: Connected to SQL Server 
2025-03-25 17:23:53 [INFO]: Server running on port 5000 
2025-03-25 17:23:53 [INFO]: Environment: development 
2025-03-25 17:26:25 [ERROR]: Not Found - /api/experts/availability/me {
  "stack": "Error: Not Found - /api/experts/availability/me\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\burky root\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/api/experts/availability/me",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-25 17:26:25 [ERROR]: Not Found - /api/experts/availability/me {
  "stack": "Error: Not Found - /api/experts/availability/me\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\burky root\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/api/experts/availability/me",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-25 17:26:25 [ERROR]: Not Found - /api/experts/availability/me {
  "stack": "Error: Not Found - /api/experts/availability/me\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\burky root\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/api/experts/availability/me",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-25 17:26:25 [ERROR]: Not Found - /api/experts/availability/me {
  "stack": "Error: Not Found - /api/experts/availability/me\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\burky root\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/api/experts/availability/me",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-25 17:26:32 [ERROR]: Not Found - /api/experts/availability/me {
  "stack": "Error: Not Found - /api/experts/availability/me\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\burky root\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/api/experts/availability/me",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-25 17:26:32 [ERROR]: Not Found - /api/experts/availability/me {
  "stack": "Error: Not Found - /api/experts/availability/me\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\burky root\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/api/experts/availability/me",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-25 17:26:32 [ERROR]: Not Found - /api/experts/availability/me {
  "stack": "Error: Not Found - /api/experts/availability/me\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\burky root\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/api/experts/availability/me",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-25 17:26:33 [ERROR]: Not Found - /api/experts/availability/me {
  "stack": "Error: Not Found - /api/experts/availability/me\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\burky root\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/api/experts/availability/me",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-25 17:28:14 [INFO]: Database configuration loaded from file 
2025-03-25 17:29:56 [INFO]: Database configuration loaded from file 
2025-03-25 17:30:30 [INFO]: Database configuration loaded from file 
2025-03-25 17:32:12 [INFO]: Database configuration loaded from file 
2025-03-25 17:32:13 [INFO]: Connected to SQL Server 
2025-03-25 17:32:13 [INFO]: Server running on port 5000 
2025-03-25 17:32:13 [INFO]: Environment: development 
2025-03-25 17:55:35 [INFO]: Login attempt for username: expert 
2025-03-25 17:55:36 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-25 18:00:43 [ERROR]: Error adding expert availability: Validation failed for parameter 'StartTime'. Invalid time. {
  "code": "EPARAM",
  "originalError": {
    "code": "EPARAM"
  },
  "name": "RequestError",
  "number": "EPARAM",
  "precedingErrors": [],
  "stack": "RequestError: Validation failed for parameter 'StartTime'. Invalid time.\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:449:19\n    at Array.forEach (<anonymous>)\n    at Request.userCallback (C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:446:46)\n    at Request.callback (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\request.js:239:14)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:1616:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"
}
2025-03-25 18:00:43 [ERROR]: Error adding expert availability: Validation failed for parameter 'StartTime'. Invalid time. {
  "code": "EPARAM",
  "originalError": {
    "code": "EPARAM"
  },
  "name": "RequestError",
  "number": "EPARAM",
  "precedingErrors": [],
  "stack": "RequestError: Validation failed for parameter 'StartTime'. Invalid time.\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:449:19\n    at Array.forEach (<anonymous>)\n    at Request.userCallback (C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:446:46)\n    at Request.callback (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\request.js:239:14)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:1616:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"
}
2025-03-25 18:05:55 [ERROR]: Error adding expert availability: Validation failed for parameter 'StartTime'. Invalid time. {
  "code": "EPARAM",
  "originalError": {
    "code": "EPARAM"
  },
  "name": "RequestError",
  "number": "EPARAM",
  "precedingErrors": [],
  "stack": "RequestError: Validation failed for parameter 'StartTime'. Invalid time.\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:449:19\n    at Array.forEach (<anonymous>)\n    at Request.userCallback (C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:446:46)\n    at Request.callback (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\request.js:239:14)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:1616:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"
}
2025-03-25 18:05:55 [ERROR]: Error adding expert availability: Validation failed for parameter 'StartTime'. Invalid time. {
  "code": "EPARAM",
  "originalError": {
    "code": "EPARAM"
  },
  "name": "RequestError",
  "number": "EPARAM",
  "precedingErrors": [],
  "stack": "RequestError: Validation failed for parameter 'StartTime'. Invalid time.\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:449:19\n    at Array.forEach (<anonymous>)\n    at Request.userCallback (C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:446:46)\n    at Request.callback (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\request.js:239:14)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:1616:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"
}
2025-03-25 18:06:08 [ERROR]: Error adding expert availability: Validation failed for parameter 'StartTime'. Invalid time. {
  "code": "EPARAM",
  "originalError": {
    "code": "EPARAM"
  },
  "name": "RequestError",
  "number": "EPARAM",
  "precedingErrors": [],
  "stack": "RequestError: Validation failed for parameter 'StartTime'. Invalid time.\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:449:19\n    at Array.forEach (<anonymous>)\n    at Request.userCallback (C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:446:46)\n    at Request.callback (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\request.js:239:14)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:1616:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"
}
2025-03-25 18:06:08 [ERROR]: Error adding expert availability: Validation failed for parameter 'StartTime'. Invalid time. {
  "code": "EPARAM",
  "originalError": {
    "code": "EPARAM"
  },
  "name": "RequestError",
  "number": "EPARAM",
  "precedingErrors": [],
  "stack": "RequestError: Validation failed for parameter 'StartTime'. Invalid time.\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:449:19\n    at Array.forEach (<anonymous>)\n    at Request.userCallback (C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:446:46)\n    at Request.callback (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\request.js:239:14)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:1616:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"
}
2025-03-25 18:07:02 [ERROR]: Error adding expert availability: Validation failed for parameter 'StartTime'. Invalid time. {
  "code": "EPARAM",
  "originalError": {
    "code": "EPARAM"
  },
  "name": "RequestError",
  "number": "EPARAM",
  "precedingErrors": [],
  "stack": "RequestError: Validation failed for parameter 'StartTime'. Invalid time.\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:449:19\n    at Array.forEach (<anonymous>)\n    at Request.userCallback (C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:446:46)\n    at Request.callback (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\request.js:239:14)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:1616:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"
}
2025-03-25 18:07:02 [ERROR]: Error adding expert availability: Validation failed for parameter 'StartTime'. Invalid time. {
  "code": "EPARAM",
  "originalError": {
    "code": "EPARAM"
  },
  "name": "RequestError",
  "number": "EPARAM",
  "precedingErrors": [],
  "stack": "RequestError: Validation failed for parameter 'StartTime'. Invalid time.\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:449:19\n    at Array.forEach (<anonymous>)\n    at Request.userCallback (C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:446:46)\n    at Request.callback (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\request.js:239:14)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:1616:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"
}
2025-03-25 18:07:28 [ERROR]: Error adding expert availability: Validation failed for parameter 'StartTime'. Invalid time. {
  "code": "EPARAM",
  "originalError": {
    "code": "EPARAM"
  },
  "name": "RequestError",
  "number": "EPARAM",
  "precedingErrors": [],
  "stack": "RequestError: Validation failed for parameter 'StartTime'. Invalid time.\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:449:19\n    at Array.forEach (<anonymous>)\n    at Request.userCallback (C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:446:46)\n    at Request.callback (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\request.js:239:14)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:1616:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"
}
2025-03-25 18:07:28 [ERROR]: Error adding expert availability: Validation failed for parameter 'StartTime'. Invalid time. {
  "code": "EPARAM",
  "originalError": {
    "code": "EPARAM"
  },
  "name": "RequestError",
  "number": "EPARAM",
  "precedingErrors": [],
  "stack": "RequestError: Validation failed for parameter 'StartTime'. Invalid time.\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:449:19\n    at Array.forEach (<anonymous>)\n    at Request.userCallback (C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:446:46)\n    at Request.callback (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\request.js:239:14)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:1616:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"
}
2025-03-25 18:08:45 [ERROR]: Error adding expert availability: Validation failed for parameter 'StartTime'. Invalid time. {
  "code": "EPARAM",
  "originalError": {
    "code": "EPARAM"
  },
  "name": "RequestError",
  "number": "EPARAM",
  "precedingErrors": [],
  "stack": "RequestError: Validation failed for parameter 'StartTime'. Invalid time.\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:449:19\n    at Array.forEach (<anonymous>)\n    at Request.userCallback (C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:446:46)\n    at Request.callback (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\request.js:239:14)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:1616:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"
}
2025-03-25 18:08:45 [ERROR]: Error adding expert availability: Validation failed for parameter 'StartTime'. Invalid time. {
  "code": "EPARAM",
  "originalError": {
    "code": "EPARAM"
  },
  "name": "RequestError",
  "number": "EPARAM",
  "precedingErrors": [],
  "stack": "RequestError: Validation failed for parameter 'StartTime'. Invalid time.\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:449:19\n    at Array.forEach (<anonymous>)\n    at Request.userCallback (C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:446:46)\n    at Request.callback (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\request.js:239:14)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:1616:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"
}
2025-03-25 18:18:10 [INFO]: Database configuration loaded from file 
2025-03-25 18:18:10 [INFO]: Connected to SQL Server 
2025-03-25 18:18:10 [INFO]: Server running on port 5000 
2025-03-25 18:18:10 [INFO]: Environment: development 
2025-03-25 18:19:30 [INFO]: Database configuration loaded from file 
2025-03-25 18:19:30 [INFO]: Connected to SQL Server 
2025-03-25 18:19:30 [INFO]: Server running on port 5000 
2025-03-25 18:19:30 [INFO]: Environment: development 
2025-03-25 18:20:03 [INFO]: Database configuration loaded from file 
2025-03-25 18:20:03 [INFO]: Connected to SQL Server 
2025-03-25 18:20:03 [INFO]: Server running on port 5000 
2025-03-25 18:20:03 [INFO]: Environment: development 
2025-03-25 18:21:11 [INFO]: Database configuration loaded from file 
2025-03-25 18:21:11 [INFO]: Connected to SQL Server 
2025-03-25 18:21:11 [INFO]: Server running on port 5000 
2025-03-25 18:21:11 [INFO]: Environment: development 
2025-03-25 18:21:26 [INFO]: Database configuration loaded from file 
2025-03-25 18:21:27 [INFO]: Connected to SQL Server 
2025-03-25 18:21:27 [INFO]: Server running on port 5000 
2025-03-25 18:21:27 [INFO]: Environment: development 
2025-03-25 18:21:45 [INFO]: Database configuration loaded from file 
2025-03-25 18:21:45 [INFO]: Connected to SQL Server 
2025-03-25 18:21:45 [INFO]: Server running on port 5000 
2025-03-25 18:21:45 [INFO]: Environment: development 
2025-03-25 18:22:05 [INFO]: Database configuration loaded from file 
2025-03-25 18:22:05 [INFO]: Connected to SQL Server 
2025-03-25 18:22:05 [INFO]: Server running on port 5000 
2025-03-25 18:22:05 [INFO]: Environment: development 
2025-03-25 18:22:53 [ERROR]: Validation errors: {
  "0": {
    "type": "field",
    "value": null,
    "msg": "Invalid value",
    "path": "specificDate",
    "location": "body"
  }
}
2025-03-25 18:23:55 [ERROR]: Validation errors: {
  "0": {
    "type": "field",
    "value": null,
    "msg": "Invalid value",
    "path": "specificDate",
    "location": "body"
  }
}
2025-03-25 18:24:47 [INFO]: Database configuration loaded from file 
2025-03-25 18:24:47 [INFO]: Connected to SQL Server 
2025-03-25 18:24:47 [INFO]: Server running on port 5000 
2025-03-25 18:24:47 [INFO]: Environment: development 
2025-03-25 18:24:57 [INFO]: Database configuration loaded from file 
2025-03-25 18:24:58 [INFO]: Connected to SQL Server 
2025-03-25 18:24:58 [INFO]: Server running on port 5000 
2025-03-25 18:24:58 [INFO]: Environment: development 
2025-03-25 18:26:26 [INFO]: Database configuration loaded from file 
2025-03-25 18:26:26 [INFO]: Connected to SQL Server 
2025-03-25 18:26:26 [INFO]: Server running on port 5000 
2025-03-25 18:26:26 [INFO]: Environment: development 
2025-03-25 18:26:50 [INFO]: Database configuration loaded from file 
2025-03-25 18:26:51 [INFO]: Connected to SQL Server 
2025-03-25 18:26:51 [INFO]: Server running on port 5000 
2025-03-25 18:26:51 [INFO]: Environment: development 
2025-03-25 18:28:06 [INFO]: Updating availability with data: {
  "availabilityId": 1,
  "expertId": 1,
  "dayOfWeek": 1,
  "startTime": "09:00:00",
  "endTime": "14:30:00",
  "isRecurring": true,
  "specificDate": null
}
2025-03-25 18:28:06 [INFO]: Time string already in correct format: '09:00:00' 
2025-03-25 18:28:06 [INFO]: Time string already in correct format: '14:30:00' 
2025-03-25 18:28:06 [INFO]: Formatted time values for update: {
  "original": {
    "startTime": "09:00:00",
    "endTime": "14:30:00"
  },
  "formatted": {
    "startTime": "09:00:00",
    "endTime": "14:30:00"
  }
}
2025-03-25 18:28:06 [INFO]: Final values for SQL update query: {
  "availabilityId": 1,
  "dayOfWeek": 1,
  "startTime": "09:00:00",
  "endTime": "14:30:00",
  "isRecurring": true,
  "specificDate": null
}
2025-03-25 18:28:06 [ERROR]: Error updating expert availability: Validation failed for parameter 'StartTime'. Invalid time. {
  "code": "EPARAM",
  "originalError": {
    "code": "EPARAM"
  },
  "name": "RequestError",
  "number": "EPARAM",
  "precedingErrors": [],
  "stack": "RequestError: Validation failed for parameter 'StartTime'. Invalid time.\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:449:19\n    at Array.forEach (<anonymous>)\n    at Request.userCallback (C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:446:46)\n    at Request.callback (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\request.js:239:14)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:1616:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"
}
2025-03-25 18:28:06 [ERROR]: Error updating expert availability: Validation failed for parameter 'StartTime'. Invalid time. {
  "code": "EPARAM",
  "originalError": {
    "code": "EPARAM"
  },
  "name": "RequestError",
  "number": "EPARAM",
  "precedingErrors": [],
  "stack": "RequestError: Validation failed for parameter 'StartTime'. Invalid time.\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:449:19\n    at Array.forEach (<anonymous>)\n    at Request.userCallback (C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:446:46)\n    at Request.callback (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\request.js:239:14)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:1616:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"
}
2025-03-25 18:29:57 [INFO]: Database configuration loaded from file 
2025-03-25 18:29:58 [INFO]: Connected to SQL Server 
2025-03-25 18:29:58 [INFO]: Server running on port 5000 
2025-03-25 18:29:58 [INFO]: Environment: development 
2025-03-25 18:34:14 [INFO]: Database configuration loaded from file 
2025-03-25 18:34:14 [INFO]: Connected to SQL Server 
2025-03-25 18:34:14 [INFO]: Server running on port 5000 
2025-03-25 18:34:14 [INFO]: Environment: development 
2025-03-25 18:34:21 [INFO]: Database configuration loaded from file 
2025-03-25 18:34:22 [INFO]: Connected to SQL Server 
2025-03-25 18:34:22 [INFO]: Server running on port 5000 
2025-03-25 18:34:22 [INFO]: Environment: development 
2025-03-25 18:34:35 [INFO]: Database configuration loaded from file 
2025-03-25 18:34:36 [INFO]: Connected to SQL Server 
2025-03-25 18:34:36 [INFO]: Server running on port 5000 
2025-03-25 18:34:36 [INFO]: Environment: development 
2025-03-25 18:34:49 [INFO]: Updating availability with data: {
  "availabilityId": 1,
  "expertId": 1,
  "dayOfWeek": 1,
  "startTime": "09:00:00",
  "endTime": "16:00:00",
  "isRecurring": true,
  "specificDate": null
}
2025-03-25 18:34:49 [INFO]: Found availability record with ID: 1 for ExpertID: 1 
2025-03-25 18:34:49 [INFO]: Time string already in correct format: '09:00:00' 
2025-03-25 18:34:49 [INFO]: Time string already in correct format: '16:00:00' 
2025-03-25 18:34:49 [INFO]: Formatted time values for update: {
  "original": {
    "startTime": "09:00:00",
    "endTime": "16:00:00"
  },
  "formatted": {
    "startTime": "09:00:00",
    "endTime": "16:00:00"
  }
}
2025-03-25 18:34:49 [INFO]: Final values for SQL update query: {
  "availabilityId": 1,
  "dayOfWeek": 1,
  "startTime": "09:00:00",
  "endTime": "16:00:00",
  "isRecurring": true,
  "specificDate": null
}
2025-03-25 18:34:50 [ERROR]: Error updating expert availability: Validation failed for parameter 'StartTime'. Invalid time. {
  "code": "EPARAM",
  "originalError": {
    "code": "EPARAM"
  },
  "name": "RequestError",
  "number": "EPARAM",
  "precedingErrors": [],
  "stack": "RequestError: Validation failed for parameter 'StartTime'. Invalid time.\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:449:19\n    at Array.forEach (<anonymous>)\n    at Request.userCallback (C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:446:46)\n    at Request.callback (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\request.js:239:14)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:1616:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"
}
2025-03-25 18:34:50 [ERROR]: Error in expert service while updating availability: {
  "error": "Validation failed for parameter 'StartTime'. Invalid time.",
  "stack": "RequestError: Validation failed for parameter 'StartTime'. Invalid time.\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:449:19\n    at Array.forEach (<anonymous>)\n    at Request.userCallback (C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:446:46)\n    at Request.callback (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\request.js:239:14)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:1616:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)",
  "availabilityId": 1,
  "availabilityData": {
    "dayOfWeek": 1,
    "startTime": "09:00:00",
    "endTime": "16:00:00",
    "isRecurring": true,
    "specificDate": null
  }
}
2025-03-25 18:36:44 [INFO]: Database configuration loaded from file 
2025-03-25 18:36:45 [INFO]: Connected to SQL Server 
2025-03-25 18:36:45 [INFO]: Server running on port 5000 
2025-03-25 18:36:45 [INFO]: Environment: development 
2025-03-25 18:38:51 [WARN]: User (ID: 5, role: 6) does not have permission: /expert/availabilities - UPDATE 
2025-03-25 18:39:38 [WARN]: User (ID: 5, role: 6) does not have permission: /expert/availabilities - UPDATE 
2025-03-25 18:42:14 [INFO]: Updating availability with data: {
  "availabilityId": 1,
  "expertId": 1,
  "dayOfWeek": 1,
  "startTime": "09:00:00",
  "endTime": "16:00:00",
  "isRecurring": true,
  "specificDate": null
}
2025-03-25 18:42:14 [INFO]: Found availability record with ID: 1 for ExpertID: 1 
2025-03-25 18:42:14 [INFO]: Time string already in correct format: '09:00:00' 
2025-03-25 18:42:14 [INFO]: Time string already in correct format: '16:00:00' 
2025-03-25 18:42:14 [INFO]: Formatted time values for update: {
  "original": {
    "startTime": "09:00:00",
    "endTime": "16:00:00"
  },
  "formatted": {
    "startTime": "09:00:00",
    "endTime": "16:00:00"
  }
}
2025-03-25 18:42:14 [INFO]: Final values for SQL update query: {
  "availabilityId": 1,
  "dayOfWeek": 1,
  "startTime": "09:00:00",
  "endTime": "16:00:00",
  "isRecurring": true,
  "specificDate": null
}
2025-03-25 18:42:14 [ERROR]: Error updating expert availability: Validation failed for parameter 'StartTime'. Invalid time. {
  "code": "EPARAM",
  "originalError": {
    "code": "EPARAM"
  },
  "name": "RequestError",
  "number": "EPARAM",
  "precedingErrors": [],
  "stack": "RequestError: Validation failed for parameter 'StartTime'. Invalid time.\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:449:19\n    at Array.forEach (<anonymous>)\n    at Request.userCallback (C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:446:46)\n    at Request.callback (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\request.js:239:14)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:1616:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"
}
2025-03-25 18:42:14 [ERROR]: Error in expert service while updating availability: {
  "error": "Validation failed for parameter 'StartTime'. Invalid time.",
  "stack": "RequestError: Validation failed for parameter 'StartTime'. Invalid time.\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:449:19\n    at Array.forEach (<anonymous>)\n    at Request.userCallback (C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:446:46)\n    at Request.callback (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\request.js:239:14)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:1616:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)",
  "availabilityId": 1,
  "availabilityData": {
    "dayOfWeek": 1,
    "startTime": "09:00:00",
    "endTime": "16:00:00",
    "isRecurring": true,
    "specificDate": null
  }
}
2025-03-25 18:42:43 [INFO]: Updating availability with data: {
  "availabilityId": 1,
  "expertId": 1,
  "dayOfWeek": 1,
  "startTime": "09:00:00",
  "endTime": "15:30:00",
  "isRecurring": true,
  "specificDate": null
}
2025-03-25 18:42:43 [INFO]: Found availability record with ID: 1 for ExpertID: 1 
2025-03-25 18:42:43 [INFO]: Time string already in correct format: '09:00:00' 
2025-03-25 18:42:43 [INFO]: Time string already in correct format: '15:30:00' 
2025-03-25 18:42:43 [INFO]: Formatted time values for update: {
  "original": {
    "startTime": "09:00:00",
    "endTime": "15:30:00"
  },
  "formatted": {
    "startTime": "09:00:00",
    "endTime": "15:30:00"
  }
}
2025-03-25 18:42:43 [INFO]: Final values for SQL update query: {
  "availabilityId": 1,
  "dayOfWeek": 1,
  "startTime": "09:00:00",
  "endTime": "15:30:00",
  "isRecurring": true,
  "specificDate": null
}
2025-03-25 18:42:43 [ERROR]: Error updating expert availability: Validation failed for parameter 'StartTime'. Invalid time. {
  "code": "EPARAM",
  "originalError": {
    "code": "EPARAM"
  },
  "name": "RequestError",
  "number": "EPARAM",
  "precedingErrors": [],
  "stack": "RequestError: Validation failed for parameter 'StartTime'. Invalid time.\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:449:19\n    at Array.forEach (<anonymous>)\n    at Request.userCallback (C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:446:46)\n    at Request.callback (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\request.js:239:14)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:1616:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"
}
2025-03-25 18:42:43 [ERROR]: Error in expert service while updating availability: {
  "error": "Validation failed for parameter 'StartTime'. Invalid time.",
  "stack": "RequestError: Validation failed for parameter 'StartTime'. Invalid time.\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:449:19\n    at Array.forEach (<anonymous>)\n    at Request.userCallback (C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:446:46)\n    at Request.callback (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\request.js:239:14)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:1616:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)",
  "availabilityId": 1,
  "availabilityData": {
    "dayOfWeek": 1,
    "startTime": "09:00:00",
    "endTime": "15:30:00",
    "isRecurring": true,
    "specificDate": null
  }
}
2025-03-25 18:43:51 [INFO]: Database configuration loaded from file 
2025-03-25 18:43:51 [INFO]: Connected to SQL Server 
2025-03-25 18:43:51 [INFO]: Server running on port 5000 
2025-03-25 18:43:51 [INFO]: Environment: development 
2025-03-25 18:44:19 [INFO]: Database configuration loaded from file 
2025-03-25 18:44:19 [INFO]: Connected to SQL Server 
2025-03-25 18:44:19 [INFO]: Server running on port 5000 
2025-03-25 18:44:19 [INFO]: Environment: development 
2025-03-25 18:44:59 [INFO]: Updating availability with data: {
  "availabilityId": 1,
  "expertId": 1,
  "dayOfWeek": 1,
  "startTime": "09:00:00",
  "endTime": "15:30:00",
  "isRecurring": true,
  "specificDate": null
}
2025-03-25 18:44:59 [INFO]: Found availability record with ID: 1 for ExpertID: 1 
2025-03-25 18:44:59 [INFO]: Time string already in correct format: '09:00:00' 
2025-03-25 18:44:59 [INFO]: Time string already in correct format: '15:30:00' 
2025-03-25 18:44:59 [INFO]: Formatted time values for update: {
  "original": {
    "startTime": "09:00:00",
    "endTime": "15:30:00"
  },
  "formatted": {
    "startTime": "09:00:00",
    "endTime": "15:30:00"
  }
}
2025-03-25 18:44:59 [INFO]: Final values for SQL update query: {
  "availabilityId": 1,
  "dayOfWeek": 1,
  "startTime": "09:00:00",
  "endTime": "15:30:00",
  "isRecurring": true,
  "specificDate": null
}
2025-03-25 18:44:59 [INFO]: Update affected 1 rows for availability ID: 1 
2025-03-25 18:44:59 [INFO]: Availability updated successfully: 
2025-03-25 18:46:25 [INFO]: Adding availability with data: {
  "expertId": 1,
  "dayOfWeek": 1,
  "startTime": "16:00:00",
  "endTime": "17:00:00",
  "isRecurring": false,
  "specificDate": "2025-03-24"
}
2025-03-25 18:46:25 [INFO]: Time string already in correct format: '16:00:00' 
2025-03-25 18:46:25 [INFO]: Time string already in correct format: '17:00:00' 
2025-03-25 18:46:25 [INFO]: Formatted time values for insert: {
  "original": {
    "startTime": "16:00:00",
    "endTime": "17:00:00"
  },
  "formatted": {
    "startTime": "16:00:00",
    "endTime": "17:00:00"
  }
}
2025-03-25 18:46:25 [INFO]: Final values for SQL query: {
  "expertId": 1,
  "dayOfWeek": 1,
  "startTime": "16:00:00",
  "endTime": "17:00:00",
  "isRecurring": false,
  "specificDate": "2025-03-24"
}
2025-03-25 18:46:25 [INFO]: New availability inserted with ID: 2 
2025-03-25 18:46:25 [INFO]: Availability added successfully with ID: 
2025-03-25 19:04:53 [INFO]: Adding availability with data: {
  "expertId": 1,
  "dayOfWeek": 2,
  "startTime": "09:00:00",
  "endTime": "17:00:00",
  "isRecurring": false,
  "specificDate": "2025-03-25"
}
2025-03-25 19:04:53 [INFO]: Time string already in correct format: '09:00:00' 
2025-03-25 19:04:53 [INFO]: Time string already in correct format: '17:00:00' 
2025-03-25 19:04:53 [INFO]: Formatted time values for insert: {
  "original": {
    "startTime": "09:00:00",
    "endTime": "17:00:00"
  },
  "formatted": {
    "startTime": "09:00:00",
    "endTime": "17:00:00"
  }
}
2025-03-25 19:04:53 [INFO]: Final values for SQL query: {
  "expertId": 1,
  "dayOfWeek": 2,
  "startTime": "09:00:00",
  "endTime": "17:00:00",
  "isRecurring": false,
  "specificDate": "2025-03-25"
}
2025-03-25 19:04:53 [INFO]: New availability inserted with ID: 3 
2025-03-25 19:04:53 [INFO]: Availability added successfully with ID: 
2025-03-25 20:26:30 [INFO]: Database configuration loaded from file 
2025-03-25 20:26:31 [INFO]: Connected to SQL Server 
2025-03-25 20:26:31 [INFO]: Server running on port 5000 
2025-03-25 20:26:31 [INFO]: Environment: development 
2025-03-25 21:03:57 [INFO]: Adding availability with data: {
  "expertId": 1,
  "dayOfWeek": 1,
  "startTime": "09:00:00",
  "endTime": "17:00:00",
  "isRecurring": true,
  "specificDate": null
}
2025-03-25 21:03:57 [INFO]: Time string already in correct format: '09:00:00' 
2025-03-25 21:03:57 [INFO]: Time string already in correct format: '17:00:00' 
2025-03-25 21:03:57 [INFO]: Formatted time values for insert: {
  "original": {
    "startTime": "09:00:00",
    "endTime": "17:00:00"
  },
  "formatted": {
    "startTime": "09:00:00",
    "endTime": "17:00:00"
  }
}
2025-03-25 21:03:57 [INFO]: Final values for SQL query: {
  "expertId": 1,
  "dayOfWeek": 1,
  "startTime": "09:00:00",
  "endTime": "17:00:00",
  "isRecurring": true,
  "specificDate": null
}
2025-03-25 21:03:57 [INFO]: New availability inserted with ID: 4 
2025-03-25 21:03:57 [INFO]: Availability added successfully with ID: 
2025-03-25 21:46:44 [INFO]: Database configuration loaded from file 
2025-03-25 21:46:44 [INFO]: Connected to SQL Server 
2025-03-25 21:46:44 [INFO]: Server running on port 5000 
2025-03-25 21:46:44 [INFO]: Environment: development 
2025-03-25 22:47:24 [INFO]: Login attempt for username: expert 
2025-03-25 22:47:24 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-25 23:12:37 [INFO]: Login attempt for username: client 
2025-03-25 23:12:37 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-03-26 01:14:30 [INFO]: Database configuration loaded from file 
2025-03-26 01:14:30 [INFO]: Connected to SQL Server 
2025-03-26 01:14:30 [INFO]: Server running on port 5000 
2025-03-26 01:14:30 [INFO]: Environment: development 
2025-03-26 01:21:10 [INFO]: Login attempt for username: expert 
2025-03-26 01:21:10 [WARN]: Security alert: Common password attempt for user: expert 
2025-03-26 01:21:10 [WARN]: Login failed: Invalid password for user: expert {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-26 01:21:10 [INFO]: Login attempt for username: expert 
2025-03-26 01:21:11 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-26 01:32:08 [INFO]: Login attempt for username: client 
2025-03-26 01:32:09 [WARN]: Security alert: Common password attempt for user: client 
2025-03-26 01:32:09 [WARN]: Login failed: Invalid password for user: client {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-26 01:32:11 [INFO]: Login attempt for username: client 
2025-03-26 01:32:11 [WARN]: Login failed: Invalid password for user: client {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-26 01:32:15 [INFO]: Login attempt for username: client 
2025-03-26 01:32:16 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-03-26 02:24:22 [INFO]: Database configuration loaded from file 
2025-03-26 02:24:23 [INFO]: Connected to SQL Server 
2025-03-26 02:24:23 [INFO]: Server running on port 5000 
2025-03-26 02:24:23 [INFO]: Environment: development 
2025-03-26 03:23:30 [INFO]: Login attempt for username: expert 
2025-03-26 03:23:31 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-26 04:43:41 [INFO]: Login attempt for username: client 
2025-03-26 04:43:42 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-03-26 12:50:17 [INFO]: Database configuration loaded from file 
2025-03-26 12:50:18 [INFO]: Connected to SQL Server 
2025-03-26 12:50:18 [INFO]: Server running on port 5000 
2025-03-26 12:50:18 [INFO]: Environment: development 
2025-03-26 13:06:10 [INFO]: Login attempt for username: client 
2025-03-26 13:06:11 [WARN]: Login failed: Invalid password for user: client {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-26 13:06:14 [INFO]: Login attempt for username: client 
2025-03-26 13:06:14 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-03-26 13:07:37 [INFO]: Login attempt for username: expert 
2025-03-26 13:07:37 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-26 16:10:57 [INFO]: Login attempt for username: expert 
2025-03-26 16:10:57 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-26 16:44:27 [INFO]: Database configuration loaded from file 
2025-03-26 16:44:28 [INFO]: Connected to SQL Server 
2025-03-26 16:44:28 [INFO]: Server running on port 5000 
2025-03-26 16:44:28 [INFO]: Environment: development 
2025-03-26 16:47:26 [INFO]: Database configuration loaded from file 
2025-03-26 16:47:26 [INFO]: Connected to SQL Server 
2025-03-26 16:47:26 [INFO]: Server running on port 5000 
2025-03-26 16:47:26 [INFO]: Environment: development 
2025-03-26 16:48:31 [INFO]: Database configuration loaded from file 
2025-03-26 16:48:33 [INFO]: Connected to SQL Server 
2025-03-26 16:48:33 [INFO]: Server running on port 5000 
2025-03-26 16:48:33 [INFO]: Environment: development 
2025-03-26 16:49:22 [INFO]: Database configuration loaded from file 
2025-03-26 16:49:23 [INFO]: Connected to SQL Server 
2025-03-26 16:49:23 [INFO]: Server running on port 5000 
2025-03-26 16:49:23 [INFO]: Environment: development 
2025-03-26 16:49:52 [INFO]: Database configuration loaded from file 
2025-03-26 16:49:56 [INFO]: Connected to SQL Server 
2025-03-26 16:49:56 [INFO]: Server running on port 5000 
2025-03-26 16:49:56 [INFO]: Environment: development 
2025-03-26 16:50:07 [INFO]: Database configuration loaded from file 
2025-03-26 16:50:08 [INFO]: Connected to SQL Server 
2025-03-26 16:50:08 [INFO]: Server running on port 5000 
2025-03-26 16:50:08 [INFO]: Environment: development 
2025-03-26 16:50:58 [INFO]: Database configuration loaded from file 
2025-03-26 16:51:33 [INFO]: Database configuration loaded from file 
2025-03-26 16:52:07 [INFO]: Database configuration loaded from file 
2025-03-26 16:52:08 [INFO]: Connected to SQL Server 
2025-03-26 16:52:08 [INFO]: Server running on port 5000 
2025-03-26 16:52:08 [INFO]: Environment: development 
2025-03-26 17:51:38 [INFO]: Database configuration loaded from file 
2025-03-26 18:19:26 [INFO]: Login attempt for username: client 
2025-03-26 18:19:26 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-03-26 18:44:01 [INFO]: Expert registered: Email verification token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************.SQff3e_zjNvUqwb_V_py6l1RR6j95_xpy7IxtZe2slE 
2025-03-26 18:44:14 [INFO]: Login attempt for username: admin 
2025-03-26 18:44:14 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-26 18:44:31 [INFO]: Login attempt for username: <EMAIL> 
2025-03-26 18:44:31 [INFO]: Login successful for user: <EMAIL> (ID: 1005, Role: Expert) 
2025-03-26 18:49:43 [INFO]: Login attempt for username: admin 
2025-03-26 18:49:43 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-26 18:53:15 [INFO]: Login attempt for username: burakbay 
2025-03-26 18:53:15 [INFO]: Login successful for user: burakbay (ID: 1005, Role: Expert) 
2025-03-26 19:00:30 [INFO]: Client registered: Email verification token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************.A9yLhsNTjRNCHKQO7blVONXhxrfuyIkzTMAMD2MsEvY 
2025-03-26 19:00:51 [INFO]: Login attempt for username: bahabay 
2025-03-26 19:00:51 [WARN]: Security alert: Common password attempt for user: bahabay 
2025-03-26 19:00:52 [WARN]: Login failed: Invalid password for user: bahabay {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-26 19:00:54 [INFO]: Login attempt for username: bahabay 
2025-03-26 19:00:55 [INFO]: Login successful for user: bahabay (ID: 1006, Role: Client) 
2025-03-26 19:01:45 [INFO]: Login attempt for username: admin 
2025-03-26 19:01:45 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-26 19:13:21 [INFO]: Login attempt for username: expert 
2025-03-26 19:13:21 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-26 19:17:13 [INFO]: Login attempt for username: client 
2025-03-26 19:17:13 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-03-26 19:19:32 [ERROR]: Not Found - /api/appointments/expert {
  "stack": "Error: Not Found - /api/appointments/expert\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\burky root\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/api/appointments/expert",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-26 19:19:32 [ERROR]: Not Found - /api/appointments/expert {
  "stack": "Error: Not Found - /api/appointments/expert\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\burky root\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/api/appointments/expert",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-26 19:20:34 [ERROR]: Not Found - /api/appointments/expert {
  "stack": "Error: Not Found - /api/appointments/expert\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\burky root\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/api/appointments/expert",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-26 19:23:16 [ERROR]: Not Found - /api/appointments/client {
  "stack": "Error: Not Found - /api/appointments/client\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\burky root\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/api/appointments/client",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-26 19:23:16 [ERROR]: Not Found - /api/appointments/client {
  "stack": "Error: Not Found - /api/appointments/client\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\burky root\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/api/appointments/client",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-26 19:29:14 [INFO]: Database configuration loaded from file 
2025-03-26 19:29:14 [INFO]: Connected to SQL Server 
2025-03-26 19:29:14 [INFO]: Server running on port 5000 
2025-03-26 19:29:14 [INFO]: Environment: development 
2025-03-26 23:59:17 [INFO]: Database configuration loaded from file 
2025-03-26 23:59:29 [INFO]: Database configuration loaded from file 
2025-03-27 00:00:25 [INFO]: Database configuration loaded from file 
2025-03-27 00:01:02 [INFO]: Database configuration loaded from file 
2025-03-27 00:01:12 [INFO]: Database configuration loaded from file 
2025-03-27 00:02:03 [INFO]: Database configuration loaded from file 
2025-03-27 00:02:03 [INFO]: Connected to SQL Server 
2025-03-27 00:02:03 [INFO]: Server running on port 5000 
2025-03-27 00:02:03 [INFO]: Environment: development 
2025-03-27 00:03:07 [WARN]: User (ID: 5, role: undefined) does not have permission: /expert/availabilities - READ 
2025-03-27 00:03:07 [WARN]: User (ID: 5, role: undefined) does not have permission: /expert/availabilities - READ 
2025-03-27 00:03:08 [WARN]: User (ID: 5, role: undefined) does not have permission: /expert/availabilities - READ 
2025-03-27 00:03:08 [WARN]: User (ID: 5, role: undefined) does not have permission: /expert/availabilities - READ 
2025-03-27 00:04:25 [INFO]: Database configuration loaded from file 
2025-03-27 00:04:26 [INFO]: Connected to SQL Server 
2025-03-27 00:04:26 [INFO]: Server running on port 5000 
2025-03-27 00:04:26 [INFO]: Environment: development 
2025-03-27 00:04:31 [WARN]: User (ID: 5, role: undefined) does not have permission: /expert/availabilities - READ 
2025-03-27 00:04:31 [WARN]: User (ID: 5, role: undefined) does not have permission: /expert/availabilities - READ 
2025-03-27 00:04:31 [WARN]: User (ID: 5, role: undefined) does not have permission: /expert/availabilities - READ 
2025-03-27 00:04:31 [WARN]: User (ID: 5, role: undefined) does not have permission: /expert/availabilities - READ 
2025-03-27 00:06:23 [INFO]: Database configuration loaded from file 
2025-03-27 00:06:24 [INFO]: Connected to SQL Server 
2025-03-27 00:06:24 [INFO]: Server running on port 5000 
2025-03-27 00:06:24 [INFO]: Environment: development 
2025-03-27 00:15:07 [INFO]: Login attempt for username: expert 
2025-03-27 00:15:07 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-27 00:15:38 [INFO]: Login attempt for username: client 
2025-03-27 00:15:38 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-03-27 00:18:15 [INFO]: Login attempt for username: expert 
2025-03-27 00:18:15 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-27 00:20:34 [INFO]: Login attempt for username: admin 
2025-03-27 00:20:34 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-27 00:39:06 [INFO]: Login attempt for username: expert 
2025-03-27 00:39:06 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-27 00:39:26 [INFO]: Login attempt for username: admin 
2025-03-27 00:39:26 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-27 00:41:22 [INFO]: Login attempt for username: expert 
2025-03-27 00:41:22 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-27 00:46:15 [INFO]: Login attempt for username: client 
2025-03-27 00:46:15 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-03-27 00:46:18 [ERROR]: Not Found - /api/clients/profile {
  "stack": "Error: Not Found - /api/clients/profile\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\burky root\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/api/clients/profile",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-27 00:46:18 [ERROR]: Not Found - /api/clients/profile {
  "stack": "Error: Not Found - /api/clients/profile\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\burky root\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/api/clients/profile",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-27 00:46:18 [ERROR]: Not Found - /api/clients/profile {
  "stack": "Error: Not Found - /api/clients/profile\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\burky root\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/api/clients/profile",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-27 00:46:19 [ERROR]: Not Found - /api/clients/profile {
  "stack": "Error: Not Found - /api/clients/profile\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\burky root\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/api/clients/profile",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-27 00:46:59 [INFO]: Login attempt for username: admin 
2025-03-27 00:46:59 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-27 00:47:36 [INFO]: Login attempt for username: client 
2025-03-27 00:47:37 [WARN]: Login failed: Invalid password for user: client {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-03-27 00:47:41 [INFO]: Login attempt for username: client 
2025-03-27 00:47:41 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-03-27 00:47:44 [ERROR]: Not Found - /api/clients/profile/me {
  "stack": "Error: Not Found - /api/clients/profile/me\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\burky root\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/api/clients/profile/me",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-27 00:47:44 [ERROR]: Not Found - /api/clients/profile/me {
  "stack": "Error: Not Found - /api/clients/profile/me\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\burky root\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/api/clients/profile/me",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-27 00:47:45 [ERROR]: Not Found - /api/clients/profile/me {
  "stack": "Error: Not Found - /api/clients/profile/me\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\burky root\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/api/clients/profile/me",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-27 00:47:45 [ERROR]: Not Found - /api/clients/profile/me {
  "stack": "Error: Not Found - /api/clients/profile/me\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\burky root\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/api/clients/profile/me",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-03-27 00:49:56 [INFO]: Database configuration loaded from file 
2025-03-27 00:51:01 [INFO]: Database configuration loaded from file 
2025-03-27 00:51:02 [INFO]: Connected to SQL Server 
2025-03-27 00:51:02 [INFO]: Server running on port 5000 
2025-03-27 00:51:02 [INFO]: Environment: development 
2025-03-27 00:51:09 [ERROR]: Error in getClientByUserId: Invalid column name 'SpecialRequirements'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 207,
      "state": 1,
      "class": 16,
      "message": "Invalid column name 'SpecialRequirements'.",
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": "",
      "lineNumber": 4
    }
  },
  "name": "RequestError",
  "number": 207,
  "lineNumber": 4,
  "state": 1,
  "class": 16,
  "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
  "procName": "",
  "precedingErrors": [
    {
      "code": "EREQUEST",
      "originalError": {
        "info": {
          "name": "ERROR",
          "handlerName": "onErrorMessage",
          "number": 207,
          "state": 1,
          "class": 16,
          "message": "Invalid column name 'PreferredLanguage'.",
          "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
          "procName": "",
          "lineNumber": 3
        }
      },
      "name": "RequestError",
      "number": 207,
      "lineNumber": 3,
      "state": 1,
      "class": 16,
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": ""
    },
    {
      "code": "EREQUEST",
      "originalError": {
        "info": {
          "name": "ERROR",
          "handlerName": "onErrorMessage",
          "number": 207,
          "state": 1,
          "class": 16,
          "message": "Invalid column name 'PreferredCommunication'.",
          "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
          "procName": "",
          "lineNumber": 3
        }
      },
      "name": "RequestError",
      "number": 207,
      "lineNumber": 3,
      "state": 1,
      "class": 16,
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": ""
    }
  ],
  "stack": "RequestError: Invalid column name 'SpecialRequirements'.\n    at handleError (C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-03-27 00:51:09 [ERROR]: Error getting client profile: Invalid column name 'SpecialRequirements'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 207,
      "state": 1,
      "class": 16,
      "message": "Invalid column name 'SpecialRequirements'.",
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": "",
      "lineNumber": 4
    }
  },
  "name": "RequestError",
  "number": 207,
  "lineNumber": 4,
  "state": 1,
  "class": 16,
  "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
  "procName": "",
  "precedingErrors": [
    {
      "code": "EREQUEST",
      "originalError": {
        "info": {
          "name": "ERROR",
          "handlerName": "onErrorMessage",
          "number": 207,
          "state": 1,
          "class": 16,
          "message": "Invalid column name 'PreferredLanguage'.",
          "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
          "procName": "",
          "lineNumber": 3
        }
      },
      "name": "RequestError",
      "number": 207,
      "lineNumber": 3,
      "state": 1,
      "class": 16,
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": ""
    },
    {
      "code": "EREQUEST",
      "originalError": {
        "info": {
          "name": "ERROR",
          "handlerName": "onErrorMessage",
          "number": 207,
          "state": 1,
          "class": 16,
          "message": "Invalid column name 'PreferredCommunication'.",
          "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
          "procName": "",
          "lineNumber": 3
        }
      },
      "name": "RequestError",
      "number": 207,
      "lineNumber": 3,
      "state": 1,
      "class": 16,
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": ""
    }
  ],
  "stack": "RequestError: Invalid column name 'SpecialRequirements'.\n    at handleError (C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-03-27 00:51:09 [ERROR]: Error in getClientByUserId: Invalid column name 'SpecialRequirements'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 207,
      "state": 1,
      "class": 16,
      "message": "Invalid column name 'SpecialRequirements'.",
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": "",
      "lineNumber": 4
    }
  },
  "name": "RequestError",
  "number": 207,
  "lineNumber": 4,
  "state": 1,
  "class": 16,
  "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
  "procName": "",
  "precedingErrors": [
    {
      "code": "EREQUEST",
      "originalError": {
        "info": {
          "name": "ERROR",
          "handlerName": "onErrorMessage",
          "number": 207,
          "state": 1,
          "class": 16,
          "message": "Invalid column name 'PreferredLanguage'.",
          "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
          "procName": "",
          "lineNumber": 3
        }
      },
      "name": "RequestError",
      "number": 207,
      "lineNumber": 3,
      "state": 1,
      "class": 16,
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": ""
    },
    {
      "code": "EREQUEST",
      "originalError": {
        "info": {
          "name": "ERROR",
          "handlerName": "onErrorMessage",
          "number": 207,
          "state": 1,
          "class": 16,
          "message": "Invalid column name 'PreferredCommunication'.",
          "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
          "procName": "",
          "lineNumber": 3
        }
      },
      "name": "RequestError",
      "number": 207,
      "lineNumber": 3,
      "state": 1,
      "class": 16,
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": ""
    }
  ],
  "stack": "RequestError: Invalid column name 'SpecialRequirements'.\n    at handleError (C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-03-27 00:51:09 [ERROR]: Error getting client profile: Invalid column name 'SpecialRequirements'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 207,
      "state": 1,
      "class": 16,
      "message": "Invalid column name 'SpecialRequirements'.",
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": "",
      "lineNumber": 4
    }
  },
  "name": "RequestError",
  "number": 207,
  "lineNumber": 4,
  "state": 1,
  "class": 16,
  "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
  "procName": "",
  "precedingErrors": [
    {
      "code": "EREQUEST",
      "originalError": {
        "info": {
          "name": "ERROR",
          "handlerName": "onErrorMessage",
          "number": 207,
          "state": 1,
          "class": 16,
          "message": "Invalid column name 'PreferredLanguage'.",
          "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
          "procName": "",
          "lineNumber": 3
        }
      },
      "name": "RequestError",
      "number": 207,
      "lineNumber": 3,
      "state": 1,
      "class": 16,
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": ""
    },
    {
      "code": "EREQUEST",
      "originalError": {
        "info": {
          "name": "ERROR",
          "handlerName": "onErrorMessage",
          "number": 207,
          "state": 1,
          "class": 16,
          "message": "Invalid column name 'PreferredCommunication'.",
          "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
          "procName": "",
          "lineNumber": 3
        }
      },
      "name": "RequestError",
      "number": 207,
      "lineNumber": 3,
      "state": 1,
      "class": 16,
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": ""
    }
  ],
  "stack": "RequestError: Invalid column name 'SpecialRequirements'.\n    at handleError (C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-03-27 00:51:09 [ERROR]: Error in getClientByUserId: Invalid column name 'SpecialRequirements'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 207,
      "state": 1,
      "class": 16,
      "message": "Invalid column name 'SpecialRequirements'.",
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": "",
      "lineNumber": 4
    }
  },
  "name": "RequestError",
  "number": 207,
  "lineNumber": 4,
  "state": 1,
  "class": 16,
  "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
  "procName": "",
  "precedingErrors": [
    {
      "code": "EREQUEST",
      "originalError": {
        "info": {
          "name": "ERROR",
          "handlerName": "onErrorMessage",
          "number": 207,
          "state": 1,
          "class": 16,
          "message": "Invalid column name 'PreferredLanguage'.",
          "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
          "procName": "",
          "lineNumber": 3
        }
      },
      "name": "RequestError",
      "number": 207,
      "lineNumber": 3,
      "state": 1,
      "class": 16,
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": ""
    },
    {
      "code": "EREQUEST",
      "originalError": {
        "info": {
          "name": "ERROR",
          "handlerName": "onErrorMessage",
          "number": 207,
          "state": 1,
          "class": 16,
          "message": "Invalid column name 'PreferredCommunication'.",
          "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
          "procName": "",
          "lineNumber": 3
        }
      },
      "name": "RequestError",
      "number": 207,
      "lineNumber": 3,
      "state": 1,
      "class": 16,
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": ""
    }
  ],
  "stack": "RequestError: Invalid column name 'SpecialRequirements'.\n    at handleError (C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-03-27 00:51:09 [ERROR]: Error getting client profile: Invalid column name 'SpecialRequirements'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 207,
      "state": 1,
      "class": 16,
      "message": "Invalid column name 'SpecialRequirements'.",
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": "",
      "lineNumber": 4
    }
  },
  "name": "RequestError",
  "number": 207,
  "lineNumber": 4,
  "state": 1,
  "class": 16,
  "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
  "procName": "",
  "precedingErrors": [
    {
      "code": "EREQUEST",
      "originalError": {
        "info": {
          "name": "ERROR",
          "handlerName": "onErrorMessage",
          "number": 207,
          "state": 1,
          "class": 16,
          "message": "Invalid column name 'PreferredLanguage'.",
          "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
          "procName": "",
          "lineNumber": 3
        }
      },
      "name": "RequestError",
      "number": 207,
      "lineNumber": 3,
      "state": 1,
      "class": 16,
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": ""
    },
    {
      "code": "EREQUEST",
      "originalError": {
        "info": {
          "name": "ERROR",
          "handlerName": "onErrorMessage",
          "number": 207,
          "state": 1,
          "class": 16,
          "message": "Invalid column name 'PreferredCommunication'.",
          "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
          "procName": "",
          "lineNumber": 3
        }
      },
      "name": "RequestError",
      "number": 207,
      "lineNumber": 3,
      "state": 1,
      "class": 16,
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": ""
    }
  ],
  "stack": "RequestError: Invalid column name 'SpecialRequirements'.\n    at handleError (C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-03-27 00:51:10 [ERROR]: Error in getClientByUserId: Invalid column name 'SpecialRequirements'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 207,
      "state": 1,
      "class": 16,
      "message": "Invalid column name 'SpecialRequirements'.",
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": "",
      "lineNumber": 4
    }
  },
  "name": "RequestError",
  "number": 207,
  "lineNumber": 4,
  "state": 1,
  "class": 16,
  "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
  "procName": "",
  "precedingErrors": [
    {
      "code": "EREQUEST",
      "originalError": {
        "info": {
          "name": "ERROR",
          "handlerName": "onErrorMessage",
          "number": 207,
          "state": 1,
          "class": 16,
          "message": "Invalid column name 'PreferredLanguage'.",
          "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
          "procName": "",
          "lineNumber": 3
        }
      },
      "name": "RequestError",
      "number": 207,
      "lineNumber": 3,
      "state": 1,
      "class": 16,
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": ""
    },
    {
      "code": "EREQUEST",
      "originalError": {
        "info": {
          "name": "ERROR",
          "handlerName": "onErrorMessage",
          "number": 207,
          "state": 1,
          "class": 16,
          "message": "Invalid column name 'PreferredCommunication'.",
          "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
          "procName": "",
          "lineNumber": 3
        }
      },
      "name": "RequestError",
      "number": 207,
      "lineNumber": 3,
      "state": 1,
      "class": 16,
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": ""
    }
  ],
  "stack": "RequestError: Invalid column name 'SpecialRequirements'.\n    at handleError (C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-03-27 00:51:10 [ERROR]: Error getting client profile: Invalid column name 'SpecialRequirements'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 207,
      "state": 1,
      "class": 16,
      "message": "Invalid column name 'SpecialRequirements'.",
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": "",
      "lineNumber": 4
    }
  },
  "name": "RequestError",
  "number": 207,
  "lineNumber": 4,
  "state": 1,
  "class": 16,
  "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
  "procName": "",
  "precedingErrors": [
    {
      "code": "EREQUEST",
      "originalError": {
        "info": {
          "name": "ERROR",
          "handlerName": "onErrorMessage",
          "number": 207,
          "state": 1,
          "class": 16,
          "message": "Invalid column name 'PreferredLanguage'.",
          "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
          "procName": "",
          "lineNumber": 3
        }
      },
      "name": "RequestError",
      "number": 207,
      "lineNumber": 3,
      "state": 1,
      "class": 16,
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": ""
    },
    {
      "code": "EREQUEST",
      "originalError": {
        "info": {
          "name": "ERROR",
          "handlerName": "onErrorMessage",
          "number": 207,
          "state": 1,
          "class": 16,
          "message": "Invalid column name 'PreferredCommunication'.",
          "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
          "procName": "",
          "lineNumber": 3
        }
      },
      "name": "RequestError",
      "number": 207,
      "lineNumber": 3,
      "state": 1,
      "class": 16,
      "serverName": "DESKTOP-4VEAR7D\\SQLEXPRESS",
      "procName": ""
    }
  ],
  "stack": "RequestError: Invalid column name 'SpecialRequirements'.\n    at handleError (C:\\burky root\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:524:28)\n    at Connection.emit (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\burky root\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-03-27 00:52:48 [INFO]: Database configuration loaded from file 
2025-03-27 00:52:48 [INFO]: Connected to SQL Server 
2025-03-27 00:52:48 [INFO]: Server running on port 5000 
2025-03-27 00:52:48 [INFO]: Environment: development 
2025-03-27 00:53:05 [INFO]: Database configuration loaded from file 
2025-03-27 00:53:06 [INFO]: Connected to SQL Server 
2025-03-27 00:53:06 [INFO]: Server running on port 5000 
2025-03-27 00:53:06 [INFO]: Environment: development 
2025-03-27 01:00:00 [INFO]: Login attempt for username: admin 
2025-03-27 01:00:00 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-27 01:00:37 [WARN]: User (ID: 6, role: 7) does not have permission: /profile - READ 
2025-03-27 01:00:37 [WARN]: User (ID: 6, role: 7) does not have permission: /profile - READ 
2025-03-27 01:00:38 [WARN]: User (ID: 6, role: 7) does not have permission: /profile - READ 
2025-03-27 01:00:38 [WARN]: User (ID: 6, role: 7) does not have permission: /profile - READ 
2025-03-27 01:02:22 [INFO]: Database configuration loaded from file 
2025-03-27 01:02:22 [INFO]: Connected to SQL Server 
2025-03-27 01:02:22 [INFO]: Server running on port 5000 
2025-03-27 01:02:22 [INFO]: Environment: development 
2025-03-27 01:02:43 [INFO]: Database configuration loaded from file 
2025-03-27 01:02:43 [INFO]: Connected to SQL Server 
2025-03-27 01:02:43 [INFO]: Server running on port 5000 
2025-03-27 01:02:43 [INFO]: Environment: development 
2025-03-27 01:03:08 [INFO]: Database configuration loaded from file 
2025-03-27 01:03:09 [INFO]: Connected to SQL Server 
2025-03-27 01:03:09 [INFO]: Server running on port 5000 
2025-03-27 01:03:09 [INFO]: Environment: development 
2025-03-27 01:05:31 [INFO]: Login attempt for username: client 
2025-03-27 01:05:31 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-03-27 01:07:53 [INFO]: Database configuration loaded from file 
2025-03-27 01:07:53 [INFO]: Connected to SQL Server 
2025-03-27 01:07:53 [INFO]: Server running on port 5000 
2025-03-27 01:07:53 [INFO]: Environment: development 
2025-03-27 01:08:46 [INFO]: Database configuration loaded from file 
2025-03-27 01:08:47 [INFO]: Connected to SQL Server 
2025-03-27 01:08:47 [INFO]: Server running on port 5000 
2025-03-27 01:08:47 [INFO]: Environment: development 
2025-03-27 01:16:54 [INFO]: Database configuration loaded from file 
2025-03-27 01:16:54 [INFO]: Connected to SQL Server 
2025-03-27 01:16:54 [INFO]: Server running on port 5000 
2025-03-27 01:16:54 [INFO]: Environment: development 
2025-03-27 01:27:12 [INFO]: Database configuration loaded from file 
2025-03-27 01:27:13 [INFO]: Connected to SQL Server 
2025-03-27 01:27:13 [INFO]: Server running on port 5000 
2025-03-27 01:27:13 [INFO]: Environment: development 
2025-03-27 01:30:12 [INFO]: Database configuration loaded from file 
2025-03-27 01:30:14 [INFO]: Connected to SQL Server 
2025-03-27 01:30:14 [INFO]: Server running on port 5000 
2025-03-27 01:30:14 [INFO]: Environment: development 
2025-03-27 01:38:09 [INFO]: Database configuration loaded from file 
2025-03-27 01:38:09 [INFO]: Connected to SQL Server 
2025-03-27 01:38:09 [INFO]: Server running on port 5000 
2025-03-27 01:38:09 [INFO]: Environment: development 
2025-03-27 01:41:55 [INFO]: Login attempt for username: expert 
2025-03-27 01:41:55 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-27 01:42:23 [INFO]: Login attempt for username: client 
2025-03-27 01:42:23 [WARN]: Login failed: Invalid password for user: client {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
}
2025-03-27 01:42:28 [INFO]: Login attempt for username: client 
2025-03-27 01:42:28 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-03-27 01:42:49 [INFO]: Login attempt for username: admin 
2025-03-27 01:42:49 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-27 14:52:07 [INFO]: Database configuration loaded from file 
2025-03-27 14:52:08 [INFO]: Connected to SQL Server 
2025-03-27 14:52:08 [INFO]: Server running on port 5000 
2025-03-27 14:52:08 [INFO]: Environment: development 
2025-03-27 14:53:33 [INFO]: Login attempt for username: expert 
2025-03-27 14:53:34 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-27 15:13:19 [INFO]: Login attempt for username: client 
2025-03-27 15:13:19 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-03-27 15:13:33 [INFO]: Login attempt for username: admin 
2025-03-27 15:13:33 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-27 15:14:29 [INFO]: Database configuration loaded from file 
2025-03-27 15:14:29 [INFO]: Connected to SQL Server 
2025-03-27 15:14:29 [INFO]: Server running on port 5000 
2025-03-27 15:14:29 [INFO]: Environment: development 
2025-03-27 17:20:45 [INFO]: Login attempt for username: admin 
2025-03-27 17:20:45 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-27 17:21:01 [INFO]: Login attempt for username: expert 
2025-03-27 17:21:01 [WARN]: Login failed: Invalid password for user: expert {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
}
2025-03-27 17:21:04 [INFO]: Login attempt for username: expert 
2025-03-27 17:21:04 [WARN]: Login failed: Invalid password for user: expert {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
}
2025-03-27 17:21:07 [INFO]: Login attempt for username: expert 
2025-03-27 17:21:07 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-27 17:26:24 [INFO]: Database configuration loaded from file 
2025-03-27 17:26:24 [INFO]: Connected to SQL Server 
2025-03-27 17:26:24 [INFO]: Server running on port 5000 
2025-03-27 17:26:24 [INFO]: Environment: development 
2025-03-27 17:26:42 [INFO]: Database configuration loaded from file 
2025-03-27 17:26:44 [INFO]: Connected to SQL Server 
2025-03-27 17:26:44 [INFO]: Server running on port 5000 
2025-03-27 17:26:44 [INFO]: Environment: development 
2025-03-27 17:28:02 [INFO]: Database configuration loaded from file 
2025-03-27 17:28:05 [INFO]: Connected to SQL Server 
2025-03-27 17:28:05 [INFO]: Server running on port 5000 
2025-03-27 17:28:05 [INFO]: Environment: development 
2025-03-27 17:30:30 [INFO]: Login attempt for username: client 
2025-03-27 17:30:30 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-03-27 17:30:44 [INFO]: Login attempt for username: expert 
2025-03-27 17:30:44 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-27 17:54:53 [INFO]: Database configuration loaded from file 
2025-03-27 17:54:54 [INFO]: Connected to SQL Server 
2025-03-27 17:54:54 [INFO]: Server running on port 5000 
2025-03-27 17:54:54 [INFO]: Environment: development 
2025-03-27 18:20:12 [INFO]: Database configuration loaded from file 
2025-03-27 18:20:12 [INFO]: Connected to SQL Server 
2025-03-27 18:20:12 [INFO]: Server running on port 5000 
2025-03-27 18:20:12 [INFO]: Environment: development 
2025-03-27 18:25:27 [INFO]: Login attempt for username: client 
2025-03-27 18:25:27 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-03-27 19:19:11 [INFO]: Database configuration loaded from file 
2025-03-27 19:19:12 [INFO]: Connected to SQL Server 
2025-03-27 19:19:12 [INFO]: Server running on port 5000 
2025-03-27 19:19:12 [INFO]: Environment: development 
2025-03-27 22:22:45 [ERROR]: Not Found - /api/experts/1/categories {
  "stack": "Error: Not Found - /api/experts/1/categories\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:47:12)",
  "path": "/api/experts/1/categories",
  "method": "GET",
  "body": {},
  "user": {
    "id": 5,
    "role": "Expert"
  }
}
2025-03-27 22:22:46 [ERROR]: Not Found - /api/experts/1/languages {
  "stack": "Error: Not Found - /api/experts/1/languages\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:47:12)",
  "path": "/api/experts/1/languages",
  "method": "GET",
  "body": {},
  "user": {
    "id": 5,
    "role": "Expert"
  }
}
2025-03-27 22:22:46 [ERROR]: Not Found - /api/experts/1/specializations {
  "stack": "Error: Not Found - /api/experts/1/specializations\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:47:12)",
  "path": "/api/experts/1/specializations",
  "method": "GET",
  "body": {},
  "user": {
    "id": 5,
    "role": "Expert"
  }
}
2025-03-27 22:22:46 [ERROR]: Not Found - /api/experts/1/categories {
  "stack": "Error: Not Found - /api/experts/1/categories\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:47:12)",
  "path": "/api/experts/1/categories",
  "method": "GET",
  "body": {},
  "user": {
    "id": 5,
    "role": "Expert"
  }
}
2025-03-27 22:22:46 [ERROR]: Not Found - /api/experts/1/languages {
  "stack": "Error: Not Found - /api/experts/1/languages\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:47:12)",
  "path": "/api/experts/1/languages",
  "method": "GET",
  "body": {},
  "user": {
    "id": 5,
    "role": "Expert"
  }
}
2025-03-27 22:22:46 [ERROR]: Not Found - /api/experts/1/specializations {
  "stack": "Error: Not Found - /api/experts/1/specializations\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:47:12)",
  "path": "/api/experts/1/specializations",
  "method": "GET",
  "body": {},
  "user": {
    "id": 5,
    "role": "Expert"
  }
}
2025-03-27 22:22:54 [ERROR]: Not Found - /api/experts/1/categories {
  "stack": "Error: Not Found - /api/experts/1/categories\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:47:12)",
  "path": "/api/experts/1/categories",
  "method": "GET",
  "body": {},
  "user": {
    "id": 5,
    "role": "Expert"
  }
}
2025-03-27 22:22:54 [ERROR]: Not Found - /api/experts/1/languages {
  "stack": "Error: Not Found - /api/experts/1/languages\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:47:12)",
  "path": "/api/experts/1/languages",
  "method": "GET",
  "body": {},
  "user": {
    "id": 5,
    "role": "Expert"
  }
}
2025-03-27 22:22:54 [ERROR]: Not Found - /api/experts/1/specializations {
  "stack": "Error: Not Found - /api/experts/1/specializations\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:47:12)",
  "path": "/api/experts/1/specializations",
  "method": "GET",
  "body": {},
  "user": {
    "id": 5,
    "role": "Expert"
  }
}
2025-03-27 22:23:32 [ERROR]: Not Found - /api/experts/1/categories {
  "stack": "Error: Not Found - /api/experts/1/categories\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:47:12)",
  "path": "/api/experts/1/categories",
  "method": "GET",
  "body": {},
  "user": {
    "id": 5,
    "role": "Expert"
  }
}
2025-03-27 22:23:32 [ERROR]: Not Found - /api/experts/1/languages {
  "stack": "Error: Not Found - /api/experts/1/languages\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:47:12)",
  "path": "/api/experts/1/languages",
  "method": "GET",
  "body": {},
  "user": {
    "id": 5,
    "role": "Expert"
  }
}
2025-03-27 22:23:32 [ERROR]: Not Found - /api/experts/1/specializations {
  "stack": "Error: Not Found - /api/experts/1/specializations\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:47:12)",
  "path": "/api/experts/1/specializations",
  "method": "GET",
  "body": {},
  "user": {
    "id": 5,
    "role": "Expert"
  }
}
2025-03-27 22:24:24 [INFO]: Database configuration loaded from file 
2025-03-27 22:24:24 [INFO]: Connected to SQL Server 
2025-03-27 22:24:24 [INFO]: Server running on port 5000 
2025-03-27 22:24:24 [INFO]: Environment: development 
2025-03-27 22:24:36 [INFO]: Database configuration loaded from file 
2025-03-27 22:24:37 [INFO]: Connected to SQL Server 
2025-03-27 22:24:37 [INFO]: Server running on port 5000 
2025-03-27 22:24:37 [INFO]: Environment: development 
2025-03-27 22:25:08 [INFO]: Database configuration loaded from file 
2025-03-27 22:25:09 [INFO]: Connected to SQL Server 
2025-03-27 22:25:09 [INFO]: Server running on port 5000 
2025-03-27 22:25:09 [INFO]: Environment: development 
2025-03-27 22:25:17 [INFO]: Database configuration loaded from file 
2025-03-27 22:25:18 [INFO]: Connected to SQL Server 
2025-03-27 22:25:18 [INFO]: Server running on port 5000 
2025-03-27 22:25:18 [INFO]: Environment: development 
2025-03-27 22:25:48 [INFO]: Database configuration loaded from file 
2025-03-27 22:25:48 [INFO]: Connected to SQL Server 
2025-03-27 22:25:48 [INFO]: Server running on port 5000 
2025-03-27 22:25:48 [INFO]: Environment: development 
2025-03-27 22:25:54 [INFO]: Database configuration loaded from file 
2025-03-27 22:25:54 [INFO]: Connected to SQL Server 
2025-03-27 22:25:54 [INFO]: Server running on port 5000 
2025-03-27 22:25:54 [INFO]: Environment: development 
2025-03-27 22:26:18 [INFO]: Database configuration loaded from file 
2025-03-27 22:26:18 [INFO]: Connected to SQL Server 
2025-03-27 22:26:18 [INFO]: Server running on port 5000 
2025-03-27 22:26:18 [INFO]: Environment: development 
2025-03-27 23:28:02 [INFO]: Database configuration loaded from file 
2025-03-27 23:28:03 [INFO]: Connected to SQL Server 
2025-03-27 23:28:03 [INFO]: Server running on port 5000 
2025-03-27 23:28:03 [INFO]: Environment: development 
2025-03-27 23:28:55 [INFO]: Database configuration loaded from file 
2025-03-27 23:28:56 [INFO]: Connected to SQL Server 
2025-03-27 23:28:56 [INFO]: Server running on port 5000 
2025-03-27 23:28:56 [INFO]: Environment: development 
2025-03-27 23:29:34 [INFO]: Database configuration loaded from file 
2025-03-27 23:29:34 [INFO]: Connected to SQL Server 
2025-03-27 23:29:34 [INFO]: Server running on port 5000 
2025-03-27 23:29:34 [INFO]: Environment: development 
2025-03-27 23:33:45 [ERROR]: Not Found - /api/experts/profile/me {
  "stack": "Error: Not Found - /api/experts/profile/me\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:47:12)",
  "path": "/api/experts/profile/me",
  "method": "PUT",
  "body": {
    "email": "<EMAIL>",
    "firstName": "Uzman",
    "lastName": "Kullanici",
    "specialty": "Psikoloji",
    "experienceYears": 5,
    "shortBio": "Deneyimli bir uzman olarak hizmet vermekteyim.a",
    "profileVisibility": true,
    "availableOnline": true,
    "availableInPerson": false,
    "responseTime": 24,
    "sessionDuration": 50,
    "profileImage": "",
    "videoIntro": "",
    "locationAddress": "",
    "locationCity": "",
    "locationCountry": ""
  },
  "user": {
    "id": 5,
    "role": "Expert"
  }
}
2025-03-27 23:37:58 [INFO]: Database configuration loaded from file 
2025-03-27 23:37:59 [INFO]: Connected to SQL Server 
2025-03-27 23:37:59 [INFO]: Server running on port 5000 
2025-03-27 23:37:59 [INFO]: Environment: development 
2025-03-27 23:50:28 [INFO]: Login attempt for username: client 
2025-03-27 23:50:28 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-03-27 23:51:16 [INFO]: Login attempt for username: admin 
2025-03-27 23:51:16 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-27 23:52:54 [INFO]: Login attempt for username: admin 
2025-03-27 23:52:54 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-27 23:53:09 [INFO]: Login attempt for username: client 
2025-03-27 23:53:09 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-03-27 23:55:09 [INFO]: Login attempt for username: expert 
2025-03-27 23:55:09 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-27 23:57:30 [INFO]: Login attempt for username: client 
2025-03-27 23:57:30 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-03-28 00:02:56 [INFO]: Login attempt for username: expert 
2025-03-28 00:02:56 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-28 01:27:25 [INFO]: Login attempt for username: admin 
2025-03-28 01:27:26 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-03-28 02:13:51 [INFO]: Login attempt for username: client 
2025-03-28 02:13:51 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-03-28 02:17:34 [INFO]: Login attempt for username: expert 
2025-03-28 02:17:34 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-03-28 02:27:29 [INFO]: Login attempt for username: client 
2025-03-28 02:27:30 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-03-28 02:30:40 [ERROR]: Not Found - /api/clients/1/preferred-categories {
  "stack": "Error: Not Found - /api/clients/1/preferred-categories\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:47:12)",
  "path": "/api/clients/1/preferred-categories",
  "method": "GET",
  "body": {},
  "user": {
    "id": 6,
    "role": "Client"
  }
}
2025-03-28 02:30:41 [ERROR]: Not Found - /api/clients/1/preferred-categories {
  "stack": "Error: Not Found - /api/clients/1/preferred-categories\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:47:12)",
  "path": "/api/clients/1/preferred-categories",
  "method": "GET",
  "body": {},
  "user": {
    "id": 6,
    "role": "Client"
  }
}
2025-03-28 02:32:46 [ERROR]: Not Found - /api/clients/1/preferred-categories {
  "stack": "Error: Not Found - /api/clients/1/preferred-categories\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:47:12)",
  "path": "/api/clients/1/preferred-categories",
  "method": "GET",
  "body": {},
  "user": {
    "id": 6,
    "role": "Client"
  }
}
2025-03-28 02:32:46 [ERROR]: Not Found - /api/clients/1/preferred-categories {
  "stack": "Error: Not Found - /api/clients/1/preferred-categories\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:47:12)",
  "path": "/api/clients/1/preferred-categories",
  "method": "GET",
  "body": {},
  "user": {
    "id": 6,
    "role": "Client"
  }
}
2025-03-28 02:35:38 [ERROR]: Not Found - /api/clients/1/preferred-categories {
  "stack": "Error: Not Found - /api/clients/1/preferred-categories\n    at notFound (C:\\burky root\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\burky root\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:47:12)",
  "path": "/api/clients/1/preferred-categories",
  "method": "GET",
  "body": {},
  "user": {
    "id": 6,
    "role": "Client"
  }
}
2025-03-28 02:44:45 [INFO]: Database configuration loaded from file 
2025-03-28 02:44:46 [INFO]: Connected to SQL Server 
2025-03-28 02:44:46 [INFO]: Server running on port 5000 
2025-03-28 02:44:46 [INFO]: Environment: development 
2025-08-05 23:22:02 [INFO]: Database configuration loaded from file 
2025-08-05 23:22:02 [INFO]: Connected to SQL Server 
2025-08-05 23:22:02 [INFO]: Server running on port 5000 
2025-08-05 23:22:02 [INFO]: Environment: development 
2025-08-05 23:23:19 [INFO]: Login attempt for username: admin 
2025-08-05 23:23:19 [WARN]: Login failed: Invalid password for user: admin {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-08-05 23:23:21 [INFO]: Login attempt for username: admin 
2025-08-05 23:23:21 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-08-05 23:24:34 [INFO]: Login attempt for username: expert 
2025-08-05 23:24:34 [WARN]: Security alert: Common password attempt for user: expert 
2025-08-05 23:24:34 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-08-05 23:40:07 [INFO]: Login attempt for username: client 
2025-08-05 23:40:07 [WARN]: Security alert: Common password attempt for user: client 
2025-08-05 23:40:07 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-08-06 00:33:47 [INFO]: Database configuration loaded from file 
2025-08-06 00:33:47 [INFO]: Connected to SQL Server 
2025-08-06 00:33:47 [INFO]: Server running on port 5000 
2025-08-06 00:33:47 [INFO]: Environment: development 
2025-08-06 14:05:41 [INFO]: Database configuration loaded from file 
2025-08-06 14:05:41 [INFO]: Connected to SQL Server 
2025-08-06 14:05:41 [INFO]: Server running on port 5000 
2025-08-06 14:05:41 [INFO]: Environment: development 
2025-08-06 14:07:26 [INFO]: Login attempt for username: admin 
2025-08-06 14:07:26 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-08-06 14:14:05 [INFO]: Login attempt for username: expert 
2025-08-06 14:14:05 [WARN]: Security alert: Common password attempt for user: expert 
2025-08-06 14:14:05 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-08-06 14:26:50 [INFO]: Database configuration loaded from file 
2025-08-06 14:26:50 [INFO]: Connected to SQL Server 
2025-08-06 14:26:50 [INFO]: Server running on port 5000 
2025-08-06 14:26:50 [INFO]: Environment: development 
2025-08-06 14:27:06 [INFO]: Database configuration loaded from file 
2025-08-06 14:27:06 [INFO]: Connected to SQL Server 
2025-08-06 14:27:06 [INFO]: Server running on port 5000 
2025-08-06 14:27:06 [INFO]: Environment: development 
2025-08-06 14:27:46 [INFO]: Database configuration loaded from file 
2025-08-06 14:27:46 [INFO]: Connected to SQL Server 
2025-08-06 14:27:46 [INFO]: Server running on port 5000 
2025-08-06 14:27:46 [INFO]: Environment: development 
2025-08-06 14:28:25 [INFO]: Database configuration loaded from file 
2025-08-06 14:28:25 [INFO]: Connected to SQL Server 
2025-08-06 14:28:25 [INFO]: Server running on port 5000 
2025-08-06 14:28:25 [INFO]: Environment: development 
2025-08-06 14:36:46 [INFO]: Database configuration loaded from file 
2025-08-06 14:36:46 [INFO]: Connected to SQL Server 
2025-08-06 14:36:46 [INFO]: Server running on port 5000 
2025-08-06 14:36:46 [INFO]: Environment: development 
2025-08-06 17:07:29 [INFO]: Database configuration loaded from file 
2025-08-06 17:07:29 [INFO]: Connected to SQL Server 
2025-08-06 17:07:29 [INFO]: Server running on port 5000 
2025-08-06 17:07:29 [INFO]: Environment: development 
2025-08-06 21:19:29 [INFO]: Database configuration loaded from file 
2025-08-06 21:19:29 [INFO]: Connected to SQL Server 
2025-08-06 21:19:29 [INFO]: Server running on port 5000 
2025-08-06 21:19:29 [INFO]: Environment: development 
2025-08-06 21:22:22 [INFO]: Database configuration loaded from file 
2025-08-06 21:22:22 [INFO]: Connected to SQL Server 
2025-08-06 21:22:58 [INFO]: Database configuration loaded from file 
2025-08-06 21:22:58 [INFO]: Connected to SQL Server 
2025-08-06 21:22:58 [INFO]: Server running on port 5000 
2025-08-06 21:22:58 [INFO]: Environment: development 
2025-08-06 21:22:58 [INFO]: Database configuration loaded from file 
2025-08-06 21:22:58 [INFO]: Connected to SQL Server 
2025-08-06 21:23:54 [INFO]: Database configuration loaded from file 
2025-08-06 21:23:54 [INFO]: Connected to SQL Server 
2025-08-06 21:23:59 [INFO]: Database configuration loaded from file 
2025-08-06 21:23:59 [INFO]: Connected to SQL Server 
2025-08-06 21:23:59 [INFO]: Server running on port 5000 
2025-08-06 21:23:59 [INFO]: Environment: development 
2025-08-06 21:24:07 [INFO]: Database configuration loaded from file 
2025-08-06 21:24:07 [INFO]: Connected to SQL Server 
2025-08-06 21:24:07 [INFO]: Server running on port 5000 
2025-08-06 21:24:07 [INFO]: Environment: development 
2025-08-06 21:25:53 [INFO]: Database configuration loaded from file 
2025-08-06 21:25:53 [INFO]: Connected to SQL Server 
2025-08-06 21:25:53 [INFO]: Server running on port 5000 
2025-08-06 21:25:53 [INFO]: Environment: development 
2025-08-06 21:29:27 [INFO]: Database configuration loaded from file 
2025-08-06 21:29:27 [INFO]: Connected to SQL Server 
2025-08-06 21:29:27 [INFO]: Server running on port 5000 
2025-08-06 21:29:27 [INFO]: Environment: development 
2025-08-06 21:29:30 [ERROR]: Not Found - /favicon.ico {
  "stack": "Error: Not Found - /favicon.ico\n    at notFound (C:\\Projeler\\kidgarden\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/favicon.ico",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-08-06 21:30:02 [INFO]: Database configuration loaded from file 
2025-08-06 21:30:02 [INFO]: Connected to SQL Server 
2025-08-06 21:30:02 [INFO]: Server running on port 5000 
2025-08-06 21:30:02 [INFO]: Environment: development 
2025-08-06 21:31:56 [INFO]: Database configuration loaded from file 
2025-08-06 21:31:56 [INFO]: Connected to SQL Server 
2025-08-06 21:31:56 [INFO]: Server running on port 5000 
2025-08-06 21:31:56 [INFO]: Environment: development 
2025-08-06 21:32:08 [ERROR]: Not Found - /.well-known/appspecific/com.chrome.devtools.json {
  "stack": "Error: Not Found - /.well-known/appspecific/com.chrome.devtools.json\n    at notFound (C:\\Projeler\\kidgarden\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/.well-known/appspecific/com.chrome.devtools.json",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-08-06 21:33:11 [ERROR]: Not Found - /.well-known/appspecific/com.chrome.devtools.json {
  "stack": "Error: Not Found - /.well-known/appspecific/com.chrome.devtools.json\n    at notFound (C:\\Projeler\\kidgarden\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/.well-known/appspecific/com.chrome.devtools.json",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-08-06 21:33:36 [ERROR]: Not Found - /.well-known/appspecific/com.chrome.devtools.json {
  "stack": "Error: Not Found - /.well-known/appspecific/com.chrome.devtools.json\n    at notFound (C:\\Projeler\\kidgarden\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/.well-known/appspecific/com.chrome.devtools.json",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-08-06 21:43:20 [INFO]: Database configuration loaded from file 
2025-08-06 21:43:20 [INFO]: Connected to SQL Server 
2025-08-06 21:43:20 [INFO]: Server running on port 5000 
2025-08-06 21:43:20 [INFO]: Environment: development 
2025-08-06 21:47:07 [INFO]: Database configuration loaded from file 
2025-08-06 21:47:07 [INFO]: Connected to SQL Server 
2025-08-06 21:47:52 [INFO]: Database configuration loaded from file 
2025-08-06 21:47:53 [INFO]: Connected to SQL Server 
2025-08-06 21:51:30 [INFO]: Database configuration loaded from file 
2025-08-06 21:51:30 [INFO]: Connected to SQL Server 
2025-08-06 21:51:30 [INFO]: Server running on port 5000 
2025-08-06 21:51:30 [INFO]: Environment: development 
2025-08-06 21:52:04 [INFO]: Database configuration loaded from file 
2025-08-06 21:52:04 [INFO]: Connected to SQL Server 
2025-08-06 21:52:04 [INFO]: Server running on port 5000 
2025-08-06 21:52:04 [INFO]: Environment: development 
2025-08-06 21:53:01 [INFO]: Database configuration loaded from file 
2025-08-06 21:53:02 [INFO]: Connected to SQL Server 
2025-08-06 21:53:02 [INFO]: Server running on port 5000 
2025-08-06 21:53:02 [INFO]: Environment: development 
2025-08-06 21:54:22 [INFO]: Database configuration loaded from file 
2025-08-06 21:55:30 [INFO]: Database configuration loaded from file 
2025-08-06 21:55:30 [INFO]: Connected to SQL Server 
2025-08-06 21:55:31 [INFO]: Server running on port 5000 
2025-08-06 21:55:31 [INFO]: Environment: development 
2025-08-06 21:55:57 [INFO]: Database configuration loaded from file 
2025-08-06 21:55:57 [INFO]: Connected to SQL Server 
2025-08-06 21:55:57 [INFO]: Server running on port 5000 
2025-08-06 21:55:57 [INFO]: Environment: development 
2025-08-06 21:56:35 [INFO]: Database configuration loaded from file 
2025-08-06 21:56:35 [INFO]: Connected to SQL Server 
2025-08-06 21:56:35 [INFO]: Server running on port 5000 
2025-08-06 21:56:35 [INFO]: Environment: development 
2025-08-06 21:57:10 [INFO]: Database configuration loaded from file 
2025-08-06 21:57:11 [INFO]: Connected to SQL Server 
2025-08-06 21:57:11 [INFO]: Server running on port 5000 
2025-08-06 21:57:11 [INFO]: Environment: development 
2025-08-06 21:58:16 [INFO]: Database configuration loaded from file 
2025-08-06 21:58:17 [INFO]: Connected to SQL Server 
2025-08-06 21:58:17 [INFO]: Server running on port 5000 
2025-08-06 21:58:17 [INFO]: Environment: development 
2025-08-06 22:07:54 [INFO]: Database configuration loaded from file 
2025-08-06 22:07:54 [INFO]: Connected to SQL Server 
2025-08-06 22:08:14 [INFO]: Database configuration loaded from file 
2025-08-06 22:08:14 [INFO]: Connected to SQL Server 
2025-08-06 22:31:20 [INFO]: Login attempt for username: expert 
2025-08-06 22:31:20 [WARN]: Security alert: Common password attempt for user: expert 
2025-08-06 22:31:20 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-08-06 22:32:50 [INFO]: Login attempt for username: client 
2025-08-06 22:32:50 [WARN]: Security alert: Common password attempt for user: client 
2025-08-06 22:32:50 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-08-06 22:32:57 [INFO]: Login attempt for username: client 
2025-08-06 22:32:57 [WARN]: Security alert: Common password attempt for user: client 
2025-08-06 22:32:58 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-08-06 22:33:15 [INFO]: Login attempt for username: admin 
2025-08-06 22:33:15 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-08-06 22:33:20 [INFO]: Login attempt for username: client 
2025-08-06 22:33:20 [WARN]: Security alert: Common password attempt for user: client 
2025-08-06 22:33:20 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-08-06 22:33:33 [INFO]: Login attempt for username: expert 
2025-08-06 22:33:33 [WARN]: Security alert: Common password attempt for user: expert 
2025-08-06 22:33:33 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-08-06 22:34:13 [INFO]: Login attempt for username: expert 
2025-08-06 22:34:13 [WARN]: Security alert: Common password attempt for user: expert 
2025-08-06 22:34:13 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-08-06 22:34:40 [INFO]: Login attempt for username: client 
2025-08-06 22:34:40 [WARN]: Security alert: Common password attempt for user: client 
2025-08-06 22:34:40 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-08-06 22:35:03 [INFO]: Login attempt for username: expert 
2025-08-06 22:35:03 [WARN]: Security alert: Common password attempt for user: expert 
2025-08-06 22:35:03 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-08-06 22:47:21 [INFO]: Database configuration loaded from file 
2025-08-06 22:47:21 [INFO]: Connected to SQL Server 
2025-08-06 22:47:21 [INFO]: Server running on port 5000 
2025-08-06 22:47:21 [INFO]: Environment: development 
2025-08-06 22:47:57 [INFO]: Database configuration loaded from file 
2025-08-06 22:47:58 [INFO]: Connected to SQL Server 
2025-08-06 22:47:58 [INFO]: Server running on port 5000 
2025-08-06 22:47:58 [INFO]: Environment: development 
2025-08-06 22:49:56 [INFO]: Database configuration loaded from file 
2025-08-06 22:49:56 [INFO]: Connected to SQL Server 
2025-08-06 22:50:36 [INFO]: Database configuration loaded from file 
2025-08-06 22:50:37 [INFO]: Connected to SQL Server 
2025-08-06 23:00:14 [INFO]: Database configuration loaded from file 
2025-08-06 23:00:14 [INFO]: Connected to SQL Server 
2025-08-06 23:00:14 [INFO]: Server running on port 5000 
2025-08-06 23:00:14 [INFO]: Environment: development 
2025-08-06 23:00:33 [INFO]: Database configuration loaded from file 
2025-08-06 23:00:33 [INFO]: Connected to SQL Server 
2025-08-06 23:00:33 [INFO]: Server running on port 5000 
2025-08-06 23:00:33 [INFO]: Environment: development 
2025-08-06 23:00:52 [INFO]: Database configuration loaded from file 
2025-08-06 23:00:52 [INFO]: Connected to SQL Server 
2025-08-06 23:00:52 [INFO]: Server running on port 5000 
2025-08-06 23:00:52 [INFO]: Environment: development 
2025-08-06 23:01:13 [INFO]: Database configuration loaded from file 
2025-08-06 23:01:13 [INFO]: Connected to SQL Server 
2025-08-06 23:01:13 [INFO]: Server running on port 5000 
2025-08-06 23:01:13 [INFO]: Environment: development 
2025-08-06 23:01:38 [INFO]: Database configuration loaded from file 
2025-08-06 23:01:38 [INFO]: Connected to SQL Server 
2025-08-06 23:01:38 [INFO]: Server running on port 5000 
2025-08-06 23:01:38 [INFO]: Environment: development 
2025-08-06 23:02:03 [INFO]: Database configuration loaded from file 
2025-08-06 23:02:03 [INFO]: Connected to SQL Server 
2025-08-06 23:02:03 [INFO]: Server running on port 5000 
2025-08-06 23:02:03 [INFO]: Environment: development 
2025-08-06 23:02:50 [INFO]: Database configuration loaded from file 
2025-08-06 23:02:50 [INFO]: Connected to SQL Server 
2025-08-06 23:02:50 [INFO]: Server running on port 5000 
2025-08-06 23:02:50 [INFO]: Environment: development 
2025-08-06 23:03:31 [INFO]: Database configuration loaded from file 
2025-08-06 23:03:31 [INFO]: Connected to SQL Server 
2025-08-06 23:03:31 [INFO]: Server running on port 5000 
2025-08-06 23:03:31 [INFO]: Environment: development 
2025-08-06 23:04:38 [INFO]: Database configuration loaded from file 
2025-08-06 23:05:10 [INFO]: Database configuration loaded from file 
2025-08-06 23:05:51 [INFO]: Database configuration loaded from file 
2025-08-06 23:05:51 [INFO]: Connected to SQL Server 
2025-08-06 23:05:51 [INFO]: Server running on port 5000 
2025-08-06 23:05:51 [INFO]: Environment: development 
2025-08-06 23:06:24 [INFO]: Database configuration loaded from file 
2025-08-06 23:06:25 [INFO]: Connected to SQL Server 
2025-08-06 23:06:25 [INFO]: Server running on port 5000 
2025-08-06 23:06:25 [INFO]: Environment: development 
2025-08-06 23:12:42 [ERROR]: ENOENT: no such file or directory, open 'C:\Projeler\kidgarden\burky_root_web\server\uploads\profile-images\expert_5_1754511162968-285032707.png' {
  "stack": "Error: ENOENT: no such file or directory, open 'C:\\Projeler\\kidgarden\\burky_root_web\\server\\uploads\\profile-images\\expert_5_1754511162968-285032707.png'",
  "path": "/api/experts/profile/upload-image",
  "method": "POST",
  "body": {},
  "user": {
    "id": 5,
    "role": "Expert"
  }
}
2025-08-06 23:15:06 [INFO]: Database configuration loaded from file 
2025-08-06 23:15:06 [INFO]: Connected to SQL Server 
2025-08-06 23:15:06 [INFO]: Server running on port 5000 
2025-08-06 23:15:06 [INFO]: Environment: development 
2025-08-06 23:15:41 [INFO]: Database configuration loaded from file 
2025-08-06 23:15:41 [INFO]: Connected to SQL Server 
2025-08-06 23:15:41 [INFO]: Server running on port 5000 
2025-08-06 23:15:41 [INFO]: Environment: development 
2025-08-06 23:16:01 [INFO]: Database configuration loaded from file 
2025-08-06 23:16:01 [INFO]: Connected to SQL Server 
2025-08-06 23:16:01 [INFO]: Server running on port 5000 
2025-08-06 23:16:01 [INFO]: Environment: development 
2025-08-06 23:17:09 [INFO]: Database configuration loaded from file 
2025-08-06 23:17:09 [INFO]: Connected to SQL Server 
2025-08-06 23:17:09 [INFO]: Server running on port 5000 
2025-08-06 23:17:09 [INFO]: Environment: development 
2025-08-06 23:17:30 [INFO]: Database configuration loaded from file 
2025-08-06 23:17:31 [INFO]: Connected to SQL Server 
2025-08-06 23:17:31 [INFO]: Server running on port 5000 
2025-08-06 23:17:31 [INFO]: Environment: development 
2025-08-06 23:18:23 [INFO]: Database configuration loaded from file 
2025-08-06 23:18:23 [INFO]: Connected to SQL Server 
2025-08-06 23:18:23 [INFO]: Server running on port 5000 
2025-08-06 23:18:23 [INFO]: Environment: development 
2025-08-06 23:18:45 [INFO]: Database configuration loaded from file 
2025-08-06 23:18:45 [INFO]: Connected to SQL Server 
2025-08-06 23:19:27 [INFO]: Database configuration loaded from file 
2025-08-06 23:19:27 [INFO]: Connected to SQL Server 
2025-08-06 23:22:17 [INFO]: Database configuration loaded from file 
2025-08-06 23:22:18 [INFO]: Connected to SQL Server 
2025-08-06 23:22:18 [INFO]: Server running on port 5000 
2025-08-06 23:22:18 [INFO]: Environment: development 
2025-08-06 23:22:35 [INFO]: Database configuration loaded from file 
2025-08-06 23:22:36 [INFO]: Connected to SQL Server 
2025-08-06 23:26:15 [INFO]: Database configuration loaded from file 
2025-08-06 23:26:15 [INFO]: Database configuration loaded from file 
2025-08-06 23:26:16 [INFO]: Connected to SQL Server 
2025-08-06 23:26:16 [INFO]: Connected to SQL Server 
2025-08-06 23:26:16 [INFO]: Server running on port 5000 
2025-08-06 23:26:16 [INFO]: Environment: development 
2025-08-06 23:28:45 [INFO]: Database configuration loaded from file 
2025-08-06 23:28:45 [INFO]: Connected to SQL Server 
2025-08-06 23:29:14 [ERROR]: Not Found - /logo512.png {
  "stack": "Error: Not Found - /logo512.png\n    at notFound (C:\\Projeler\\kidgarden\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/logo512.png",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-08-06 23:31:20 [INFO]: Database configuration loaded from file 
2025-08-06 23:31:20 [INFO]: Database configuration loaded from file 
2025-08-06 23:31:20 [INFO]: Connected to SQL Server 
2025-08-06 23:31:20 [INFO]: Connected to SQL Server 
2025-08-06 23:31:20 [INFO]: Server running on port 5000 
2025-08-06 23:31:20 [INFO]: Environment: development 
2025-08-06 23:31:20 [INFO]: Database configuration loaded from file 
2025-08-06 23:31:20 [INFO]: Connected to SQL Server 
2025-08-06 23:34:42 [INFO]: Database configuration loaded from file 
2025-08-06 23:34:42 [INFO]: Database configuration loaded from file 
2025-08-06 23:34:42 [INFO]: Connected to SQL Server 
2025-08-06 23:34:42 [INFO]: Connected to SQL Server 
2025-08-06 23:34:42 [INFO]: Server running on port 5000 
2025-08-06 23:34:42 [INFO]: Environment: development 
2025-08-06 23:34:42 [INFO]: Database configuration loaded from file 
2025-08-06 23:34:42 [INFO]: Connected to SQL Server 
2025-08-06 23:35:11 [INFO]: Database configuration loaded from file 
2025-08-06 23:35:11 [INFO]: Database configuration loaded from file 
2025-08-06 23:35:12 [INFO]: Connected to SQL Server 
2025-08-06 23:35:12 [INFO]: Connected to SQL Server 
2025-08-06 23:35:12 [INFO]: Server running on port 5000 
2025-08-06 23:35:12 [INFO]: Environment: development 
2025-08-06 23:35:12 [INFO]: Database configuration loaded from file 
2025-08-06 23:35:12 [INFO]: Connected to SQL Server 
2025-08-06 23:35:55 [INFO]: Database configuration loaded from file 
2025-08-06 23:35:55 [INFO]: Database configuration loaded from file 
2025-08-06 23:35:55 [INFO]: Connected to SQL Server 
2025-08-06 23:35:55 [INFO]: Connected to SQL Server 
2025-08-06 23:35:55 [INFO]: Server running on port 5000 
2025-08-06 23:35:55 [INFO]: Environment: development 
2025-08-06 23:35:55 [INFO]: Database configuration loaded from file 
2025-08-06 23:35:55 [INFO]: Connected to SQL Server 
2025-08-06 23:36:55 [INFO]: Database configuration loaded from file 
2025-08-06 23:36:55 [INFO]: Database configuration loaded from file 
2025-08-06 23:36:55 [INFO]: Connected to SQL Server 
2025-08-06 23:36:55 [INFO]: Connected to SQL Server 
2025-08-06 23:36:55 [INFO]: Server running on port 5000 
2025-08-06 23:36:55 [INFO]: Environment: development 
2025-08-06 23:36:55 [INFO]: Database configuration loaded from file 
2025-08-06 23:36:55 [INFO]: Connected to SQL Server 
2025-08-06 23:37:24 [INFO]: Database configuration loaded from file 
2025-08-06 23:37:24 [INFO]: Database configuration loaded from file 
2025-08-06 23:37:24 [INFO]: Connected to SQL Server 
2025-08-06 23:37:25 [INFO]: Connected to SQL Server 
2025-08-06 23:37:25 [INFO]: Server running on port 5000 
2025-08-06 23:37:25 [INFO]: Environment: development 
2025-08-06 23:37:24 [INFO]: Database configuration loaded from file 
2025-08-06 23:37:25 [INFO]: Connected to SQL Server 
2025-08-06 23:48:09 [ERROR]: Not Found - /logo512.png {
  "stack": "Error: Not Found - /logo512.png\n    at notFound (C:\\Projeler\\kidgarden\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/logo512.png",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-08-06 23:51:15 [INFO]: Database configuration loaded from file 
2025-08-06 23:51:15 [INFO]: Database configuration loaded from file 
2025-08-06 23:51:15 [INFO]: Connected to SQL Server 
2025-08-06 23:51:15 [INFO]: Connected to SQL Server 
2025-08-06 23:51:15 [INFO]: Server running on port 5000 
2025-08-06 23:51:15 [INFO]: Environment: development 
2025-08-06 23:51:15 [INFO]: Database configuration loaded from file 
2025-08-06 23:51:15 [INFO]: Connected to SQL Server 
2025-08-06 23:53:39 [INFO]: Database configuration loaded from file 
2025-08-06 23:53:39 [INFO]: Connected to SQL Server 
2025-08-06 23:54:25 [INFO]: Database configuration loaded from file 
2025-08-06 23:54:25 [INFO]: Connected to SQL Server 
2025-08-06 23:55:58 [INFO]: Database configuration loaded from file 
2025-08-06 23:55:58 [INFO]: Connected to SQL Server 
2025-08-06 23:55:58 [INFO]: Server running on port 5000 
2025-08-06 23:55:58 [INFO]: Environment: development 
2025-08-06 23:56:11 [INFO]: Database configuration loaded from file 
2025-08-06 23:56:11 [INFO]: Connected to SQL Server 
2025-08-06 23:56:11 [INFO]: Server running on port 5000 
2025-08-06 23:56:11 [INFO]: Environment: development 
2025-08-06 23:56:17 [ERROR]: Not Found - /main.e498cd81525e2d08d61a.hot-update.json {
  "stack": "Error: Not Found - /main.e498cd81525e2d08d61a.hot-update.json\n    at notFound (C:\\Projeler\\kidgarden\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/main.e498cd81525e2d08d61a.hot-update.json",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-08-06 23:56:17 [ERROR]: Not Found - /main.e498cd81525e2d08d61a.hot-update.json {
  "stack": "Error: Not Found - /main.e498cd81525e2d08d61a.hot-update.json\n    at notFound (C:\\Projeler\\kidgarden\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/main.e498cd81525e2d08d61a.hot-update.json",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-08-07 00:47:41 [INFO]: Database configuration loaded from file 
2025-08-07 00:47:41 [INFO]: Connected to SQL Server 
2025-08-07 00:47:41 [INFO]: Server running on port 5000 
2025-08-07 00:47:41 [INFO]: Environment: development 
2025-08-07 00:48:09 [INFO]: Database configuration loaded from file 
2025-08-07 00:48:09 [INFO]: Connected to SQL Server 
2025-08-07 00:48:09 [INFO]: Server running on port 5000 
2025-08-07 00:48:09 [INFO]: Environment: development 
2025-08-07 00:48:42 [INFO]: Database configuration loaded from file 
2025-08-07 00:48:42 [INFO]: Connected to SQL Server 
2025-08-07 00:48:42 [INFO]: Server running on port 5000 
2025-08-07 00:48:42 [INFO]: Environment: development 
2025-08-07 00:49:20 [INFO]: Database configuration loaded from file 
2025-08-07 00:49:20 [INFO]: Connected to SQL Server 
2025-08-07 00:49:20 [INFO]: Server running on port 5000 
2025-08-07 00:49:20 [INFO]: Environment: development 
2025-08-07 00:49:43 [INFO]: Database configuration loaded from file 
2025-08-07 00:49:44 [INFO]: Connected to SQL Server 
2025-08-07 00:49:44 [INFO]: Server running on port 5000 
2025-08-07 00:49:44 [INFO]: Environment: development 
2025-08-07 00:56:00 [INFO]: Database configuration loaded from file 
2025-08-07 00:56:00 [INFO]: Connected to SQL Server 
2025-08-07 00:56:00 [INFO]: Server running on port 5000 
2025-08-07 00:56:00 [INFO]: Environment: development 
2025-08-07 01:01:57 [INFO]: Database configuration loaded from file 
2025-08-07 01:01:57 [INFO]: Connected to SQL Server 
2025-08-07 01:02:10 [INFO]: Database configuration loaded from file 
2025-08-07 01:02:10 [INFO]: Connected to SQL Server 
2025-08-07 01:07:11 [INFO]: Database configuration loaded from file 
2025-08-07 01:07:11 [INFO]: Connected to SQL Server 
2025-08-07 01:07:11 [INFO]: Server running on port 5000 
2025-08-07 01:07:11 [INFO]: Environment: development 
2025-08-07 01:07:35 [INFO]: Database configuration loaded from file 
2025-08-07 01:07:36 [INFO]: Connected to SQL Server 
2025-08-07 01:07:36 [INFO]: Server running on port 5000 
2025-08-07 01:07:36 [INFO]: Environment: development 
2025-08-07 01:12:19 [INFO]: Database configuration loaded from file 
2025-08-07 01:12:19 [INFO]: Connected to SQL Server 
2025-08-07 01:47:22 [INFO]: Database configuration loaded from file 
2025-08-07 01:47:22 [INFO]: Connected to SQL Server 
2025-08-07 01:47:22 [INFO]: Server running on port 5000 
2025-08-07 01:47:22 [INFO]: Environment: development 
2025-08-07 01:47:22 [INFO]: Database configuration loaded from file 
2025-08-07 01:47:23 [INFO]: Connected to SQL Server 
2025-08-07 01:48:01 [INFO]: Database configuration loaded from file 
2025-08-07 01:48:01 [INFO]: Database configuration loaded from file 
2025-08-07 01:48:01 [INFO]: Connected to SQL Server 
2025-08-07 01:48:01 [INFO]: Server running on port 5000 
2025-08-07 01:48:01 [INFO]: Environment: development 
2025-08-07 01:48:01 [INFO]: Connected to SQL Server 
2025-08-07 01:48:13 [INFO]: Database configuration loaded from file 
2025-08-07 01:48:13 [INFO]: Database configuration loaded from file 
2025-08-07 01:48:44 [INFO]: Database configuration loaded from file 
2025-08-07 01:48:44 [INFO]: Database configuration loaded from file 
2025-08-07 01:49:09 [INFO]: Database configuration loaded from file 
2025-08-07 01:49:09 [INFO]: Database configuration loaded from file 
2025-08-07 01:49:42 [INFO]: Database configuration loaded from file 
2025-08-07 01:49:42 [INFO]: Database configuration loaded from file 
2025-08-07 02:04:10 [INFO]: Database configuration loaded from file 
2025-08-07 02:04:11 [INFO]: Connected to SQL Server 
2025-08-07 02:04:11 [INFO]: Server running on port 5000 
2025-08-07 02:04:11 [INFO]: Environment: development 
2025-08-07 02:04:47 [INFO]: Database configuration loaded from file 
2025-08-07 02:04:48 [INFO]: Connected to SQL Server 
2025-08-07 02:04:48 [INFO]: Server running on port 5000 
2025-08-07 02:04:48 [INFO]: Environment: development 
2025-08-07 21:32:03 [INFO]: Database configuration loaded from file 
2025-08-07 21:32:03 [INFO]: Connected to SQL Server 
2025-08-07 21:32:03 [INFO]: Server running on port 5000 
2025-08-07 21:32:03 [INFO]: Environment: development 
2025-08-07 21:32:46 [INFO]: Login attempt for username: expert 
2025-08-07 21:32:46 [WARN]: Login failed: Invalid password for user: expert {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-08-07 21:32:50 [INFO]: Login attempt for username: expert 
2025-08-07 21:32:50 [WARN]: Login failed: Invalid password for user: expert {
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-08-07 21:32:54 [INFO]: Login attempt for username: expert 
2025-08-07 21:32:54 [WARN]: Security alert: Common password attempt for user: expert 
2025-08-07 21:32:54 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-08-07 21:33:40 [INFO]: Login attempt for username: client 
2025-08-07 21:33:40 [WARN]: Security alert: Common password attempt for user: client 
2025-08-07 21:33:41 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-08-07 21:58:56 [INFO]: Database configuration loaded from file 
2025-08-07 21:58:56 [INFO]: Connected to SQL Server 
2025-08-07 21:58:56 [INFO]: Server running on port 5000 
2025-08-07 21:58:56 [INFO]: Environment: development 
2025-08-07 21:59:21 [INFO]: Database configuration loaded from file 
2025-08-07 21:59:21 [INFO]: Connected to SQL Server 
2025-08-07 21:59:21 [INFO]: Server running on port 5000 
2025-08-07 21:59:21 [INFO]: Environment: development 
2025-08-07 21:59:52 [INFO]: Database configuration loaded from file 
2025-08-07 21:59:53 [INFO]: Connected to SQL Server 
2025-08-07 21:59:53 [INFO]: Server running on port 5000 
2025-08-07 21:59:53 [INFO]: Environment: development 
2025-08-07 22:00:17 [INFO]: Database configuration loaded from file 
2025-08-07 22:00:18 [INFO]: Connected to SQL Server 
2025-08-07 22:00:18 [INFO]: Server running on port 5000 
2025-08-07 22:00:18 [INFO]: Environment: development 
2025-08-07 22:10:03 [INFO]: Database configuration loaded from file 
2025-08-07 22:10:03 [INFO]: Connected to SQL Server 
2025-08-07 22:10:03 [INFO]: Server running on port 5000 
2025-08-07 22:10:03 [INFO]: Environment: development 
2025-08-08 19:49:45 [INFO]: Database configuration loaded from file 
2025-08-08 19:49:45 [INFO]: Connected to SQL Server 
2025-08-08 19:49:45 [INFO]: Server running on port 5000 
2025-08-08 19:49:45 [INFO]: Environment: development 
2025-08-08 19:50:06 [INFO]: Login attempt for username: client 
2025-08-08 19:50:06 [WARN]: Security alert: Common password attempt for user: client 
2025-08-08 19:50:06 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-08-08 20:08:17 [INFO]: Database configuration loaded from file 
2025-08-08 20:08:17 [INFO]: Connected to SQL Server 
2025-08-08 20:08:17 [INFO]: Server running on port 5000 
2025-08-08 20:08:17 [INFO]: Environment: development 
2025-08-08 20:08:31 [INFO]: Database configuration loaded from file 
2025-08-08 20:08:31 [INFO]: Connected to SQL Server 
2025-08-08 20:08:31 [INFO]: Server running on port 5000 
2025-08-08 20:08:31 [INFO]: Environment: development 
2025-08-08 20:17:06 [INFO]: Login attempt for username: expert 
2025-08-08 20:17:06 [WARN]: Security alert: Common password attempt for user: expert 
2025-08-08 20:17:06 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-08-08 20:31:39 [INFO]: Adding availability with data: {
  "expertId": 1,
  "dayOfWeek": 2,
  "startTime": "09:00:00",
  "endTime": "17:00:00",
  "isRecurring": true,
  "specificDate": null
}
2025-08-08 20:31:39 [INFO]: Time string already in correct format: '09:00:00' 
2025-08-08 20:31:39 [INFO]: Time string already in correct format: '17:00:00' 
2025-08-08 20:31:39 [INFO]: Formatted time values for insert: {
  "original": {
    "startTime": "09:00:00",
    "endTime": "17:00:00"
  },
  "formatted": {
    "startTime": "09:00:00",
    "endTime": "17:00:00"
  }
}
2025-08-08 20:31:39 [INFO]: Final values for SQL query: {
  "expertId": 1,
  "dayOfWeek": 2,
  "startTime": "09:00:00",
  "endTime": "17:00:00",
  "isRecurring": true,
  "specificDate": null
}
2025-08-08 20:31:39 [INFO]: New availability inserted with ID: 5 
2025-08-08 20:31:39 [INFO]: Availability added successfully with ID: 
2025-08-08 20:33:19 [INFO]: Database configuration loaded from file 
2025-08-08 20:33:19 [INFO]: Connected to SQL Server 
2025-08-08 20:33:19 [INFO]: Server running on port 5000 
2025-08-08 20:33:19 [INFO]: Environment: development 
2025-08-08 21:25:27 [WARN]: User (ID: 6, role: 7) does not have permission: /client/appointments - CREATE 
2025-08-08 21:38:03 [WARN]: User (ID: 6, role: 7) does not have permission: /client/appointments - CREATE 
2025-08-08 21:38:40 [INFO]: Login attempt for username: admin 
2025-08-08 21:38:40 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-08-08 21:49:07 [INFO]: Login attempt for username: expert 
2025-08-08 21:49:07 [WARN]: Security alert: Common password attempt for user: expert 
2025-08-08 21:49:07 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-08-08 21:51:37 [INFO]: Database configuration loaded from file 
2025-08-08 21:52:01 [INFO]: Database configuration loaded from file 
2025-08-08 21:52:01 [INFO]: Connected to SQL Server 
2025-08-08 21:52:01 [INFO]: Server running on port 5000 
2025-08-08 21:52:01 [INFO]: Environment: development 
2025-08-08 21:52:27 [INFO]: Database configuration loaded from file 
2025-08-08 21:52:27 [INFO]: Connected to SQL Server 
2025-08-08 21:52:27 [INFO]: Server running on port 5000 
2025-08-08 21:52:27 [INFO]: Environment: development 
2025-08-08 22:20:52 [INFO]: Database configuration loaded from file 
2025-08-08 22:20:52 [INFO]: Connected to SQL Server 
2025-08-08 22:20:52 [INFO]: Server running on port 5000 
2025-08-08 22:20:52 [INFO]: Environment: development 
2025-08-08 22:25:10 [WARN]: User (ID: 6, role: 7) does not have permission: /admin/clients - READ 
2025-08-08 22:25:10 [WARN]: User (ID: 6, role: 7) does not have permission: /admin/clients - READ 
2025-08-08 22:25:11 [WARN]: User (ID: 6, role: 7) does not have permission: /admin/clients - READ 
2025-08-08 22:25:11 [WARN]: User (ID: 6, role: 7) does not have permission: /admin/clients - READ 
2025-08-08 22:26:31 [WARN]: User (ID: 6, role: 7) does not have permission: /admin/clients - READ 
2025-08-08 22:26:31 [WARN]: User (ID: 6, role: 7) does not have permission: /admin/clients - READ 
2025-08-08 22:26:32 [WARN]: User (ID: 6, role: 7) does not have permission: /admin/clients - READ 
2025-08-08 22:26:32 [WARN]: User (ID: 6, role: 7) does not have permission: /admin/clients - READ 
2025-08-08 22:26:41 [WARN]: User (ID: 6, role: 7) does not have permission: /admin/clients - READ 
2025-08-08 22:26:41 [WARN]: User (ID: 6, role: 7) does not have permission: /admin/clients - READ 
2025-08-08 22:26:41 [WARN]: User (ID: 6, role: 7) does not have permission: /admin/clients - READ 
2025-08-08 22:26:41 [WARN]: User (ID: 6, role: 7) does not have permission: /admin/clients - READ 
2025-08-08 22:28:18 [WARN]: User (ID: 6, role: 7) does not have permission: /admin/clients - READ 
2025-08-08 22:28:19 [WARN]: User (ID: 6, role: 7) does not have permission: /admin/clients - READ 
2025-08-08 22:28:19 [WARN]: User (ID: 6, role: 7) does not have permission: /admin/clients - READ 
2025-08-08 22:28:19 [WARN]: User (ID: 6, role: 7) does not have permission: /admin/clients - READ 
2025-08-08 22:28:21 [INFO]: Database configuration loaded from file 
2025-08-08 22:28:22 [INFO]: Connected to SQL Server 
2025-08-08 22:28:22 [INFO]: Server running on port 5000 
2025-08-08 22:28:22 [INFO]: Environment: development 
2025-08-08 22:28:32 [WARN]: User (ID: 6, role: 7) does not have permission: /admin/clients - READ 
2025-08-08 22:28:33 [WARN]: User (ID: 6, role: 7) does not have permission: /admin/clients - READ 
2025-08-08 22:28:33 [WARN]: User (ID: 6, role: 7) does not have permission: /admin/clients - READ 
2025-08-08 22:28:33 [WARN]: User (ID: 6, role: 7) does not have permission: /admin/clients - READ 
2025-08-08 22:28:36 [WARN]: User (ID: 6, role: 7) does not have permission: /admin/clients - READ 
2025-08-08 22:28:36 [WARN]: User (ID: 6, role: 7) does not have permission: /admin/clients - READ 
2025-08-08 22:28:36 [WARN]: User (ID: 6, role: 7) does not have permission: /admin/clients - READ 
2025-08-08 22:28:36 [WARN]: User (ID: 6, role: 7) does not have permission: /admin/clients - READ 
2025-08-08 22:29:43 [INFO]: Database configuration loaded from file 
2025-08-08 22:29:43 [INFO]: Connected to SQL Server 
2025-08-08 22:29:43 [INFO]: Server running on port 5000 
2025-08-08 22:29:43 [INFO]: Environment: development 
2025-08-08 22:30:01 [INFO]: Database configuration loaded from file 
2025-08-08 22:30:01 [INFO]: Connected to SQL Server 
2025-08-08 22:31:19 [INFO]: Database configuration loaded from file 
2025-08-08 22:31:19 [INFO]: Connected to SQL Server 
2025-08-08 22:31:19 [INFO]: Server running on port 5000 
2025-08-08 22:31:19 [INFO]: Environment: development 
2025-08-08 22:56:12 [INFO]: Database configuration loaded from file 
2025-08-08 22:56:12 [INFO]: Connected to SQL Server 
2025-08-08 22:56:12 [INFO]: Server running on port 5000 
2025-08-08 22:56:12 [INFO]: Environment: development 
2025-08-08 22:56:41 [INFO]: Database configuration loaded from file 
2025-08-08 22:56:42 [INFO]: Connected to SQL Server 
2025-08-08 22:56:42 [INFO]: Server running on port 5000 
2025-08-08 22:56:42 [INFO]: Environment: development 
2025-08-08 22:56:57 [INFO]: Database configuration loaded from file 
2025-08-08 22:56:57 [INFO]: Connected to SQL Server 
2025-08-08 22:56:57 [INFO]: Server running on port 5000 
2025-08-08 22:56:57 [INFO]: Environment: development 
2025-08-08 22:57:18 [INFO]: Database configuration loaded from file 
2025-08-08 22:57:18 [INFO]: Connected to SQL Server 
2025-08-08 22:57:18 [INFO]: Server running on port 5000 
2025-08-08 22:57:18 [INFO]: Environment: development 
2025-08-08 22:57:39 [INFO]: Database configuration loaded from file 
2025-08-08 22:57:39 [INFO]: Connected to SQL Server 
2025-08-08 22:57:39 [INFO]: Server running on port 5000 
2025-08-08 22:57:39 [INFO]: Environment: development 
2025-08-08 22:58:21 [INFO]: Database configuration loaded from file 
2025-08-08 22:58:21 [INFO]: Connected to SQL Server 
2025-08-08 22:58:21 [INFO]: Server running on port 5000 
2025-08-08 22:58:21 [INFO]: Environment: development 
2025-08-08 22:59:02 [INFO]: Database configuration loaded from file 
2025-08-08 22:59:02 [INFO]: Connected to SQL Server 
2025-08-08 22:59:02 [INFO]: Server running on port 5000 
2025-08-08 22:59:02 [INFO]: Environment: development 
2025-08-08 22:59:14 [INFO]: Database configuration loaded from file 
2025-08-08 22:59:32 [INFO]: Database configuration loaded from file 
2025-08-08 22:59:45 [INFO]: Database configuration loaded from file 
2025-08-08 22:59:45 [INFO]: Connected to SQL Server 
2025-08-08 22:59:45 [INFO]: Server running on port 5000 
2025-08-08 22:59:45 [INFO]: Environment: development 
2025-08-08 23:01:25 [INFO]: Login attempt for username: admin 
2025-08-08 23:01:25 [INFO]: Login successful for user: admin (ID: 1, Role: Admin) 
2025-08-08 23:04:13 [INFO]: Database configuration loaded from file 
2025-08-08 23:04:30 [INFO]: Database configuration loaded from file 
2025-08-08 23:04:44 [INFO]: Database configuration loaded from file 
2025-08-08 23:04:57 [INFO]: Database configuration loaded from file 
2025-08-08 23:05:11 [INFO]: Database configuration loaded from file 
2025-08-08 23:05:11 [INFO]: Connected to SQL Server 
2025-08-08 23:05:11 [INFO]: Server running on port 5000 
2025-08-08 23:05:11 [INFO]: Environment: development 
2025-08-08 23:07:05 [INFO]: Database configuration loaded from file 
2025-08-08 23:07:05 [INFO]: Connected to SQL Server 
2025-08-08 23:07:05 [INFO]: Server running on port 5000 
2025-08-08 23:07:05 [INFO]: Environment: development 
2025-08-08 23:07:32 [INFO]: Login attempt for username: expert 
2025-08-08 23:07:32 [WARN]: Security alert: Common password attempt for user: expert 
2025-08-08 23:07:32 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-08-08 23:10:31 [INFO]: Database configuration loaded from file 
2025-08-08 23:10:31 [INFO]: Connected to SQL Server 
2025-08-08 23:10:31 [INFO]: Server running on port 5000 
2025-08-08 23:10:31 [INFO]: Environment: development 
2025-08-08 23:10:58 [INFO]: Database configuration loaded from file 
2025-08-08 23:10:58 [INFO]: Connected to SQL Server 
2025-08-08 23:10:58 [INFO]: Server running on port 5000 
2025-08-08 23:10:58 [INFO]: Environment: development 
2025-08-08 23:11:14 [INFO]: Database configuration loaded from file 
2025-08-08 23:11:15 [INFO]: Connected to SQL Server 
2025-08-08 23:11:15 [INFO]: Server running on port 5000 
2025-08-08 23:11:15 [INFO]: Environment: development 
2025-08-08 23:11:22 [INFO]: Database configuration loaded from file 
2025-08-08 23:11:22 [INFO]: Connected to SQL Server 
2025-08-08 23:11:22 [INFO]: Server running on port 5000 
2025-08-08 23:11:22 [INFO]: Environment: development 
2025-08-08 23:12:06 [INFO]: Database configuration loaded from file 
2025-08-08 23:12:06 [INFO]: Connected to SQL Server 
2025-08-08 23:12:06 [INFO]: Server running on port 5000 
2025-08-08 23:12:06 [INFO]: Environment: development 
2025-08-08 23:12:24 [INFO]: Database configuration loaded from file 
2025-08-08 23:12:46 [INFO]: Database configuration loaded from file 
2025-08-08 23:12:57 [INFO]: Database configuration loaded from file 
2025-08-08 23:19:25 [INFO]: Database configuration loaded from file 
2025-08-08 23:19:39 [INFO]: Database configuration loaded from file 
2025-08-08 23:19:52 [INFO]: Database configuration loaded from file 
2025-08-08 23:20:06 [INFO]: Database configuration loaded from file 
2025-08-08 23:23:20 [INFO]: Database configuration loaded from file 
2025-08-08 23:23:26 [INFO]: Database configuration loaded from file 
2025-08-08 23:23:47 [INFO]: Database configuration loaded from file 
2025-08-08 23:23:58 [INFO]: Database configuration loaded from file 
2025-08-08 23:23:59 [INFO]: Connected to SQL Server 
2025-08-08 23:23:59 [INFO]: Server running on port 5000 
2025-08-08 23:23:59 [INFO]: Environment: development 
2025-08-08 23:24:11 [ERROR]: Not Found - /main.0cc13b7984c4ac1c8990.hot-update.json {
  "stack": "Error: Not Found - /main.0cc13b7984c4ac1c8990.hot-update.json\n    at notFound (C:\\Projeler\\kidgarden\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/main.0cc13b7984c4ac1c8990.hot-update.json",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-08-08 23:24:11 [ERROR]: Not Found - /main.0cc13b7984c4ac1c8990.hot-update.json {
  "stack": "Error: Not Found - /main.0cc13b7984c4ac1c8990.hot-update.json\n    at notFound (C:\\Projeler\\kidgarden\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/main.0cc13b7984c4ac1c8990.hot-update.json",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-08-08 23:24:11 [ERROR]: Not Found - /main.0cc13b7984c4ac1c8990.hot-update.json {
  "stack": "Error: Not Found - /main.0cc13b7984c4ac1c8990.hot-update.json\n    at notFound (C:\\Projeler\\kidgarden\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/main.0cc13b7984c4ac1c8990.hot-update.json",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-08-08 23:24:24 [WARN]: User (ID: 6, role: 7) does not have permission: /messages - READ 
2025-08-08 23:24:24 [WARN]: User (ID: 6, role: 7) does not have permission: /messages - READ 
2025-08-08 23:24:38 [WARN]: User (ID: 6, role: 7) does not have permission: /messages - READ 
2025-08-08 23:24:38 [WARN]: User (ID: 6, role: 7) does not have permission: /messages - READ 
2025-08-08 23:25:55 [INFO]: Database configuration loaded from file 
2025-08-08 23:25:55 [INFO]: Connected to SQL Server 
2025-08-08 23:25:55 [INFO]: Server running on port 5000 
2025-08-08 23:25:55 [INFO]: Environment: development 
2025-08-08 23:28:19 [INFO]: Database configuration loaded from file 
2025-08-08 23:28:20 [INFO]: Connected to SQL Server 
2025-08-08 23:28:20 [INFO]: Server running on port 5000 
2025-08-08 23:28:20 [INFO]: Environment: development 
2025-08-08 23:30:18 [ERROR]: Error in getUserConversations: Invalid column name 'Role'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 207,
      "state": 1,
      "class": 16,
      "message": "Invalid column name 'Role'.",
      "serverName": "admin\\BURAKSQL",
      "procName": "",
      "lineNumber": 20
    }
  },
  "name": "RequestError",
  "number": 207,
  "lineNumber": 20,
  "state": 1,
  "class": 16,
  "serverName": "admin\\BURAKSQL",
  "procName": "",
  "precedingErrors": [
    {
      "code": "EREQUEST",
      "originalError": {
        "info": {
          "name": "ERROR",
          "handlerName": "onErrorMessage",
          "number": 207,
          "state": 1,
          "class": 16,
          "message": "Invalid column name 'Role'.",
          "serverName": "admin\\BURAKSQL",
          "procName": "",
          "lineNumber": 19
        }
      },
      "name": "RequestError",
      "number": 207,
      "lineNumber": 19,
      "state": 1,
      "class": 16,
      "serverName": "admin\\BURAKSQL",
      "procName": ""
    }
  ],
  "stack": "RequestError: Invalid column name 'Role'.\n    at handleError (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:519:28)\n    at Connection.emit (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:536:3)\n    at Readable.push (node:internal/streams/readable:391:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-08-08 23:30:18 [ERROR]: Error getting user conversations: Invalid column name 'Role'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 207,
      "state": 1,
      "class": 16,
      "message": "Invalid column name 'Role'.",
      "serverName": "admin\\BURAKSQL",
      "procName": "",
      "lineNumber": 20
    }
  },
  "name": "RequestError",
  "number": 207,
  "lineNumber": 20,
  "state": 1,
  "class": 16,
  "serverName": "admin\\BURAKSQL",
  "procName": "",
  "precedingErrors": [
    {
      "code": "EREQUEST",
      "originalError": {
        "info": {
          "name": "ERROR",
          "handlerName": "onErrorMessage",
          "number": 207,
          "state": 1,
          "class": 16,
          "message": "Invalid column name 'Role'.",
          "serverName": "admin\\BURAKSQL",
          "procName": "",
          "lineNumber": 19
        }
      },
      "name": "RequestError",
      "number": 207,
      "lineNumber": 19,
      "state": 1,
      "class": 16,
      "serverName": "admin\\BURAKSQL",
      "procName": ""
    }
  ],
  "stack": "RequestError: Invalid column name 'Role'.\n    at handleError (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:519:28)\n    at Connection.emit (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:536:3)\n    at Readable.push (node:internal/streams/readable:391:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-08-08 23:30:18 [ERROR]: Error in getUserConversations: Invalid column name 'Role'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 207,
      "state": 1,
      "class": 16,
      "message": "Invalid column name 'Role'.",
      "serverName": "admin\\BURAKSQL",
      "procName": "",
      "lineNumber": 20
    }
  },
  "name": "RequestError",
  "number": 207,
  "lineNumber": 20,
  "state": 1,
  "class": 16,
  "serverName": "admin\\BURAKSQL",
  "procName": "",
  "precedingErrors": [
    {
      "code": "EREQUEST",
      "originalError": {
        "info": {
          "name": "ERROR",
          "handlerName": "onErrorMessage",
          "number": 207,
          "state": 1,
          "class": 16,
          "message": "Invalid column name 'Role'.",
          "serverName": "admin\\BURAKSQL",
          "procName": "",
          "lineNumber": 19
        }
      },
      "name": "RequestError",
      "number": 207,
      "lineNumber": 19,
      "state": 1,
      "class": 16,
      "serverName": "admin\\BURAKSQL",
      "procName": ""
    }
  ],
  "stack": "RequestError: Invalid column name 'Role'.\n    at handleError (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:519:28)\n    at Connection.emit (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:536:3)\n    at Readable.push (node:internal/streams/readable:391:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-08-08 23:30:18 [ERROR]: Error getting user conversations: Invalid column name 'Role'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 207,
      "state": 1,
      "class": 16,
      "message": "Invalid column name 'Role'.",
      "serverName": "admin\\BURAKSQL",
      "procName": "",
      "lineNumber": 20
    }
  },
  "name": "RequestError",
  "number": 207,
  "lineNumber": 20,
  "state": 1,
  "class": 16,
  "serverName": "admin\\BURAKSQL",
  "procName": "",
  "precedingErrors": [
    {
      "code": "EREQUEST",
      "originalError": {
        "info": {
          "name": "ERROR",
          "handlerName": "onErrorMessage",
          "number": 207,
          "state": 1,
          "class": 16,
          "message": "Invalid column name 'Role'.",
          "serverName": "admin\\BURAKSQL",
          "procName": "",
          "lineNumber": 19
        }
      },
      "name": "RequestError",
      "number": 207,
      "lineNumber": 19,
      "state": 1,
      "class": 16,
      "serverName": "admin\\BURAKSQL",
      "procName": ""
    }
  ],
  "stack": "RequestError: Invalid column name 'Role'.\n    at handleError (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:519:28)\n    at Connection.emit (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:536:3)\n    at Readable.push (node:internal/streams/readable:391:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-08-08 23:30:58 [INFO]: Database configuration loaded from file 
2025-08-08 23:30:58 [INFO]: Connected to SQL Server 
2025-08-08 23:31:14 [INFO]: Database configuration loaded from file 
2025-08-08 23:31:14 [INFO]: Connected to SQL Server 
2025-08-08 23:31:25 [ERROR]: Not Found - /favicon.ico {
  "stack": "Error: Not Found - /favicon.ico\n    at notFound (C:\\Projeler\\kidgarden\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/favicon.ico",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-08-08 23:31:41 [ERROR]: Error in getUserConversations: Invalid column name 'Role'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 207,
      "state": 1,
      "class": 16,
      "message": "Invalid column name 'Role'.",
      "serverName": "admin\\BURAKSQL",
      "procName": "",
      "lineNumber": 20
    }
  },
  "name": "RequestError",
  "number": 207,
  "lineNumber": 20,
  "state": 1,
  "class": 16,
  "serverName": "admin\\BURAKSQL",
  "procName": "",
  "precedingErrors": [
    {
      "code": "EREQUEST",
      "originalError": {
        "info": {
          "name": "ERROR",
          "handlerName": "onErrorMessage",
          "number": 207,
          "state": 1,
          "class": 16,
          "message": "Invalid column name 'Role'.",
          "serverName": "admin\\BURAKSQL",
          "procName": "",
          "lineNumber": 19
        }
      },
      "name": "RequestError",
      "number": 207,
      "lineNumber": 19,
      "state": 1,
      "class": 16,
      "serverName": "admin\\BURAKSQL",
      "procName": ""
    }
  ],
  "stack": "RequestError: Invalid column name 'Role'.\n    at handleError (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:519:28)\n    at Connection.emit (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:536:3)\n    at Readable.push (node:internal/streams/readable:391:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-08-08 23:31:41 [ERROR]: Error getting user conversations: Invalid column name 'Role'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 207,
      "state": 1,
      "class": 16,
      "message": "Invalid column name 'Role'.",
      "serverName": "admin\\BURAKSQL",
      "procName": "",
      "lineNumber": 20
    }
  },
  "name": "RequestError",
  "number": 207,
  "lineNumber": 20,
  "state": 1,
  "class": 16,
  "serverName": "admin\\BURAKSQL",
  "procName": "",
  "precedingErrors": [
    {
      "code": "EREQUEST",
      "originalError": {
        "info": {
          "name": "ERROR",
          "handlerName": "onErrorMessage",
          "number": 207,
          "state": 1,
          "class": 16,
          "message": "Invalid column name 'Role'.",
          "serverName": "admin\\BURAKSQL",
          "procName": "",
          "lineNumber": 19
        }
      },
      "name": "RequestError",
      "number": 207,
      "lineNumber": 19,
      "state": 1,
      "class": 16,
      "serverName": "admin\\BURAKSQL",
      "procName": ""
    }
  ],
  "stack": "RequestError: Invalid column name 'Role'.\n    at handleError (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:519:28)\n    at Connection.emit (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:536:3)\n    at Readable.push (node:internal/streams/readable:391:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-08-08 23:31:42 [ERROR]: Error in getUserConversations: Invalid column name 'Role'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 207,
      "state": 1,
      "class": 16,
      "message": "Invalid column name 'Role'.",
      "serverName": "admin\\BURAKSQL",
      "procName": "",
      "lineNumber": 20
    }
  },
  "name": "RequestError",
  "number": 207,
  "lineNumber": 20,
  "state": 1,
  "class": 16,
  "serverName": "admin\\BURAKSQL",
  "procName": "",
  "precedingErrors": [
    {
      "code": "EREQUEST",
      "originalError": {
        "info": {
          "name": "ERROR",
          "handlerName": "onErrorMessage",
          "number": 207,
          "state": 1,
          "class": 16,
          "message": "Invalid column name 'Role'.",
          "serverName": "admin\\BURAKSQL",
          "procName": "",
          "lineNumber": 19
        }
      },
      "name": "RequestError",
      "number": 207,
      "lineNumber": 19,
      "state": 1,
      "class": 16,
      "serverName": "admin\\BURAKSQL",
      "procName": ""
    }
  ],
  "stack": "RequestError: Invalid column name 'Role'.\n    at handleError (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:519:28)\n    at Connection.emit (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:536:3)\n    at Readable.push (node:internal/streams/readable:391:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-08-08 23:31:42 [ERROR]: Error getting user conversations: Invalid column name 'Role'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 207,
      "state": 1,
      "class": 16,
      "message": "Invalid column name 'Role'.",
      "serverName": "admin\\BURAKSQL",
      "procName": "",
      "lineNumber": 20
    }
  },
  "name": "RequestError",
  "number": 207,
  "lineNumber": 20,
  "state": 1,
  "class": 16,
  "serverName": "admin\\BURAKSQL",
  "procName": "",
  "precedingErrors": [
    {
      "code": "EREQUEST",
      "originalError": {
        "info": {
          "name": "ERROR",
          "handlerName": "onErrorMessage",
          "number": 207,
          "state": 1,
          "class": 16,
          "message": "Invalid column name 'Role'.",
          "serverName": "admin\\BURAKSQL",
          "procName": "",
          "lineNumber": 19
        }
      },
      "name": "RequestError",
      "number": 207,
      "lineNumber": 19,
      "state": 1,
      "class": 16,
      "serverName": "admin\\BURAKSQL",
      "procName": ""
    }
  ],
  "stack": "RequestError: Invalid column name 'Role'.\n    at handleError (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:519:28)\n    at Connection.emit (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:536:3)\n    at Readable.push (node:internal/streams/readable:391:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-08-08 23:31:49 [ERROR]: Error in getUserConversations: Invalid column name 'Role'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 207,
      "state": 1,
      "class": 16,
      "message": "Invalid column name 'Role'.",
      "serverName": "admin\\BURAKSQL",
      "procName": "",
      "lineNumber": 20
    }
  },
  "name": "RequestError",
  "number": 207,
  "lineNumber": 20,
  "state": 1,
  "class": 16,
  "serverName": "admin\\BURAKSQL",
  "procName": "",
  "precedingErrors": [
    {
      "code": "EREQUEST",
      "originalError": {
        "info": {
          "name": "ERROR",
          "handlerName": "onErrorMessage",
          "number": 207,
          "state": 1,
          "class": 16,
          "message": "Invalid column name 'Role'.",
          "serverName": "admin\\BURAKSQL",
          "procName": "",
          "lineNumber": 19
        }
      },
      "name": "RequestError",
      "number": 207,
      "lineNumber": 19,
      "state": 1,
      "class": 16,
      "serverName": "admin\\BURAKSQL",
      "procName": ""
    }
  ],
  "stack": "RequestError: Invalid column name 'Role'.\n    at handleError (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:519:28)\n    at Connection.emit (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:536:3)\n    at Readable.push (node:internal/streams/readable:391:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-08-08 23:31:49 [ERROR]: Error getting user conversations: Invalid column name 'Role'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 207,
      "state": 1,
      "class": 16,
      "message": "Invalid column name 'Role'.",
      "serverName": "admin\\BURAKSQL",
      "procName": "",
      "lineNumber": 20
    }
  },
  "name": "RequestError",
  "number": 207,
  "lineNumber": 20,
  "state": 1,
  "class": 16,
  "serverName": "admin\\BURAKSQL",
  "procName": "",
  "precedingErrors": [
    {
      "code": "EREQUEST",
      "originalError": {
        "info": {
          "name": "ERROR",
          "handlerName": "onErrorMessage",
          "number": 207,
          "state": 1,
          "class": 16,
          "message": "Invalid column name 'Role'.",
          "serverName": "admin\\BURAKSQL",
          "procName": "",
          "lineNumber": 19
        }
      },
      "name": "RequestError",
      "number": 207,
      "lineNumber": 19,
      "state": 1,
      "class": 16,
      "serverName": "admin\\BURAKSQL",
      "procName": ""
    }
  ],
  "stack": "RequestError: Invalid column name 'Role'.\n    at handleError (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:519:28)\n    at Connection.emit (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:536:3)\n    at Readable.push (node:internal/streams/readable:391:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-08-08 23:31:50 [ERROR]: Error in getUserConversations: Invalid column name 'Role'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 207,
      "state": 1,
      "class": 16,
      "message": "Invalid column name 'Role'.",
      "serverName": "admin\\BURAKSQL",
      "procName": "",
      "lineNumber": 20
    }
  },
  "name": "RequestError",
  "number": 207,
  "lineNumber": 20,
  "state": 1,
  "class": 16,
  "serverName": "admin\\BURAKSQL",
  "procName": "",
  "precedingErrors": [
    {
      "code": "EREQUEST",
      "originalError": {
        "info": {
          "name": "ERROR",
          "handlerName": "onErrorMessage",
          "number": 207,
          "state": 1,
          "class": 16,
          "message": "Invalid column name 'Role'.",
          "serverName": "admin\\BURAKSQL",
          "procName": "",
          "lineNumber": 19
        }
      },
      "name": "RequestError",
      "number": 207,
      "lineNumber": 19,
      "state": 1,
      "class": 16,
      "serverName": "admin\\BURAKSQL",
      "procName": ""
    }
  ],
  "stack": "RequestError: Invalid column name 'Role'.\n    at handleError (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:519:28)\n    at Connection.emit (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:536:3)\n    at Readable.push (node:internal/streams/readable:391:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-08-08 23:31:50 [ERROR]: Error getting user conversations: Invalid column name 'Role'. {
  "code": "EREQUEST",
  "originalError": {
    "info": {
      "name": "ERROR",
      "handlerName": "onErrorMessage",
      "number": 207,
      "state": 1,
      "class": 16,
      "message": "Invalid column name 'Role'.",
      "serverName": "admin\\BURAKSQL",
      "procName": "",
      "lineNumber": 20
    }
  },
  "name": "RequestError",
  "number": 207,
  "lineNumber": 20,
  "state": 1,
  "class": 16,
  "serverName": "admin\\BURAKSQL",
  "procName": "",
  "precedingErrors": [
    {
      "code": "EREQUEST",
      "originalError": {
        "info": {
          "name": "ERROR",
          "handlerName": "onErrorMessage",
          "number": 207,
          "state": 1,
          "class": 16,
          "message": "Invalid column name 'Role'.",
          "serverName": "admin\\BURAKSQL",
          "procName": "",
          "lineNumber": 19
        }
      },
      "name": "RequestError",
      "number": 207,
      "lineNumber": 19,
      "state": 1,
      "class": 16,
      "serverName": "admin\\BURAKSQL",
      "procName": ""
    }
  ],
  "stack": "RequestError: Invalid column name 'Role'.\n    at handleError (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:519:28)\n    at Connection.emit (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:536:3)\n    at Readable.push (node:internal/streams/readable:391:5)\n    at nextAsync (node:internal/streams/from:194:22)"
}
2025-08-08 23:32:08 [INFO]: Database configuration loaded from file 
2025-08-08 23:32:09 [INFO]: Connected to SQL Server 
2025-08-08 23:32:29 [INFO]: Database configuration loaded from file 
2025-08-08 23:32:29 [INFO]: Connected to SQL Server 
2025-08-08 23:32:29 [INFO]: Server running on port 5000 
2025-08-08 23:32:29 [INFO]: Environment: development 
2025-08-08 23:33:02 [INFO]: Database configuration loaded from file 
2025-08-08 23:33:02 [INFO]: Connected to SQL Server 
2025-08-08 23:33:02 [INFO]: Server running on port 5000 
2025-08-08 23:33:02 [INFO]: Environment: development 
2025-08-08 23:33:26 [INFO]: Database configuration loaded from file 
2025-08-08 23:33:26 [INFO]: Connected to SQL Server 
2025-08-08 23:33:26 [INFO]: Server running on port 5000 
2025-08-08 23:33:26 [INFO]: Environment: development 
2025-08-08 23:54:46 [INFO]: Database configuration loaded from file 
2025-08-08 23:54:46 [INFO]: Connected to SQL Server 
2025-08-08 23:54:46 [INFO]: Server running on port 5000 
2025-08-08 23:54:46 [INFO]: Environment: development 
2025-08-08 23:56:00 [INFO]: Database configuration loaded from file 
2025-08-08 23:56:00 [INFO]: Connected to SQL Server 
2025-08-08 23:56:00 [INFO]: Server running on port 5000 
2025-08-08 23:56:00 [INFO]: Environment: development 
2025-08-08 23:56:13 [INFO]: Database configuration loaded from file 
2025-08-08 23:56:14 [INFO]: Connected to SQL Server 
2025-08-08 23:56:14 [INFO]: Server running on port 5000 
2025-08-08 23:56:14 [INFO]: Environment: development 
2025-08-09 00:00:25 [INFO]: Database configuration loaded from file 
2025-08-09 00:00:26 [INFO]: Connected to SQL Server 
2025-08-09 00:00:26 [INFO]: Server running on port 5000 
2025-08-09 00:00:26 [INFO]: Environment: development 
2025-08-09 00:27:00 [INFO]: Database configuration loaded from file 
2025-08-09 00:27:00 [INFO]: Connected to SQL Server 
2025-08-09 00:27:00 [INFO]: Server running on port 5000 
2025-08-09 00:27:00 [INFO]: Environment: development 
2025-08-09 11:34:11 [INFO]: Database configuration loaded from file 
2025-08-09 11:34:11 [INFO]: Connected to SQL Server 
2025-08-09 11:34:11 [INFO]: Server running on port 5000 
2025-08-09 11:34:11 [INFO]: Environment: development 
2025-08-09 11:34:31 [INFO]: Login attempt for username: client 
2025-08-09 11:34:31 [WARN]: Security alert: Common password attempt for user: client 
2025-08-09 11:34:32 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-08-09 11:34:37 [INFO]: Login attempt for username: expert 
2025-08-09 11:34:37 [WARN]: Security alert: Common password attempt for user: expert 
2025-08-09 11:34:37 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-08-09 12:55:59 [INFO]: Database configuration loaded from file 
2025-08-09 12:55:59 [INFO]: Connected to SQL Server 
2025-08-09 12:55:59 [INFO]: Server running on port 5000 
2025-08-09 12:55:59 [INFO]: Environment: development 
2025-08-09 13:27:42 [INFO]: Database configuration loaded from file 
2025-08-09 13:27:42 [INFO]: Connected to SQL Server 
2025-08-09 13:27:42 [INFO]: Server running on port 5000 
2025-08-09 13:27:42 [INFO]: Environment: development 
2025-08-10 16:33:46 [INFO]: Database configuration loaded from file 
2025-08-10 16:33:47 [INFO]: Connected to SQL Server 
2025-08-10 16:33:47 [INFO]: Server running on port 5000 
2025-08-10 16:33:47 [INFO]: Environment: development 
2025-08-10 16:42:23 [INFO]: Database configuration loaded from file 
2025-08-10 16:42:23 [INFO]: Connected to SQL Server 
2025-08-10 16:42:23 [INFO]: Server running on port 5000 
2025-08-10 16:42:23 [INFO]: Environment: development 
2025-08-10 16:43:01 [INFO]: Database configuration loaded from file 
2025-08-10 16:43:01 [INFO]: Connected to SQL Server 
2025-08-10 16:43:01 [INFO]: Server running on port 5000 
2025-08-10 16:43:01 [INFO]: Environment: development 
2025-08-10 16:43:39 [INFO]: Database configuration loaded from file 
2025-08-10 16:43:40 [INFO]: Connected to SQL Server 
2025-08-10 16:43:40 [INFO]: Server running on port 5000 
2025-08-10 16:43:40 [INFO]: Environment: development 
2025-08-10 16:45:19 [INFO]: Login attempt for username: expert 
2025-08-10 16:45:20 [WARN]: Security alert: Common password attempt for user: expert 
2025-08-10 16:45:20 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-08-10 16:45:28 [INFO]: Login attempt for username: client 
2025-08-10 16:45:28 [WARN]: Security alert: Common password attempt for user: client 
2025-08-10 16:45:28 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-08-10 16:50:13 [INFO]: Database configuration loaded from file 
2025-08-10 16:50:13 [INFO]: Connected to SQL Server 
2025-08-10 16:50:13 [INFO]: Server running on port 5000 
2025-08-10 16:50:13 [INFO]: Environment: development 
2025-08-10 16:50:38 [INFO]: Database configuration loaded from file 
2025-08-10 16:50:39 [INFO]: Connected to SQL Server 
2025-08-10 16:50:39 [INFO]: Server running on port 5000 
2025-08-10 16:50:39 [INFO]: Environment: development 
2025-08-10 16:51:34 [INFO]: Database configuration loaded from file 
2025-08-10 16:51:34 [INFO]: Connected to SQL Server 
2025-08-10 16:51:34 [INFO]: Server running on port 5000 
2025-08-10 16:51:34 [INFO]: Environment: development 
2025-08-10 16:53:20 [INFO]: Database configuration loaded from file 
2025-08-10 16:53:20 [INFO]: Connected to SQL Server 
2025-08-10 16:53:20 [INFO]: Server running on port 5000 
2025-08-10 16:53:20 [INFO]: Environment: development 
2025-08-10 16:53:30 [ERROR]: Not Found - /main.2e0651096c7fbfc47702.hot-update.json {
  "stack": "Error: Not Found - /main.2e0651096c7fbfc47702.hot-update.json\n    at notFound (C:\\Projeler\\kidgarden\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/main.2e0651096c7fbfc47702.hot-update.json",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-08-10 16:53:30 [ERROR]: Not Found - /main.2e0651096c7fbfc47702.hot-update.json {
  "stack": "Error: Not Found - /main.2e0651096c7fbfc47702.hot-update.json\n    at notFound (C:\\Projeler\\kidgarden\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9",
  "path": "/main.2e0651096c7fbfc47702.hot-update.json",
  "method": "GET",
  "body": {},
  "user": "unauthenticated"
}
2025-08-10 16:55:35 [INFO]: Socket authenticated for user 6 
2025-08-10 16:55:35 [INFO]: User 6 connected via socket 
2025-08-10 16:55:35 [INFO]: User 6 joined conversation 1 
2025-08-10 16:55:36 [INFO]: User 6 joined conversation 1 
2025-08-10 16:55:45 [INFO]: Socket authenticated for user 5 
2025-08-10 16:55:45 [INFO]: User 5 connected via socket 
2025-08-10 16:55:45 [INFO]: User 5 joined conversation 1 
2025-08-10 16:55:45 [INFO]: User 5 joined conversation 1 
2025-08-10 16:56:25 [INFO]: User 6 disconnected, reason: transport close 
2025-08-10 16:56:25 [INFO]: Socket authenticated for user 6 
2025-08-10 16:56:25 [INFO]: User 6 connected via socket 
2025-08-10 16:56:25 [INFO]: User 6 joined conversation 1 
2025-08-10 16:56:26 [INFO]: User 6 joined conversation 1 
2025-08-10 16:56:27 [INFO]: User 5 disconnected, reason: transport close 
2025-08-10 16:56:27 [INFO]: Socket authenticated for user 5 
2025-08-10 16:56:27 [INFO]: User 5 connected via socket 
2025-08-10 16:56:28 [INFO]: User 5 joined conversation 1 
2025-08-10 16:56:28 [INFO]: User 5 joined conversation 1 
2025-08-10 16:56:34 [INFO]: User 5 joined conversation 1 
2025-08-10 16:56:34 [INFO]: User 5 joined conversation 1 
2025-08-10 16:56:34 [INFO]: User 6 joined conversation 1 
2025-08-10 16:56:34 [INFO]: User 6 joined conversation 1 
2025-08-10 16:56:39 [INFO]: User 5 joined conversation 1 
2025-08-10 16:56:39 [INFO]: User 5 joined conversation 1 
2025-08-10 16:56:39 [INFO]: User 6 joined conversation 1 
2025-08-10 16:56:39 [INFO]: User 6 joined conversation 1 
2025-08-10 16:58:42 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 16:58:42 [INFO]: Socket authenticated for user 5 
2025-08-10 16:58:42 [INFO]: User 5 connected via socket 
2025-08-10 16:58:42 [INFO]: User 5 joined conversation 1 
2025-08-10 16:58:42 [INFO]: User 5 joined conversation 1 
2025-08-10 16:58:55 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 16:58:55 [INFO]: Socket authenticated for user 6 
2025-08-10 16:58:55 [INFO]: User 6 connected via socket 
2025-08-10 16:58:55 [INFO]: User 6 joined conversation 1 
2025-08-10 16:58:55 [INFO]: User 6 joined conversation 1 
2025-08-10 16:59:41 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 16:59:41 [INFO]: Socket authenticated for user 5 
2025-08-10 16:59:41 [INFO]: User 5 connected via socket 
2025-08-10 16:59:41 [INFO]: User 5 joined conversation 1 
2025-08-10 17:00:01 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 17:00:01 [INFO]: Socket authenticated for user 6 
2025-08-10 17:00:01 [INFO]: User 6 connected via socket 
2025-08-10 17:00:01 [INFO]: User 6 joined conversation 1 
2025-08-10 17:00:26 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 17:00:26 [INFO]: Socket authenticated for user 5 
2025-08-10 17:00:26 [INFO]: User 5 connected via socket 
2025-08-10 17:00:52 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 17:00:52 [INFO]: Socket authenticated for user 6 
2025-08-10 17:00:52 [INFO]: User 6 connected via socket 
2025-08-10 17:01:18 [INFO]: Database configuration loaded from file 
2025-08-10 17:01:18 [INFO]: Connected to SQL Server 
2025-08-10 17:01:18 [INFO]: Server running on port 5000 
2025-08-10 17:01:18 [INFO]: Environment: development 
2025-08-10 17:01:18 [INFO]: Socket authenticated for user 6 
2025-08-10 17:01:18 [INFO]: User 6 connected via socket 
2025-08-10 17:01:18 [INFO]: Socket authenticated for user 5 
2025-08-10 17:01:18 [INFO]: User 5 connected via socket 
2025-08-10 17:02:11 [INFO]: User 6 disconnected, reason: transport close 
2025-08-10 17:02:12 [INFO]: Socket authenticated for user 6 
2025-08-10 17:02:12 [INFO]: User 6 connected via socket 
2025-08-10 17:02:12 [INFO]: User 6 joined conversation 1 
2025-08-10 17:02:19 [INFO]: User 5 disconnected, reason: transport close 
2025-08-10 17:02:19 [INFO]: Socket authenticated for user 5 
2025-08-10 17:02:19 [INFO]: User 5 connected via socket 
2025-08-10 17:02:20 [INFO]: User 5 joined conversation 1 
2025-08-10 17:03:26 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 17:03:27 [INFO]: Socket authenticated for user 6 
2025-08-10 17:03:27 [INFO]: User 6 connected via socket 
2025-08-10 17:03:27 [INFO]: User 6 joined conversation 1 
2025-08-10 17:03:34 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 17:03:38 [INFO]: Socket authenticated for user 6 
2025-08-10 17:03:38 [INFO]: User 6 connected via socket 
2025-08-10 17:03:38 [INFO]: User 6 joined conversation 1 
2025-08-10 17:03:41 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 17:03:41 [INFO]: Socket authenticated for user 6 
2025-08-10 17:03:41 [INFO]: User 6 connected via socket 
2025-08-10 17:03:41 [INFO]: User 6 joined conversation 1 
2025-08-10 17:05:23 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 17:05:23 [INFO]: Socket authenticated for user 6 
2025-08-10 17:05:23 [INFO]: User 6 connected via socket 
2025-08-10 17:05:23 [INFO]: User 6 joined conversation 1 
2025-08-10 17:06:39 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 17:06:39 [INFO]: Socket authenticated for user 6 
2025-08-10 17:06:39 [INFO]: User 6 connected via socket 
2025-08-10 17:06:39 [INFO]: User 6 joined conversation 1 
2025-08-10 17:07:03 [INFO]: User 6 disconnected, reason: transport close 
2025-08-10 17:07:03 [INFO]: Socket authenticated for user 6 
2025-08-10 17:07:03 [INFO]: User 6 connected via socket 
2025-08-10 17:07:03 [INFO]: User 6 joined conversation 1 
2025-08-10 17:07:07 [INFO]: User 6 disconnected, reason: transport close 
2025-08-10 17:07:07 [INFO]: Socket authenticated for user 6 
2025-08-10 17:07:07 [INFO]: User 6 connected via socket 
2025-08-10 17:07:07 [INFO]: User 6 joined conversation 1 
2025-08-10 17:07:10 [INFO]: User 6 disconnected, reason: transport close 
2025-08-10 17:07:10 [INFO]: Socket authenticated for user 6 
2025-08-10 17:07:10 [INFO]: User 6 connected via socket 
2025-08-10 17:07:10 [INFO]: User 6 joined conversation 1 
2025-08-10 17:09:54 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 17:09:54 [INFO]: Socket authenticated for user 6 
2025-08-10 17:09:54 [INFO]: User 6 connected via socket 
2025-08-10 17:10:38 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 17:10:38 [INFO]: Socket authenticated for user 5 
2025-08-10 17:10:38 [INFO]: User 5 connected via socket 
2025-08-10 17:11:13 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 17:11:13 [INFO]: Socket authenticated for user 6 
2025-08-10 17:11:13 [INFO]: User 6 connected via socket 
2025-08-10 17:11:57 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 17:11:57 [INFO]: Socket authenticated for user 6 
2025-08-10 17:11:57 [INFO]: User 6 connected via socket 
2025-08-10 17:12:11 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 17:12:11 [INFO]: Socket authenticated for user 5 
2025-08-10 17:12:11 [INFO]: User 5 connected via socket 
2025-08-10 17:12:43 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 17:12:43 [INFO]: Socket authenticated for user 5 
2025-08-10 17:12:43 [INFO]: User 5 connected via socket 
2025-08-10 17:13:08 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 17:13:08 [INFO]: Socket authenticated for user 6 
2025-08-10 17:13:08 [INFO]: User 6 connected via socket 
2025-08-10 17:13:09 [ERROR]: Error in getConversationMessages: Unauthorized access to conversation {
  "stack": "Error: Unauthorized access to conversation\n    at Object.getConversationMessages (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.service.js:96:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async markMessageAsRead (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.controller.js:119:24)"
}
2025-08-10 17:13:09 [ERROR]: Error marking message as read: Unauthorized access to conversation {
  "stack": "Error: Unauthorized access to conversation\n    at Object.getConversationMessages (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.service.js:96:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async markMessageAsRead (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.controller.js:119:24)"
}
2025-08-10 17:13:25 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 17:13:25 [INFO]: Socket authenticated for user 5 
2025-08-10 17:13:25 [INFO]: User 5 connected via socket 
2025-08-10 17:13:26 [ERROR]: Error in getConversationMessages: Unauthorized access to conversation {
  "stack": "Error: Unauthorized access to conversation\n    at Object.getConversationMessages (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.service.js:96:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async markMessageAsRead (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.controller.js:119:24)"
}
2025-08-10 17:13:26 [ERROR]: Error marking message as read: Unauthorized access to conversation {
  "stack": "Error: Unauthorized access to conversation\n    at Object.getConversationMessages (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.service.js:96:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async markMessageAsRead (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.controller.js:119:24)"
}
2025-08-10 17:13:50 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 17:13:50 [INFO]: Socket authenticated for user 6 
2025-08-10 17:13:50 [INFO]: User 6 connected via socket 
2025-08-10 17:14:09 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 17:14:09 [INFO]: Socket authenticated for user 6 
2025-08-10 17:14:09 [INFO]: User 6 connected via socket 
2025-08-10 17:14:10 [ERROR]: Error in getConversationMessages: Unauthorized access to conversation {
  "stack": "Error: Unauthorized access to conversation\n    at Object.getConversationMessages (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.service.js:96:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async markMessageAsRead (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.controller.js:119:24)"
}
2025-08-10 17:14:10 [ERROR]: Error marking message as read: Unauthorized access to conversation {
  "stack": "Error: Unauthorized access to conversation\n    at Object.getConversationMessages (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.service.js:96:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async markMessageAsRead (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.controller.js:119:24)"
}
2025-08-10 17:14:30 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 17:14:30 [INFO]: Socket authenticated for user 5 
2025-08-10 17:14:30 [INFO]: User 5 connected via socket 
2025-08-10 17:14:30 [ERROR]: Error in getConversationMessages: Unauthorized access to conversation {
  "stack": "Error: Unauthorized access to conversation\n    at Object.getConversationMessages (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.service.js:96:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async markMessageAsRead (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.controller.js:119:24)"
}
2025-08-10 17:14:30 [ERROR]: Error marking message as read: Unauthorized access to conversation {
  "stack": "Error: Unauthorized access to conversation\n    at Object.getConversationMessages (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.service.js:96:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async markMessageAsRead (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.controller.js:119:24)"
}
2025-08-10 17:14:48 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 17:14:48 [INFO]: Socket authenticated for user 5 
2025-08-10 17:14:48 [INFO]: User 5 connected via socket 
2025-08-10 17:14:48 [ERROR]: Error in getConversationMessages: Unauthorized access to conversation {
  "stack": "Error: Unauthorized access to conversation\n    at Object.getConversationMessages (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.service.js:96:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async markMessageAsRead (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.controller.js:119:24)"
}
2025-08-10 17:14:48 [ERROR]: Error marking message as read: Unauthorized access to conversation {
  "stack": "Error: Unauthorized access to conversation\n    at Object.getConversationMessages (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.service.js:96:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async markMessageAsRead (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.controller.js:119:24)"
}
2025-08-10 17:15:19 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 17:15:19 [INFO]: Socket authenticated for user 6 
2025-08-10 17:15:19 [INFO]: User 6 connected via socket 
2025-08-10 17:15:47 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 17:15:47 [INFO]: Socket authenticated for user 5 
2025-08-10 17:15:47 [INFO]: User 5 connected via socket 
2025-08-10 17:15:48 [ERROR]: Error in getConversationMessages: Unauthorized access to conversation {
  "stack": "Error: Unauthorized access to conversation\n    at Object.getConversationMessages (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.service.js:96:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async markMessageAsRead (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.controller.js:119:24)"
}
2025-08-10 17:15:48 [ERROR]: Error marking message as read: Unauthorized access to conversation {
  "stack": "Error: Unauthorized access to conversation\n    at Object.getConversationMessages (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.service.js:96:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async markMessageAsRead (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.controller.js:119:24)"
}
2025-08-10 17:16:56 [ERROR]: Error in getConversationMessages: Unauthorized access to conversation {
  "stack": "Error: Unauthorized access to conversation\n    at Object.getConversationMessages (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.service.js:96:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async markMessageAsRead (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.controller.js:119:24)"
}
2025-08-10 17:16:56 [ERROR]: Error marking message as read: Unauthorized access to conversation {
  "stack": "Error: Unauthorized access to conversation\n    at Object.getConversationMessages (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.service.js:96:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async markMessageAsRead (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.controller.js:119:24)"
}
2025-08-10 17:16:57 [INFO]: User 5 disconnected, reason: transport close 
2025-08-10 17:16:58 [INFO]: Socket authenticated for user 5 
2025-08-10 17:16:58 [INFO]: User 5 connected via socket 
2025-08-10 17:16:58 [INFO]: User 5 joined conversation 1 
2025-08-10 17:17:00 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 17:17:00 [INFO]: Socket authenticated for user 5 
2025-08-10 17:17:00 [INFO]: User 5 connected via socket 
2025-08-10 17:17:00 [INFO]: User 5 joined conversation 1 
2025-08-10 17:17:08 [INFO]: User 6 disconnected, reason: transport close 
2025-08-10 17:17:09 [INFO]: Socket authenticated for user 6 
2025-08-10 17:17:09 [INFO]: User 6 connected via socket 
2025-08-10 17:17:09 [INFO]: User 6 joined conversation 1 
2025-08-10 17:17:13 [ERROR]: Error in getConversationMessages: Unauthorized access to conversation {
  "stack": "Error: Unauthorized access to conversation\n    at Object.getConversationMessages (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.service.js:96:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async markMessageAsRead (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.controller.js:119:24)"
}
2025-08-10 17:17:13 [ERROR]: Error marking message as read: Unauthorized access to conversation {
  "stack": "Error: Unauthorized access to conversation\n    at Object.getConversationMessages (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.service.js:96:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async markMessageAsRead (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.controller.js:119:24)"
}
2025-08-10 17:17:29 [ERROR]: Error in getConversationMessages: Unauthorized access to conversation {
  "stack": "Error: Unauthorized access to conversation\n    at Object.getConversationMessages (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.service.js:96:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async markMessageAsRead (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.controller.js:119:24)"
}
2025-08-10 17:17:29 [ERROR]: Error marking message as read: Unauthorized access to conversation {
  "stack": "Error: Unauthorized access to conversation\n    at Object.getConversationMessages (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.service.js:96:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async markMessageAsRead (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.controller.js:119:24)"
}
2025-08-10 17:17:32 [ERROR]: Error in getConversationMessages: Unauthorized access to conversation {
  "stack": "Error: Unauthorized access to conversation\n    at Object.getConversationMessages (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.service.js:96:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async markMessageAsRead (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.controller.js:119:24)"
}
2025-08-10 17:17:32 [ERROR]: Error marking message as read: Unauthorized access to conversation {
  "stack": "Error: Unauthorized access to conversation\n    at Object.getConversationMessages (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.service.js:96:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async markMessageAsRead (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.controller.js:119:24)"
}
2025-08-10 17:17:42 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 17:17:42 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 17:17:42 [INFO]: Socket authenticated for user 6 
2025-08-10 17:17:42 [INFO]: User 6 connected via socket 
2025-08-10 17:17:42 [INFO]: Socket authenticated for user 5 
2025-08-10 17:17:42 [INFO]: User 5 connected via socket 
2025-08-10 17:24:28 [INFO]: User 5 disconnected, reason: transport close 
2025-08-10 17:24:29 [INFO]: Socket authenticated for user 5 
2025-08-10 17:24:29 [INFO]: User 5 connected via socket 
2025-08-10 17:24:29 [INFO]: User 5 joined conversation 1 
2025-08-10 17:24:38 [INFO]: User 6 disconnected, reason: transport close 
2025-08-10 17:24:39 [INFO]: Socket authenticated for user 6 
2025-08-10 17:24:39 [INFO]: User 6 connected via socket 
2025-08-10 17:24:39 [INFO]: User 6 joined conversation 1 
2025-08-10 17:24:53 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 17:27:06 [INFO]: Socket authenticated for user 5 
2025-08-10 17:27:06 [INFO]: User 5 connected via socket 
2025-08-10 17:27:06 [INFO]: User 5 joined conversation 1 
2025-08-10 17:52:59 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 17:52:59 [INFO]: Socket authenticated for user 6 
2025-08-10 17:52:59 [INFO]: User 6 connected via socket 
2025-08-10 17:53:28 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 17:53:28 [INFO]: Socket authenticated for user 5 
2025-08-10 17:53:28 [INFO]: User 5 connected via socket 
2025-08-10 17:54:21 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 17:54:21 [INFO]: Socket authenticated for user 6 
2025-08-10 17:54:21 [INFO]: User 6 connected via socket 
2025-08-10 17:54:48 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 17:54:48 [INFO]: Socket authenticated for user 6 
2025-08-10 17:54:48 [INFO]: User 6 connected via socket 
2025-08-10 17:55:20 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 17:55:20 [INFO]: Socket authenticated for user 6 
2025-08-10 17:55:20 [INFO]: User 6 connected via socket 
2025-08-10 17:55:45 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 17:55:45 [INFO]: Socket authenticated for user 5 
2025-08-10 17:55:45 [INFO]: User 5 connected via socket 
2025-08-10 17:56:50 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 17:56:50 [INFO]: Socket authenticated for user 5 
2025-08-10 17:56:50 [INFO]: User 5 connected via socket 
2025-08-10 17:57:40 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 17:57:40 [INFO]: Socket authenticated for user 5 
2025-08-10 17:57:40 [INFO]: User 5 connected via socket 
2025-08-10 17:58:43 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 17:58:43 [INFO]: Socket authenticated for user 6 
2025-08-10 17:58:43 [INFO]: User 6 connected via socket 
2025-08-10 17:58:43 [ERROR]: Not Found - /api/messages/conversations/1/read {
  "stack": "Error: Not Found - /api/messages/conversations/1/read\n    at notFound (C:\\Projeler\\kidgarden\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:47:12)",
  "path": "/api/messages/conversations/1/read",
  "method": "PUT",
  "body": {},
  "user": {
    "id": 6,
    "role": "Client"
  }
}
2025-08-10 17:59:05 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 17:59:05 [INFO]: Socket authenticated for user 5 
2025-08-10 17:59:05 [INFO]: User 5 connected via socket 
2025-08-10 17:59:06 [ERROR]: Not Found - /api/messages/conversations/1/read {
  "stack": "Error: Not Found - /api/messages/conversations/1/read\n    at notFound (C:\\Projeler\\kidgarden\\burky_root_web\\server\\middleware\\error.js:10:17)\n    at Layer.handle [as handle_request] (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Projeler\\kidgarden\\burky_root_web\\server\\node_modules\\express\\lib\\router\\index.js:47:12)",
  "path": "/api/messages/conversations/1/read",
  "method": "PUT",
  "body": {},
  "user": {
    "id": 5,
    "role": "Expert"
  }
}
2025-08-10 18:00:14 [INFO]: Database configuration loaded from file 
2025-08-10 18:01:01 [INFO]: Database configuration loaded from file 
2025-08-10 18:01:01 [INFO]: Connected to SQL Server 
2025-08-10 18:01:01 [INFO]: Server running on port 5000 
2025-08-10 18:01:01 [INFO]: Environment: development 
2025-08-10 18:01:22 [INFO]: Socket authenticated for user 5 
2025-08-10 18:01:22 [INFO]: User 5 connected via socket 
2025-08-10 18:01:22 [INFO]: User 5 joined conversation 1 
2025-08-10 18:02:41 [INFO]: Database configuration loaded from file 
2025-08-10 18:02:41 [INFO]: Connected to SQL Server 
2025-08-10 18:02:41 [INFO]: Server running on port 5000 
2025-08-10 18:02:41 [INFO]: Environment: development 
2025-08-10 18:02:41 [INFO]: Socket authenticated for user 5 
2025-08-10 18:02:41 [INFO]: User 5 connected via socket 
2025-08-10 18:03:26 [INFO]: Socket authenticated for user 6 
2025-08-10 18:03:26 [INFO]: User 6 connected via socket 
2025-08-10 18:03:44 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 18:03:44 [INFO]: Socket authenticated for user 6 
2025-08-10 18:03:44 [INFO]: User 6 connected via socket 
2025-08-10 18:04:09 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 18:04:09 [INFO]: Socket authenticated for user 5 
2025-08-10 18:04:09 [INFO]: User 5 connected via socket 
2025-08-10 18:04:54 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 18:04:54 [INFO]: Socket authenticated for user 5 
2025-08-10 18:04:54 [INFO]: User 5 connected via socket 
2025-08-10 18:42:24 [INFO]: User 5 disconnected, reason: transport close 
2025-08-10 18:42:25 [INFO]: Socket authenticated for user 5 
2025-08-10 18:42:25 [INFO]: User 5 connected via socket 
2025-08-10 18:42:25 [INFO]: User 5 joined conversation 1 
2025-08-10 18:42:27 [INFO]: User 6 disconnected, reason: transport close 
2025-08-10 18:42:27 [INFO]: Socket authenticated for user 6 
2025-08-10 18:42:27 [INFO]: User 6 connected via socket 
2025-08-10 18:42:27 [INFO]: User 6 joined conversation 1 
2025-08-10 18:43:07 [INFO]: User 5 disconnected, reason: transport close 
2025-08-10 18:43:07 [INFO]: Socket authenticated for user 5 
2025-08-10 18:43:07 [INFO]: User 5 connected via socket 
2025-08-10 18:43:08 [INFO]: User 5 joined conversation 1 
2025-08-10 18:43:19 [INFO]: User 6 disconnected, reason: transport close 
2025-08-10 18:43:19 [INFO]: Socket authenticated for user 6 
2025-08-10 18:43:19 [INFO]: User 6 connected via socket 
2025-08-10 18:43:19 [INFO]: User 6 joined conversation 1 
2025-08-10 18:44:20 [INFO]: User 5 disconnected, reason: transport close 
2025-08-10 18:44:20 [INFO]: Socket authenticated for user 5 
2025-08-10 18:44:20 [INFO]: User 5 connected via socket 
2025-08-10 18:44:20 [INFO]: User 5 joined conversation 1 
2025-08-10 18:46:10 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 18:46:11 [INFO]: Socket authenticated for user 5 
2025-08-10 18:46:11 [INFO]: User 5 connected via socket 
2025-08-10 18:46:11 [INFO]: User 5 joined conversation 1 
2025-08-10 18:48:11 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 18:48:11 [INFO]: Socket authenticated for user 5 
2025-08-10 18:48:11 [INFO]: User 5 connected via socket 
2025-08-10 18:48:11 [INFO]: User 5 joined conversation 1 
2025-08-10 18:48:20 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 18:48:20 [INFO]: Socket authenticated for user 6 
2025-08-10 18:48:20 [INFO]: User 6 connected via socket 
2025-08-10 18:48:20 [INFO]: User 6 joined conversation 1 
2025-08-10 18:48:24 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 18:48:25 [INFO]: Socket authenticated for user 6 
2025-08-10 18:48:25 [INFO]: User 6 connected via socket 
2025-08-10 18:48:25 [INFO]: User 6 joined conversation 1 
2025-08-10 18:49:26 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 18:49:26 [INFO]: Socket authenticated for user 6 
2025-08-10 18:49:26 [INFO]: User 6 connected via socket 
2025-08-10 18:49:51 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 18:49:51 [INFO]: Socket authenticated for user 5 
2025-08-10 18:49:51 [INFO]: User 5 connected via socket 
2025-08-10 18:50:26 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 18:50:26 [INFO]: Socket authenticated for user 6 
2025-08-10 18:50:26 [INFO]: User 6 connected via socket 
2025-08-10 18:51:04 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 18:51:04 [INFO]: Socket authenticated for user 5 
2025-08-10 18:51:04 [INFO]: User 5 connected via socket 
2025-08-10 18:51:31 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 18:51:31 [INFO]: Socket authenticated for user 6 
2025-08-10 18:51:31 [INFO]: User 6 connected via socket 
2025-08-10 18:51:57 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 18:51:57 [INFO]: Socket authenticated for user 5 
2025-08-10 18:51:57 [INFO]: User 5 connected via socket 
2025-08-10 18:52:14 [INFO]: Database configuration loaded from file 
2025-08-10 18:52:15 [INFO]: Connected to SQL Server 
2025-08-10 18:52:15 [INFO]: Server running on port 5000 
2025-08-10 18:52:15 [INFO]: Environment: development 
2025-08-10 18:52:15 [INFO]: Socket authenticated for user 5 
2025-08-10 18:52:15 [INFO]: User 5 connected via socket 
2025-08-10 18:52:15 [INFO]: Socket authenticated for user 6 
2025-08-10 18:52:15 [INFO]: User 6 connected via socket 
2025-08-10 18:52:39 [INFO]: Database configuration loaded from file 
2025-08-10 18:52:40 [INFO]: Connected to SQL Server 
2025-08-10 18:52:40 [INFO]: Server running on port 5000 
2025-08-10 18:52:40 [INFO]: Environment: development 
2025-08-10 18:52:40 [INFO]: Socket authenticated for user 6 
2025-08-10 18:52:40 [INFO]: User 6 connected via socket 
2025-08-10 18:52:40 [INFO]: Socket authenticated for user 5 
2025-08-10 18:52:40 [INFO]: User 5 connected via socket 
2025-08-10 18:53:13 [INFO]: User 5 disconnected, reason: transport close 
2025-08-10 18:53:14 [INFO]: Socket authenticated for user 5 
2025-08-10 18:53:14 [INFO]: User 5 connected via socket 
2025-08-10 18:53:14 [INFO]: User 5 joined conversation 1 
2025-08-10 18:53:15 [INFO]: User 6 disconnected, reason: transport close 
2025-08-10 18:53:16 [INFO]: Socket authenticated for user 6 
2025-08-10 18:53:16 [INFO]: User 6 connected via socket 
2025-08-10 18:53:16 [INFO]: User 6 joined conversation 1 
2025-08-10 18:55:30 [INFO]: User 5 disconnected, reason: transport close 
2025-08-10 18:55:31 [INFO]: Socket authenticated for user 5 
2025-08-10 18:55:31 [INFO]: User 5 connected via socket 
2025-08-10 18:55:31 [INFO]: User 5 joined conversation 1 
2025-08-10 18:56:49 [INFO]: Database configuration loaded from file 
2025-08-10 18:56:49 [INFO]: Connected to SQL Server 
2025-08-10 18:56:49 [INFO]: Server running on port 5000 
2025-08-10 18:56:49 [INFO]: Environment: development 
2025-08-10 18:56:49 [INFO]: Socket authenticated for user 6 
2025-08-10 18:56:49 [INFO]: User 6 connected via socket 
2025-08-10 18:56:49 [INFO]: Socket authenticated for user 5 
2025-08-10 18:56:49 [INFO]: User 5 connected via socket 
2025-08-10 18:57:52 [INFO]: User 5 disconnected, reason: transport close 
2025-08-10 18:57:53 [INFO]: Socket authenticated for user 5 
2025-08-10 18:57:53 [INFO]: User 5 connected via socket 
2025-08-10 18:57:53 [INFO]: User 5 joined conversation 1 
2025-08-10 18:57:56 [INFO]: User 6 disconnected, reason: transport close 
2025-08-10 18:57:57 [INFO]: Socket authenticated for user 6 
2025-08-10 18:57:57 [INFO]: User 6 connected via socket 
2025-08-10 18:57:57 [INFO]: User 6 joined conversation 1 
2025-08-10 19:00:02 [INFO]: Database configuration loaded from file 
2025-08-10 19:00:03 [INFO]: Connected to SQL Server 
2025-08-10 19:00:03 [INFO]: Server running on port 5000 
2025-08-10 19:00:03 [INFO]: Environment: development 
2025-08-10 19:00:03 [INFO]: Socket authenticated for user 6 
2025-08-10 19:00:03 [INFO]: User 6 connected via socket 
2025-08-10 19:00:03 [INFO]: Socket authenticated for user 5 
2025-08-10 19:00:03 [INFO]: User 5 connected via socket 
2025-08-10 19:00:26 [INFO]: Database configuration loaded from file 
2025-08-10 19:00:27 [INFO]: Connected to SQL Server 
2025-08-10 19:00:27 [INFO]: Server running on port 5000 
2025-08-10 19:00:27 [INFO]: Environment: development 
2025-08-10 19:00:27 [INFO]: Socket authenticated for user 5 
2025-08-10 19:00:27 [INFO]: User 5 connected via socket 
2025-08-10 19:00:27 [INFO]: Socket authenticated for user 6 
2025-08-10 19:00:27 [INFO]: User 6 connected via socket 
2025-08-10 19:00:28 [INFO]: User 5 disconnected, reason: transport close 
2025-08-10 19:00:29 [INFO]: Socket authenticated for user 5 
2025-08-10 19:00:29 [INFO]: User 5 connected via socket 
2025-08-10 19:00:29 [INFO]: User 5 joined conversation 1 
2025-08-10 19:01:02 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 19:01:03 [INFO]: Socket authenticated for user 5 
2025-08-10 19:01:03 [INFO]: User 5 connected via socket 
2025-08-10 19:01:03 [INFO]: User 5 joined conversation 1 
2025-08-10 19:01:06 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 19:01:06 [INFO]: Socket authenticated for user 6 
2025-08-10 19:01:06 [INFO]: User 6 connected via socket 
2025-08-10 19:01:06 [INFO]: User 6 joined conversation 1 
2025-08-10 19:01:09 [INFO]: User 6 disconnected, reason: transport close 
2025-08-10 19:01:10 [INFO]: Socket authenticated for user 6 
2025-08-10 19:01:10 [INFO]: User 6 connected via socket 
2025-08-10 19:01:10 [INFO]: User 6 joined conversation 1 
2025-08-10 19:01:12 [INFO]: User 5 disconnected, reason: transport close 
2025-08-10 19:01:13 [INFO]: Socket authenticated for user 5 
2025-08-10 19:01:13 [INFO]: User 5 connected via socket 
2025-08-10 19:01:13 [INFO]: User 5 joined conversation 1 
2025-08-10 19:01:19 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 19:01:20 [INFO]: Socket authenticated for user 5 
2025-08-10 19:01:20 [INFO]: User 5 connected via socket 
2025-08-10 19:01:20 [INFO]: User 5 joined conversation 1 
2025-08-10 19:01:43 [INFO]: User 5 disconnected, reason: transport close 
2025-08-10 19:01:44 [INFO]: Socket authenticated for user 5 
2025-08-10 19:01:44 [INFO]: User 5 connected via socket 
2025-08-10 19:01:44 [INFO]: User 5 joined conversation 1 
2025-08-10 19:01:51 [INFO]: User 6 disconnected, reason: transport close 
2025-08-10 19:01:51 [INFO]: Socket authenticated for user 6 
2025-08-10 19:01:51 [INFO]: User 6 connected via socket 
2025-08-10 19:01:51 [INFO]: User 6 joined conversation 1 
2025-08-10 19:02:46 [INFO]: User 5 disconnected, reason: transport close 
2025-08-10 19:02:46 [INFO]: Socket authenticated for user 5 
2025-08-10 19:02:46 [INFO]: User 5 connected via socket 
2025-08-10 19:02:46 [INFO]: User 5 joined conversation 1 
2025-08-10 19:02:50 [INFO]: User 6 disconnected, reason: transport close 
2025-08-10 19:02:50 [INFO]: Socket authenticated for user 6 
2025-08-10 19:02:50 [INFO]: User 6 connected via socket 
2025-08-10 19:02:51 [INFO]: User 6 joined conversation 1 
2025-08-10 19:03:05 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 19:03:06 [INFO]: Socket authenticated for user 5 
2025-08-10 19:03:06 [INFO]: User 5 connected via socket 
2025-08-10 19:03:06 [INFO]: User 5 joined conversation 1 
2025-08-10 19:03:20 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 19:03:21 [INFO]: Socket authenticated for user 5 
2025-08-10 19:03:21 [INFO]: User 5 connected via socket 
2025-08-10 19:03:21 [INFO]: User 5 joined conversation 1 
2025-08-10 19:03:52 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 19:03:53 [INFO]: Socket authenticated for user 6 
2025-08-10 19:03:53 [INFO]: User 6 connected via socket 
2025-08-10 19:03:53 [INFO]: User 6 joined conversation 1 
2025-08-10 19:03:55 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 19:04:02 [INFO]: Socket authenticated for user 6 
2025-08-10 19:04:02 [INFO]: User 6 connected via socket 
2025-08-10 19:04:02 [INFO]: User 6 joined conversation 1 
2025-08-10 19:04:05 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 19:04:26 [INFO]: Socket authenticated for user 6 
2025-08-10 19:04:26 [INFO]: User 6 connected via socket 
2025-08-10 19:04:26 [INFO]: User 6 joined conversation 1 
2025-08-10 19:04:27 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 19:05:02 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 19:05:13 [INFO]: Socket authenticated for user 5 
2025-08-10 19:05:13 [INFO]: User 5 connected via socket 
2025-08-10 19:05:13 [INFO]: User 5 joined conversation 1 
2025-08-10 19:05:13 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 19:05:33 [INFO]: Socket authenticated for user 5 
2025-08-10 19:05:33 [INFO]: User 5 connected via socket 
2025-08-10 19:05:33 [INFO]: User 5 joined conversation 1 
2025-08-10 19:06:09 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 19:06:19 [INFO]: Socket authenticated for user 5 
2025-08-10 19:06:19 [INFO]: User 5 connected via socket 
2025-08-10 19:06:19 [INFO]: User 5 joined conversation 1 
2025-08-10 19:06:19 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 19:06:20 [INFO]: Socket authenticated for user 5 
2025-08-10 19:06:20 [INFO]: User 5 connected via socket 
2025-08-10 19:06:20 [INFO]: User 5 joined conversation 1 
2025-08-10 21:43:13 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 21:43:13 [INFO]: Socket authenticated for user 5 
2025-08-10 21:43:13 [INFO]: User 5 connected via socket 
2025-08-10 21:43:13 [INFO]: User 5 joined conversation 1 
2025-08-10 21:43:30 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 21:44:27 [INFO]: Socket authenticated for user 6 
2025-08-10 21:44:27 [INFO]: User 6 connected via socket 
2025-08-10 21:44:27 [INFO]: User 6 joined conversation 1 
2025-08-10 21:45:22 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 21:45:33 [INFO]: Socket authenticated for user 6 
2025-08-10 21:45:33 [INFO]: User 6 connected via socket 
2025-08-10 21:45:33 [INFO]: User 6 joined conversation 1 
2025-08-10 21:46:34 [INFO]: Socket authenticated for user 5 
2025-08-10 21:46:34 [INFO]: User 5 connected via socket 
2025-08-10 21:46:34 [INFO]: User 5 joined conversation 1 
2025-08-10 21:46:40 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 21:46:40 [INFO]: Socket authenticated for user 5 
2025-08-10 21:46:40 [INFO]: User 5 connected via socket 
2025-08-10 21:46:40 [INFO]: User 5 joined conversation 1 
2025-08-10 21:52:28 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 21:52:28 [INFO]: Socket authenticated for user 6 
2025-08-10 21:52:28 [INFO]: User 6 connected via socket 
2025-08-10 21:53:15 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 21:53:15 [INFO]: Socket authenticated for user 5 
2025-08-10 21:53:15 [INFO]: User 5 connected via socket 
2025-08-10 21:54:33 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 21:54:33 [INFO]: Socket authenticated for user 6 
2025-08-10 21:54:33 [INFO]: User 6 connected via socket 
2025-08-10 21:56:14 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 21:56:14 [INFO]: Socket authenticated for user 5 
2025-08-10 21:56:14 [INFO]: User 5 connected via socket 
2025-08-10 21:56:52 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 21:56:52 [INFO]: Socket authenticated for user 6 
2025-08-10 21:56:52 [INFO]: User 6 connected via socket 
2025-08-10 21:57:26 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 21:57:26 [INFO]: Socket authenticated for user 5 
2025-08-10 21:57:26 [INFO]: User 5 connected via socket 
2025-08-10 21:58:17 [INFO]: User 5 disconnected, reason: transport close 
2025-08-10 21:58:17 [INFO]: Socket authenticated for user 5 
2025-08-10 21:58:17 [INFO]: User 5 connected via socket 
2025-08-10 21:58:17 [INFO]: User 5 joined conversation 1 
2025-08-10 21:58:24 [INFO]: User 6 disconnected, reason: transport close 
2025-08-10 21:58:24 [INFO]: Socket authenticated for user 6 
2025-08-10 21:58:24 [INFO]: User 6 connected via socket 
2025-08-10 21:58:25 [INFO]: User 6 joined conversation 1 
2025-08-10 21:58:37 [ERROR]: Error in getConversationMessages: Unauthorized access to conversation {
  "stack": "Error: Unauthorized access to conversation\n    at Object.getConversationMessages (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.service.js:96:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async markMessageAsRead (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.controller.js:119:24)"
}
2025-08-10 21:58:37 [ERROR]: Error marking message as read: Unauthorized access to conversation {
  "stack": "Error: Unauthorized access to conversation\n    at Object.getConversationMessages (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.service.js:96:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async markMessageAsRead (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\messages\\messages.controller.js:119:24)"
}
2025-08-10 21:59:32 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 21:59:32 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 21:59:32 [INFO]: Socket authenticated for user 6 
2025-08-10 21:59:32 [INFO]: User 6 connected via socket 
2025-08-10 21:59:32 [INFO]: Socket authenticated for user 5 
2025-08-10 21:59:32 [INFO]: User 5 connected via socket 
2025-08-10 22:17:38 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 22:17:38 [INFO]: Socket authenticated for user 6 
2025-08-10 22:17:38 [INFO]: User 6 connected via socket 
2025-08-10 22:18:20 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 22:18:20 [INFO]: Socket authenticated for user 6 
2025-08-10 22:18:20 [INFO]: User 6 connected via socket 
2025-08-10 22:18:37 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 22:18:37 [INFO]: Socket authenticated for user 5 
2025-08-10 22:18:37 [INFO]: User 5 connected via socket 
2025-08-10 22:19:11 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 22:19:11 [INFO]: Socket authenticated for user 5 
2025-08-10 22:19:11 [INFO]: User 5 connected via socket 
2025-08-10 22:19:53 [INFO]: User 5 disconnected, reason: transport close 
2025-08-10 22:19:53 [INFO]: Socket authenticated for user 5 
2025-08-10 22:19:53 [INFO]: User 5 connected via socket 
2025-08-10 22:19:53 [INFO]: User 5 joined conversation 1 
2025-08-10 22:19:59 [INFO]: User 6 disconnected, reason: transport close 
2025-08-10 22:20:00 [INFO]: Socket authenticated for user 6 
2025-08-10 22:20:00 [INFO]: User 6 connected via socket 
2025-08-10 22:20:00 [INFO]: User 6 joined conversation 1 
2025-08-10 22:20:37 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 22:20:37 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 22:20:37 [INFO]: Socket authenticated for user 6 
2025-08-10 22:20:37 [INFO]: User 6 connected via socket 
2025-08-10 22:20:37 [INFO]: Socket authenticated for user 5 
2025-08-10 22:20:37 [INFO]: User 5 connected via socket 
2025-08-10 22:22:20 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 22:22:20 [INFO]: Socket authenticated for user 6 
2025-08-10 22:22:20 [INFO]: User 6 connected via socket 
2025-08-10 22:22:41 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 22:22:41 [INFO]: Socket authenticated for user 5 
2025-08-10 22:22:41 [INFO]: User 5 connected via socket 
2025-08-10 22:23:15 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 22:23:15 [INFO]: Socket authenticated for user 6 
2025-08-10 22:23:15 [INFO]: User 6 connected via socket 
2025-08-10 22:23:46 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 22:23:46 [INFO]: Socket authenticated for user 5 
2025-08-10 22:23:46 [INFO]: User 5 connected via socket 
2025-08-10 22:24:10 [INFO]: Database configuration loaded from file 
2025-08-10 22:24:10 [INFO]: Connected to SQL Server 
2025-08-10 22:24:10 [INFO]: Server running on port 5000 
2025-08-10 22:24:10 [INFO]: Environment: development 
2025-08-10 22:24:10 [INFO]: Socket authenticated for user 5 
2025-08-10 22:24:10 [INFO]: User 5 connected via socket 
2025-08-10 22:24:11 [INFO]: Socket authenticated for user 6 
2025-08-10 22:24:11 [INFO]: User 6 connected via socket 
2025-08-10 22:24:49 [INFO]: Database configuration loaded from file 
2025-08-10 22:24:49 [INFO]: Connected to SQL Server 
2025-08-10 22:24:49 [INFO]: Server running on port 5000 
2025-08-10 22:24:49 [INFO]: Environment: development 
2025-08-10 22:24:50 [INFO]: Socket authenticated for user 6 
2025-08-10 22:24:50 [INFO]: User 6 connected via socket 
2025-08-10 22:24:50 [INFO]: Socket authenticated for user 5 
2025-08-10 22:24:50 [INFO]: User 5 connected via socket 
2025-08-10 22:25:23 [INFO]: Database configuration loaded from file 
2025-08-10 22:25:23 [INFO]: Connected to SQL Server 
2025-08-10 22:25:23 [INFO]: Server running on port 5000 
2025-08-10 22:25:23 [INFO]: Environment: development 
2025-08-10 22:25:24 [INFO]: Socket authenticated for user 6 
2025-08-10 22:25:24 [INFO]: User 6 connected via socket 
2025-08-10 22:25:24 [INFO]: Socket authenticated for user 5 
2025-08-10 22:25:24 [INFO]: User 5 connected via socket 
2025-08-10 22:25:56 [INFO]: Database configuration loaded from file 
2025-08-10 22:25:56 [INFO]: Connected to SQL Server 
2025-08-10 22:25:56 [INFO]: Server running on port 5000 
2025-08-10 22:25:56 [INFO]: Environment: development 
2025-08-10 22:25:56 [INFO]: Socket authenticated for user 5 
2025-08-10 22:25:56 [INFO]: User 5 connected via socket 
2025-08-10 22:25:57 [INFO]: Socket authenticated for user 6 
2025-08-10 22:25:57 [INFO]: User 6 connected via socket 
2025-08-10 22:26:25 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 22:26:25 [INFO]: Socket authenticated for user 6 
2025-08-10 22:26:25 [INFO]: User 6 connected via socket 
2025-08-10 22:26:45 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 22:26:45 [INFO]: Socket authenticated for user 6 
2025-08-10 22:26:45 [INFO]: User 6 connected via socket 
2025-08-10 22:27:06 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 22:27:06 [INFO]: Socket authenticated for user 6 
2025-08-10 22:27:06 [INFO]: User 6 connected via socket 
2025-08-10 22:27:23 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 22:27:23 [INFO]: Socket authenticated for user 6 
2025-08-10 22:27:23 [INFO]: User 6 connected via socket 
2025-08-10 22:27:45 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 22:27:45 [INFO]: Socket authenticated for user 5 
2025-08-10 22:27:45 [INFO]: User 5 connected via socket 
2025-08-10 22:28:08 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 22:28:08 [INFO]: Socket authenticated for user 5 
2025-08-10 22:28:08 [INFO]: User 5 connected via socket 
2025-08-10 22:28:33 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 22:28:33 [INFO]: Socket authenticated for user 5 
2025-08-10 22:28:33 [INFO]: User 5 connected via socket 
2025-08-10 22:28:56 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 22:28:56 [INFO]: Socket authenticated for user 5 
2025-08-10 22:28:56 [INFO]: User 5 connected via socket 
2025-08-10 22:29:18 [INFO]: User 5 disconnected, reason: transport close 
2025-08-10 22:29:19 [INFO]: Socket authenticated for user 5 
2025-08-10 22:29:19 [INFO]: User 5 connected via socket 
2025-08-10 22:29:19 [INFO]: User 5 joined conversation 1 
2025-08-10 22:29:21 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 22:29:21 [INFO]: Socket authenticated for user 5 
2025-08-10 22:29:21 [INFO]: User 5 connected via socket 
2025-08-10 22:29:21 [INFO]: User 5 joined conversation 1 
2025-08-10 22:29:31 [INFO]: User 6 disconnected, reason: transport close 
2025-08-10 22:29:31 [INFO]: Socket authenticated for user 6 
2025-08-10 22:29:31 [INFO]: User 6 connected via socket 
2025-08-10 22:29:31 [INFO]: User 6 joined conversation 1 
2025-08-10 22:30:57 [INFO]: User 6 disconnected, reason: transport close 
2025-08-10 22:30:57 [INFO]: Socket authenticated for user 6 
2025-08-10 22:30:57 [INFO]: User 6 connected via socket 
2025-08-10 22:30:57 [INFO]: User 6 joined conversation 1 
2025-08-10 22:34:33 [INFO]: Database configuration loaded from file 
2025-08-10 22:34:33 [INFO]: Connected to SQL Server 
2025-08-10 22:34:33 [INFO]: Server running on port 5000 
2025-08-10 22:34:33 [INFO]: Environment: development 
2025-08-10 22:34:33 [INFO]: Socket authenticated for user 5 
2025-08-10 22:34:33 [INFO]: User 5 connected via socket 
2025-08-10 22:34:34 [INFO]: Socket authenticated for user 6 
2025-08-10 22:34:34 [INFO]: User 6 connected via socket 
2025-08-10 22:35:31 [INFO]: Database configuration loaded from file 
2025-08-10 22:35:32 [INFO]: Connected to SQL Server 
2025-08-10 22:35:32 [INFO]: Server running on port 5000 
2025-08-10 22:35:32 [INFO]: Environment: development 
2025-08-10 22:35:32 [INFO]: Socket authenticated for user 5 
2025-08-10 22:35:32 [INFO]: User 5 connected via socket 
2025-08-10 22:35:32 [INFO]: Socket authenticated for user 6 
2025-08-10 22:35:32 [INFO]: User 6 connected via socket 
2025-08-10 22:36:57 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 22:36:57 [INFO]: Socket authenticated for user 6 
2025-08-10 22:36:57 [INFO]: User 6 connected via socket 
2025-08-10 22:39:16 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 22:39:16 [INFO]: Socket authenticated for user 5 
2025-08-10 22:39:16 [INFO]: User 5 connected via socket 
2025-08-10 22:40:26 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 22:40:26 [INFO]: Socket authenticated for user 6 
2025-08-10 22:40:26 [INFO]: User 6 connected via socket 
2025-08-10 22:40:41 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 22:40:41 [INFO]: Socket authenticated for user 5 
2025-08-10 22:40:41 [INFO]: User 5 connected via socket 
2025-08-10 22:41:15 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 22:41:15 [INFO]: Socket authenticated for user 6 
2025-08-10 22:41:15 [INFO]: User 6 connected via socket 
2025-08-10 22:41:45 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 22:41:45 [INFO]: Socket authenticated for user 5 
2025-08-10 22:41:45 [INFO]: User 5 connected via socket 
2025-08-10 22:42:07 [INFO]: User 5 disconnected, reason: transport close 
2025-08-10 22:42:08 [INFO]: Socket authenticated for user 5 
2025-08-10 22:42:08 [INFO]: User 5 connected via socket 
2025-08-10 22:42:08 [INFO]: User 5 joined conversation 1 
2025-08-10 22:42:17 [INFO]: User 6 disconnected, reason: transport close 
2025-08-10 22:42:18 [INFO]: Socket authenticated for user 6 
2025-08-10 22:42:18 [INFO]: User 6 connected via socket 
2025-08-10 22:42:18 [INFO]: User 6 joined conversation 1 
2025-08-10 22:42:56 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 22:43:03 [INFO]: Socket authenticated for user 5 
2025-08-10 22:43:03 [INFO]: User 5 connected via socket 
2025-08-10 22:43:03 [INFO]: User 5 joined conversation 1 
2025-08-10 22:43:30 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 22:43:31 [INFO]: Socket authenticated for user 6 
2025-08-10 22:43:31 [INFO]: User 6 connected via socket 
2025-08-10 22:43:31 [INFO]: User 6 joined conversation 1 
2025-08-10 22:43:39 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 22:43:39 [INFO]: Socket authenticated for user 6 
2025-08-10 22:43:39 [INFO]: User 6 connected via socket 
2025-08-10 22:43:39 [INFO]: User 6 joined conversation 1 
2025-08-10 22:43:44 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 22:43:50 [INFO]: Login attempt for username: client 
2025-08-10 22:43:50 [WARN]: Security alert: Common password attempt for user: client 
2025-08-10 22:43:50 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-08-10 22:43:59 [INFO]: Socket authenticated for user 6 
2025-08-10 22:43:59 [INFO]: User 6 connected via socket 
2025-08-10 22:43:59 [INFO]: User 6 joined conversation 1 
2025-08-10 22:44:01 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 22:44:11 [INFO]: Socket authenticated for user 6 
2025-08-10 22:44:11 [INFO]: User 6 connected via socket 
2025-08-10 22:44:11 [INFO]: User 6 joined conversation 1 
2025-08-10 22:44:26 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 22:44:40 [INFO]: Socket authenticated for user 5 
2025-08-10 22:44:40 [INFO]: User 5 connected via socket 
2025-08-10 22:44:40 [INFO]: User 5 joined conversation 1 
2025-08-10 22:50:08 [INFO]: User 5 disconnected, reason: client namespace disconnect 
2025-08-10 22:51:37 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-10 23:02:46 [INFO]: Database configuration loaded from file 
2025-08-10 23:02:46 [INFO]: Connected to SQL Server 
2025-08-10 23:02:46 [INFO]: Server running on port 5000 
2025-08-10 23:02:46 [INFO]: Environment: development 
2025-08-10 23:03:14 [INFO]: Database configuration loaded from file 
2025-08-10 23:03:14 [INFO]: Connected to SQL Server 
2025-08-10 23:03:14 [INFO]: Server running on port 5000 
2025-08-10 23:03:14 [INFO]: Environment: development 
2025-08-10 23:03:32 [INFO]: Database configuration loaded from file 
2025-08-10 23:03:32 [INFO]: Connected to SQL Server 
2025-08-10 23:03:32 [INFO]: Server running on port 5000 
2025-08-10 23:03:32 [INFO]: Environment: development 
2025-08-10 23:03:53 [INFO]: Database configuration loaded from file 
2025-08-10 23:03:53 [INFO]: Connected to SQL Server 
2025-08-10 23:03:53 [INFO]: Server running on port 5000 
2025-08-10 23:03:53 [INFO]: Environment: development 
2025-08-10 23:04:04 [INFO]: Database configuration loaded from file 
2025-08-10 23:04:04 [INFO]: Connected to SQL Server 
2025-08-10 23:04:04 [INFO]: Server running on port 5000 
2025-08-10 23:04:04 [INFO]: Environment: development 
2025-08-10 23:04:31 [INFO]: Database configuration loaded from file 
2025-08-10 23:49:26 [INFO]: Database configuration loaded from file 
2025-08-10 23:49:26 [INFO]: Connected to SQL Server 
2025-08-10 23:49:26 [INFO]: Server running on port 5000 
2025-08-10 23:49:26 [INFO]: Environment: development 
2025-08-10 23:49:38 [INFO]: Database configuration loaded from file 
2025-08-10 23:49:38 [INFO]: Connected to SQL Server 
2025-08-10 23:49:38 [INFO]: Server running on port 5000 
2025-08-10 23:49:38 [INFO]: Environment: development 
2025-08-11 12:15:27 [INFO]: Database configuration loaded from file 
2025-08-11 12:15:27 [INFO]: Connected to SQL Server 
2025-08-11 12:15:27 [INFO]: Server running on port 5000 
2025-08-11 12:15:27 [INFO]: Environment: development 
2025-08-11 12:15:57 [INFO]: Login attempt for username: expert 
2025-08-11 12:15:57 [WARN]: Security alert: Common password attempt for user: expert 
2025-08-11 12:15:57 [INFO]: Login successful for user: expert (ID: 5, Role: Expert) 
2025-08-11 12:16:20 [INFO]: Login attempt for username: client 
2025-08-11 12:16:20 [WARN]: Security alert: Common password attempt for user: client 
2025-08-11 12:16:20 [INFO]: Login successful for user: client (ID: 6, Role: Client) 
2025-08-11 12:16:24 [INFO]: Socket authenticated for user 6 
2025-08-11 12:16:24 [INFO]: User 6 connected via socket 
2025-08-11 12:16:24 [INFO]: User 6 joined conversation 1 
2025-08-11 12:16:25 [INFO]: User 6 disconnected, reason: client namespace disconnect 
2025-08-11 12:19:32 [INFO]: Database configuration loaded from file 
2025-08-11 12:19:32 [INFO]: Connected to SQL Server 
2025-08-11 12:19:32 [INFO]: Server running on port 5000 
2025-08-11 12:19:32 [INFO]: Environment: development 
2025-08-11 12:20:11 [INFO]: Database configuration loaded from file 
2025-08-11 12:20:11 [INFO]: Connected to SQL Server 
2025-08-11 12:20:11 [INFO]: Server running on port 5000 
2025-08-11 12:20:11 [INFO]: Environment: development 
2025-08-11 12:20:39 [INFO]: Database configuration loaded from file 
2025-08-11 12:20:40 [INFO]: Connected to SQL Server 
2025-08-11 12:20:40 [INFO]: Server running on port 5000 
2025-08-11 12:20:40 [INFO]: Environment: development 
2025-08-11 12:20:52 [INFO]: Database configuration loaded from file 
2025-08-11 12:20:52 [INFO]: Connected to SQL Server 
2025-08-11 12:20:52 [INFO]: Server running on port 5000 
2025-08-11 12:20:52 [INFO]: Environment: development 
2025-08-11 12:21:24 [INFO]: Database configuration loaded from file 
2025-08-11 12:21:24 [INFO]: Connected to SQL Server 
2025-08-11 12:21:24 [INFO]: Server running on port 5000 
2025-08-11 12:21:24 [INFO]: Environment: development 
2025-08-11 12:21:36 [INFO]: Database configuration loaded from file 
2025-08-11 12:21:36 [INFO]: Connected to SQL Server 
2025-08-11 12:21:36 [INFO]: Server running on port 5000 
2025-08-11 12:21:36 [INFO]: Environment: development 
2025-08-11 12:21:48 [INFO]: Database configuration loaded from file 
2025-08-11 12:26:36 [INFO]: Database configuration loaded from file 
