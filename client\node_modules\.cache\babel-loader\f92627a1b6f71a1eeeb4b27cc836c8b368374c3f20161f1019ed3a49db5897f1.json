{"ast": null, "code": "var _jsxFileName = \"C:\\\\burky root\\\\burky_root_web\\\\client\\\\src\\\\index.jsx\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport { BrowserRouter } from 'react-router-dom';\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport { ReactQueryDevtools } from '@tanstack/react-query-devtools';\nimport { AuthProvider } from './contexts/AuthContext';\nimport { Toaster } from 'react-hot-toast';\nimport App from './App';\nimport './index.css';\n\n// Create a client for React Query\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      refetchOnWindowFocus: false,\n      staleTime: 5 * 60 * 1000,\n      // 5 minutes\n      retry: 1\n    }\n  }\n});\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(BrowserRouter, {\n    children: /*#__PURE__*/_jsxDEV(QueryClientProvider, {\n      client: queryClient,\n      children: [/*#__PURE__*/_jsxDEV(AuthProvider, {\n        children: [/*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Toaster, {\n          position: \"top-right\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(ReactQueryDevtools, {\n        initialIsOpen: false\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 52\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 24,\n  columnNumber: 3\n}, this));", "map": {"version": 3, "names": ["React", "ReactDOM", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "QueryClient", "QueryClientProvider", "ReactQueryDevtools", "<PERSON>th<PERSON><PERSON><PERSON>", "Toaster", "App", "jsxDEV", "_jsxDEV", "queryClient", "defaultOptions", "queries", "refetchOnWindowFocus", "staleTime", "retry", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children", "client", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "process", "env", "NODE_ENV", "initialIsOpen"], "sources": ["C:/burky root/burky_root_web/client/src/index.jsx"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport { BrowserRouter } from 'react-router-dom';\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport { ReactQueryDevtools } from '@tanstack/react-query-devtools';\nimport { AuthProvider } from './contexts/AuthContext';\nimport { Toaster } from 'react-hot-toast';\nimport App from './App';\nimport './index.css';\n\n// Create a client for React Query\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      refetchOnWindowFocus: false,\n      staleTime: 5 * 60 * 1000, // 5 minutes\n      retry: 1,\n    },\n  },\n});\n\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(\n  <React.StrictMode>\n    <BrowserRouter>\n      <QueryClientProvider client={queryClient}>\n        <AuthProvider>\n          <App />\n          <Toaster position=\"top-right\" />\n        </AuthProvider>\n        {process.env.NODE_ENV === 'development' && <ReactQueryDevtools initialIsOpen={false} />}\n      </QueryClientProvider>\n    </BrowserRouter>\n  </React.StrictMode>\n);\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,uBAAuB;AACxE,SAASC,kBAAkB,QAAQ,gCAAgC;AACnE,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,OAAO,QAAQ,iBAAiB;AACzC,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAO,aAAa;;AAEpB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,GAAG,IAAIR,WAAW,CAAC;EAClCS,cAAc,EAAE;IACdC,OAAO,EAAE;MACPC,oBAAoB,EAAE,KAAK;MAC3BC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;MAAE;MAC1BC,KAAK,EAAE;IACT;EACF;AACF,CAAC,CAAC;AAEF,MAAMC,IAAI,GAAGhB,QAAQ,CAACiB,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC;AACjEH,IAAI,CAACI,MAAM,cACTX,OAAA,CAACV,KAAK,CAACsB,UAAU;EAAAC,QAAA,eACfb,OAAA,CAACR,aAAa;IAAAqB,QAAA,eACZb,OAAA,CAACN,mBAAmB;MAACoB,MAAM,EAAEb,WAAY;MAAAY,QAAA,gBACvCb,OAAA,CAACJ,YAAY;QAAAiB,QAAA,gBACXb,OAAA,CAACF,GAAG;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACPlB,OAAA,CAACH,OAAO;UAACsB,QAAQ,EAAC;QAAW;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,EACdE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBAAItB,OAAA,CAACL,kBAAkB;QAAC4B,aAAa,EAAE;MAAM;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACA,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}