{"ast": null, "code": "var _jsxFileName = \"C:\\\\burky root\\\\burky_root_web\\\\client\\\\src\\\\contexts\\\\AuthContext.jsx\",\n  _s = $RefreshSig$();\nimport React, { createContext, useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { jwtDecode } from 'jwt-decode';\nimport api from '../services/api';\nimport toast from 'react-hot-toast';\n\n// Create context\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const AuthContext = /*#__PURE__*/createContext();\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [user, setUser] = useState(null);\n  const [permissions, setPermissions] = useState([]);\n  const [isInitialized, setIsInitialized] = useState(false);\n  const navigate = useNavigate();\n\n  // Initialize auth state\n  useEffect(() => {\n    const initAuth = async () => {\n      try {\n        const accessToken = localStorage.getItem('accessToken');\n        if (accessToken) {\n          // Validate token\n          try {\n            const decoded = jwtDecode(accessToken);\n            console.log('JWT içeriği:', decoded);\n\n            // Check if token is expired\n            if (decoded.exp * 1000 < Date.now()) {\n              throw new Error('Token expired');\n            }\n\n            // İlgili rolleri belirle (decoded'dan)\n            const isAdmin = decoded.roleName === 'Admin';\n            const isExpert = decoded.roleName === 'Expert' || decoded.userType === 'expert';\n            const isClient = decoded.roleName === 'Client' || decoded.userType === 'client';\n            console.log('Decoded JWT role bilgisi:', {\n              isAdmin,\n              isExpert,\n              isClient,\n              roleName: decoded.roleName,\n              userType: decoded.userType\n            });\n\n            // Set user from token data\n            setUser({\n              id: decoded.id,\n              username: decoded.username,\n              email: decoded.email,\n              firstName: decoded.firstName || '',\n              lastName: decoded.lastName || '',\n              role: {\n                id: decoded.roleId,\n                name: decoded.roleName\n              },\n              userType: decoded.userType,\n              isExpert: isExpert,\n              isClient: isClient\n            });\n\n            // Get user permissions\n            await fetchUserPermissions();\n          } catch (error) {\n            console.error('Token decode hatası:', error);\n            // Clear invalid token\n            localStorage.removeItem('accessToken');\n            localStorage.removeItem('refreshToken');\n            setUser(null);\n            setPermissions([]);\n          }\n        }\n      } catch (error) {\n        console.error('Auth initialization error:', error);\n      } finally {\n        setIsInitialized(true);\n      }\n    };\n    initAuth();\n  }, []);\n\n  // Fetch user permissions\n  const fetchUserPermissions = async () => {\n    try {\n      var _user$role, _response$data;\n      const response = await api.get('/users/permissions');\n\n      // LOGLAMA: Veritabanından gelen izinleri daha net ve anlaşılır şekilde göster\n      console.log('----- KULLANICI İZİNLERİ -----');\n      console.log(`Kullanıcı: ${user === null || user === void 0 ? void 0 : user.username} (Rol: ${user === null || user === void 0 ? void 0 : (_user$role = user.role) === null || _user$role === void 0 ? void 0 : _user$role.name})`);\n      console.log(`İzinler: Toplam ${((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.length) || 0} adet izin bulundu`);\n      if (response.data && response.data.length > 0) {\n        // İzinleri tabloda göster\n        const formattedPermissions = response.data.map(p => ({\n          key: p.permissionKey,\n          id: p.permissionId\n        }));\n        console.table(formattedPermissions);\n\n        // İzin formatı kontrolü - doğru formatta mı?\n        const hasSpecialFormatPermissions = response.data.some(p => p.permissionKey && p.permissionKey.includes('_DASHBOARD'));\n        if (!hasSpecialFormatPermissions) {\n          console.warn('⚠️ UYARI: İzinler sadece READ, CREATE formatında, sayfa spesifik izinler (READ_DASHBOARD) yok!');\n          console.warn('Bu durum izin kontrolünün doğru çalışmasını engeller. Rolleri doğru şekilde atayın.');\n        }\n      } else {\n        console.warn('⚠️ UYARI: Kullanıcı için hiç izin bulunamadı!');\n      }\n      console.log('------------------------------');\n      setPermissions(response.data || []);\n      return response.data || [];\n    } catch (error) {\n      console.error('❌ İzinler alınırken hata:', error);\n      setPermissions([]);\n      return [];\n    }\n  };\n\n  // Login handler\n  const login = async (username, password) => {\n    try {\n      const response = await api.post('/auth/login', {\n        username,\n        password\n      });\n      const {\n        user,\n        accessToken,\n        refreshToken\n      } = response.data;\n\n      // Store tokens\n      localStorage.setItem('accessToken', accessToken);\n      localStorage.setItem('refreshToken', refreshToken);\n\n      // JWT'den role bilgilerini al\n      const decoded = jwtDecode(accessToken);\n\n      // İlgili rolleri belirle\n      const isAdmin = user.role.name === 'Admin' || decoded.roleName === 'Admin';\n      const isExpert = user.role.name === 'Expert' || decoded.roleName === 'Expert' || user.userType === 'expert' || decoded.userType === 'expert';\n      const isClient = user.role.name === 'Client' || decoded.roleName === 'Client' || user.userType === 'client' || decoded.userType === 'client';\n      console.log('Login sonrası role bilgisi:', {\n        isAdmin,\n        isExpert,\n        isClient,\n        userRole: user.role,\n        decodedRole: decoded.roleName\n      });\n\n      // Set user with role flags\n      setUser({\n        ...user,\n        userType: decoded.userType,\n        isExpert: isExpert,\n        isClient: isClient\n      });\n\n      // Fetch permissions\n      await fetchUserPermissions();\n\n      // Redirect to dashboard\n      navigate('/dashboard');\n      toast.success('Login successful');\n      return true;\n    } catch (error) {\n      return false;\n    }\n  };\n\n  // Register handler\n  const register = async userData => {\n    try {\n      const response = await api.post('/auth/register', userData);\n      const {\n        user,\n        accessToken\n      } = response.data;\n\n      // Store token\n      localStorage.setItem('accessToken', accessToken);\n\n      // Set user\n      setUser(user);\n\n      // Fetch permissions\n      await fetchUserPermissions();\n\n      // Redirect to dashboard\n      navigate('/dashboard');\n      toast.success('Registration successful');\n      return true;\n    } catch (error) {\n      return false;\n    }\n  };\n\n  // Logout handler\n  const logout = () => {\n    // Clear tokens\n    localStorage.removeItem('accessToken');\n    localStorage.removeItem('refreshToken');\n\n    // Clear state\n    setUser(null);\n    setPermissions([]);\n\n    // Redirect to login\n    navigate('/login');\n    toast.success('Logged out successfully');\n  };\n\n  // Check if user has permission\n  const hasPermission = (permissionKeyOrPath, permissionKeyParam) => {\n    var _user$role2, _user$role3;\n    if (!user) return false;\n\n    // Admin role has all permissions\n    if (((_user$role2 = user.role) === null || _user$role2 === void 0 ? void 0 : _user$role2.name) === 'Admin') {\n      return true;\n    }\n\n    // Rol bazlı sayfa kontrolü - özel roller için\n    if ((_user$role3 = user.role) !== null && _user$role3 !== void 0 && _user$role3.name && !['Admin', 'Expert', 'Client'].includes(user.role.name)) {\n      // İzin istenen temel sayfaları belirle - sürekli log üretmemek için\n      const isMenuCheck = ['READ_DASHBOARD', 'READ_ADMIN_USERS', 'READ_ADMIN_ROLES'].includes(permissionKeyOrPath);\n\n      // Özel izin kontrolü - sadece özel durumlarda loğla\n      if (!isMenuCheck && permissionKeyOrPath.startsWith('READ_')) {\n        console.log(`Özel rol (${user.role.name}) için izin kontrolü: ${permissionKeyOrPath}`);\n      }\n\n      // İzin listesinde tam olarak bu izin var mı kontrol et\n      if (permissions && permissions.length > 0) {\n        // Tam izin kontrolü - permissionKey === permissionKeyOrPath\n        const hasExactPermission = permissions.some(p => {\n          return p.permissionKey === permissionKeyOrPath || p.permissionFullKey === permissionKeyOrPath;\n        });\n\n        // İzin eşleşirse true döndür\n        if (hasExactPermission) {\n          if (!isMenuCheck && permissionKeyOrPath.startsWith('READ_')) {\n            console.log(`✅ İzin bulundu: ${permissionKeyOrPath}`);\n          }\n          return true;\n        }\n\n        // FORM 1 - Eğer veritabanındaki izin READ ve istenen izin READ_DASHBOARD gibi bir izinse,\n        // burada özel bir kontrol yapmalıyız\n\n        // İzin anahtarının ilk kısmını al (READ_DASHBOARD -> READ)\n        const basePermissionType = permissionKeyOrPath.split('_')[0]; // READ, CREATE, UPDATE, DELETE\n\n        // Eğer izin listesinde basePermission varsa ve izin yapısı eski tipte ise (tek kelime, \"_\" yok)\n        // İzinde sayfa spesifik kısmı yok sadece READ varsa, ve izin listesi Roles tablosunda checkboxlar ile atanmışsa\n        const hasBasePermissionOnly = permissions.some(p => {\n          const isBasePermissionOnly = p.permissionKey === basePermissionType && !p.permissionKey.includes('_');\n          if (isBasePermissionOnly && !isMenuCheck && permissionKeyOrPath.startsWith('READ_')) {\n            console.log(`🔍 İzin sisteminde sadece temel '${basePermissionType}' izni var, sayfa özel izni yok`);\n            console.log(`📋 Bu izinlerin doğru çalışması için formata çevrilmesi gerekiyor (${permissionKeyOrPath})`);\n          }\n          return isBasePermissionOnly;\n        });\n\n        // Şu an için eski tip izinleri kabul etmiyoruz - rol sayfasında izinlerin doğru şekilde atanması gerekiyor\n        // return hasBasePermissionOnly;\n      }\n\n      // İzin bulunamadıysa false döndür\n      if (!isMenuCheck && permissionKeyOrPath.startsWith('READ_')) {\n        console.log(`❌ İzin reddedildi: ${permissionKeyOrPath}`);\n      }\n      return false;\n    }\n\n    // Rol bazlı izin kontrolü - standart roller (Expert, Client)\n\n    // 1. İzin yoksa varsayılan davranış\n    if (!permissions || permissions.length === 0) {\n      // Uzman ve danışan rolleri için varsayılan izinler\n      if (user.isExpert && permissionKeyOrPath.startsWith('READ_EXPERT_')) {\n        return true;\n      }\n      if (user.isClient && permissionKeyOrPath.startsWith('READ_CLIENT_')) {\n        return true;\n      }\n      return false;\n    }\n\n    // 2. İlk parametre role-specific dashboard izniyse ve user'ın ilgili rolü varsa izin ver\n    if (permissionKeyOrPath === 'READ_EXPERT_DASHBOARD' && user.isExpert) {\n      return true;\n    }\n    if (permissionKeyOrPath === 'READ_CLIENT_DASHBOARD' && user.isClient) {\n      return true;\n    }\n\n    // 3. Eğer rota izni bir rol bölümü için ise ve kullanıcı o role sahipse geçişine izin ver\n    if (permissionKeyOrPath.startsWith('READ_EXPERT_') && user.isExpert) {\n      return true;\n    }\n    if (permissionKeyOrPath.startsWith('READ_CLIENT_') && user.isClient) {\n      return true;\n    }\n\n    // 4. Eski format kontrolü (iki parametre)\n    if (permissionKeyParam !== undefined) {\n      const pagePath = permissionKeyOrPath;\n      const permKey = permissionKeyParam;\n      const hasOldStylePermission = permissions.some(p => p.pagePath === pagePath && p.permissionKey === permKey);\n\n      // Sayfa yönlendirmelerinde logla\n      if (pagePath.startsWith('/') && permKey === 'READ') {\n        console.log(`Sayfa izin kontrolü: ${pagePath} - ${permKey} = ${hasOldStylePermission ? '✅' : '❌'}`);\n      }\n      return hasOldStylePermission;\n    }\n\n    // 5. Tam izin anahtarı kontrolü\n    const hasExactPermission = permissions.some(p => p.permissionKey === permissionKeyOrPath);\n    return hasExactPermission;\n  };\n\n  // Refresh permissions after role permission changes\n  const refreshPermissions = async () => {\n    const newPermissions = await fetchUserPermissions();\n    return newPermissions;\n  };\n\n  // Kullanıcı bilgilerini güncelleme\n  const updateUser = userData => {\n    setUser(prevUser => ({\n      ...prevUser,\n      ...userData\n    }));\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: {\n      user,\n      isInitialized,\n      permissions,\n      login,\n      register,\n      logout,\n      hasPermission,\n      refreshPermissions,\n      fetchUserPermissions,\n      updateUser\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 346,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthProvider, \"ujLO0lH+2f5KzrNTNpRqOttERKE=\", false, function () {\n  return [useNavigate];\n});\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useState", "useEffect", "useNavigate", "jwtDecode", "api", "toast", "jsxDEV", "_jsxDEV", "AuthContext", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "user", "setUser", "permissions", "setPermissions", "isInitialized", "setIsInitialized", "navigate", "initAuth", "accessToken", "localStorage", "getItem", "decoded", "console", "log", "exp", "Date", "now", "Error", "isAdmin", "<PERSON><PERSON><PERSON>", "isExpert", "userType", "isClient", "id", "username", "email", "firstName", "lastName", "role", "roleId", "name", "fetchUserPermissions", "error", "removeItem", "_user$role", "_response$data", "response", "get", "data", "length", "formattedPermissions", "map", "p", "key", "<PERSON><PERSON><PERSON>", "permissionId", "table", "hasSpecialFormatPermissions", "some", "includes", "warn", "login", "password", "post", "refreshToken", "setItem", "userRole", "decodedRole", "success", "register", "userData", "logout", "hasPermission", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "permissionKeyParam", "_user$role2", "_user$role3", "isMenuCheck", "startsWith", "hasExactPermission", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "basePermissionType", "split", "hasBasePermissionOnly", "isBasePermissionOnly", "undefined", "pagePath", "permKey", "hasOldStylePermission", "refreshPermissions", "newPermissions", "updateUser", "prevUser", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/burky root/burky_root_web/client/src/contexts/AuthContext.jsx"], "sourcesContent": ["import React, { createContext, useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { jwtDecode } from 'jwt-decode';\nimport api from '../services/api';\nimport toast from 'react-hot-toast';\n\n// Create context\nexport const AuthContext = createContext();\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [permissions, setPermissions] = useState([]);\n  const [isInitialized, setIsInitialized] = useState(false);\n  const navigate = useNavigate();\n\n  // Initialize auth state\n  useEffect(() => {\n    const initAuth = async () => {\n      try {\n        const accessToken = localStorage.getItem('accessToken');\n        \n        if (accessToken) {\n          // Validate token\n          try {\n            const decoded = jwtDecode(accessToken);\n            console.log('JWT içeriği:', decoded);\n            \n            // Check if token is expired\n            if (decoded.exp * 1000 < Date.now()) {\n              throw new Error('Token expired');\n            }\n            \n            // İlgili rolleri belirle (decoded'dan)\n            const isAdmin = decoded.roleName === 'Admin';\n            const isExpert = decoded.roleName === 'Expert' || decoded.userType === 'expert';\n            const isClient = decoded.roleName === 'Client' || decoded.userType === 'client';\n            \n            console.log('Decoded JWT role bilgisi:', { isAdmin, isExpert, isClient, roleName: decoded.roleName, userType: decoded.userType });\n            \n            // Set user from token data\n            setUser({\n              id: decoded.id,\n              username: decoded.username,\n              email: decoded.email,\n              firstName: decoded.firstName || '',\n              lastName: decoded.lastName || '',\n              role: {\n                id: decoded.roleId,\n                name: decoded.roleName\n              },\n              userType: decoded.userType,\n              isExpert: isExpert,\n              isClient: isClient\n            });\n            \n            // Get user permissions\n            await fetchUserPermissions();\n          } catch (error) {\n            console.error('Token decode hatası:', error);\n            // Clear invalid token\n            localStorage.removeItem('accessToken');\n            localStorage.removeItem('refreshToken');\n            setUser(null);\n            setPermissions([]);\n          }\n        }\n      } catch (error) {\n        console.error('Auth initialization error:', error);\n      } finally {\n        setIsInitialized(true);\n      }\n    };\n    \n    initAuth();\n  }, []);\n\n  // Fetch user permissions\n  const fetchUserPermissions = async () => {\n    try {\n      const response = await api.get('/users/permissions');\n      \n      // LOGLAMA: Veritabanından gelen izinleri daha net ve anlaşılır şekilde göster\n      console.log('----- KULLANICI İZİNLERİ -----');\n      console.log(`Kullanıcı: ${user?.username} (Rol: ${user?.role?.name})`);\n      console.log(`İzinler: Toplam ${response.data?.length || 0} adet izin bulundu`);\n      \n      if (response.data && response.data.length > 0) {\n        // İzinleri tabloda göster\n        const formattedPermissions = response.data.map(p => ({\n          key: p.permissionKey,\n          id: p.permissionId\n        }));\n        console.table(formattedPermissions);\n        \n        // İzin formatı kontrolü - doğru formatta mı?\n        const hasSpecialFormatPermissions = response.data.some(p => \n          p.permissionKey && p.permissionKey.includes('_DASHBOARD')\n        );\n        \n        if (!hasSpecialFormatPermissions) {\n          console.warn('⚠️ UYARI: İzinler sadece READ, CREATE formatında, sayfa spesifik izinler (READ_DASHBOARD) yok!');\n          console.warn('Bu durum izin kontrolünün doğru çalışmasını engeller. Rolleri doğru şekilde atayın.');\n        }\n      } else {\n        console.warn('⚠️ UYARI: Kullanıcı için hiç izin bulunamadı!');\n      }\n      console.log('------------------------------');\n      \n      setPermissions(response.data || []);\n      return response.data || [];\n    } catch (error) {\n      console.error('❌ İzinler alınırken hata:', error);\n      setPermissions([]);\n      return [];\n    }\n  };\n\n  // Login handler\n  const login = async (username, password) => {\n    try {\n      const response = await api.post('/auth/login', {\n        username,\n        password\n      });\n      \n      const { user, accessToken, refreshToken } = response.data;\n      \n      // Store tokens\n      localStorage.setItem('accessToken', accessToken);\n      localStorage.setItem('refreshToken', refreshToken);\n      \n      // JWT'den role bilgilerini al\n      const decoded = jwtDecode(accessToken);\n      \n      // İlgili rolleri belirle\n      const isAdmin = user.role.name === 'Admin' || decoded.roleName === 'Admin';\n      const isExpert = user.role.name === 'Expert' || decoded.roleName === 'Expert' || \n                      user.userType === 'expert' || decoded.userType === 'expert';\n      const isClient = user.role.name === 'Client' || decoded.roleName === 'Client' || \n                      user.userType === 'client' || decoded.userType === 'client';\n      \n      console.log('Login sonrası role bilgisi:', { isAdmin, isExpert, isClient, userRole: user.role, decodedRole: decoded.roleName });\n      \n      // Set user with role flags\n      setUser({\n        ...user,\n        userType: decoded.userType,\n        isExpert: isExpert,\n        isClient: isClient\n      });\n      \n      // Fetch permissions\n      await fetchUserPermissions();\n      \n      // Redirect to dashboard\n      navigate('/dashboard');\n      \n      toast.success('Login successful');\n      \n      return true;\n    } catch (error) {\n      return false;\n    }\n  };\n\n  // Register handler\n  const register = async (userData) => {\n    try {\n      const response = await api.post('/auth/register', userData);\n      \n      const { user, accessToken } = response.data;\n      \n      // Store token\n      localStorage.setItem('accessToken', accessToken);\n      \n      // Set user\n      setUser(user);\n      \n      // Fetch permissions\n      await fetchUserPermissions();\n      \n      // Redirect to dashboard\n      navigate('/dashboard');\n      \n      toast.success('Registration successful');\n      \n      return true;\n    } catch (error) {\n      return false;\n    }\n  };\n\n  // Logout handler\n  const logout = () => {\n    // Clear tokens\n    localStorage.removeItem('accessToken');\n    localStorage.removeItem('refreshToken');\n    \n    // Clear state\n    setUser(null);\n    setPermissions([]);\n    \n    // Redirect to login\n    navigate('/login');\n    \n    toast.success('Logged out successfully');\n  };\n\n  // Check if user has permission\n  const hasPermission = (permissionKeyOrPath, permissionKeyParam) => {\n    if (!user) return false;\n    \n    // Admin role has all permissions\n    if (user.role?.name === 'Admin') {\n      return true;\n    }\n    \n    // Rol bazlı sayfa kontrolü - özel roller için\n    if (user.role?.name && !['Admin', 'Expert', 'Client'].includes(user.role.name)) {\n      // İzin istenen temel sayfaları belirle - sürekli log üretmemek için\n      const isMenuCheck = ['READ_DASHBOARD', 'READ_ADMIN_USERS', 'READ_ADMIN_ROLES'].includes(permissionKeyOrPath);\n      \n      // Özel izin kontrolü - sadece özel durumlarda loğla\n      if (!isMenuCheck && permissionKeyOrPath.startsWith('READ_')) {\n        console.log(`Özel rol (${user.role.name}) için izin kontrolü: ${permissionKeyOrPath}`);\n      }\n      \n      // İzin listesinde tam olarak bu izin var mı kontrol et\n      if (permissions && permissions.length > 0) {\n        // Tam izin kontrolü - permissionKey === permissionKeyOrPath\n        const hasExactPermission = permissions.some(p => {\n          return p.permissionKey === permissionKeyOrPath || \n                 p.permissionFullKey === permissionKeyOrPath;\n        });\n        \n        // İzin eşleşirse true döndür\n        if (hasExactPermission) {\n          if (!isMenuCheck && permissionKeyOrPath.startsWith('READ_')) {\n            console.log(`✅ İzin bulundu: ${permissionKeyOrPath}`);\n          }\n          return true;\n        }\n        \n        // FORM 1 - Eğer veritabanındaki izin READ ve istenen izin READ_DASHBOARD gibi bir izinse,\n        // burada özel bir kontrol yapmalıyız\n        \n        // İzin anahtarının ilk kısmını al (READ_DASHBOARD -> READ)\n        const basePermissionType = permissionKeyOrPath.split('_')[0]; // READ, CREATE, UPDATE, DELETE\n        \n        // Eğer izin listesinde basePermission varsa ve izin yapısı eski tipte ise (tek kelime, \"_\" yok)\n        // İzinde sayfa spesifik kısmı yok sadece READ varsa, ve izin listesi Roles tablosunda checkboxlar ile atanmışsa\n        const hasBasePermissionOnly = permissions.some(p => {\n          const isBasePermissionOnly = p.permissionKey === basePermissionType && !p.permissionKey.includes('_');\n          if (isBasePermissionOnly && !isMenuCheck && permissionKeyOrPath.startsWith('READ_')) {\n            console.log(`🔍 İzin sisteminde sadece temel '${basePermissionType}' izni var, sayfa özel izni yok`);\n            console.log(`📋 Bu izinlerin doğru çalışması için formata çevrilmesi gerekiyor (${permissionKeyOrPath})`);\n          }\n          return isBasePermissionOnly;\n        });\n        \n        // Şu an için eski tip izinleri kabul etmiyoruz - rol sayfasında izinlerin doğru şekilde atanması gerekiyor\n        // return hasBasePermissionOnly;\n      }\n      \n      // İzin bulunamadıysa false döndür\n      if (!isMenuCheck && permissionKeyOrPath.startsWith('READ_')) {\n        console.log(`❌ İzin reddedildi: ${permissionKeyOrPath}`);\n      }\n      return false;\n    }\n\n    // Rol bazlı izin kontrolü - standart roller (Expert, Client)\n    \n    // 1. İzin yoksa varsayılan davranış\n    if (!permissions || permissions.length === 0) {\n      // Uzman ve danışan rolleri için varsayılan izinler\n      if (user.isExpert && permissionKeyOrPath.startsWith('READ_EXPERT_')) {\n        return true;\n      }\n      \n      if (user.isClient && permissionKeyOrPath.startsWith('READ_CLIENT_')) {\n        return true;\n      }\n      \n      return false;\n    }\n    \n    // 2. İlk parametre role-specific dashboard izniyse ve user'ın ilgili rolü varsa izin ver\n    if (permissionKeyOrPath === 'READ_EXPERT_DASHBOARD' && user.isExpert) {\n      return true;\n    }\n    \n    if (permissionKeyOrPath === 'READ_CLIENT_DASHBOARD' && user.isClient) {\n      return true;\n    }\n    \n    // 3. Eğer rota izni bir rol bölümü için ise ve kullanıcı o role sahipse geçişine izin ver\n    if (permissionKeyOrPath.startsWith('READ_EXPERT_') && user.isExpert) {\n      return true;\n    }\n    \n    if (permissionKeyOrPath.startsWith('READ_CLIENT_') && user.isClient) {\n      return true;\n    }\n    \n    // 4. Eski format kontrolü (iki parametre)\n    if (permissionKeyParam !== undefined) {\n      const pagePath = permissionKeyOrPath;\n      const permKey = permissionKeyParam;\n      \n      const hasOldStylePermission = permissions.some(\n        p => p.pagePath === pagePath && p.permissionKey === permKey\n      );\n      \n      // Sayfa yönlendirmelerinde logla\n      if (pagePath.startsWith('/') && permKey === 'READ') {\n        console.log(`Sayfa izin kontrolü: ${pagePath} - ${permKey} = ${hasOldStylePermission ? '✅' : '❌'}`);\n      }\n      \n      return hasOldStylePermission;\n    }\n    \n    // 5. Tam izin anahtarı kontrolü\n    const hasExactPermission = permissions.some(\n      p => p.permissionKey === permissionKeyOrPath\n    );\n    \n    return hasExactPermission;\n  };\n\n  // Refresh permissions after role permission changes\n  const refreshPermissions = async () => {\n    const newPermissions = await fetchUserPermissions();\n    return newPermissions;\n  };\n\n  // Kullanıcı bilgilerini güncelleme\n  const updateUser = (userData) => {\n    setUser(prevUser => ({\n      ...prevUser,\n      ...userData\n    }));\n  };\n\n  return (\n    <AuthContext.Provider\n      value={{\n        user,\n        isInitialized,\n        permissions,\n        login,\n        register,\n        logout,\n        hasPermission,\n        refreshPermissions,\n        fetchUserPermissions,\n        updateUser\n      }}\n    >\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AACjE,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,SAAS,QAAQ,YAAY;AACtC,OAAOC,GAAG,MAAM,iBAAiB;AACjC,OAAOC,KAAK,MAAM,iBAAiB;;AAEnC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAO,MAAMC,WAAW,gBAAGT,aAAa,CAAC,CAAC;AAE1C,OAAO,MAAMU,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACc,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgB,aAAa,EAAEC,gBAAgB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAMkB,QAAQ,GAAGhB,WAAW,CAAC,CAAC;;EAE9B;EACAD,SAAS,CAAC,MAAM;IACd,MAAMkB,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,IAAI;QACF,MAAMC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;QAEvD,IAAIF,WAAW,EAAE;UACf;UACA,IAAI;YACF,MAAMG,OAAO,GAAGpB,SAAS,CAACiB,WAAW,CAAC;YACtCI,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,OAAO,CAAC;;YAEpC;YACA,IAAIA,OAAO,CAACG,GAAG,GAAG,IAAI,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;cACnC,MAAM,IAAIC,KAAK,CAAC,eAAe,CAAC;YAClC;;YAEA;YACA,MAAMC,OAAO,GAAGP,OAAO,CAACQ,QAAQ,KAAK,OAAO;YAC5C,MAAMC,QAAQ,GAAGT,OAAO,CAACQ,QAAQ,KAAK,QAAQ,IAAIR,OAAO,CAACU,QAAQ,KAAK,QAAQ;YAC/E,MAAMC,QAAQ,GAAGX,OAAO,CAACQ,QAAQ,KAAK,QAAQ,IAAIR,OAAO,CAACU,QAAQ,KAAK,QAAQ;YAE/ET,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;cAAEK,OAAO;cAAEE,QAAQ;cAAEE,QAAQ;cAAEH,QAAQ,EAAER,OAAO,CAACQ,QAAQ;cAAEE,QAAQ,EAAEV,OAAO,CAACU;YAAS,CAAC,CAAC;;YAEjI;YACApB,OAAO,CAAC;cACNsB,EAAE,EAAEZ,OAAO,CAACY,EAAE;cACdC,QAAQ,EAAEb,OAAO,CAACa,QAAQ;cAC1BC,KAAK,EAAEd,OAAO,CAACc,KAAK;cACpBC,SAAS,EAAEf,OAAO,CAACe,SAAS,IAAI,EAAE;cAClCC,QAAQ,EAAEhB,OAAO,CAACgB,QAAQ,IAAI,EAAE;cAChCC,IAAI,EAAE;gBACJL,EAAE,EAAEZ,OAAO,CAACkB,MAAM;gBAClBC,IAAI,EAAEnB,OAAO,CAACQ;cAChB,CAAC;cACDE,QAAQ,EAAEV,OAAO,CAACU,QAAQ;cAC1BD,QAAQ,EAAEA,QAAQ;cAClBE,QAAQ,EAAEA;YACZ,CAAC,CAAC;;YAEF;YACA,MAAMS,oBAAoB,CAAC,CAAC;UAC9B,CAAC,CAAC,OAAOC,KAAK,EAAE;YACdpB,OAAO,CAACoB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;YAC5C;YACAvB,YAAY,CAACwB,UAAU,CAAC,aAAa,CAAC;YACtCxB,YAAY,CAACwB,UAAU,CAAC,cAAc,CAAC;YACvChC,OAAO,CAAC,IAAI,CAAC;YACbE,cAAc,CAAC,EAAE,CAAC;UACpB;QACF;MACF,CAAC,CAAC,OAAO6B,KAAK,EAAE;QACdpB,OAAO,CAACoB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACpD,CAAC,SAAS;QACR3B,gBAAgB,CAAC,IAAI,CAAC;MACxB;IACF,CAAC;IAEDE,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMwB,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MAAA,IAAAG,UAAA,EAAAC,cAAA;MACF,MAAMC,QAAQ,GAAG,MAAM5C,GAAG,CAAC6C,GAAG,CAAC,oBAAoB,CAAC;;MAEpD;MACAzB,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAC7CD,OAAO,CAACC,GAAG,CAAC,cAAcb,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwB,QAAQ,UAAUxB,IAAI,aAAJA,IAAI,wBAAAkC,UAAA,GAAJlC,IAAI,CAAE4B,IAAI,cAAAM,UAAA,uBAAVA,UAAA,CAAYJ,IAAI,GAAG,CAAC;MACtElB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAAsB,cAAA,GAAAC,QAAQ,CAACE,IAAI,cAAAH,cAAA,uBAAbA,cAAA,CAAeI,MAAM,KAAI,CAAC,oBAAoB,CAAC;MAE9E,IAAIH,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QAC7C;QACA,MAAMC,oBAAoB,GAAGJ,QAAQ,CAACE,IAAI,CAACG,GAAG,CAACC,CAAC,KAAK;UACnDC,GAAG,EAAED,CAAC,CAACE,aAAa;UACpBrB,EAAE,EAAEmB,CAAC,CAACG;QACR,CAAC,CAAC,CAAC;QACHjC,OAAO,CAACkC,KAAK,CAACN,oBAAoB,CAAC;;QAEnC;QACA,MAAMO,2BAA2B,GAAGX,QAAQ,CAACE,IAAI,CAACU,IAAI,CAACN,CAAC,IACtDA,CAAC,CAACE,aAAa,IAAIF,CAAC,CAACE,aAAa,CAACK,QAAQ,CAAC,YAAY,CAC1D,CAAC;QAED,IAAI,CAACF,2BAA2B,EAAE;UAChCnC,OAAO,CAACsC,IAAI,CAAC,gGAAgG,CAAC;UAC9GtC,OAAO,CAACsC,IAAI,CAAC,qFAAqF,CAAC;QACrG;MACF,CAAC,MAAM;QACLtC,OAAO,CAACsC,IAAI,CAAC,+CAA+C,CAAC;MAC/D;MACAtC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAE7CV,cAAc,CAACiC,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;MACnC,OAAOF,QAAQ,CAACE,IAAI,IAAI,EAAE;IAC5B,CAAC,CAAC,OAAON,KAAK,EAAE;MACdpB,OAAO,CAACoB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD7B,cAAc,CAAC,EAAE,CAAC;MAClB,OAAO,EAAE;IACX;EACF,CAAC;;EAED;EACA,MAAMgD,KAAK,GAAG,MAAAA,CAAO3B,QAAQ,EAAE4B,QAAQ,KAAK;IAC1C,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAM5C,GAAG,CAAC6D,IAAI,CAAC,aAAa,EAAE;QAC7C7B,QAAQ;QACR4B;MACF,CAAC,CAAC;MAEF,MAAM;QAAEpD,IAAI;QAAEQ,WAAW;QAAE8C;MAAa,CAAC,GAAGlB,QAAQ,CAACE,IAAI;;MAEzD;MACA7B,YAAY,CAAC8C,OAAO,CAAC,aAAa,EAAE/C,WAAW,CAAC;MAChDC,YAAY,CAAC8C,OAAO,CAAC,cAAc,EAAED,YAAY,CAAC;;MAElD;MACA,MAAM3C,OAAO,GAAGpB,SAAS,CAACiB,WAAW,CAAC;;MAEtC;MACA,MAAMU,OAAO,GAAGlB,IAAI,CAAC4B,IAAI,CAACE,IAAI,KAAK,OAAO,IAAInB,OAAO,CAACQ,QAAQ,KAAK,OAAO;MAC1E,MAAMC,QAAQ,GAAGpB,IAAI,CAAC4B,IAAI,CAACE,IAAI,KAAK,QAAQ,IAAInB,OAAO,CAACQ,QAAQ,KAAK,QAAQ,IAC7DnB,IAAI,CAACqB,QAAQ,KAAK,QAAQ,IAAIV,OAAO,CAACU,QAAQ,KAAK,QAAQ;MAC3E,MAAMC,QAAQ,GAAGtB,IAAI,CAAC4B,IAAI,CAACE,IAAI,KAAK,QAAQ,IAAInB,OAAO,CAACQ,QAAQ,KAAK,QAAQ,IAC7DnB,IAAI,CAACqB,QAAQ,KAAK,QAAQ,IAAIV,OAAO,CAACU,QAAQ,KAAK,QAAQ;MAE3ET,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE;QAAEK,OAAO;QAAEE,QAAQ;QAAEE,QAAQ;QAAEkC,QAAQ,EAAExD,IAAI,CAAC4B,IAAI;QAAE6B,WAAW,EAAE9C,OAAO,CAACQ;MAAS,CAAC,CAAC;;MAE/H;MACAlB,OAAO,CAAC;QACN,GAAGD,IAAI;QACPqB,QAAQ,EAAEV,OAAO,CAACU,QAAQ;QAC1BD,QAAQ,EAAEA,QAAQ;QAClBE,QAAQ,EAAEA;MACZ,CAAC,CAAC;;MAEF;MACA,MAAMS,oBAAoB,CAAC,CAAC;;MAE5B;MACAzB,QAAQ,CAAC,YAAY,CAAC;MAEtBb,KAAK,CAACiE,OAAO,CAAC,kBAAkB,CAAC;MAEjC,OAAO,IAAI;IACb,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACd,OAAO,KAAK;IACd;EACF,CAAC;;EAED;EACA,MAAM2B,QAAQ,GAAG,MAAOC,QAAQ,IAAK;IACnC,IAAI;MACF,MAAMxB,QAAQ,GAAG,MAAM5C,GAAG,CAAC6D,IAAI,CAAC,gBAAgB,EAAEO,QAAQ,CAAC;MAE3D,MAAM;QAAE5D,IAAI;QAAEQ;MAAY,CAAC,GAAG4B,QAAQ,CAACE,IAAI;;MAE3C;MACA7B,YAAY,CAAC8C,OAAO,CAAC,aAAa,EAAE/C,WAAW,CAAC;;MAEhD;MACAP,OAAO,CAACD,IAAI,CAAC;;MAEb;MACA,MAAM+B,oBAAoB,CAAC,CAAC;;MAE5B;MACAzB,QAAQ,CAAC,YAAY,CAAC;MAEtBb,KAAK,CAACiE,OAAO,CAAC,yBAAyB,CAAC;MAExC,OAAO,IAAI;IACb,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACd,OAAO,KAAK;IACd;EACF,CAAC;;EAED;EACA,MAAM6B,MAAM,GAAGA,CAAA,KAAM;IACnB;IACApD,YAAY,CAACwB,UAAU,CAAC,aAAa,CAAC;IACtCxB,YAAY,CAACwB,UAAU,CAAC,cAAc,CAAC;;IAEvC;IACAhC,OAAO,CAAC,IAAI,CAAC;IACbE,cAAc,CAAC,EAAE,CAAC;;IAElB;IACAG,QAAQ,CAAC,QAAQ,CAAC;IAElBb,KAAK,CAACiE,OAAO,CAAC,yBAAyB,CAAC;EAC1C,CAAC;;EAED;EACA,MAAMI,aAAa,GAAGA,CAACC,mBAAmB,EAAEC,kBAAkB,KAAK;IAAA,IAAAC,WAAA,EAAAC,WAAA;IACjE,IAAI,CAAClE,IAAI,EAAE,OAAO,KAAK;;IAEvB;IACA,IAAI,EAAAiE,WAAA,GAAAjE,IAAI,CAAC4B,IAAI,cAAAqC,WAAA,uBAATA,WAAA,CAAWnC,IAAI,MAAK,OAAO,EAAE;MAC/B,OAAO,IAAI;IACb;;IAEA;IACA,IAAI,CAAAoC,WAAA,GAAAlE,IAAI,CAAC4B,IAAI,cAAAsC,WAAA,eAATA,WAAA,CAAWpC,IAAI,IAAI,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAACmB,QAAQ,CAACjD,IAAI,CAAC4B,IAAI,CAACE,IAAI,CAAC,EAAE;MAC9E;MACA,MAAMqC,WAAW,GAAG,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,kBAAkB,CAAC,CAAClB,QAAQ,CAACc,mBAAmB,CAAC;;MAE5G;MACA,IAAI,CAACI,WAAW,IAAIJ,mBAAmB,CAACK,UAAU,CAAC,OAAO,CAAC,EAAE;QAC3DxD,OAAO,CAACC,GAAG,CAAC,aAAab,IAAI,CAAC4B,IAAI,CAACE,IAAI,yBAAyBiC,mBAAmB,EAAE,CAAC;MACxF;;MAEA;MACA,IAAI7D,WAAW,IAAIA,WAAW,CAACqC,MAAM,GAAG,CAAC,EAAE;QACzC;QACA,MAAM8B,kBAAkB,GAAGnE,WAAW,CAAC8C,IAAI,CAACN,CAAC,IAAI;UAC/C,OAAOA,CAAC,CAACE,aAAa,KAAKmB,mBAAmB,IACvCrB,CAAC,CAAC4B,iBAAiB,KAAKP,mBAAmB;QACpD,CAAC,CAAC;;QAEF;QACA,IAAIM,kBAAkB,EAAE;UACtB,IAAI,CAACF,WAAW,IAAIJ,mBAAmB,CAACK,UAAU,CAAC,OAAO,CAAC,EAAE;YAC3DxD,OAAO,CAACC,GAAG,CAAC,mBAAmBkD,mBAAmB,EAAE,CAAC;UACvD;UACA,OAAO,IAAI;QACb;;QAEA;QACA;;QAEA;QACA,MAAMQ,kBAAkB,GAAGR,mBAAmB,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE9D;QACA;QACA,MAAMC,qBAAqB,GAAGvE,WAAW,CAAC8C,IAAI,CAACN,CAAC,IAAI;UAClD,MAAMgC,oBAAoB,GAAGhC,CAAC,CAACE,aAAa,KAAK2B,kBAAkB,IAAI,CAAC7B,CAAC,CAACE,aAAa,CAACK,QAAQ,CAAC,GAAG,CAAC;UACrG,IAAIyB,oBAAoB,IAAI,CAACP,WAAW,IAAIJ,mBAAmB,CAACK,UAAU,CAAC,OAAO,CAAC,EAAE;YACnFxD,OAAO,CAACC,GAAG,CAAC,oCAAoC0D,kBAAkB,iCAAiC,CAAC;YACpG3D,OAAO,CAACC,GAAG,CAAC,sEAAsEkD,mBAAmB,GAAG,CAAC;UAC3G;UACA,OAAOW,oBAAoB;QAC7B,CAAC,CAAC;;QAEF;QACA;MACF;;MAEA;MACA,IAAI,CAACP,WAAW,IAAIJ,mBAAmB,CAACK,UAAU,CAAC,OAAO,CAAC,EAAE;QAC3DxD,OAAO,CAACC,GAAG,CAAC,sBAAsBkD,mBAAmB,EAAE,CAAC;MAC1D;MACA,OAAO,KAAK;IACd;;IAEA;;IAEA;IACA,IAAI,CAAC7D,WAAW,IAAIA,WAAW,CAACqC,MAAM,KAAK,CAAC,EAAE;MAC5C;MACA,IAAIvC,IAAI,CAACoB,QAAQ,IAAI2C,mBAAmB,CAACK,UAAU,CAAC,cAAc,CAAC,EAAE;QACnE,OAAO,IAAI;MACb;MAEA,IAAIpE,IAAI,CAACsB,QAAQ,IAAIyC,mBAAmB,CAACK,UAAU,CAAC,cAAc,CAAC,EAAE;QACnE,OAAO,IAAI;MACb;MAEA,OAAO,KAAK;IACd;;IAEA;IACA,IAAIL,mBAAmB,KAAK,uBAAuB,IAAI/D,IAAI,CAACoB,QAAQ,EAAE;MACpE,OAAO,IAAI;IACb;IAEA,IAAI2C,mBAAmB,KAAK,uBAAuB,IAAI/D,IAAI,CAACsB,QAAQ,EAAE;MACpE,OAAO,IAAI;IACb;;IAEA;IACA,IAAIyC,mBAAmB,CAACK,UAAU,CAAC,cAAc,CAAC,IAAIpE,IAAI,CAACoB,QAAQ,EAAE;MACnE,OAAO,IAAI;IACb;IAEA,IAAI2C,mBAAmB,CAACK,UAAU,CAAC,cAAc,CAAC,IAAIpE,IAAI,CAACsB,QAAQ,EAAE;MACnE,OAAO,IAAI;IACb;;IAEA;IACA,IAAI0C,kBAAkB,KAAKW,SAAS,EAAE;MACpC,MAAMC,QAAQ,GAAGb,mBAAmB;MACpC,MAAMc,OAAO,GAAGb,kBAAkB;MAElC,MAAMc,qBAAqB,GAAG5E,WAAW,CAAC8C,IAAI,CAC5CN,CAAC,IAAIA,CAAC,CAACkC,QAAQ,KAAKA,QAAQ,IAAIlC,CAAC,CAACE,aAAa,KAAKiC,OACtD,CAAC;;MAED;MACA,IAAID,QAAQ,CAACR,UAAU,CAAC,GAAG,CAAC,IAAIS,OAAO,KAAK,MAAM,EAAE;QAClDjE,OAAO,CAACC,GAAG,CAAC,wBAAwB+D,QAAQ,MAAMC,OAAO,MAAMC,qBAAqB,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC;MACrG;MAEA,OAAOA,qBAAqB;IAC9B;;IAEA;IACA,MAAMT,kBAAkB,GAAGnE,WAAW,CAAC8C,IAAI,CACzCN,CAAC,IAAIA,CAAC,CAACE,aAAa,KAAKmB,mBAC3B,CAAC;IAED,OAAOM,kBAAkB;EAC3B,CAAC;;EAED;EACA,MAAMU,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,MAAMC,cAAc,GAAG,MAAMjD,oBAAoB,CAAC,CAAC;IACnD,OAAOiD,cAAc;EACvB,CAAC;;EAED;EACA,MAAMC,UAAU,GAAIrB,QAAQ,IAAK;IAC/B3D,OAAO,CAACiF,QAAQ,KAAK;MACnB,GAAGA,QAAQ;MACX,GAAGtB;IACL,CAAC,CAAC,CAAC;EACL,CAAC;EAED,oBACEjE,OAAA,CAACC,WAAW,CAACuF,QAAQ;IACnBC,KAAK,EAAE;MACLpF,IAAI;MACJI,aAAa;MACbF,WAAW;MACXiD,KAAK;MACLQ,QAAQ;MACRE,MAAM;MACNC,aAAa;MACbiB,kBAAkB;MAClBhD,oBAAoB;MACpBkD;IACF,CAAE;IAAAnF,QAAA,EAEDA;EAAQ;IAAAuF,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACzF,EAAA,CAjWWF,YAAY;EAAA,QAINP,WAAW;AAAA;AAAAmG,EAAA,GAJjB5F,YAAY;AAAA,IAAA4F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}