{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: \"eeee 'اللي جاي الساعة' p\",\n  yesterday: \"'إمبارح الساعة' p\",\n  today: \"'النهاردة الساعة' p\",\n  tomorrow: \"'بكرة الساعة' p\",\n  nextWeek: \"eeee 'الساعة' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/burky root/burky_root_web/node_modules/date-fns/esm/locale/ar-EG/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: \"eeee 'اللي جاي الساعة' p\",\n  yesterday: \"'إمبارح الساعة' p\",\n  today: \"'النهاردة الساعة' p\",\n  tomorrow: \"'بكرة الساعة' p\",\n  nextWeek: \"eeee 'الساعة' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,0BAA0B;EACpCC,SAAS,EAAE,mBAAmB;EAC9BC,KAAK,EAAE,qBAAqB;EAC5BC,QAAQ,EAAE,iBAAiB;EAC3BC,QAAQ,EAAE,iBAAiB;EAC3BC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EAC9E,OAAOX,oBAAoB,CAACQ,KAAK,CAAC;AACpC,CAAC;AACD,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}