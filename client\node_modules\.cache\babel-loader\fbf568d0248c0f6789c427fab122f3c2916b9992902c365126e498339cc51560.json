{"ast": null, "code": "import formatDistance from \"../de/_lib/formatDistance/index.js\";\nimport formatLong from \"../de/_lib/formatLong/index.js\";\nimport formatRelative from \"../de/_lib/formatRelative/index.js\";\nimport match from \"../de/_lib/match/index.js\";\n// difference to 'de' locale\nimport localize from \"./_lib/localize/index.js\";\n/**\n * @type {Locale}\n * @category Locales\n * @summary German locale (Austria).\n * @language German\n * @iso-639-2 deu\n * <AUTHOR> [@cstenglein]{@link https://github.com/cstenglein}\n */\nvar locale = {\n  code: 'de-AT',\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\nexport default locale;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "match", "localize", "locale", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/burky root/burky_root_web/node_modules/date-fns/esm/locale/de-AT/index.js"], "sourcesContent": ["import formatDistance from \"../de/_lib/formatDistance/index.js\";\nimport formatLong from \"../de/_lib/formatLong/index.js\";\nimport formatRelative from \"../de/_lib/formatRelative/index.js\";\nimport match from \"../de/_lib/match/index.js\";\n// difference to 'de' locale\nimport localize from \"./_lib/localize/index.js\";\n/**\n * @type {Locale}\n * @category Locales\n * @summary German locale (Austria).\n * @language German\n * @iso-639-2 deu\n * <AUTHOR> [@cstenglein]{@link https://github.com/cstenglein}\n */\nvar locale = {\n  code: 'de-AT',\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\nexport default locale;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,oCAAoC;AAC/D,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,cAAc,MAAM,oCAAoC;AAC/D,OAAOC,KAAK,MAAM,2BAA2B;AAC7C;AACA,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,MAAM,GAAG;EACXC,IAAI,EAAE,OAAO;EACbN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BE,QAAQ,EAAEA,QAAQ;EAClBD,KAAK,EAAEA,KAAK;EACZI,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;AACD,eAAeJ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}