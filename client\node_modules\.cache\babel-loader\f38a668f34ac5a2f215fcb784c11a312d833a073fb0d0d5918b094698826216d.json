{"ast": null, "code": "var _jsxFileName = \"C:\\\\claude\\\\burky_root_web\\\\client\\\\src\\\\components\\\\ui\\\\FormTextarea.jsx\";\nimport React, { forwardRef } from 'react';\n\n/**\n * Modern, özelleştirilebilir çok satırlı metin girişi bileşeni\n * \n * @param {Object} props - Bileşen özellikleri\n * @param {string} props.label - Textarea etiketi\n * @param {string} props.id - Textarea ID'si\n * @param {string} props.name - Textarea adı\n * @param {string} props.placeholder - Placeholder metni\n * @param {string} props.error - Hata mesajı (varsa)\n * @param {boolean} props.fullWidth - Tam genişlik kullanılsın mı\n * @param {string} props.variant - Stil varyantı ('outlined', 'filled', 'underlined')\n * @param {string} props.size - Boyut ('sm', 'md', 'lg')\n * @param {boolean} props.disabled - Etkin değil mi\n * @param {string} props.helperText - <PERSON><PERSON>m<PERSON><PERSON> metin\n * @param {number} props.rows - Satır sayısı\n * @param {string} props.className - Ek sınıflar\n * @param {number} props.maxLength - Maksimum karakter sayısı\n * @param {boolean} props.showCount - Karakter sayacı gösterilsin mi\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FormTextarea = /*#__PURE__*/forwardRef(_c = ({\n  label,\n  id,\n  name,\n  placeholder,\n  error,\n  fullWidth = true,\n  variant = 'outlined',\n  size = 'md',\n  disabled = false,\n  helperText,\n  rows = 4,\n  className,\n  maxLength,\n  showCount = false,\n  value = '',\n  ...rest\n}, ref) => {\n  // Boyuta göre padding ve font size\n  const sizeClasses = {\n    sm: 'px-3 py-1.5 text-xs',\n    md: 'px-4 py-2 text-sm',\n    lg: 'px-5 py-3 text-base'\n  };\n\n  // Variant'a göre stil sınıfları\n  const getVariantClasses = () => {\n    switch (variant) {\n      case 'filled':\n        return `bg-gray-100 border-transparent hover:bg-gray-200 focus:bg-white ${error ? 'border-red-500' : 'focus:border-primary-500'}`;\n      case 'underlined':\n        return `border-t-0 border-r-0 border-l-0 border-b-2 rounded-none px-0 ${error ? 'border-red-500' : 'border-gray-300 focus:border-primary-500'}`;\n      case 'outlined':\n      default:\n        return `bg-white ${error ? 'border-red-400 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 hover:border-gray-400 focus:ring-primary-500 focus:border-primary-500'}`;\n    }\n  };\n\n  // Karakter sayısını gösterme\n  const renderCharCount = () => {\n    if (!showCount || !maxLength) return null;\n    const count = (value === null || value === void 0 ? void 0 : value.length) || 0;\n    const isNearLimit = count > maxLength * 0.8;\n    const isAtLimit = count >= maxLength;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `text-xs mt-1 text-right ${isAtLimit ? 'text-red-500 font-medium' : isNearLimit ? 'text-yellow-600' : 'text-gray-500'}`,\n      children: [count, \"/\", maxLength]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `${fullWidth ? 'w-full' : 'w-auto'} ${className || ''}`,\n    children: [label && /*#__PURE__*/_jsxDEV(\"label\", {\n      htmlFor: id,\n      className: \"block text-sm font-medium mb-1.5 text-gray-700\",\n      children: label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n      id: id,\n      name: name,\n      placeholder: placeholder,\n      disabled: disabled,\n      rows: rows,\n      ref: ref,\n      maxLength: maxLength,\n      className: `\n          block w-full\n          border rounded-lg\n          focus:outline-none focus:ring-2 focus:ring-opacity-50\n          transition-colors duration-200\n          shadow-sm\n          resize-y\n          ${sizeClasses[size]}\n          ${getVariantClasses()}\n          ${disabled ? 'opacity-60 cursor-not-allowed bg-gray-100' : ''}\n        `,\n      ...rest\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this), renderCharCount(), (error || helperText) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `mt-1 ${showCount ? '' : 'mb-0'}`,\n      children: error ? /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs text-red-600\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 13\n      }, this) : helperText ? /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs text-gray-500\",\n        children: helperText\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 13\n      }, this) : null\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 88,\n    columnNumber: 5\n  }, this);\n});\n_c2 = FormTextarea;\nFormTextarea.displayName = 'FormTextarea';\nexport default FormTextarea;\nvar _c, _c2;\n$RefreshReg$(_c, \"FormTextarea$forwardRef\");\n$RefreshReg$(_c2, \"FormTextarea\");", "map": {"version": 3, "names": ["React", "forwardRef", "jsxDEV", "_jsxDEV", "FormTextarea", "_c", "label", "id", "name", "placeholder", "error", "fullWidth", "variant", "size", "disabled", "helperText", "rows", "className", "max<PERSON><PERSON><PERSON>", "showCount", "value", "rest", "ref", "sizeClasses", "sm", "md", "lg", "getVariantClasses", "renderCharCount", "count", "length", "isNearLimit", "isAtLimit", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "htmlFor", "_c2", "displayName", "$RefreshReg$"], "sources": ["C:/claude/burky_root_web/client/src/components/ui/FormTextarea.jsx"], "sourcesContent": ["import React, { forwardRef } from 'react';\n\n/**\n * Modern, özelleştirilebilir çok satırlı metin girişi bileşeni\n * \n * @param {Object} props - Bileşen özellikleri\n * @param {string} props.label - Textarea etiketi\n * @param {string} props.id - Textarea ID'si\n * @param {string} props.name - Textarea adı\n * @param {string} props.placeholder - Placeholder metni\n * @param {string} props.error - <PERSON><PERSON> mesajı (varsa)\n * @param {boolean} props.fullWidth - Tam genişlik kullanılsın mı\n * @param {string} props.variant - Stil varyantı ('outlined', 'filled', 'underlined')\n * @param {string} props.size - Boyut ('sm', 'md', 'lg')\n * @param {boolean} props.disabled - Etkin değil mi\n * @param {string} props.helperText - Yardımcı metin\n * @param {number} props.rows - <PERSON><PERSON><PERSON><PERSON> sayısı\n * @param {string} props.className - Ek sınıflar\n * @param {number} props.maxLength - Maks<PERSON>um karakter sayısı\n * @param {boolean} props.showCount - Karakter sayacı gösterilsin mi\n */\nconst FormTextarea = forwardRef(({\n  label,\n  id,\n  name,\n  placeholder,\n  error,\n  fullWidth = true,\n  variant = 'outlined',\n  size = 'md',\n  disabled = false,\n  helperText,\n  rows = 4,\n  className,\n  maxLength,\n  showCount = false,\n  value = '',\n  ...rest\n}, ref) => {\n  // Boyuta göre padding ve font size\n  const sizeClasses = {\n    sm: 'px-3 py-1.5 text-xs',\n    md: 'px-4 py-2 text-sm',\n    lg: 'px-5 py-3 text-base'\n  };\n\n  // Variant'a göre stil sınıfları\n  const getVariantClasses = () => {\n    switch (variant) {\n      case 'filled':\n        return `bg-gray-100 border-transparent hover:bg-gray-200 focus:bg-white ${\n          error ? 'border-red-500' : 'focus:border-primary-500'\n        }`;\n      case 'underlined':\n        return `border-t-0 border-r-0 border-l-0 border-b-2 rounded-none px-0 ${\n          error ? 'border-red-500' : 'border-gray-300 focus:border-primary-500'\n        }`;\n      case 'outlined':\n      default:\n        return `bg-white ${\n          error \n            ? 'border-red-400 focus:ring-red-500 focus:border-red-500' \n            : 'border-gray-300 hover:border-gray-400 focus:ring-primary-500 focus:border-primary-500'\n        }`;\n    }\n  };\n\n  // Karakter sayısını gösterme\n  const renderCharCount = () => {\n    if (!showCount || !maxLength) return null;\n    \n    const count = value?.length || 0;\n    const isNearLimit = count > maxLength * 0.8;\n    const isAtLimit = count >= maxLength;\n    \n    return (\n      <div className={`text-xs mt-1 text-right ${\n        isAtLimit ? 'text-red-500 font-medium' : \n        isNearLimit ? 'text-yellow-600' : \n        'text-gray-500'\n      }`}>\n        {count}/{maxLength}\n      </div>\n    );\n  };\n\n  return (\n    <div className={`${fullWidth ? 'w-full' : 'w-auto'} ${className || ''}`}>\n      {label && (\n        <label \n          htmlFor={id}\n          className=\"block text-sm font-medium mb-1.5 text-gray-700\"\n        >\n          {label}\n        </label>\n      )}\n      \n      <textarea\n        id={id}\n        name={name}\n        placeholder={placeholder}\n        disabled={disabled}\n        rows={rows}\n        ref={ref}\n        maxLength={maxLength}\n        className={`\n          block w-full\n          border rounded-lg\n          focus:outline-none focus:ring-2 focus:ring-opacity-50\n          transition-colors duration-200\n          shadow-sm\n          resize-y\n          ${sizeClasses[size]}\n          ${getVariantClasses()}\n          ${disabled ? 'opacity-60 cursor-not-allowed bg-gray-100' : ''}\n        `}\n        {...rest}\n      />\n      \n      {renderCharCount()}\n      \n      {(error || helperText) && (\n        <div className={`mt-1 ${showCount ? '' : 'mb-0'}`}>\n          {error ? (\n            <p className=\"text-xs text-red-600\">{error}</p>\n          ) : helperText ? (\n            <p className=\"text-xs text-gray-500\">{helperText}</p>\n          ) : null}\n        </div>\n      )}\n    </div>\n  );\n});\n\nFormTextarea.displayName = 'FormTextarea';\n\nexport default FormTextarea;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAlBA,SAAAC,MAAA,IAAAC,OAAA;AAmBA,MAAMC,YAAY,gBAAGH,UAAU,CAAAI,EAAA,GAACA,CAAC;EAC/BC,KAAK;EACLC,EAAE;EACFC,IAAI;EACJC,WAAW;EACXC,KAAK;EACLC,SAAS,GAAG,IAAI;EAChBC,OAAO,GAAG,UAAU;EACpBC,IAAI,GAAG,IAAI;EACXC,QAAQ,GAAG,KAAK;EAChBC,UAAU;EACVC,IAAI,GAAG,CAAC;EACRC,SAAS;EACTC,SAAS;EACTC,SAAS,GAAG,KAAK;EACjBC,KAAK,GAAG,EAAE;EACV,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT;EACA,MAAMC,WAAW,GAAG;IAClBC,EAAE,EAAE,qBAAqB;IACzBC,EAAE,EAAE,mBAAmB;IACvBC,EAAE,EAAE;EACN,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQf,OAAO;MACb,KAAK,QAAQ;QACX,OAAO,mEACLF,KAAK,GAAG,gBAAgB,GAAG,0BAA0B,EACrD;MACJ,KAAK,YAAY;QACf,OAAO,iEACLA,KAAK,GAAG,gBAAgB,GAAG,0CAA0C,EACrE;MACJ,KAAK,UAAU;MACf;QACE,OAAO,YACLA,KAAK,GACD,wDAAwD,GACxD,uFAAuF,EAC3F;IACN;EACF,CAAC;;EAED;EACA,MAAMkB,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACT,SAAS,IAAI,CAACD,SAAS,EAAE,OAAO,IAAI;IAEzC,MAAMW,KAAK,GAAG,CAAAT,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEU,MAAM,KAAI,CAAC;IAChC,MAAMC,WAAW,GAAGF,KAAK,GAAGX,SAAS,GAAG,GAAG;IAC3C,MAAMc,SAAS,GAAGH,KAAK,IAAIX,SAAS;IAEpC,oBACEf,OAAA;MAAKc,SAAS,EAAE,2BACde,SAAS,GAAG,0BAA0B,GACtCD,WAAW,GAAG,iBAAiB,GAC/B,eAAe,EACd;MAAAE,QAAA,GACAJ,KAAK,EAAC,GAAC,EAACX,SAAS;IAAA;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC;EAEV,CAAC;EAED,oBACElC,OAAA;IAAKc,SAAS,EAAE,GAAGN,SAAS,GAAG,QAAQ,GAAG,QAAQ,IAAIM,SAAS,IAAI,EAAE,EAAG;IAAAgB,QAAA,GACrE3B,KAAK,iBACJH,OAAA;MACEmC,OAAO,EAAE/B,EAAG;MACZU,SAAS,EAAC,gDAAgD;MAAAgB,QAAA,EAEzD3B;IAAK;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAEDlC,OAAA;MACEI,EAAE,EAAEA,EAAG;MACPC,IAAI,EAAEA,IAAK;MACXC,WAAW,EAAEA,WAAY;MACzBK,QAAQ,EAAEA,QAAS;MACnBE,IAAI,EAAEA,IAAK;MACXM,GAAG,EAAEA,GAAI;MACTJ,SAAS,EAAEA,SAAU;MACrBD,SAAS,EAAE;AACnB;AACA;AACA;AACA;AACA;AACA;AACA,YAAYM,WAAW,CAACV,IAAI,CAAC;AAC7B,YAAYc,iBAAiB,CAAC,CAAC;AAC/B,YAAYb,QAAQ,GAAG,2CAA2C,GAAG,EAAE;AACvE,SAAU;MAAA,GACEO;IAAI;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,EAEDT,eAAe,CAAC,CAAC,EAEjB,CAAClB,KAAK,IAAIK,UAAU,kBACnBZ,OAAA;MAAKc,SAAS,EAAE,QAAQE,SAAS,GAAG,EAAE,GAAG,MAAM,EAAG;MAAAc,QAAA,EAC/CvB,KAAK,gBACJP,OAAA;QAAGc,SAAS,EAAC,sBAAsB;QAAAgB,QAAA,EAAEvB;MAAK;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,GAC7CtB,UAAU,gBACZZ,OAAA;QAAGc,SAAS,EAAC,uBAAuB;QAAAgB,QAAA,EAAElB;MAAU;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,GACnD;IAAI;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC,CAAC;AAACE,GAAA,GA/GGnC,YAAY;AAiHlBA,YAAY,CAACoC,WAAW,GAAG,cAAc;AAEzC,eAAepC,YAAY;AAAC,IAAAC,EAAA,EAAAkC,GAAA;AAAAE,YAAA,CAAApC,EAAA;AAAAoC,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}