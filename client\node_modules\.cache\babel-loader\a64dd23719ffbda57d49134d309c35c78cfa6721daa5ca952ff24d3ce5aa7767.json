{"ast": null, "code": "var _jsxFileName = \"C:\\\\Projeler\\\\kidgarden\\\\burky_root_web\\\\client\\\\src\\\\pages\\\\client\\\\messages\\\\ClientMessagesPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '../../../hooks/useAuth';\nimport api from '../../../services/api';\nimport { toast } from 'react-hot-toast';\nimport io from 'socket.io-client';\nimport { ChatBubbleLeftEllipsisIcon, MagnifyingGlassIcon, UserIcon, PaperAirplaneIcon, PaperClipIcon, FaceSmileIcon, UserCircleIcon, EllipsisHorizontalIcon, PhoneIcon, VideoCameraIcon, InformationCircleIcon, ClockIcon, CheckCircleIcon, XMarkIcon, TrashIcon, ArchiveBoxIcon, StarIcon, UserGroupIcon, CalendarIcon } from '@heroicons/react/24/outline';\nimport { format } from 'date-fns';\nimport { tr } from 'date-fns/locale';\nimport { Link } from 'react-router-dom';\n\n/**\n * <PERSON><PERSON><PERSON><PERSON> me<PERSON>jlaş<PERSON> sayfası\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ClientMessagesPage = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [isLoading, setIsLoading] = useState(true);\n  const [conversations, setConversations] = useState([]);\n  const [selectedConversation, setSelectedConversation] = useState(null);\n  const [messages, setMessages] = useState([]);\n  const [messageText, setMessageText] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filter, setFilter] = useState('all'); // all, unread, archived, starred\n  const [socket, setSocket] = useState(null);\n  const [onlineUsers, setOnlineUsers] = useState(new Set());\n  const [typingUsers, setTypingUsers] = useState(new Set());\n  const messagesEndRef = useRef(null);\n  const conversationsRef = useRef(null);\n  const messagesContainerRef = useRef(null);\n\n  // Socket.IO connection - ayrı useEffect\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      console.log('🔌 Socket.IO bağlantısı kuruluyor...');\n      const socketConnection = io(process.env.REACT_APP_API_URL || 'http://localhost:5000', {\n        auth: {\n          token\n        },\n        transports: ['websocket', 'polling'],\n        reconnection: true,\n        reconnectionDelay: 1000,\n        reconnectionAttempts: 5,\n        timeout: 20000\n      });\n      setSocket(socketConnection);\n\n      // Connection events\n      socketConnection.on('connect', () => {\n        console.log('✅ Socket.IO bağlantısı kuruldu:', socketConnection.id);\n        // Kullanıcının online olduğunu bildir\n        socketConnection.emit('user_online');\n      });\n      socketConnection.on('disconnect', reason => {\n        console.log('❌ Socket.IO bağlantısı kesildi:', reason);\n      });\n      socketConnection.on('connect_error', error => {\n        console.error('🔥 Socket.IO bağlantı hatası:', error);\n      });\n      socketConnection.on('reconnect', attemptNumber => {\n        console.log('🔄 Socket.IO yeniden bağlandı, deneme:', attemptNumber);\n      });\n      socketConnection.on('reconnect_error', error => {\n        console.error('🔥 Socket.IO yeniden bağlanma hatası:', error);\n      });\n      return () => {\n        socketConnection.disconnect();\n      };\n    }\n  }, []);\n\n  // Socket event listeners - ayrı useEffect\n  useEffect(() => {\n    if (socket) {\n      console.log('🎧 Socket event listeners kuruluyor...');\n\n      // Socket event listeners\n      const handleNewMessage = message => {\n        console.log('📨 Yeni mesaj alındı:', message);\n\n        // Conversation listesini ÖNCE güncelle (her zaman)\n        setConversations(prev => prev.map(conv => conv.id === message.conversationId ? {\n          ...conv,\n          lastMessage: message.content,\n          timestamp: message.createdAt,\n          unread: message.senderId !== user.id\n        } : conv));\n\n        // Eğer şu anda bu conversation açıksa, mesajı da ekle\n        setSelectedConversation(currentSelected => {\n          if (currentSelected && message.conversationId === currentSelected.id) {\n            setMessages(prev => [...prev, {\n              id: message.id,\n              senderId: message.senderId,\n              senderName: message.sender.name,\n              senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n              text: message.content,\n              timestamp: message.createdAt,\n              read: message.isRead\n            }]);\n\n            // Scroll to bottom\n            setTimeout(() => {\n              var _messagesEndRef$curre;\n              (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n                behavior: 'smooth'\n              });\n            }, 100);\n          }\n          return currentSelected;\n        });\n      };\n      const handleMessageSent = message => {\n        console.log('✅ Mesaj gönderildi onayı:', message);\n      };\n      const handleUserStatusChange = data => {\n        setOnlineUsers(prev => {\n          const newSet = new Set(prev);\n          if (data.status === 'online') {\n            newSet.add(data.userId);\n          } else {\n            newSet.delete(data.userId);\n          }\n          return newSet;\n        });\n      };\n      const handleUserTyping = data => {\n        setSelectedConversation(currentSelected => {\n          if (currentSelected && data.conversationId === currentSelected.id) {\n            setTypingUsers(prev => new Set([...prev, data.userId]));\n          }\n          return currentSelected;\n        });\n      };\n      const handleUserStoppedTyping = data => {\n        setTypingUsers(prev => {\n          const newSet = new Set(prev);\n          newSet.delete(data.userId);\n          return newSet;\n        });\n      };\n\n      // Event listener'ları ekle\n      socket.on('new_message', handleNewMessage);\n      socket.on('message_sent', handleMessageSent);\n      socket.on('user_status_change', handleUserStatusChange);\n      socket.on('user_typing', handleUserTyping);\n      socket.on('user_stopped_typing', handleUserStoppedTyping);\n\n      // Cleanup function\n      return () => {\n        socket.off('new_message', handleNewMessage);\n        socket.off('message_sent', handleMessageSent);\n        socket.off('user_status_change', handleUserStatusChange);\n        socket.off('user_typing', handleUserTyping);\n        socket.off('user_stopped_typing', handleUserStoppedTyping);\n      };\n    }\n  }, [socket, user.id]);\n\n  // Conversations yükleme - ayrı useEffect\n  useEffect(() => {\n    loadConversations();\n  }, []);\n\n  // Conversations yükleme\n  const loadConversations = async () => {\n    try {\n      setIsLoading(true);\n      const response = await api.get('/messages/conversations');\n\n      // API verisini frontend formatına çevir\n      const formattedConversations = response.data.conversations.map(conversation => {\n        var _conversation$lastMes, _conversation$lastMes2;\n        return {\n          id: conversation.id,\n          expertId: conversation.otherUser.id,\n          expertName: conversation.otherUser.name,\n          expertTitle: conversation.otherUser.role === 'Expert' ? 'Uzman' : conversation.otherUser.role,\n          lastMessage: ((_conversation$lastMes = conversation.lastMessage) === null || _conversation$lastMes === void 0 ? void 0 : _conversation$lastMes.content) || 'Henüz mesaj yok',\n          timestamp: ((_conversation$lastMes2 = conversation.lastMessage) === null || _conversation$lastMes2 === void 0 ? void 0 : _conversation$lastMes2.timestamp) || conversation.createdAt,\n          unread: conversation.lastMessage ? !conversation.lastMessage.isRead && conversation.lastMessage.senderId !== user.id : false,\n          avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(conversation.otherUser.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n          status: onlineUsers.has(conversation.otherUser.id) ? 'online' : 'offline',\n          starred: false,\n          archived: false\n        };\n      });\n      setConversations(formattedConversations);\n    } catch (error) {\n      console.error('Konuşmalar yüklenirken hata:', error);\n      toast.error('Konuşmalar yüklenemedi');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Mesaj yükleme\n  useEffect(() => {\n    if (selectedConversation) {\n      loadMessages(selectedConversation.id);\n\n      // Socket'e conversation'a katıl\n      if (socket) {\n        console.log('🏠 Konuşmaya katılıyor:', selectedConversation.id);\n        socket.emit('join_conversation', selectedConversation.id);\n      }\n    }\n  }, [selectedConversation, socket]);\n\n  // Conversations yükleme\n  const loadMessages = async conversationId => {\n    try {\n      const response = await api.get(`/messages/conversations/${conversationId}`);\n\n      // API verisini frontend formatına çevir\n      const formattedMessages = response.data.messages.map(message => ({\n        id: message.id,\n        senderId: message.senderId,\n        senderName: message.sender.name,\n        senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        text: message.content,\n        timestamp: message.createdAt,\n        read: message.isRead,\n        attachments: message.attachments\n      }));\n      setMessages(formattedMessages);\n\n      // Scroll to bottom\n      setTimeout(() => {\n        var _messagesEndRef$curre2;\n        (_messagesEndRef$curre2 = messagesEndRef.current) === null || _messagesEndRef$curre2 === void 0 ? void 0 : _messagesEndRef$curre2.scrollIntoView({\n          behavior: 'smooth'\n        });\n      }, 100);\n    } catch (error) {\n      console.error('Mesajlar yüklenirken hata:', error);\n      toast.error('Mesajlar yüklenemedi');\n    }\n  };\n  // Mesaj gönderme\n  const sendMessage = async () => {\n    if (!messageText.trim() || !selectedConversation) return;\n    console.log('📤 Mesaj gönderiliyor:', {\n      receiverId: selectedConversation.expertId,\n      content: messageText.trim()\n    });\n    try {\n      const response = await api.post('/messages/send', {\n        receiverId: selectedConversation.expertId,\n        content: messageText.trim()\n      });\n      console.log('✅ Mesaj gönderildi:', response.data);\n\n      // Mesajı local state'e ekle\n      const newMessage = {\n        id: response.data.message.id,\n        senderId: user.id,\n        senderName: `${user.firstName} ${user.lastName}`,\n        senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(user.firstName)}+${encodeURIComponent(user.lastName)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        text: messageText.trim(),\n        timestamp: response.data.message.createdAt,\n        read: false\n      };\n      setMessages(prev => [...prev, newMessage]);\n      setMessageText('');\n\n      // Conversation listesini güncelle\n      setConversations(prev => prev.map(conv => conv.id === selectedConversation.id ? {\n        ...conv,\n        lastMessage: messageText.trim(),\n        timestamp: newMessage.timestamp\n      } : conv));\n\n      // Scroll to bottom\n      setTimeout(() => {\n        var _messagesEndRef$curre3;\n        (_messagesEndRef$curre3 = messagesEndRef.current) === null || _messagesEndRef$curre3 === void 0 ? void 0 : _messagesEndRef$curre3.scrollIntoView({\n          behavior: 'smooth'\n        });\n      }, 100);\n    } catch (error) {\n      console.error('Mesaj gönderilirken hata:', error);\n      toast.error('Mesaj gönderilemedi');\n    }\n  };\n  useEffect(() => {\n    // Mesajları otomatik kaydır\n    if (messagesEndRef.current && messagesContainerRef.current) {\n      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;\n    }\n  }, [messages]);\n\n  // Mesaj gönderme\n  const handleSendMessage = e => {\n    e.preventDefault();\n    sendMessage();\n  };\n\n  // Konuşma seçme\n  const handleSelectConversation = conversation => {\n    setSelectedConversation(conversation);\n  };\n\n  // Tarih formatı\n  const formatMessageDate = dateString => {\n    const date = new Date(dateString);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    if (date.toDateString() === today.toDateString()) {\n      return format(date, 'HH:mm', {\n        locale: tr\n      });\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Dün ' + format(date, 'HH:mm', {\n        locale: tr\n      });\n    } else {\n      return format(date, 'dd MMM HH:mm', {\n        locale: tr\n      });\n    }\n  };\n\n  // Konuşmaları filtrele\n  const filteredConversations = conversations.filter(conv => {\n    // Arama filtresi\n    const matchesSearch = conv.expertName.toLowerCase().includes(searchTerm.toLowerCase()) || conv.lastMessage.toLowerCase().includes(searchTerm.toLowerCase());\n\n    // Durum filtresi\n    const matchesFilter = filter === 'all' || filter === 'unread' && conv.unread || filter === 'archived' && conv.archived || filter === 'starred' && conv.starred;\n    return matchesSearch && matchesFilter;\n  });\n\n  // Konuşmayı yıldızla\n  const toggleStar = id => {\n    setConversations(prevConversations => prevConversations.map(conv => conv.id === id ? {\n      ...conv,\n      starred: !conv.starred\n    } : conv));\n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({\n        ...prev,\n        starred: !prev.starred\n      }));\n    }\n  };\n\n  // Konuşmayı arşivle\n  const toggleArchive = id => {\n    setConversations(prevConversations => prevConversations.map(conv => conv.id === id ? {\n      ...conv,\n      archived: !conv.archived\n    } : conv));\n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({\n        ...prev,\n        archived: !prev.archived\n      }));\n    }\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 387,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 386,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-gray-50 min-h-screen pb-12\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-pink-500 to-pink-600 shadow-lg rounded-lg p-6 mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-white\",\n              children: \"Mesajlar\\u0131m\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-pink-100\",\n              children: \"Uzmanlar\\u0131n\\u0131zla g\\xFCvenli bir \\u015Fekilde ileti\\u015Fim kurun\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-3 sm:mt-0 flex space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/client/experts\",\n              className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-pink-600 bg-white hover:bg-pink-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-colors duration-150\",\n              children: [/*#__PURE__*/_jsxDEV(UserGroupIcon, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 17\n              }, this), \"Uzmanlar\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/client/appointments\",\n              className: \"inline-flex items-center px-4 py-2 border border-white text-sm font-medium rounded-md shadow-sm text-white hover:bg-pink-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-colors duration-150\",\n              children: [/*#__PURE__*/_jsxDEV(CalendarIcon, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 17\n              }, this), \"Randevular\\u0131m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-md overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-12 h-[75vh]\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-span-12 md:col-span-4 border-r border-gray-200 flex flex-col\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 border-b border-gray-200 bg-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-xl font-semibold text-gray-800 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(ChatBubbleLeftEllipsisIcon, {\n                  className: \"h-6 w-6 text-teal-600 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 19\n                }, this), \"Mesajlar\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-3 relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Konu\\u015Fmalarda ara...\",\n                  className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500\",\n                  value: searchTerm,\n                  onChange: e => setSearchTerm(e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                  className: \"h-5 w-5 text-gray-400 absolute left-3 top-2.5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-3 flex space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `px-3 py-1 text-sm rounded-full ${filter === 'all' ? 'bg-teal-100 text-teal-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                  onClick: () => setFilter('all'),\n                  children: \"T\\xFCm\\xFC\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `px-3 py-1 text-sm rounded-full ${filter === 'unread' ? 'bg-teal-100 text-teal-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                  onClick: () => setFilter('unread'),\n                  children: \"Okunmam\\u0131\\u015F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `px-3 py-1 text-sm rounded-full ${filter === 'starred' ? 'bg-teal-100 text-teal-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                  onClick: () => setFilter('starred'),\n                  children: \"Y\\u0131ld\\u0131zl\\u0131\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 460,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `px-3 py-1 text-sm rounded-full ${filter === 'archived' ? 'bg-teal-100 text-teal-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                  onClick: () => setFilter('archived'),\n                  children: \"Ar\\u015Fiv\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 468,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: conversationsRef,\n              style: {\n                height: 'calc(75vh - 145px)',\n                overflowY: 'auto',\n                scrollbarWidth: 'thin',\n                scrollbarColor: '#D1D5DB #F3F4F6'\n              },\n              children: filteredConversations.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 text-center text-gray-500\",\n                children: \"Hi\\xE7 mesaj\\u0131n\\u0131z yok\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 19\n              }, this) : filteredConversations.map(conversation => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `p-3 cursor-pointer hover:bg-gray-50 transition-colors duration-200 ${(selectedConversation === null || selectedConversation === void 0 ? void 0 : selectedConversation.id) === conversation.id ? 'bg-teal-50' : ''} ${conversation.unread ? 'bg-teal-50 hover:bg-teal-100' : ''}`,\n                onClick: () => handleSelectConversation(conversation),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative flex-shrink-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: conversation.avatar,\n                      alt: conversation.expertName,\n                      className: `h-10 w-10 rounded-full ${(selectedConversation === null || selectedConversation === void 0 ? void 0 : selectedConversation.id) === conversation.id ? 'ring-2 ring-teal-600' : ''}`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 502,\n                      columnNumber: 27\n                    }, this), conversation.status === 'online' && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 512,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 501,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-start\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: `text-sm font-medium ${conversation.unread ? 'text-gray-900' : 'text-gray-700'}`,\n                        children: conversation.expertName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 517,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [conversation.starred && /*#__PURE__*/_jsxDEV(StarIcon, {\n                          className: \"h-4 w-4 text-yellow-400 fill-current\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 522,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-xs text-gray-500\",\n                          children: formatMessageDate(conversation.timestamp)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 524,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 520,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 516,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500\",\n                      children: conversation.expertTitle\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 529,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: `text-sm truncate mt-1 ${conversation.unread ? 'text-gray-900 font-medium' : 'text-gray-500'}`,\n                      children: conversation.lastMessage\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 530,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-center mt-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-gray-500\",\n                        children: conversation.status === 'online' ? 'Çevrimiçi' : conversation.lastSeen ? `Son görülme: ${conversation.lastSeen}` : ''\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 536,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex space-x-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: e => {\n                            e.stopPropagation();\n                            toggleStar(conversation.id);\n                          },\n                          className: \"text-gray-400 hover:text-yellow-400\",\n                          children: /*#__PURE__*/_jsxDEV(StarIcon, {\n                            className: `h-4 w-4 ${conversation.starred ? 'text-yellow-400 fill-current' : ''}`\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 551,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 544,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: e => {\n                            e.stopPropagation();\n                            toggleArchive(conversation.id);\n                          },\n                          className: \"text-gray-400 hover:text-gray-600\",\n                          children: /*#__PURE__*/_jsxDEV(ArchiveBoxIcon, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 560,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 553,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 543,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 535,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 515,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 23\n                }, this), conversation.unread && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"inline-flex items-center ml-12 mt-1 px-2 py-0.5 rounded-full text-xs font-medium bg-teal-600 text-white\",\n                  children: \"Yeni\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 25\n                }, this)]\n              }, conversation.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-span-12 md:col-span-8 flex flex-col\",\n            children: selectedConversation ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 border-b border-gray-200 bg-white flex justify-between items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative mr-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: selectedConversation.avatar,\n                      alt: selectedConversation.expertName,\n                      className: \"h-10 w-10 rounded-full\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 584,\n                      columnNumber: 25\n                    }, this), selectedConversation.status === 'online' && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 590,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 583,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-lg font-medium text-gray-800\",\n                      children: selectedConversation.expertName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 594,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500\",\n                      children: [selectedConversation.expertTitle, \" \", ' • ', selectedConversation.status === 'online' ? 'Çevrimiçi' : selectedConversation.lastSeen ? `Son görülme: ${selectedConversation.lastSeen}` : '']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 597,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 593,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 582,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Link, {\n                    to: `/client/experts/${selectedConversation.expertId}`,\n                    className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                    children: /*#__PURE__*/_jsxDEV(UserIcon, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 612,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 608,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Link, {\n                    to: `/client/appointments?expert=${selectedConversation.expertId}`,\n                    className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                    children: /*#__PURE__*/_jsxDEV(ClockIcon, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 618,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 614,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Link, {\n                    to: `/client/sessions?expert=${selectedConversation.expertId}`,\n                    className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                    children: /*#__PURE__*/_jsxDEV(VideoCameraIcon, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 624,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 620,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                    children: /*#__PURE__*/_jsxDEV(EllipsisHorizontalIcon, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 627,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 626,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 607,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 581,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                ref: messagesContainerRef,\n                className: \"p-4 bg-gray-50\",\n                style: {\n                  height: 'calc(75vh - 195px)',\n                  overflowY: 'auto',\n                  scrollbarWidth: 'thin',\n                  scrollbarColor: '#D1D5DB #F3F4F6'\n                },\n                children: [messages.map((message, index) => {\n                  const isSender = message.senderId === user.id;\n                  const showAvatar = index === 0 || messages[index - 1].senderId !== message.senderId;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `flex ${isSender ? 'justify-end' : 'justify-start'} mb-4`,\n                    children: [!isSender && showAvatar && /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: message.senderAvatar,\n                      alt: message.senderName,\n                      className: \"h-8 w-8 rounded-full mr-2 mt-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 650,\n                      columnNumber: 29\n                    }, this), !isSender && !showAvatar && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-8 mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 656,\n                      columnNumber: 56\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `max-w-xs md:max-w-md lg:max-w-lg rounded-lg px-4 py-2 ${isSender ? 'bg-teal-600 text-white rounded-br-none' : 'bg-white text-gray-800 rounded-bl-none border border-gray-200'}`,\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm\",\n                        children: message.text\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 664,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `text-xs mt-1 ${isSender ? 'text-teal-200' : 'text-gray-500'} flex items-center justify-end`,\n                        children: [formatMessageDate(message.timestamp), isSender && /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                          className: \"h-3 w-3 ml-1\",\n                          title: message.read ? 'Okundu' : 'İletildi'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 668,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 665,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 657,\n                      columnNumber: 27\n                    }, this), isSender && showAvatar && /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: message.senderAvatar,\n                      alt: message.senderName,\n                      className: \"h-8 w-8 rounded-full ml-2 mt-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 673,\n                      columnNumber: 29\n                    }, this), isSender && !showAvatar && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-8 ml-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 679,\n                      columnNumber: 55\n                    }, this)]\n                  }, message.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 648,\n                    columnNumber: 25\n                  }, this);\n                }), /*#__PURE__*/_jsxDEV(\"div\", {\n                  ref: messagesEndRef\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 683,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 633,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3 border-t border-gray-200 bg-white\",\n                children: /*#__PURE__*/_jsxDEV(\"form\", {\n                  onSubmit: handleSendMessage,\n                  className: \"flex items-end\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    className: \"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\",\n                    children: /*#__PURE__*/_jsxDEV(PaperClipIcon, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 693,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 689,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n                      className: \"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 resize-none\",\n                      placeholder: \"Mesaj\\u0131n\\u0131z\\u0131 yaz\\u0131n...\",\n                      rows: \"2\",\n                      value: messageText,\n                      onChange: e => setMessageText(e.target.value)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 696,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 695,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    className: \"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\",\n                    children: /*#__PURE__*/_jsxDEV(FaceSmileIcon, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 708,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 704,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"submit\",\n                    disabled: !messageText.trim(),\n                    className: `ml-2 p-2 rounded-full ${messageText.trim() ? 'bg-teal-600 text-white hover:bg-teal-700' : 'bg-gray-200 text-gray-400 cursor-not-allowed'} focus:outline-none`,\n                    children: /*#__PURE__*/_jsxDEV(PaperAirplaneIcon, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 719,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 710,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 688,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 687,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) :\n            /*#__PURE__*/\n            // Mesaj seçilmediğinde\n            _jsxDEV(\"div\", {\n              className: \"flex-1 flex flex-col items-center justify-center p-6 bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full max-w-md text-center\",\n                children: [/*#__PURE__*/_jsxDEV(ChatBubbleLeftEllipsisIcon, {\n                  className: \"h-16 w-16 text-gray-300 mb-4 mx-auto\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 728,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-medium text-gray-800 mb-2\",\n                  children: \"Mesajlar\\u0131n\\u0131z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 729,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-500 mx-auto\",\n                  children: \"Mesajla\\u015Fmaya ba\\u015Flamak i\\xE7in sol taraftan bir konu\\u015Fma se\\xE7in veya yeni bir uzmanla ileti\\u015Fime ge\\xE7in.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 730,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-6\",\n                  children: /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/client/experts\",\n                    className: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500\",\n                    children: [/*#__PURE__*/_jsxDEV(UserIcon, {\n                      className: \"-ml-1 mr-2 h-5 w-5\",\n                      \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 738,\n                      columnNumber: 25\n                    }, this), \"Uzmanlar\\u0131 Ke\\u015Ffedin\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 734,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 733,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 727,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 726,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 577,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 424,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 394,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 393,\n    columnNumber: 5\n  }, this);\n};\n_s(ClientMessagesPage, \"/HIBSL47wEHSC+087hW9D9bi+v8=\", false, function () {\n  return [useAuth];\n});\n_c = ClientMessagesPage;\nexport default ClientMessagesPage;\nvar _c;\n$RefreshReg$(_c, \"ClientMessagesPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useAuth", "api", "toast", "io", "ChatBubbleLeftEllipsisIcon", "MagnifyingGlassIcon", "UserIcon", "PaperAirplaneIcon", "PaperClipIcon", "FaceSmileIcon", "UserCircleIcon", "EllipsisHorizontalIcon", "PhoneIcon", "VideoCameraIcon", "InformationCircleIcon", "ClockIcon", "CheckCircleIcon", "XMarkIcon", "TrashIcon", "ArchiveBoxIcon", "StarIcon", "UserGroupIcon", "CalendarIcon", "format", "tr", "Link", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ClientMessagesPage", "_s", "user", "isLoading", "setIsLoading", "conversations", "setConversations", "selectedConversation", "setSelectedConversation", "messages", "setMessages", "messageText", "setMessageText", "searchTerm", "setSearchTerm", "filter", "setFilter", "socket", "setSocket", "onlineUsers", "setOnlineUsers", "Set", "typingUsers", "setTypingUsers", "messagesEndRef", "conversationsRef", "messagesContainerRef", "token", "localStorage", "getItem", "console", "log", "socketConnection", "process", "env", "REACT_APP_API_URL", "auth", "transports", "reconnection", "reconnectionDelay", "reconnectionAttempts", "timeout", "on", "id", "emit", "reason", "error", "attemptNumber", "disconnect", "handleNewMessage", "message", "prev", "map", "conv", "conversationId", "lastMessage", "content", "timestamp", "createdAt", "unread", "senderId", "currentSelected", "sender<PERSON>ame", "sender", "name", "senderAvatar", "encodeURIComponent", "text", "read", "isRead", "setTimeout", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleMessageSent", "handleUserStatusChange", "data", "newSet", "status", "add", "userId", "delete", "handleUserTyping", "handleUserStoppedTyping", "off", "loadConversations", "response", "get", "formattedConversations", "conversation", "_conversation$lastMes", "_conversation$lastMes2", "expertId", "otherUser", "expertName", "expert<PERSON><PERSON>le", "role", "avatar", "has", "starred", "archived", "loadMessages", "formattedMessages", "attachments", "_messagesEndRef$curre2", "sendMessage", "trim", "receiverId", "post", "newMessage", "firstName", "lastName", "_messagesEndRef$curre3", "scrollTop", "scrollHeight", "handleSendMessage", "e", "preventDefault", "handleSelectConversation", "formatMessageDate", "dateString", "date", "Date", "today", "yesterday", "setDate", "getDate", "toDateString", "locale", "filteredConversations", "matchesSearch", "toLowerCase", "includes", "matchesFilter", "toggleStar", "prevConversations", "toggleArchive", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "type", "placeholder", "value", "onChange", "target", "onClick", "ref", "style", "height", "overflowY", "scrollbarWidth", "scrollbarColor", "length", "src", "alt", "lastSeen", "stopPropagation", "index", "isSender", "showAvatar", "title", "onSubmit", "rows", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Projeler/kidgarden/burky_root_web/client/src/pages/client/messages/ClientMessagesPage.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '../../../hooks/useAuth';\nimport api from '../../../services/api';\nimport { toast } from 'react-hot-toast';\nimport io from 'socket.io-client';\nimport {\n  ChatBubbleLeftEllipsisIcon,\n  MagnifyingGlassIcon,\n  UserIcon,\n  PaperAirplaneIcon,\n  PaperClipIcon,\n  FaceSmileIcon,\n  UserCircleIcon,\n  EllipsisHorizontalIcon,\n  PhoneIcon,\n  VideoCameraIcon,\n  InformationCircleIcon,\n  ClockIcon,\n  CheckCircleIcon,\n  XMarkIcon,\n  TrashIcon,\n  ArchiveBoxIcon,\n  StarIcon,\n  UserGroupIcon,\n  CalendarIcon\n} from '@heroicons/react/24/outline';\nimport { format } from 'date-fns';\nimport { tr } from 'date-fns/locale';\nimport { Link } from 'react-router-dom';\n\n/**\n * Danışan mesajlaşma sayfası\n */\nconst ClientMessagesPage = () => {\n  const { user } = useAuth();\n  const [isLoading, setIsLoading] = useState(true);\n  const [conversations, setConversations] = useState([]);\n  const [selectedConversation, setSelectedConversation] = useState(null);\n  const [messages, setMessages] = useState([]);\n  const [messageText, setMessageText] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filter, setFilter] = useState('all'); // all, unread, archived, starred\n  const [socket, setSocket] = useState(null);\n  const [onlineUsers, setOnlineUsers] = useState(new Set());\n  const [typingUsers, setTypingUsers] = useState(new Set());\n  const messagesEndRef = useRef(null);\n  const conversationsRef = useRef(null);\n  const messagesContainerRef = useRef(null);\n\n  // Socket.IO connection - ayrı useEffect\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      console.log('🔌 Socket.IO bağlantısı kuruluyor...');\n      const socketConnection = io(process.env.REACT_APP_API_URL || 'http://localhost:5000', {\n        auth: { token },\n        transports: ['websocket', 'polling'],\n        reconnection: true,\n        reconnectionDelay: 1000,\n        reconnectionAttempts: 5,\n        timeout: 20000\n      });\n\n      setSocket(socketConnection);\n\n      // Connection events\n      socketConnection.on('connect', () => {\n        console.log('✅ Socket.IO bağlantısı kuruldu:', socketConnection.id);\n        // Kullanıcının online olduğunu bildir\n        socketConnection.emit('user_online');\n      });\n\n      socketConnection.on('disconnect', (reason) => {\n        console.log('❌ Socket.IO bağlantısı kesildi:', reason);\n      });\n\n      socketConnection.on('connect_error', (error) => {\n        console.error('🔥 Socket.IO bağlantı hatası:', error);\n      });\n\n      socketConnection.on('reconnect', (attemptNumber) => {\n        console.log('🔄 Socket.IO yeniden bağlandı, deneme:', attemptNumber);\n      });\n\n      socketConnection.on('reconnect_error', (error) => {\n        console.error('🔥 Socket.IO yeniden bağlanma hatası:', error);\n      });\n\n      return () => {\n        socketConnection.disconnect();\n      };\n    }\n  }, []);\n\n  // Socket event listeners - ayrı useEffect\n  useEffect(() => {\n    if (socket) {\n      console.log('🎧 Socket event listeners kuruluyor...');\n\n      // Socket event listeners\n      const handleNewMessage = (message) => {\n        console.log('📨 Yeni mesaj alındı:', message);\n\n        // Conversation listesini ÖNCE güncelle (her zaman)\n        setConversations(prev => prev.map(conv =>\n          conv.id === message.conversationId\n            ? { ...conv, lastMessage: message.content, timestamp: message.createdAt, unread: message.senderId !== user.id }\n            : conv\n        ));\n\n        // Eğer şu anda bu conversation açıksa, mesajı da ekle\n        setSelectedConversation(currentSelected => {\n          if (currentSelected && message.conversationId === currentSelected.id) {\n            setMessages(prev => [...prev, {\n              id: message.id,\n              senderId: message.senderId,\n              senderName: message.sender.name,\n              senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n              text: message.content,\n              timestamp: message.createdAt,\n              read: message.isRead\n            }]);\n\n            // Scroll to bottom\n            setTimeout(() => {\n              messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n            }, 100);\n          }\n          return currentSelected;\n        });\n      };\n\n      const handleMessageSent = (message) => {\n        console.log('✅ Mesaj gönderildi onayı:', message);\n      };\n\n      const handleUserStatusChange = (data) => {\n        setOnlineUsers(prev => {\n          const newSet = new Set(prev);\n          if (data.status === 'online') {\n            newSet.add(data.userId);\n          } else {\n            newSet.delete(data.userId);\n          }\n          return newSet;\n        });\n      };\n\n      const handleUserTyping = (data) => {\n        setSelectedConversation(currentSelected => {\n          if (currentSelected && data.conversationId === currentSelected.id) {\n            setTypingUsers(prev => new Set([...prev, data.userId]));\n          }\n          return currentSelected;\n        });\n      };\n\n      const handleUserStoppedTyping = (data) => {\n        setTypingUsers(prev => {\n          const newSet = new Set(prev);\n          newSet.delete(data.userId);\n          return newSet;\n        });\n      };\n\n      // Event listener'ları ekle\n      socket.on('new_message', handleNewMessage);\n      socket.on('message_sent', handleMessageSent);\n      socket.on('user_status_change', handleUserStatusChange);\n      socket.on('user_typing', handleUserTyping);\n      socket.on('user_stopped_typing', handleUserStoppedTyping);\n\n      // Cleanup function\n      return () => {\n        socket.off('new_message', handleNewMessage);\n        socket.off('message_sent', handleMessageSent);\n        socket.off('user_status_change', handleUserStatusChange);\n        socket.off('user_typing', handleUserTyping);\n        socket.off('user_stopped_typing', handleUserStoppedTyping);\n      };\n    }\n  }, [socket, user.id]);\n\n  // Conversations yükleme - ayrı useEffect\n  useEffect(() => {\n    loadConversations();\n  }, []);\n\n  // Conversations yükleme\n  const loadConversations = async () => {\n    try {\n      setIsLoading(true);\n      const response = await api.get('/messages/conversations');\n\n      // API verisini frontend formatına çevir\n      const formattedConversations = response.data.conversations.map(conversation => ({\n        id: conversation.id,\n        expertId: conversation.otherUser.id,\n        expertName: conversation.otherUser.name,\n        expertTitle: conversation.otherUser.role === 'Expert' ? 'Uzman' : conversation.otherUser.role,\n        lastMessage: conversation.lastMessage?.content || 'Henüz mesaj yok',\n        timestamp: conversation.lastMessage?.timestamp || conversation.createdAt,\n        unread: conversation.lastMessage ? !conversation.lastMessage.isRead && conversation.lastMessage.senderId !== user.id : false,\n        avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(conversation.otherUser.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        status: onlineUsers.has(conversation.otherUser.id) ? 'online' : 'offline',\n        starred: false,\n        archived: false\n      }));\n\n      setConversations(formattedConversations);\n    } catch (error) {\n      console.error('Konuşmalar yüklenirken hata:', error);\n      toast.error('Konuşmalar yüklenemedi');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Mesaj yükleme\n  useEffect(() => {\n    if (selectedConversation) {\n      loadMessages(selectedConversation.id);\n\n      // Socket'e conversation'a katıl\n      if (socket) {\n        console.log('🏠 Konuşmaya katılıyor:', selectedConversation.id);\n        socket.emit('join_conversation', selectedConversation.id);\n      }\n    }\n  }, [selectedConversation, socket]);\n\n  // Conversations yükleme\n  const loadMessages = async (conversationId) => {\n    try {\n      const response = await api.get(`/messages/conversations/${conversationId}`);\n\n      // API verisini frontend formatına çevir\n      const formattedMessages = response.data.messages.map(message => ({\n        id: message.id,\n        senderId: message.senderId,\n        senderName: message.sender.name,\n        senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        text: message.content,\n        timestamp: message.createdAt,\n        read: message.isRead,\n        attachments: message.attachments\n      }));\n\n      setMessages(formattedMessages);\n\n      // Scroll to bottom\n      setTimeout(() => {\n        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n      }, 100);\n    } catch (error) {\n      console.error('Mesajlar yüklenirken hata:', error);\n      toast.error('Mesajlar yüklenemedi');\n    }\n  };\n  // Mesaj gönderme\n  const sendMessage = async () => {\n    if (!messageText.trim() || !selectedConversation) return;\n\n    console.log('📤 Mesaj gönderiliyor:', {\n      receiverId: selectedConversation.expertId,\n      content: messageText.trim()\n    });\n\n    try {\n      const response = await api.post('/messages/send', {\n        receiverId: selectedConversation.expertId,\n        content: messageText.trim()\n      });\n\n      console.log('✅ Mesaj gönderildi:', response.data);\n\n      // Mesajı local state'e ekle\n      const newMessage = {\n        id: response.data.message.id,\n        senderId: user.id,\n        senderName: `${user.firstName} ${user.lastName}`,\n        senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(user.firstName)}+${encodeURIComponent(user.lastName)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        text: messageText.trim(),\n        timestamp: response.data.message.createdAt,\n        read: false\n      };\n\n      setMessages(prev => [...prev, newMessage]);\n      setMessageText('');\n\n      // Conversation listesini güncelle\n      setConversations(prev => prev.map(conv =>\n        conv.id === selectedConversation.id\n          ? { ...conv, lastMessage: messageText.trim(), timestamp: newMessage.timestamp }\n          : conv\n      ));\n\n      // Scroll to bottom\n      setTimeout(() => {\n        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n      }, 100);\n\n    } catch (error) {\n      console.error('Mesaj gönderilirken hata:', error);\n      toast.error('Mesaj gönderilemedi');\n    }\n  };\n\n  useEffect(() => {\n    // Mesajları otomatik kaydır\n    if (messagesEndRef.current && messagesContainerRef.current) {\n      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;\n    }\n  }, [messages]);\n\n  // Mesaj gönderme\n  const handleSendMessage = (e) => {\n    e.preventDefault();\n    sendMessage();\n  };\n\n  // Konuşma seçme\n  const handleSelectConversation = (conversation) => {\n    setSelectedConversation(conversation);\n  };\n\n  // Tarih formatı\n  const formatMessageDate = (dateString) => {\n    const date = new Date(dateString);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    \n    if (date.toDateString() === today.toDateString()) {\n      return format(date, 'HH:mm', { locale: tr });\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Dün ' + format(date, 'HH:mm', { locale: tr });\n    } else {\n      return format(date, 'dd MMM HH:mm', { locale: tr });\n    }\n  };\n\n  // Konuşmaları filtrele\n  const filteredConversations = conversations.filter(conv => {\n    // Arama filtresi\n    const matchesSearch = conv.expertName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         conv.lastMessage.toLowerCase().includes(searchTerm.toLowerCase());\n    \n    // Durum filtresi\n    const matchesFilter = filter === 'all' ||\n                         (filter === 'unread' && conv.unread) ||\n                         (filter === 'archived' && conv.archived) ||\n                         (filter === 'starred' && conv.starred);\n                        \n    return matchesSearch && matchesFilter;\n  });\n\n  // Konuşmayı yıldızla\n  const toggleStar = (id) => {\n    setConversations(prevConversations =>\n      prevConversations.map(conv =>\n        conv.id === id ? { ...conv, starred: !conv.starred } : conv\n      )\n    );\n    \n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({ ...prev, starred: !prev.starred }));\n    }\n  };\n\n  // Konuşmayı arşivle\n  const toggleArchive = (id) => {\n    setConversations(prevConversations =>\n      prevConversations.map(conv =>\n        conv.id === id ? { ...conv, archived: !conv.archived } : conv\n      )\n    );\n    \n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({ ...prev, archived: !prev.archived }));\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-gray-50 min-h-screen pb-12\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-8\">\n        {/* Başlık ve Üst Kısım */}\n        <div className=\"bg-gradient-to-r from-pink-500 to-pink-600 shadow-lg rounded-lg p-6 mb-6\">\n          <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-white\">Mesajlarım</h1>\n              <p className=\"mt-1 text-pink-100\">\n                Uzmanlarınızla güvenli bir şekilde iletişim kurun\n              </p>\n            </div>\n            <div className=\"mt-3 sm:mt-0 flex space-x-2\">\n              <Link\n                to=\"/client/experts\"\n                className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-pink-600 bg-white hover:bg-pink-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-colors duration-150\"\n              >\n                <UserGroupIcon className=\"h-4 w-4 mr-2\" />\n                Uzmanlar\n              </Link>\n              <Link\n                to=\"/client/appointments\"\n                className=\"inline-flex items-center px-4 py-2 border border-white text-sm font-medium rounded-md shadow-sm text-white hover:bg-pink-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-colors duration-150\"\n              >\n                <CalendarIcon className=\"h-4 w-4 mr-2\" />\n                Randevularım\n              </Link>\n            </div>\n          </div>\n        </div>\n        \n        {/* Mesajlaşma arayüzü */}\n        <div className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n          <div className=\"grid grid-cols-12 h-[75vh]\">\n            {/* Sol Kenar - Konuşma Listesi */}\n            <div className=\"col-span-12 md:col-span-4 border-r border-gray-200 flex flex-col\">\n              <div className=\"p-4 border-b border-gray-200 bg-white\">\n                <h1 className=\"text-xl font-semibold text-gray-800 flex items-center\">\n                  <ChatBubbleLeftEllipsisIcon className=\"h-6 w-6 text-teal-600 mr-2\" />\n                  Mesajlar\n                </h1>\n                <div className=\"mt-3 relative\">\n                  <input\n                    type=\"text\"\n                    placeholder=\"Konuşmalarda ara...\"\n                    className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                  />\n                  <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400 absolute left-3 top-2.5\" />\n                </div>\n                <div className=\"mt-3 flex space-x-2\">\n                  <button\n                    className={`px-3 py-1 text-sm rounded-full ${filter === 'all' \n                      ? 'bg-teal-100 text-teal-800' \n                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                    onClick={() => setFilter('all')}\n                  >\n                    Tümü\n                  </button>\n                  <button\n                    className={`px-3 py-1 text-sm rounded-full ${filter === 'unread' \n                      ? 'bg-teal-100 text-teal-800' \n                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                    onClick={() => setFilter('unread')}\n                  >\n                    Okunmamış\n                  </button>\n                  <button\n                    className={`px-3 py-1 text-sm rounded-full ${filter === 'starred' \n                      ? 'bg-teal-100 text-teal-800' \n                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                    onClick={() => setFilter('starred')}\n                  >\n                    Yıldızlı\n                  </button>\n                  <button\n                    className={`px-3 py-1 text-sm rounded-full ${filter === 'archived' \n                      ? 'bg-teal-100 text-teal-800' \n                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                    onClick={() => setFilter('archived')}\n                  >\n                    Arşiv\n                  </button>\n                </div>\n              </div>\n              <div \n                ref={conversationsRef}\n                style={{\n                  height: 'calc(75vh - 145px)',\n                  overflowY: 'auto',\n                  scrollbarWidth: 'thin',\n                  scrollbarColor: '#D1D5DB #F3F4F6'\n                }}\n              >\n                {filteredConversations.length === 0 ? (\n                  <div className=\"p-4 text-center text-gray-500\">\n                    Hiç mesajınız yok\n                  </div>\n                ) : (\n                  filteredConversations.map(conversation => (\n                    <div\n                      key={conversation.id}\n                      className={`p-3 cursor-pointer hover:bg-gray-50 transition-colors duration-200 ${\n                        selectedConversation?.id === conversation.id ? 'bg-teal-50' : ''\n                      } ${conversation.unread ? 'bg-teal-50 hover:bg-teal-100' : ''}`}\n                      onClick={() => handleSelectConversation(conversation)}\n                    >\n                      <div className=\"flex items-start space-x-3\">\n                        <div className=\"relative flex-shrink-0\">\n                          <img\n                            src={conversation.avatar}\n                            alt={conversation.expertName}\n                            className={`h-10 w-10 rounded-full ${\n                              selectedConversation?.id === conversation.id \n                                ? 'ring-2 ring-teal-600' \n                                : ''\n                            }`}\n                          />\n                          {conversation.status === 'online' && (\n                            <span className=\"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"></span>\n                          )}\n                        </div>\n                        <div className=\"flex-1 min-w-0\">\n                          <div className=\"flex justify-between items-start\">\n                            <h3 className={`text-sm font-medium ${conversation.unread ? 'text-gray-900' : 'text-gray-700'}`}>\n                              {conversation.expertName}\n                            </h3>\n                            <div className=\"flex items-center space-x-1\">\n                              {conversation.starred && (\n                                <StarIcon className=\"h-4 w-4 text-yellow-400 fill-current\" />\n                              )}\n                              <span className=\"text-xs text-gray-500\">\n                                {formatMessageDate(conversation.timestamp)}\n                              </span>\n                            </div>\n                          </div>\n                          <p className=\"text-xs text-gray-500\">{conversation.expertTitle}</p>\n                          <p className={`text-sm truncate mt-1 ${\n                            conversation.unread ? 'text-gray-900 font-medium' : 'text-gray-500'\n                          }`}>\n                            {conversation.lastMessage}\n                          </p>\n                          <div className=\"flex justify-between items-center mt-1\">\n                            <span className=\"text-xs text-gray-500\">\n                              {conversation.status === 'online' \n                                ? 'Çevrimiçi' \n                                : conversation.lastSeen \n                                  ? `Son görülme: ${conversation.lastSeen}` \n                                  : ''}\n                            </span>\n                            <div className=\"flex space-x-1\">\n                              <button \n                                onClick={(e) => {\n                                  e.stopPropagation();\n                                  toggleStar(conversation.id);\n                                }}\n                                className=\"text-gray-400 hover:text-yellow-400\"\n                              >\n                                <StarIcon className={`h-4 w-4 ${conversation.starred ? 'text-yellow-400 fill-current' : ''}`} />\n                              </button>\n                              <button \n                                onClick={(e) => {\n                                  e.stopPropagation();\n                                  toggleArchive(conversation.id);\n                                }}\n                                className=\"text-gray-400 hover:text-gray-600\"\n                              >\n                                <ArchiveBoxIcon className=\"h-4 w-4\" />\n                              </button>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                      {conversation.unread && (\n                        <span className=\"inline-flex items-center ml-12 mt-1 px-2 py-0.5 rounded-full text-xs font-medium bg-teal-600 text-white\">\n                          Yeni\n                        </span>\n                      )}\n                    </div>\n                  ))\n                )}\n              </div>\n            </div>\n            {/* Sağ Taraf - Mesaj Alanı */}\n            <div className=\"col-span-12 md:col-span-8 flex flex-col\">\n              {selectedConversation ? (\n                <>\n                  {/* Mesajlaşma Başlığı */}\n                  <div className=\"p-4 border-b border-gray-200 bg-white flex justify-between items-center\">\n                    <div className=\"flex items-center\">\n                      <div className=\"relative mr-3\">\n                        <img\n                          src={selectedConversation.avatar}\n                          alt={selectedConversation.expertName}\n                          className=\"h-10 w-10 rounded-full\"\n                        />\n                        {selectedConversation.status === 'online' && (\n                          <span className=\"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"></span>\n                        )}\n                      </div>\n                      <div>\n                        <h2 className=\"text-lg font-medium text-gray-800\">\n                          {selectedConversation.expertName}\n                        </h2>\n                        <p className=\"text-xs text-gray-500\">\n                          {selectedConversation.expertTitle} {' • '}\n                          {selectedConversation.status === 'online' \n                            ? 'Çevrimiçi' \n                            : selectedConversation.lastSeen \n                              ? `Son görülme: ${selectedConversation.lastSeen}` \n                              : ''}\n                        </p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center space-x-3\">\n                      <Link \n                        to={`/client/experts/${selectedConversation.expertId}`}\n                        className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\"\n                      >\n                        <UserIcon className=\"h-5 w-5\" />\n                      </Link>\n                      <Link \n                        to={`/client/appointments?expert=${selectedConversation.expertId}`}\n                        className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\"\n                      >\n                        <ClockIcon className=\"h-5 w-5\" />\n                      </Link>\n                      <Link \n                        to={`/client/sessions?expert=${selectedConversation.expertId}`}\n                        className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\"\n                      >\n                        <VideoCameraIcon className=\"h-5 w-5\" />\n                      </Link>\n                      <button className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\">\n                        <EllipsisHorizontalIcon className=\"h-5 w-5\" />\n                      </button>\n                    </div>\n                  </div>\n\n                  {/* Mesaj Alanı */}\n                  <div \n                    ref={messagesContainerRef}\n                    className=\"p-4 bg-gray-50\"\n                    style={{\n                      height: 'calc(75vh - 195px)',\n                      overflowY: 'auto',\n                      scrollbarWidth: 'thin',\n                      scrollbarColor: '#D1D5DB #F3F4F6'\n                    }}\n                  >\n                    {messages.map((message, index) => {\n                      const isSender = message.senderId === user.id;\n                      const showAvatar = index === 0 || messages[index - 1].senderId !== message.senderId;\n                      \n                      return (\n                        <div key={message.id} className={`flex ${isSender ? 'justify-end' : 'justify-start'} mb-4`}>\n                          {!isSender && showAvatar && (\n                            <img \n                              src={message.senderAvatar} \n                              alt={message.senderName}\n                              className=\"h-8 w-8 rounded-full mr-2 mt-1\"\n                            />\n                          )}\n                          {!isSender && !showAvatar && <div className=\"w-8 mr-2\"></div>}\n                          <div \n                            className={`max-w-xs md:max-w-md lg:max-w-lg rounded-lg px-4 py-2 ${\n                              isSender \n                                ? 'bg-teal-600 text-white rounded-br-none' \n                                : 'bg-white text-gray-800 rounded-bl-none border border-gray-200'\n                            }`}\n                          >\n                            <p className=\"text-sm\">{message.text}</p>\n                            <div className={`text-xs mt-1 ${isSender ? 'text-teal-200' : 'text-gray-500'} flex items-center justify-end`}>\n                              {formatMessageDate(message.timestamp)}\n                              {isSender && (\n                                <CheckCircleIcon className=\"h-3 w-3 ml-1\" title={message.read ? 'Okundu' : 'İletildi'} />\n                              )}\n                            </div>\n                          </div>\n                          {isSender && showAvatar && (\n                            <img \n                              src={message.senderAvatar}\n                              alt={message.senderName}\n                              className=\"h-8 w-8 rounded-full ml-2 mt-1\"\n                            />\n                          )}\n                          {isSender && !showAvatar && <div className=\"w-8 ml-2\"></div>}\n                        </div>\n                      );\n                    })}\n                    <div ref={messagesEndRef} />\n                  </div>\n\n                  {/* Mesaj Giriş Alanı */}\n                  <div className=\"p-3 border-t border-gray-200 bg-white\">\n                    <form onSubmit={handleSendMessage} className=\"flex items-end\">\n                      <button\n                        type=\"button\"\n                        className=\"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\"\n                      >\n                        <PaperClipIcon className=\"h-5 w-5\" />\n                      </button>\n                      <div className=\"flex-1 mx-2\">\n                        <textarea\n                          className=\"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 resize-none\"\n                          placeholder=\"Mesajınızı yazın...\"\n                          rows=\"2\"\n                          value={messageText}\n                          onChange={(e) => setMessageText(e.target.value)}\n                        ></textarea>\n                      </div>\n                      <button\n                        type=\"button\"\n                        className=\"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\"\n                      >\n                        <FaceSmileIcon className=\"h-5 w-5\" />\n                      </button>\n                      <button\n                        type=\"submit\"\n                        disabled={!messageText.trim()}\n                        className={`ml-2 p-2 rounded-full ${\n                          messageText.trim() \n                            ? 'bg-teal-600 text-white hover:bg-teal-700' \n                            : 'bg-gray-200 text-gray-400 cursor-not-allowed'\n                        } focus:outline-none`}\n                      >\n                        <PaperAirplaneIcon className=\"h-5 w-5\" />\n                      </button>\n                    </form>\n                  </div>\n                </>\n              ) : (\n                // Mesaj seçilmediğinde\n                <div className=\"flex-1 flex flex-col items-center justify-center p-6 bg-gray-50\">\n                  <div className=\"w-full max-w-md text-center\">\n                    <ChatBubbleLeftEllipsisIcon className=\"h-16 w-16 text-gray-300 mb-4 mx-auto\" />\n                    <h3 className=\"text-xl font-medium text-gray-800 mb-2\">Mesajlarınız</h3>\n                    <p className=\"text-gray-500 mx-auto\">\n                      Mesajlaşmaya başlamak için sol taraftan bir konuşma seçin veya yeni bir uzmanla iletişime geçin.\n                    </p>\n                    <div className=\"mt-6\">\n                      <Link\n                        to=\"/client/experts\"\n                        className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500\"\n                      >\n                        <UserIcon className=\"-ml-1 mr-2 h-5 w-5\" aria-hidden=\"true\" />\n                        Uzmanları Keşfedin\n                      </Link>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n} \n\nexport default ClientMessagesPage; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,GAAG,MAAM,uBAAuB;AACvC,SAASC,KAAK,QAAQ,iBAAiB;AACvC,OAAOC,EAAE,MAAM,kBAAkB;AACjC,SACEC,0BAA0B,EAC1BC,mBAAmB,EACnBC,QAAQ,EACRC,iBAAiB,EACjBC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,sBAAsB,EACtBC,SAAS,EACTC,eAAe,EACfC,qBAAqB,EACrBC,SAAS,EACTC,eAAe,EACfC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,QAAQ,EACRC,aAAa,EACbC,YAAY,QACP,6BAA6B;AACpC,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,EAAE,QAAQ,iBAAiB;AACpC,SAASC,IAAI,QAAQ,kBAAkB;;AAEvC;AACA;AACA;AAFA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM;IAAEC;EAAK,CAAC,GAAGhC,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACsC,aAAa,EAAEC,gBAAgB,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACwC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAAC0C,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC4C,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC8C,UAAU,EAAEC,aAAa,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgD,MAAM,EAAEC,SAAS,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACkD,MAAM,EAAEC,SAAS,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACoD,WAAW,EAAEC,cAAc,CAAC,GAAGrD,QAAQ,CAAC,IAAIsD,GAAG,CAAC,CAAC,CAAC;EACzD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGxD,QAAQ,CAAC,IAAIsD,GAAG,CAAC,CAAC,CAAC;EACzD,MAAMG,cAAc,GAAGvD,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMwD,gBAAgB,GAAGxD,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMyD,oBAAoB,GAAGzD,MAAM,CAAC,IAAI,CAAC;;EAEzC;EACAD,SAAS,CAAC,MAAM;IACd,MAAM2D,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACTG,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnD,MAAMC,gBAAgB,GAAG3D,EAAE,CAAC4D,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB,EAAE;QACpFC,IAAI,EAAE;UAAET;QAAM,CAAC;QACfU,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;QACpCC,YAAY,EAAE,IAAI;QAClBC,iBAAiB,EAAE,IAAI;QACvBC,oBAAoB,EAAE,CAAC;QACvBC,OAAO,EAAE;MACX,CAAC,CAAC;MAEFvB,SAAS,CAACc,gBAAgB,CAAC;;MAE3B;MACAA,gBAAgB,CAACU,EAAE,CAAC,SAAS,EAAE,MAAM;QACnCZ,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEC,gBAAgB,CAACW,EAAE,CAAC;QACnE;QACAX,gBAAgB,CAACY,IAAI,CAAC,aAAa,CAAC;MACtC,CAAC,CAAC;MAEFZ,gBAAgB,CAACU,EAAE,CAAC,YAAY,EAAGG,MAAM,IAAK;QAC5Cf,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEc,MAAM,CAAC;MACxD,CAAC,CAAC;MAEFb,gBAAgB,CAACU,EAAE,CAAC,eAAe,EAAGI,KAAK,IAAK;QAC9ChB,OAAO,CAACgB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD,CAAC,CAAC;MAEFd,gBAAgB,CAACU,EAAE,CAAC,WAAW,EAAGK,aAAa,IAAK;QAClDjB,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEgB,aAAa,CAAC;MACtE,CAAC,CAAC;MAEFf,gBAAgB,CAACU,EAAE,CAAC,iBAAiB,EAAGI,KAAK,IAAK;QAChDhB,OAAO,CAACgB,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC/D,CAAC,CAAC;MAEF,OAAO,MAAM;QACXd,gBAAgB,CAACgB,UAAU,CAAC,CAAC;MAC/B,CAAC;IACH;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAhF,SAAS,CAAC,MAAM;IACd,IAAIiD,MAAM,EAAE;MACVa,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;MAErD;MACA,MAAMkB,gBAAgB,GAAIC,OAAO,IAAK;QACpCpB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEmB,OAAO,CAAC;;QAE7C;QACA5C,gBAAgB,CAAC6C,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IACpCA,IAAI,CAACV,EAAE,KAAKO,OAAO,CAACI,cAAc,GAC9B;UAAE,GAAGD,IAAI;UAAEE,WAAW,EAAEL,OAAO,CAACM,OAAO;UAAEC,SAAS,EAAEP,OAAO,CAACQ,SAAS;UAAEC,MAAM,EAAET,OAAO,CAACU,QAAQ,KAAK1D,IAAI,CAACyC;QAAG,CAAC,GAC7GU,IACN,CAAC,CAAC;;QAEF;QACA7C,uBAAuB,CAACqD,eAAe,IAAI;UACzC,IAAIA,eAAe,IAAIX,OAAO,CAACI,cAAc,KAAKO,eAAe,CAAClB,EAAE,EAAE;YACpEjC,WAAW,CAACyC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;cAC5BR,EAAE,EAAEO,OAAO,CAACP,EAAE;cACdiB,QAAQ,EAAEV,OAAO,CAACU,QAAQ;cAC1BE,UAAU,EAAEZ,OAAO,CAACa,MAAM,CAACC,IAAI;cAC/BC,YAAY,EAAE,oCAAoCC,kBAAkB,CAAChB,OAAO,CAACa,MAAM,CAACC,IAAI,CAAC,qDAAqD;cAC9IG,IAAI,EAAEjB,OAAO,CAACM,OAAO;cACrBC,SAAS,EAAEP,OAAO,CAACQ,SAAS;cAC5BU,IAAI,EAAElB,OAAO,CAACmB;YAChB,CAAC,CAAC,CAAC;;YAEH;YACAC,UAAU,CAAC,MAAM;cAAA,IAAAC,qBAAA;cACf,CAAAA,qBAAA,GAAA/C,cAAc,CAACgD,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;gBAAEC,QAAQ,EAAE;cAAS,CAAC,CAAC;YAChE,CAAC,EAAE,GAAG,CAAC;UACT;UACA,OAAOb,eAAe;QACxB,CAAC,CAAC;MACJ,CAAC;MAED,MAAMc,iBAAiB,GAAIzB,OAAO,IAAK;QACrCpB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEmB,OAAO,CAAC;MACnD,CAAC;MAED,MAAM0B,sBAAsB,GAAIC,IAAI,IAAK;QACvCzD,cAAc,CAAC+B,IAAI,IAAI;UACrB,MAAM2B,MAAM,GAAG,IAAIzD,GAAG,CAAC8B,IAAI,CAAC;UAC5B,IAAI0B,IAAI,CAACE,MAAM,KAAK,QAAQ,EAAE;YAC5BD,MAAM,CAACE,GAAG,CAACH,IAAI,CAACI,MAAM,CAAC;UACzB,CAAC,MAAM;YACLH,MAAM,CAACI,MAAM,CAACL,IAAI,CAACI,MAAM,CAAC;UAC5B;UACA,OAAOH,MAAM;QACf,CAAC,CAAC;MACJ,CAAC;MAED,MAAMK,gBAAgB,GAAIN,IAAI,IAAK;QACjCrE,uBAAuB,CAACqD,eAAe,IAAI;UACzC,IAAIA,eAAe,IAAIgB,IAAI,CAACvB,cAAc,KAAKO,eAAe,CAAClB,EAAE,EAAE;YACjEpB,cAAc,CAAC4B,IAAI,IAAI,IAAI9B,GAAG,CAAC,CAAC,GAAG8B,IAAI,EAAE0B,IAAI,CAACI,MAAM,CAAC,CAAC,CAAC;UACzD;UACA,OAAOpB,eAAe;QACxB,CAAC,CAAC;MACJ,CAAC;MAED,MAAMuB,uBAAuB,GAAIP,IAAI,IAAK;QACxCtD,cAAc,CAAC4B,IAAI,IAAI;UACrB,MAAM2B,MAAM,GAAG,IAAIzD,GAAG,CAAC8B,IAAI,CAAC;UAC5B2B,MAAM,CAACI,MAAM,CAACL,IAAI,CAACI,MAAM,CAAC;UAC1B,OAAOH,MAAM;QACf,CAAC,CAAC;MACJ,CAAC;;MAED;MACA7D,MAAM,CAACyB,EAAE,CAAC,aAAa,EAAEO,gBAAgB,CAAC;MAC1ChC,MAAM,CAACyB,EAAE,CAAC,cAAc,EAAEiC,iBAAiB,CAAC;MAC5C1D,MAAM,CAACyB,EAAE,CAAC,oBAAoB,EAAEkC,sBAAsB,CAAC;MACvD3D,MAAM,CAACyB,EAAE,CAAC,aAAa,EAAEyC,gBAAgB,CAAC;MAC1ClE,MAAM,CAACyB,EAAE,CAAC,qBAAqB,EAAE0C,uBAAuB,CAAC;;MAEzD;MACA,OAAO,MAAM;QACXnE,MAAM,CAACoE,GAAG,CAAC,aAAa,EAAEpC,gBAAgB,CAAC;QAC3ChC,MAAM,CAACoE,GAAG,CAAC,cAAc,EAAEV,iBAAiB,CAAC;QAC7C1D,MAAM,CAACoE,GAAG,CAAC,oBAAoB,EAAET,sBAAsB,CAAC;QACxD3D,MAAM,CAACoE,GAAG,CAAC,aAAa,EAAEF,gBAAgB,CAAC;QAC3ClE,MAAM,CAACoE,GAAG,CAAC,qBAAqB,EAAED,uBAAuB,CAAC;MAC5D,CAAC;IACH;EACF,CAAC,EAAE,CAACnE,MAAM,EAAEf,IAAI,CAACyC,EAAE,CAAC,CAAC;;EAErB;EACA3E,SAAS,CAAC,MAAM;IACdsH,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMA,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFlF,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMmF,QAAQ,GAAG,MAAMpH,GAAG,CAACqH,GAAG,CAAC,yBAAyB,CAAC;;MAEzD;MACA,MAAMC,sBAAsB,GAAGF,QAAQ,CAACV,IAAI,CAACxE,aAAa,CAAC+C,GAAG,CAACsC,YAAY;QAAA,IAAAC,qBAAA,EAAAC,sBAAA;QAAA,OAAK;UAC9EjD,EAAE,EAAE+C,YAAY,CAAC/C,EAAE;UACnBkD,QAAQ,EAAEH,YAAY,CAACI,SAAS,CAACnD,EAAE;UACnCoD,UAAU,EAAEL,YAAY,CAACI,SAAS,CAAC9B,IAAI;UACvCgC,WAAW,EAAEN,YAAY,CAACI,SAAS,CAACG,IAAI,KAAK,QAAQ,GAAG,OAAO,GAAGP,YAAY,CAACI,SAAS,CAACG,IAAI;UAC7F1C,WAAW,EAAE,EAAAoC,qBAAA,GAAAD,YAAY,CAACnC,WAAW,cAAAoC,qBAAA,uBAAxBA,qBAAA,CAA0BnC,OAAO,KAAI,iBAAiB;UACnEC,SAAS,EAAE,EAAAmC,sBAAA,GAAAF,YAAY,CAACnC,WAAW,cAAAqC,sBAAA,uBAAxBA,sBAAA,CAA0BnC,SAAS,KAAIiC,YAAY,CAAChC,SAAS;UACxEC,MAAM,EAAE+B,YAAY,CAACnC,WAAW,GAAG,CAACmC,YAAY,CAACnC,WAAW,CAACc,MAAM,IAAIqB,YAAY,CAACnC,WAAW,CAACK,QAAQ,KAAK1D,IAAI,CAACyC,EAAE,GAAG,KAAK;UAC5HuD,MAAM,EAAE,oCAAoChC,kBAAkB,CAACwB,YAAY,CAACI,SAAS,CAAC9B,IAAI,CAAC,qDAAqD;UAChJe,MAAM,EAAE5D,WAAW,CAACgF,GAAG,CAACT,YAAY,CAACI,SAAS,CAACnD,EAAE,CAAC,GAAG,QAAQ,GAAG,SAAS;UACzEyD,OAAO,EAAE,KAAK;UACdC,QAAQ,EAAE;QACZ,CAAC;MAAA,CAAC,CAAC;MAEH/F,gBAAgB,CAACmF,sBAAsB,CAAC;IAC1C,CAAC,CAAC,OAAO3C,KAAK,EAAE;MACdhB,OAAO,CAACgB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD1E,KAAK,CAAC0E,KAAK,CAAC,wBAAwB,CAAC;IACvC,CAAC,SAAS;MACR1C,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACApC,SAAS,CAAC,MAAM;IACd,IAAIuC,oBAAoB,EAAE;MACxB+F,YAAY,CAAC/F,oBAAoB,CAACoC,EAAE,CAAC;;MAErC;MACA,IAAI1B,MAAM,EAAE;QACVa,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAExB,oBAAoB,CAACoC,EAAE,CAAC;QAC/D1B,MAAM,CAAC2B,IAAI,CAAC,mBAAmB,EAAErC,oBAAoB,CAACoC,EAAE,CAAC;MAC3D;IACF;EACF,CAAC,EAAE,CAACpC,oBAAoB,EAAEU,MAAM,CAAC,CAAC;;EAElC;EACA,MAAMqF,YAAY,GAAG,MAAOhD,cAAc,IAAK;IAC7C,IAAI;MACF,MAAMiC,QAAQ,GAAG,MAAMpH,GAAG,CAACqH,GAAG,CAAC,2BAA2BlC,cAAc,EAAE,CAAC;;MAE3E;MACA,MAAMiD,iBAAiB,GAAGhB,QAAQ,CAACV,IAAI,CAACpE,QAAQ,CAAC2C,GAAG,CAACF,OAAO,KAAK;QAC/DP,EAAE,EAAEO,OAAO,CAACP,EAAE;QACdiB,QAAQ,EAAEV,OAAO,CAACU,QAAQ;QAC1BE,UAAU,EAAEZ,OAAO,CAACa,MAAM,CAACC,IAAI;QAC/BC,YAAY,EAAE,oCAAoCC,kBAAkB,CAAChB,OAAO,CAACa,MAAM,CAACC,IAAI,CAAC,qDAAqD;QAC9IG,IAAI,EAAEjB,OAAO,CAACM,OAAO;QACrBC,SAAS,EAAEP,OAAO,CAACQ,SAAS;QAC5BU,IAAI,EAAElB,OAAO,CAACmB,MAAM;QACpBmC,WAAW,EAAEtD,OAAO,CAACsD;MACvB,CAAC,CAAC,CAAC;MAEH9F,WAAW,CAAC6F,iBAAiB,CAAC;;MAE9B;MACAjC,UAAU,CAAC,MAAM;QAAA,IAAAmC,sBAAA;QACf,CAAAA,sBAAA,GAAAjF,cAAc,CAACgD,OAAO,cAAAiC,sBAAA,uBAAtBA,sBAAA,CAAwBhC,cAAc,CAAC;UAAEC,QAAQ,EAAE;QAAS,CAAC,CAAC;MAChE,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAO5B,KAAK,EAAE;MACdhB,OAAO,CAACgB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD1E,KAAK,CAAC0E,KAAK,CAAC,sBAAsB,CAAC;IACrC;EACF,CAAC;EACD;EACA,MAAM4D,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAAC/F,WAAW,CAACgG,IAAI,CAAC,CAAC,IAAI,CAACpG,oBAAoB,EAAE;IAElDuB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;MACpC6E,UAAU,EAAErG,oBAAoB,CAACsF,QAAQ;MACzCrC,OAAO,EAAE7C,WAAW,CAACgG,IAAI,CAAC;IAC5B,CAAC,CAAC;IAEF,IAAI;MACF,MAAMpB,QAAQ,GAAG,MAAMpH,GAAG,CAAC0I,IAAI,CAAC,gBAAgB,EAAE;QAChDD,UAAU,EAAErG,oBAAoB,CAACsF,QAAQ;QACzCrC,OAAO,EAAE7C,WAAW,CAACgG,IAAI,CAAC;MAC5B,CAAC,CAAC;MAEF7E,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEwD,QAAQ,CAACV,IAAI,CAAC;;MAEjD;MACA,MAAMiC,UAAU,GAAG;QACjBnE,EAAE,EAAE4C,QAAQ,CAACV,IAAI,CAAC3B,OAAO,CAACP,EAAE;QAC5BiB,QAAQ,EAAE1D,IAAI,CAACyC,EAAE;QACjBmB,UAAU,EAAE,GAAG5D,IAAI,CAAC6G,SAAS,IAAI7G,IAAI,CAAC8G,QAAQ,EAAE;QAChD/C,YAAY,EAAE,oCAAoCC,kBAAkB,CAAChE,IAAI,CAAC6G,SAAS,CAAC,IAAI7C,kBAAkB,CAAChE,IAAI,CAAC8G,QAAQ,CAAC,qDAAqD;QAC9K7C,IAAI,EAAExD,WAAW,CAACgG,IAAI,CAAC,CAAC;QACxBlD,SAAS,EAAE8B,QAAQ,CAACV,IAAI,CAAC3B,OAAO,CAACQ,SAAS;QAC1CU,IAAI,EAAE;MACR,CAAC;MAED1D,WAAW,CAACyC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE2D,UAAU,CAAC,CAAC;MAC1ClG,cAAc,CAAC,EAAE,CAAC;;MAElB;MACAN,gBAAgB,CAAC6C,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IACpCA,IAAI,CAACV,EAAE,KAAKpC,oBAAoB,CAACoC,EAAE,GAC/B;QAAE,GAAGU,IAAI;QAAEE,WAAW,EAAE5C,WAAW,CAACgG,IAAI,CAAC,CAAC;QAAElD,SAAS,EAAEqD,UAAU,CAACrD;MAAU,CAAC,GAC7EJ,IACN,CAAC,CAAC;;MAEF;MACAiB,UAAU,CAAC,MAAM;QAAA,IAAA2C,sBAAA;QACf,CAAAA,sBAAA,GAAAzF,cAAc,CAACgD,OAAO,cAAAyC,sBAAA,uBAAtBA,sBAAA,CAAwBxC,cAAc,CAAC;UAAEC,QAAQ,EAAE;QAAS,CAAC,CAAC;MAChE,CAAC,EAAE,GAAG,CAAC;IAET,CAAC,CAAC,OAAO5B,KAAK,EAAE;MACdhB,OAAO,CAACgB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD1E,KAAK,CAAC0E,KAAK,CAAC,qBAAqB,CAAC;IACpC;EACF,CAAC;EAED9E,SAAS,CAAC,MAAM;IACd;IACA,IAAIwD,cAAc,CAACgD,OAAO,IAAI9C,oBAAoB,CAAC8C,OAAO,EAAE;MAC1D9C,oBAAoB,CAAC8C,OAAO,CAAC0C,SAAS,GAAGxF,oBAAoB,CAAC8C,OAAO,CAAC2C,YAAY;IACpF;EACF,CAAC,EAAE,CAAC1G,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAM2G,iBAAiB,GAAIC,CAAC,IAAK;IAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBZ,WAAW,CAAC,CAAC;EACf,CAAC;;EAED;EACA,MAAMa,wBAAwB,GAAI7B,YAAY,IAAK;IACjDlF,uBAAuB,CAACkF,YAAY,CAAC;EACvC,CAAC;;EAED;EACA,MAAM8B,iBAAiB,GAAIC,UAAU,IAAK;IACxC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,KAAK,GAAG,IAAID,IAAI,CAAC,CAAC;IACxB,MAAME,SAAS,GAAG,IAAIF,IAAI,CAACC,KAAK,CAAC;IACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IAE1C,IAAIL,IAAI,CAACM,YAAY,CAAC,CAAC,KAAKJ,KAAK,CAACI,YAAY,CAAC,CAAC,EAAE;MAChD,OAAOvI,MAAM,CAACiI,IAAI,EAAE,OAAO,EAAE;QAAEO,MAAM,EAAEvI;MAAG,CAAC,CAAC;IAC9C,CAAC,MAAM,IAAIgI,IAAI,CAACM,YAAY,CAAC,CAAC,KAAKH,SAAS,CAACG,YAAY,CAAC,CAAC,EAAE;MAC3D,OAAO,MAAM,GAAGvI,MAAM,CAACiI,IAAI,EAAE,OAAO,EAAE;QAAEO,MAAM,EAAEvI;MAAG,CAAC,CAAC;IACvD,CAAC,MAAM;MACL,OAAOD,MAAM,CAACiI,IAAI,EAAE,cAAc,EAAE;QAAEO,MAAM,EAAEvI;MAAG,CAAC,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAMwI,qBAAqB,GAAG7H,aAAa,CAACU,MAAM,CAACsC,IAAI,IAAI;IACzD;IACA,MAAM8E,aAAa,GAAG9E,IAAI,CAAC0C,UAAU,CAACqC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxH,UAAU,CAACuH,WAAW,CAAC,CAAC,CAAC,IACjE/E,IAAI,CAACE,WAAW,CAAC6E,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxH,UAAU,CAACuH,WAAW,CAAC,CAAC,CAAC;;IAEtF;IACA,MAAME,aAAa,GAAGvH,MAAM,KAAK,KAAK,IAChBA,MAAM,KAAK,QAAQ,IAAIsC,IAAI,CAACM,MAAO,IACnC5C,MAAM,KAAK,UAAU,IAAIsC,IAAI,CAACgD,QAAS,IACvCtF,MAAM,KAAK,SAAS,IAAIsC,IAAI,CAAC+C,OAAQ;IAE3D,OAAO+B,aAAa,IAAIG,aAAa;EACvC,CAAC,CAAC;;EAEF;EACA,MAAMC,UAAU,GAAI5F,EAAE,IAAK;IACzBrC,gBAAgB,CAACkI,iBAAiB,IAChCA,iBAAiB,CAACpF,GAAG,CAACC,IAAI,IACxBA,IAAI,CAACV,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGU,IAAI;MAAE+C,OAAO,EAAE,CAAC/C,IAAI,CAAC+C;IAAQ,CAAC,GAAG/C,IACzD,CACF,CAAC;IAED,IAAI9C,oBAAoB,IAAIA,oBAAoB,CAACoC,EAAE,KAAKA,EAAE,EAAE;MAC1DnC,uBAAuB,CAAC2C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEiD,OAAO,EAAE,CAACjD,IAAI,CAACiD;MAAQ,CAAC,CAAC,CAAC;IACxE;EACF,CAAC;;EAED;EACA,MAAMqC,aAAa,GAAI9F,EAAE,IAAK;IAC5BrC,gBAAgB,CAACkI,iBAAiB,IAChCA,iBAAiB,CAACpF,GAAG,CAACC,IAAI,IACxBA,IAAI,CAACV,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGU,IAAI;MAAEgD,QAAQ,EAAE,CAAChD,IAAI,CAACgD;IAAS,CAAC,GAAGhD,IAC3D,CACF,CAAC;IAED,IAAI9C,oBAAoB,IAAIA,oBAAoB,CAACoC,EAAE,KAAKA,EAAE,EAAE;MAC1DnC,uBAAuB,CAAC2C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEkD,QAAQ,EAAE,CAAClD,IAAI,CAACkD;MAAS,CAAC,CAAC,CAAC;IAC1E;EACF,CAAC;EAED,IAAIlG,SAAS,EAAE;IACb,oBACEN,OAAA;MAAK6I,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5D9I,OAAA;QAAK6I,SAAS,EAAC;MAA2E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9F,CAAC;EAEV;EAEA,oBACElJ,OAAA;IAAK6I,SAAS,EAAC,+BAA+B;IAAAC,QAAA,eAC5C9I,OAAA;MAAK6I,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1D9I,OAAA;QAAK6I,SAAS,EAAC,0EAA0E;QAAAC,QAAA,eACvF9I,OAAA;UAAK6I,SAAS,EAAC,uEAAuE;UAAAC,QAAA,gBACpF9I,OAAA;YAAA8I,QAAA,gBACE9I,OAAA;cAAI6I,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7DlJ,OAAA;cAAG6I,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAElC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNlJ,OAAA;YAAK6I,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C9I,OAAA,CAACF,IAAI;cACHqJ,EAAE,EAAC,iBAAiB;cACpBN,SAAS,EAAC,sPAAsP;cAAAC,QAAA,gBAEhQ9I,OAAA,CAACN,aAAa;gBAACmJ,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAE5C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPlJ,OAAA,CAACF,IAAI;cACHqJ,EAAE,EAAC,sBAAsB;cACzBN,SAAS,EAAC,qOAAqO;cAAAC,QAAA,gBAE/O9I,OAAA,CAACL,YAAY;gBAACkJ,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,qBAE3C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlJ,OAAA;QAAK6I,SAAS,EAAC,+CAA+C;QAAAC,QAAA,eAC5D9I,OAAA;UAAK6I,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBAEzC9I,OAAA;YAAK6I,SAAS,EAAC,kEAAkE;YAAAC,QAAA,gBAC/E9I,OAAA;cAAK6I,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD9I,OAAA;gBAAI6I,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,gBACnE9I,OAAA,CAACvB,0BAA0B;kBAACoK,SAAS,EAAC;gBAA4B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,YAEvE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLlJ,OAAA;gBAAK6I,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B9I,OAAA;kBACEoJ,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,0BAAqB;kBACjCR,SAAS,EAAC,iHAAiH;kBAC3HS,KAAK,EAAEtI,UAAW;kBAClBuI,QAAQ,EAAG/B,CAAC,IAAKvG,aAAa,CAACuG,CAAC,CAACgC,MAAM,CAACF,KAAK;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACFlJ,OAAA,CAACtB,mBAAmB;kBAACmK,SAAS,EAAC;gBAA+C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC,eACNlJ,OAAA;gBAAK6I,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAClC9I,OAAA;kBACE6I,SAAS,EAAE,kCAAkC3H,MAAM,KAAK,KAAK,GACzD,2BAA2B,GAC3B,6CAA6C,EAAG;kBACpDuI,OAAO,EAAEA,CAAA,KAAMtI,SAAS,CAAC,KAAK,CAAE;kBAAA2H,QAAA,EACjC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlJ,OAAA;kBACE6I,SAAS,EAAE,kCAAkC3H,MAAM,KAAK,QAAQ,GAC5D,2BAA2B,GAC3B,6CAA6C,EAAG;kBACpDuI,OAAO,EAAEA,CAAA,KAAMtI,SAAS,CAAC,QAAQ,CAAE;kBAAA2H,QAAA,EACpC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlJ,OAAA;kBACE6I,SAAS,EAAE,kCAAkC3H,MAAM,KAAK,SAAS,GAC7D,2BAA2B,GAC3B,6CAA6C,EAAG;kBACpDuI,OAAO,EAAEA,CAAA,KAAMtI,SAAS,CAAC,SAAS,CAAE;kBAAA2H,QAAA,EACrC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlJ,OAAA;kBACE6I,SAAS,EAAE,kCAAkC3H,MAAM,KAAK,UAAU,GAC9D,2BAA2B,GAC3B,6CAA6C,EAAG;kBACpDuI,OAAO,EAAEA,CAAA,KAAMtI,SAAS,CAAC,UAAU,CAAE;kBAAA2H,QAAA,EACtC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlJ,OAAA;cACE0J,GAAG,EAAE9H,gBAAiB;cACtB+H,KAAK,EAAE;gBACLC,MAAM,EAAE,oBAAoB;gBAC5BC,SAAS,EAAE,MAAM;gBACjBC,cAAc,EAAE,MAAM;gBACtBC,cAAc,EAAE;cAClB,CAAE;cAAAjB,QAAA,EAEDT,qBAAqB,CAAC2B,MAAM,KAAK,CAAC,gBACjChK,OAAA;gBAAK6I,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,GAENb,qBAAqB,CAAC9E,GAAG,CAACsC,YAAY,iBACpC7F,OAAA;gBAEE6I,SAAS,EAAE,sEACT,CAAAnI,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEoC,EAAE,MAAK+C,YAAY,CAAC/C,EAAE,GAAG,YAAY,GAAG,EAAE,IAC9D+C,YAAY,CAAC/B,MAAM,GAAG,8BAA8B,GAAG,EAAE,EAAG;gBAChE2F,OAAO,EAAEA,CAAA,KAAM/B,wBAAwB,CAAC7B,YAAY,CAAE;gBAAAiD,QAAA,gBAEtD9I,OAAA;kBAAK6I,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzC9I,OAAA;oBAAK6I,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrC9I,OAAA;sBACEiK,GAAG,EAAEpE,YAAY,CAACQ,MAAO;sBACzB6D,GAAG,EAAErE,YAAY,CAACK,UAAW;sBAC7B2C,SAAS,EAAE,0BACT,CAAAnI,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEoC,EAAE,MAAK+C,YAAY,CAAC/C,EAAE,GACxC,sBAAsB,GACtB,EAAE;oBACL;sBAAAiG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,EACDrD,YAAY,CAACX,MAAM,KAAK,QAAQ,iBAC/BlF,OAAA;sBAAM6I,SAAS,EAAC;oBAAyF;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CACjH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACNlJ,OAAA;oBAAK6I,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC7B9I,OAAA;sBAAK6I,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,gBAC/C9I,OAAA;wBAAI6I,SAAS,EAAE,uBAAuBhD,YAAY,CAAC/B,MAAM,GAAG,eAAe,GAAG,eAAe,EAAG;wBAAAgF,QAAA,EAC7FjD,YAAY,CAACK;sBAAU;wBAAA6C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtB,CAAC,eACLlJ,OAAA;wBAAK6I,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,GACzCjD,YAAY,CAACU,OAAO,iBACnBvG,OAAA,CAACP,QAAQ;0BAACoJ,SAAS,EAAC;wBAAsC;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAC7D,eACDlJ,OAAA;0BAAM6I,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,EACpCnB,iBAAiB,CAAC9B,YAAY,CAACjC,SAAS;wBAAC;0BAAAmF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNlJ,OAAA;sBAAG6I,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEjD,YAAY,CAACM;oBAAW;sBAAA4C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnElJ,OAAA;sBAAG6I,SAAS,EAAE,yBACZhD,YAAY,CAAC/B,MAAM,GAAG,2BAA2B,GAAG,eAAe,EAClE;sBAAAgF,QAAA,EACAjD,YAAY,CAACnC;oBAAW;sBAAAqF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC,eACJlJ,OAAA;sBAAK6I,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrD9I,OAAA;wBAAM6I,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EACpCjD,YAAY,CAACX,MAAM,KAAK,QAAQ,GAC7B,WAAW,GACXW,YAAY,CAACsE,QAAQ,GACnB,gBAAgBtE,YAAY,CAACsE,QAAQ,EAAE,GACvC;sBAAE;wBAAApB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACPlJ,OAAA;wBAAK6I,SAAS,EAAC,gBAAgB;wBAAAC,QAAA,gBAC7B9I,OAAA;0BACEyJ,OAAO,EAAGjC,CAAC,IAAK;4BACdA,CAAC,CAAC4C,eAAe,CAAC,CAAC;4BACnB1B,UAAU,CAAC7C,YAAY,CAAC/C,EAAE,CAAC;0BAC7B,CAAE;0BACF+F,SAAS,EAAC,qCAAqC;0BAAAC,QAAA,eAE/C9I,OAAA,CAACP,QAAQ;4BAACoJ,SAAS,EAAE,WAAWhD,YAAY,CAACU,OAAO,GAAG,8BAA8B,GAAG,EAAE;0BAAG;4BAAAwC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1F,CAAC,eACTlJ,OAAA;0BACEyJ,OAAO,EAAGjC,CAAC,IAAK;4BACdA,CAAC,CAAC4C,eAAe,CAAC,CAAC;4BACnBxB,aAAa,CAAC/C,YAAY,CAAC/C,EAAE,CAAC;0BAChC,CAAE;0BACF+F,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,eAE7C9I,OAAA,CAACR,cAAc;4BAACqJ,SAAS,EAAC;0BAAS;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EACLrD,YAAY,CAAC/B,MAAM,iBAClB9D,OAAA;kBAAM6I,SAAS,EAAC,yGAAyG;kBAAAC,QAAA,EAAC;gBAE1H;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACP;cAAA,GA5EIrD,YAAY,CAAC/C,EAAE;gBAAAiG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6EjB,CACN;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlJ,OAAA;YAAK6I,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EACrDpI,oBAAoB,gBACnBV,OAAA,CAAAE,SAAA;cAAA4I,QAAA,gBAEE9I,OAAA;gBAAK6I,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,gBACtF9I,OAAA;kBAAK6I,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC9I,OAAA;oBAAK6I,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5B9I,OAAA;sBACEiK,GAAG,EAAEvJ,oBAAoB,CAAC2F,MAAO;sBACjC6D,GAAG,EAAExJ,oBAAoB,CAACwF,UAAW;sBACrC2C,SAAS,EAAC;oBAAwB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC,EACDxI,oBAAoB,CAACwE,MAAM,KAAK,QAAQ,iBACvClF,OAAA;sBAAM6I,SAAS,EAAC;oBAAyF;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CACjH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACNlJ,OAAA;oBAAA8I,QAAA,gBACE9I,OAAA;sBAAI6I,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAC9CpI,oBAAoB,CAACwF;oBAAU;sBAAA6C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9B,CAAC,eACLlJ,OAAA;sBAAG6I,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GACjCpI,oBAAoB,CAACyF,WAAW,EAAC,GAAC,EAAC,KAAK,EACxCzF,oBAAoB,CAACwE,MAAM,KAAK,QAAQ,GACrC,WAAW,GACXxE,oBAAoB,CAACyJ,QAAQ,GAC3B,gBAAgBzJ,oBAAoB,CAACyJ,QAAQ,EAAE,GAC/C,EAAE;oBAAA;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlJ,OAAA;kBAAK6I,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C9I,OAAA,CAACF,IAAI;oBACHqJ,EAAE,EAAE,mBAAmBzI,oBAAoB,CAACsF,QAAQ,EAAG;oBACvD6C,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,eAE5D9I,OAAA,CAACrB,QAAQ;sBAACkK,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACPlJ,OAAA,CAACF,IAAI;oBACHqJ,EAAE,EAAE,+BAA+BzI,oBAAoB,CAACsF,QAAQ,EAAG;oBACnE6C,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,eAE5D9I,OAAA,CAACZ,SAAS;sBAACyJ,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC,eACPlJ,OAAA,CAACF,IAAI;oBACHqJ,EAAE,EAAE,2BAA2BzI,oBAAoB,CAACsF,QAAQ,EAAG;oBAC/D6C,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,eAE5D9I,OAAA,CAACd,eAAe;sBAAC2J,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC,eACPlJ,OAAA;oBAAQ6I,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,eAClE9I,OAAA,CAAChB,sBAAsB;sBAAC6J,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNlJ,OAAA;gBACE0J,GAAG,EAAE7H,oBAAqB;gBAC1BgH,SAAS,EAAC,gBAAgB;gBAC1Bc,KAAK,EAAE;kBACLC,MAAM,EAAE,oBAAoB;kBAC5BC,SAAS,EAAE,MAAM;kBACjBC,cAAc,EAAE,MAAM;kBACtBC,cAAc,EAAE;gBAClB,CAAE;gBAAAjB,QAAA,GAEDlI,QAAQ,CAAC2C,GAAG,CAAC,CAACF,OAAO,EAAEgH,KAAK,KAAK;kBAChC,MAAMC,QAAQ,GAAGjH,OAAO,CAACU,QAAQ,KAAK1D,IAAI,CAACyC,EAAE;kBAC7C,MAAMyH,UAAU,GAAGF,KAAK,KAAK,CAAC,IAAIzJ,QAAQ,CAACyJ,KAAK,GAAG,CAAC,CAAC,CAACtG,QAAQ,KAAKV,OAAO,CAACU,QAAQ;kBAEnF,oBACE/D,OAAA;oBAAsB6I,SAAS,EAAE,QAAQyB,QAAQ,GAAG,aAAa,GAAG,eAAe,OAAQ;oBAAAxB,QAAA,GACxF,CAACwB,QAAQ,IAAIC,UAAU,iBACtBvK,OAAA;sBACEiK,GAAG,EAAE5G,OAAO,CAACe,YAAa;sBAC1B8F,GAAG,EAAE7G,OAAO,CAACY,UAAW;sBACxB4E,SAAS,EAAC;oBAAgC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C,CACF,EACA,CAACoB,QAAQ,IAAI,CAACC,UAAU,iBAAIvK,OAAA;sBAAK6I,SAAS,EAAC;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC7DlJ,OAAA;sBACE6I,SAAS,EAAE,yDACTyB,QAAQ,GACJ,wCAAwC,GACxC,+DAA+D,EAClE;sBAAAxB,QAAA,gBAEH9I,OAAA;wBAAG6I,SAAS,EAAC,SAAS;wBAAAC,QAAA,EAAEzF,OAAO,CAACiB;sBAAI;wBAAAyE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACzClJ,OAAA;wBAAK6I,SAAS,EAAE,gBAAgByB,QAAQ,GAAG,eAAe,GAAG,eAAe,gCAAiC;wBAAAxB,QAAA,GAC1GnB,iBAAiB,CAACtE,OAAO,CAACO,SAAS,CAAC,EACpC0G,QAAQ,iBACPtK,OAAA,CAACX,eAAe;0BAACwJ,SAAS,EAAC,cAAc;0BAAC2B,KAAK,EAAEnH,OAAO,CAACkB,IAAI,GAAG,QAAQ,GAAG;wBAAW;0BAAAwE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CACzF;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,EACLoB,QAAQ,IAAIC,UAAU,iBACrBvK,OAAA;sBACEiK,GAAG,EAAE5G,OAAO,CAACe,YAAa;sBAC1B8F,GAAG,EAAE7G,OAAO,CAACY,UAAW;sBACxB4E,SAAS,EAAC;oBAAgC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C,CACF,EACAoB,QAAQ,IAAI,CAACC,UAAU,iBAAIvK,OAAA;sBAAK6I,SAAS,EAAC;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA,GA/BpD7F,OAAO,CAACP,EAAE;oBAAAiG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAgCf,CAAC;gBAEV,CAAC,CAAC,eACFlJ,OAAA;kBAAK0J,GAAG,EAAE/H;gBAAe;kBAAAoH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eAGNlJ,OAAA;gBAAK6I,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,eACpD9I,OAAA;kBAAMyK,QAAQ,EAAElD,iBAAkB;kBAACsB,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC3D9I,OAAA;oBACEoJ,IAAI,EAAC,QAAQ;oBACbP,SAAS,EAAC,uEAAuE;oBAAAC,QAAA,eAEjF9I,OAAA,CAACnB,aAAa;sBAACgK,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACTlJ,OAAA;oBAAK6I,SAAS,EAAC,aAAa;oBAAAC,QAAA,eAC1B9I,OAAA;sBACE6I,SAAS,EAAC,iHAAiH;sBAC3HQ,WAAW,EAAC,yCAAqB;sBACjCqB,IAAI,EAAC,GAAG;sBACRpB,KAAK,EAAExI,WAAY;sBACnByI,QAAQ,EAAG/B,CAAC,IAAKzG,cAAc,CAACyG,CAAC,CAACgC,MAAM,CAACF,KAAK;oBAAE;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACNlJ,OAAA;oBACEoJ,IAAI,EAAC,QAAQ;oBACbP,SAAS,EAAC,uEAAuE;oBAAAC,QAAA,eAEjF9I,OAAA,CAAClB,aAAa;sBAAC+J,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACTlJ,OAAA;oBACEoJ,IAAI,EAAC,QAAQ;oBACbuB,QAAQ,EAAE,CAAC7J,WAAW,CAACgG,IAAI,CAAC,CAAE;oBAC9B+B,SAAS,EAAE,yBACT/H,WAAW,CAACgG,IAAI,CAAC,CAAC,GACd,0CAA0C,GAC1C,8CAA8C,qBAC9B;oBAAAgC,QAAA,eAEtB9I,OAAA,CAACpB,iBAAiB;sBAACiK,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,eACN,CAAC;YAAA;YAEH;YACAlJ,OAAA;cAAK6I,SAAS,EAAC,iEAAiE;cAAAC,QAAA,eAC9E9I,OAAA;gBAAK6I,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1C9I,OAAA,CAACvB,0BAA0B;kBAACoK,SAAS,EAAC;gBAAsC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/ElJ,OAAA;kBAAI6I,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxElJ,OAAA;kBAAG6I,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAErC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJlJ,OAAA;kBAAK6I,SAAS,EAAC,MAAM;kBAAAC,QAAA,eACnB9I,OAAA,CAACF,IAAI;oBACHqJ,EAAE,EAAC,iBAAiB;oBACpBN,SAAS,EAAC,wNAAwN;oBAAAC,QAAA,gBAElO9I,OAAA,CAACrB,QAAQ;sBAACkK,SAAS,EAAC,oBAAoB;sBAAC,eAAY;oBAAM;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gCAEhE;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAA9I,EAAA,CA7sBKD,kBAAkB;EAAA,QACL9B,OAAO;AAAA;AAAAuM,EAAA,GADpBzK,kBAAkB;AA+sBxB,eAAeA,kBAAkB;AAAC,IAAAyK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}