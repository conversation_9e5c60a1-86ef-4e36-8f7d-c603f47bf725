{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'mindre än en sekund',\n    other: 'mindre än {{count}} sekunder'\n  },\n  xSeconds: {\n    one: 'en sekund',\n    other: '{{count}} sekunder'\n  },\n  halfAMinute: 'en halv minut',\n  lessThanXMinutes: {\n    one: 'mindre än en minut',\n    other: 'mindre än {{count}} minuter'\n  },\n  xMinutes: {\n    one: 'en minut',\n    other: '{{count}} minuter'\n  },\n  aboutXHours: {\n    one: 'ungefär en timme',\n    other: 'ungefär {{count}} timmar'\n  },\n  xHours: {\n    one: 'en timme',\n    other: '{{count}} timmar'\n  },\n  xDays: {\n    one: 'en dag',\n    other: '{{count}} dagar'\n  },\n  aboutXWeeks: {\n    one: 'ungefär en vecka',\n    other: 'ungefär {{count}} vecka'\n  },\n  xWeeks: {\n    one: 'en vecka',\n    other: '{{count}} vecka'\n  },\n  aboutXMonths: {\n    one: 'ungefär en månad',\n    other: 'ungefär {{count}} månader'\n  },\n  xMonths: {\n    one: 'en månad',\n    other: '{{count}} månader'\n  },\n  aboutXYears: {\n    one: 'ungefär ett år',\n    other: 'ungefär {{count}} år'\n  },\n  xYears: {\n    one: 'ett år',\n    other: '{{count}} år'\n  },\n  overXYears: {\n    one: 'över ett år',\n    other: 'över {{count}} år'\n  },\n  almostXYears: {\n    one: 'nästan ett år',\n    other: 'nästan {{count}} år'\n  }\n};\nvar wordMapping = ['noll', 'en', 'två', 'tre', 'fyra', 'fem', 'sex', 'sju', 'åtta', 'nio', 'tio', 'elva', 'tolv'];\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    if (options && options.onlyNumeric) {\n      result = tokenValue.other.replace('{{count}}', String(count));\n    } else {\n      result = tokenValue.other.replace('{{count}}', count < 13 ? wordMapping[count] : String(count));\n    }\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'om ' + result;\n    } else {\n      return result + ' sedan';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "wordMapping", "formatDistance", "token", "count", "options", "result", "tokenValue", "onlyNumeric", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/Projeler/kidgarden/burky_root_web/node_modules/date-fns/esm/locale/sv/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'mindre än en sekund',\n    other: 'mindre än {{count}} sekunder'\n  },\n  xSeconds: {\n    one: 'en sekund',\n    other: '{{count}} sekunder'\n  },\n  halfAMinute: 'en halv minut',\n  lessThanXMinutes: {\n    one: 'mindre än en minut',\n    other: 'mindre än {{count}} minuter'\n  },\n  xMinutes: {\n    one: 'en minut',\n    other: '{{count}} minuter'\n  },\n  aboutXHours: {\n    one: 'ungefär en timme',\n    other: 'ungefär {{count}} timmar'\n  },\n  xHours: {\n    one: 'en timme',\n    other: '{{count}} timmar'\n  },\n  xDays: {\n    one: 'en dag',\n    other: '{{count}} dagar'\n  },\n  aboutXWeeks: {\n    one: 'ungefär en vecka',\n    other: 'ungefär {{count}} vecka'\n  },\n  xWeeks: {\n    one: 'en vecka',\n    other: '{{count}} vecka'\n  },\n  aboutXMonths: {\n    one: 'ungefär en månad',\n    other: 'ungefär {{count}} månader'\n  },\n  xMonths: {\n    one: 'en månad',\n    other: '{{count}} månader'\n  },\n  aboutXYears: {\n    one: 'ungefär ett år',\n    other: 'ungefär {{count}} år'\n  },\n  xYears: {\n    one: 'ett år',\n    other: '{{count}} år'\n  },\n  overXYears: {\n    one: 'över ett år',\n    other: 'över {{count}} år'\n  },\n  almostXYears: {\n    one: 'nästan ett år',\n    other: 'nästan {{count}} år'\n  }\n};\nvar wordMapping = ['noll', 'en', 'två', 'tre', 'fyra', 'fem', 'sex', 'sju', 'åtta', 'nio', 'tio', 'elva', 'tolv'];\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    if (options && options.onlyNumeric) {\n      result = tokenValue.other.replace('{{count}}', String(count));\n    } else {\n      result = tokenValue.other.replace('{{count}}', count < 13 ? wordMapping[count] : String(count));\n    }\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'om ' + result;\n    } else {\n      return result + ' sedan';\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,eAAe;EAC5BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXN,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNP,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLR,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXT,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZX,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNd,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVf,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZhB,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,WAAW,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;AACjH,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGzB,oBAAoB,CAACqB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACvB,GAAG;EACzB,CAAC,MAAM;IACL,IAAIqB,OAAO,IAAIA,OAAO,CAACG,WAAW,EAAE;MAClCF,MAAM,GAAGC,UAAU,CAACtB,KAAK,CAACwB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACN,KAAK,CAAC,CAAC;IAC/D,CAAC,MAAM;MACLE,MAAM,GAAGC,UAAU,CAACtB,KAAK,CAACwB,OAAO,CAAC,WAAW,EAAEL,KAAK,GAAG,EAAE,GAAGH,WAAW,CAACG,KAAK,CAAC,GAAGM,MAAM,CAACN,KAAK,CAAC,CAAC;IACjG;EACF;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACM,SAAS,EAAE;IAC/D,IAAIN,OAAO,CAACO,UAAU,IAAIP,OAAO,CAACO,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,KAAK,GAAGN,MAAM;IACvB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,QAAQ;IAC1B;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}