{"ast": null, "code": "var t;\nimport r from \"react\";\nlet a = (t = r.startTransition) != null ? t : function (i) {\n  i();\n};\nexport { a as startTransition };", "map": {"version": 3, "names": ["t", "r", "a", "startTransition", "i"], "sources": ["C:/burky root/burky_root_web/client/node_modules/@headlessui/react/dist/utils/start-transition.js"], "sourcesContent": ["var t;import r from\"react\";let a=(t=r.startTransition)!=null?t:function(i){i()};export{a as startTransition};\n"], "mappings": "AAAA,IAAIA,CAAC;AAAC,OAAOC,CAAC,MAAK,OAAO;AAAC,IAAIC,CAAC,GAAC,CAACF,CAAC,GAACC,CAAC,CAACE,eAAe,KAAG,IAAI,GAACH,CAAC,GAAC,UAASI,CAAC,EAAC;EAACA,CAAC,CAAC,CAAC;AAAA,CAAC;AAAC,SAAOF,CAAC,IAAIC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}