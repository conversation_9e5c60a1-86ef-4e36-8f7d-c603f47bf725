{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'bir saniyeden az',\n    other: '{{count}} saniyeden az'\n  },\n  xSeconds: {\n    one: '1 saniye',\n    other: '{{count}} saniye'\n  },\n  halfAMinute: 'yarım dakika',\n  lessThanXMinutes: {\n    one: 'bir dakikadan az',\n    other: '{{count}} dakikadan az'\n  },\n  xMinutes: {\n    one: '1 dakika',\n    other: '{{count}} dakika'\n  },\n  aboutXHours: {\n    one: 'yaklaşık 1 saat',\n    other: 'yaklaşık {{count}} saat'\n  },\n  xHours: {\n    one: '1 saat',\n    other: '{{count}} saat'\n  },\n  xDays: {\n    one: '1 gün',\n    other: '{{count}} gün'\n  },\n  aboutXWeeks: {\n    one: 'yaklaşık 1 hafta',\n    other: 'yaklaşık {{count}} hafta'\n  },\n  xWeeks: {\n    one: '1 hafta',\n    other: '{{count}} hafta'\n  },\n  aboutXMonths: {\n    one: 'yaklaşık 1 ay',\n    other: 'yaklaşık {{count}} ay'\n  },\n  xMonths: {\n    one: '1 ay',\n    other: '{{count}} ay'\n  },\n  aboutXYears: {\n    one: 'yaklaşık 1 yıl',\n    other: 'yaklaşık {{count}} yıl'\n  },\n  xYears: {\n    one: '1 yıl',\n    other: '{{count}} yıl'\n  },\n  overXYears: {\n    one: '1 yıldan fazla',\n    other: '{{count}} yıldan fazla'\n  },\n  almostXYears: {\n    one: 'neredeyse 1 yıl',\n    other: 'neredeyse {{count}} yıl'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', count.toString());\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + ' sonra';\n    } else {\n      return result + ' önce';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "toString", "addSuffix", "comparison"], "sources": ["C:/Projeler/kidgarden/burky_root_web/node_modules/date-fns/esm/locale/tr/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'bir saniyeden az',\n    other: '{{count}} saniyeden az'\n  },\n  xSeconds: {\n    one: '1 saniye',\n    other: '{{count}} saniye'\n  },\n  halfAMinute: 'yarım dakika',\n  lessThanXMinutes: {\n    one: 'bir dakikadan az',\n    other: '{{count}} dakikadan az'\n  },\n  xMinutes: {\n    one: '1 dakika',\n    other: '{{count}} dakika'\n  },\n  aboutXHours: {\n    one: 'yaklaşık 1 saat',\n    other: 'yaklaşık {{count}} saat'\n  },\n  xHours: {\n    one: '1 saat',\n    other: '{{count}} saat'\n  },\n  xDays: {\n    one: '1 gün',\n    other: '{{count}} gün'\n  },\n  aboutXWeeks: {\n    one: 'yaklaşık 1 hafta',\n    other: 'yaklaşık {{count}} hafta'\n  },\n  xWeeks: {\n    one: '1 hafta',\n    other: '{{count}} hafta'\n  },\n  aboutXMonths: {\n    one: 'yaklaşık 1 ay',\n    other: 'yaklaşık {{count}} ay'\n  },\n  xMonths: {\n    one: '1 ay',\n    other: '{{count}} ay'\n  },\n  aboutXYears: {\n    one: 'yaklaşık 1 yıl',\n    other: 'yaklaşık {{count}} yıl'\n  },\n  xYears: {\n    one: '1 yıl',\n    other: '{{count}} yıl'\n  },\n  overXYears: {\n    one: '1 yıldan fazla',\n    other: '{{count}} yıldan fazla'\n  },\n  almostXYears: {\n    one: 'neredeyse 1 yıl',\n    other: 'neredeyse {{count}} yıl'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', count.toString());\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + ' sonra';\n    } else {\n      return result + ' önce';\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,cAAc;EAC3BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXN,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNP,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLR,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXT,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZX,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNd,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVf,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZhB,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEJ,KAAK,CAACK,QAAQ,CAAC,CAAC,CAAC;EAClE;EACA,IAAIJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAOL,MAAM,GAAG,QAAQ;IAC1B,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,OAAO;IACzB;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}