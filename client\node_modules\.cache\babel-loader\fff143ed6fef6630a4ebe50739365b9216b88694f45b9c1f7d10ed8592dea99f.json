{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'بىر سىكۇنت ئىچىدە',\n    other: 'سىكۇنت ئىچىدە {{count}}'\n  },\n  xSeconds: {\n    one: 'بىر سىكۇنت',\n    other: 'سىكۇنت {{count}}'\n  },\n  halfAMinute: 'يىرىم مىنۇت',\n  lessThanXMinutes: {\n    one: 'بىر مىنۇت ئىچىدە',\n    other: 'مىنۇت ئىچىدە {{count}}'\n  },\n  xMinutes: {\n    one: 'بىر مىنۇت',\n    other: 'مىنۇت {{count}}'\n  },\n  aboutXHours: {\n    one: 'تەخمىنەن بىر سائەت',\n    other: 'سائەت {{count}} تەخمىنەن'\n  },\n  xHours: {\n    one: 'بىر سائەت',\n    other: 'سائەت {{count}}'\n  },\n  xDays: {\n    one: 'بىر كۈن',\n    other: 'كۈن {{count}}'\n  },\n  aboutXWeeks: {\n    one: 'تەخمىنەن بىرھەپتە',\n    other: 'ھەپتە {{count}} تەخمىنەن'\n  },\n  xWeeks: {\n    one: 'بىرھەپتە',\n    other: 'ھەپتە {{count}}'\n  },\n  aboutXMonths: {\n    one: 'تەخمىنەن بىر ئاي',\n    other: 'ئاي {{count}} تەخمىنەن'\n  },\n  xMonths: {\n    one: 'بىر ئاي',\n    other: 'ئاي {{count}}'\n  },\n  aboutXYears: {\n    one: 'تەخمىنەن بىر يىل',\n    other: 'يىل {{count}} تەخمىنەن'\n  },\n  xYears: {\n    one: 'بىر يىل',\n    other: 'يىل {{count}}'\n  },\n  overXYears: {\n    one: 'بىر يىلدىن ئارتۇق',\n    other: 'يىلدىن ئارتۇق {{count}}'\n  },\n  almostXYears: {\n    one: 'ئاساسەن بىر يىل',\n    other: 'يىل {{count}} ئاساسەن'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result;\n    } else {\n      return result + ' بولدى';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/burky root/burky_root_web/node_modules/date-fns/esm/locale/ug/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'بىر سىكۇنت ئىچىدە',\n    other: 'سىكۇنت ئىچىدە {{count}}'\n  },\n  xSeconds: {\n    one: 'بىر سىكۇنت',\n    other: 'سىكۇنت {{count}}'\n  },\n  halfAMinute: 'يىرىم مىنۇت',\n  lessThanXMinutes: {\n    one: 'بىر مىنۇت ئىچىدە',\n    other: 'مىنۇت ئىچىدە {{count}}'\n  },\n  xMinutes: {\n    one: 'بىر مىنۇت',\n    other: 'مىنۇت {{count}}'\n  },\n  aboutXHours: {\n    one: 'تەخمىنەن بىر سائەت',\n    other: 'سائەت {{count}} تەخمىنەن'\n  },\n  xHours: {\n    one: 'بىر سائەت',\n    other: 'سائەت {{count}}'\n  },\n  xDays: {\n    one: 'بىر كۈن',\n    other: 'كۈن {{count}}'\n  },\n  aboutXWeeks: {\n    one: 'تەخمىنەن بىرھەپتە',\n    other: 'ھەپتە {{count}} تەخمىنەن'\n  },\n  xWeeks: {\n    one: 'بىرھەپتە',\n    other: 'ھەپتە {{count}}'\n  },\n  aboutXMonths: {\n    one: 'تەخمىنەن بىر ئاي',\n    other: 'ئاي {{count}} تەخمىنەن'\n  },\n  xMonths: {\n    one: 'بىر ئاي',\n    other: 'ئاي {{count}}'\n  },\n  aboutXYears: {\n    one: 'تەخمىنەن بىر يىل',\n    other: 'يىل {{count}} تەخمىنەن'\n  },\n  xYears: {\n    one: 'بىر يىل',\n    other: 'يىل {{count}}'\n  },\n  overXYears: {\n    one: 'بىر يىلدىن ئارتۇق',\n    other: 'يىلدىن ئارتۇق {{count}}'\n  },\n  almostXYears: {\n    one: 'ئاساسەن بىر يىل',\n    other: 'يىل {{count}} ئاساسەن'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result;\n    } else {\n      return result + ' بولدى';\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,aAAa;EAC1BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXN,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNP,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLR,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXT,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZX,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNd,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVf,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZhB,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAOL,MAAM;IACf,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,QAAQ;IAC1B;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}