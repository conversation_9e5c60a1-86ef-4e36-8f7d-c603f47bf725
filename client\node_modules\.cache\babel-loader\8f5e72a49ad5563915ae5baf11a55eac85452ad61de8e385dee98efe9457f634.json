{"ast": null, "code": "var _jsxFileName = \"C:\\\\Projeler\\\\kidgarden\\\\burky_root_web\\\\client\\\\src\\\\pages\\\\expert\\\\messages\\\\MessagesPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\nimport { useAuth } from '../../../hooks/useAuth';\nimport api from '../../../services/api';\nimport { toast } from 'react-hot-toast';\nimport io from 'socket.io-client';\nimport { ChatBubbleLeftEllipsisIcon, MagnifyingGlassIcon, PaperAirplaneIcon, PaperClipIcon, FaceSmileIcon, EllipsisHorizontalIcon, VideoCameraIcon, CheckCircleIcon, ArchiveBoxIcon, StarIcon, PhoneIcon, InformationCircleIcon } from '@heroicons/react/24/outline';\nimport { format } from 'date-fns';\nimport { tr } from 'date-fns/locale';\n\n/**\n * <PERSON><PERSON> mesajlaşma sayfası\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MessagesPage = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [isLoading, setIsLoading] = useState(true);\n  const [conversations, setConversations] = useState([]);\n  const [selectedConversation, setSelectedConversation] = useState(null);\n  const [messages, setMessages] = useState([]);\n  const [messageText, setMessageText] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filter, setFilter] = useState('all'); // all, unread, archived, starred\n  const [socket, setSocket] = useState(null);\n  const [onlineUsers, setOnlineUsers] = useState(new Set());\n  const messagesEndRef = useRef(null);\n  const conversationsRef = useRef(null);\n  const messagesContainerRef = useRef(null);\n\n  // Socket.IO connection - ayrı useEffect\n  useEffect(() => {\n    const token = localStorage.getItem('accessToken');\n    if (token && user !== null && user !== void 0 && user.id) {\n      console.log('🔌 EXPERT: Socket.IO bağlantısı kuruluyor... User ID:', user.id);\n      const socketConnection = io(process.env.REACT_APP_API_URL || 'http://localhost:5000', {\n        auth: {\n          token\n        },\n        transports: ['websocket', 'polling'],\n        reconnection: true,\n        reconnectionDelay: 1000,\n        reconnectionAttempts: 5,\n        timeout: 20000\n      });\n      setSocket(socketConnection);\n\n      // Connection events\n      socketConnection.on('connect', () => {\n        console.log('✅ EXPERT: Socket.IO bağlantısı kuruldu:', socketConnection.id);\n        console.log('🔗 EXPERT: Socket connected:', socketConnection.connected);\n        console.log('🏠 EXPERT: Socket rooms:', socketConnection.rooms);\n        // Kullanıcının online olduğunu bildir\n        socketConnection.emit('user_online');\n        // Online kullanıcı listesini al\n        socketConnection.emit('get_online_users');\n      });\n      socketConnection.on('disconnect', reason => {\n        console.log('❌ EXPERT: Socket.IO bağlantısı kesildi:', reason);\n      });\n      socketConnection.on('connect_error', error => {\n        console.error('🔥 EXPERT: Socket.IO bağlantı hatası:', error);\n      });\n      socketConnection.on('reconnect', attemptNumber => {\n        console.log('🔄 EXPERT: Socket.IO yeniden bağlandı, deneme:', attemptNumber);\n      });\n      socketConnection.on('reconnect_error', error => {\n        console.error('🔥 EXPERT: Socket.IO yeniden bağlanma hatası:', error);\n      });\n\n      // Heartbeat mekanizması\n      const heartbeatInterval = setInterval(() => {\n        if (socketConnection.connected) {\n          socketConnection.emit('ping', response => {\n            if (response === 'pong') {\n              console.log('💓 EXPERT: Heartbeat OK');\n            }\n          });\n        }\n      }, 30000); // 30 saniyede bir ping gönder\n\n      return () => {\n        clearInterval(heartbeatInterval);\n        socketConnection.disconnect();\n      };\n    } else {\n      console.log('❌ EXPERT: Socket bağlantısı kurulamıyor - token veya user eksik:', {\n        hasToken: !!token,\n        hasUser: !!(user !== null && user !== void 0 && user.id)\n      });\n    }\n  }, [user === null || user === void 0 ? void 0 : user.id]);\n\n  // Socket event listeners - ayrı useEffect\n  useEffect(() => {\n    if (socket && user !== null && user !== void 0 && user.id) {\n      console.log('🎧 Socket event listeners kuruluyor...');\n\n      // Sayfa yüklendiğinde online users listesini al\n      if (socket.connected) {\n        console.log('🔄 EXPERT: Requesting online users list on page load');\n        socket.emit('get_online_users');\n      }\n\n      // Socket event listeners\n      const handleNewMessage = message => {\n        console.log('📨 EXPERT: Yeni mesaj alındı:', message);\n        console.log('👤 EXPERT: Current user ID:', user.id);\n        console.log('💬 EXPERT: Message sender ID:', message.senderId);\n        console.log('🏠 EXPERT: Message conversation ID:', message.conversationId);\n\n        // Conversation listesini ÖNCE güncelle (her zaman)\n        setConversations(prev => {\n          console.log('📋 EXPERT: Updating conversations list');\n          return prev.map(conv => conv.id === message.conversationId ? {\n            ...conv,\n            lastMessage: message.content,\n            timestamp: message.createdAt,\n            unread: message.senderId !== user.id\n          } : conv);\n        });\n\n        // Eğer şu anda bu conversation açıksa, mesajı da ekle\n        setSelectedConversation(currentSelected => {\n          console.log('🎯 EXPERT: Current selected conversation:', currentSelected === null || currentSelected === void 0 ? void 0 : currentSelected.id);\n          if (currentSelected && message.conversationId === currentSelected.id) {\n            console.log('✅ EXPERT: Adding message to current conversation');\n            setMessages(prev => {\n              // Mesajın zaten var olup olmadığını kontrol et\n              const messageExists = prev.some(msg => msg.id === message.id);\n              if (messageExists) {\n                console.log('⚠️ EXPERT: Message already exists, skipping:', message.id);\n                return prev;\n              }\n              return [...prev, {\n                id: message.id,\n                senderId: message.senderId,\n                senderName: message.sender.name,\n                senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n                text: message.content,\n                timestamp: message.createdAt,\n                read: message.isRead,\n                delivered: true // Socket'ten gelen mesajlar delivered olarak kabul edilir\n              }];\n            });\n\n            // Eğer mesaj başka birinden geliyorsa ve conversation açıksa, otomatik okundu işaretle\n            if (message.senderId !== user.id) {\n              console.log('📖 EXPERT: Auto-marking message as read since conversation is open');\n              setTimeout(() => {\n                markConversationAsRead(message.conversationId);\n              }, 500); // Kısa bir gecikme ile\n            }\n\n            // Scroll to bottom\n            setTimeout(() => {\n              var _messagesEndRef$curre;\n              (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n                behavior: 'smooth'\n              });\n            }, 100);\n          } else {\n            console.log('❌ EXPERT: Not adding message - conversation not selected or different conversation');\n          }\n          return currentSelected;\n        });\n      };\n      const handleMessageSent = message => {\n        console.log('✅ Mesaj gönderildi onayı:', message);\n      };\n      const handleUserStatusChange = data => {\n        console.log('🔄 EXPERT: User status change:', data);\n        setOnlineUsers(prev => {\n          const newSet = new Set(prev);\n          if (data.status === 'online') {\n            newSet.add(data.userId);\n          } else {\n            newSet.delete(data.userId);\n          }\n          console.log('🟢 EXPERT: Online users updated:', Array.from(newSet));\n          return newSet;\n        });\n      };\n      const handleOnlineUsersList = userIds => {\n        console.log('📋 EXPERT: Online users list received:', userIds);\n        setOnlineUsers(new Set(userIds));\n      };\n      const handleUserTyping = data => {\n        // Typing indicator functionality can be implemented here\n        console.log('User typing:', data);\n      };\n      const handleUserStoppedTyping = data => {\n        // Stop typing indicator functionality can be implemented here\n        console.log('User stopped typing:', data);\n      };\n      const handleMessagesRead = data => {\n        console.log('👁️ EXPERT: Messages read event:', data);\n        console.log('👁️ EXPERT: Read by user:', data.readBy, 'Current user:', user.id);\n        console.log('👁️ EXPERT: Message IDs marked as read:', data.messageIds);\n\n        // Eğer başka bir kullanıcı mesajları okudu ise, kendi gönderdiğim mesajları okundu olarak işaretle\n        if (data.readBy !== user.id) {\n          setMessages(prev => prev.map(msg => {\n            // Kendi gönderdiğim mesajları ve conversation'daki tüm mesajları güncelle\n            if (msg.senderId === user.id && msg.conversationId === data.conversationId) {\n              console.log('👁️ EXPERT: Marking my message as read:', msg.id);\n              return {\n                ...msg,\n                read: true\n              };\n            }\n            // Ayrıca messageIds listesindeki mesajları da güncelle\n            if (data.messageIds.includes(msg.id)) {\n              console.log('👁️ EXPERT: Marking specific message as read:', msg.id);\n              return {\n                ...msg,\n                read: true\n              };\n            }\n            return msg;\n          }));\n\n          // Conversation listesindeki unread durumunu da güncelle\n          setConversations(prev => prev.map(conv => conv.id === data.conversationId ? {\n            ...conv,\n            unread: false\n          } : conv));\n        }\n      };\n\n      // Event listener'ları ekle\n      socket.on('new_message', handleNewMessage);\n      socket.on('message_sent', handleMessageSent);\n      socket.on('user_status_change', handleUserStatusChange);\n      socket.on('user_typing', handleUserTyping);\n      socket.on('user_stopped_typing', handleUserStoppedTyping);\n      socket.on('messages_read', handleMessagesRead);\n      socket.on('online_users_list', handleOnlineUsersList);\n\n      // Cleanup function\n      return () => {\n        socket.off('new_message', handleNewMessage);\n        socket.off('message_sent', handleMessageSent);\n        socket.off('user_status_change', handleUserStatusChange);\n        socket.off('user_typing', handleUserTyping);\n        socket.off('user_stopped_typing', handleUserStoppedTyping);\n        socket.off('messages_read', handleMessagesRead);\n        socket.off('online_users_list', handleOnlineUsersList);\n      };\n    }\n  }, [socket, user.id]);\n\n  // Conversations yükleme - ayrı useEffect\n  useEffect(() => {\n    loadConversations();\n  }, []);\n\n  // Socket bağlandığında tüm conversation'lara join ol - sadece bir kez\n  const [joinedConversations, setJoinedConversations] = useState(new Set());\n  useEffect(() => {\n    if (socket && conversations.length > 0) {\n      console.log('🏠 EXPERT: Tüm conversation\\'lara katılıyor...', conversations.length, 'conversation');\n      conversations.forEach(conversation => {\n        if (!joinedConversations.has(conversation.id)) {\n          socket.emit('join_conversation', conversation.id);\n          console.log(`🏠 EXPERT: Conversation ${conversation.id} katıldı`);\n          setJoinedConversations(prev => new Set([...prev, conversation.id]));\n        }\n      });\n    }\n  }, [socket, conversations, joinedConversations]);\n\n  // Conversations yükleme\n  const loadConversations = async () => {\n    try {\n      setIsLoading(true);\n      const response = await api.get('/messages/conversations');\n\n      // API verisini frontend formatına çevir\n      const formattedConversations = response.data.conversations.map(conversation => {\n        var _conversation$lastMes, _conversation$lastMes2;\n        return {\n          id: conversation.id,\n          clientId: conversation.otherUser.id,\n          clientName: conversation.otherUser.name,\n          lastMessage: ((_conversation$lastMes = conversation.lastMessage) === null || _conversation$lastMes === void 0 ? void 0 : _conversation$lastMes.content) || 'Henüz mesaj yok',\n          timestamp: ((_conversation$lastMes2 = conversation.lastMessage) === null || _conversation$lastMes2 === void 0 ? void 0 : _conversation$lastMes2.timestamp) || conversation.createdAt,\n          unread: conversation.lastMessage ? !conversation.lastMessage.isRead && conversation.lastMessage.senderId !== user.id : false,\n          avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(conversation.otherUser.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n          status: onlineUsers.has(conversation.otherUser.id) ? 'online' : 'offline',\n          starred: false,\n          archived: false\n        };\n      });\n      setConversations(formattedConversations);\n    } catch (error) {\n      console.error('Konuşmalar yüklenirken hata:', error);\n      toast.error('Konuşmalar yüklenemedi');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Mesaj yükleme ve okundu işaretleme\n  useEffect(() => {\n    if (selectedConversation) {\n      loadMessages(selectedConversation.id);\n      markConversationAsRead(selectedConversation.id);\n\n      // Conversation'ı seçildiğinde unread durumunu false yap\n      setConversations(prev => prev.map(conv => conv.id === selectedConversation.id ? {\n        ...conv,\n        unread: false\n      } : conv));\n    }\n  }, [selectedConversation, loadMessages, markConversationAsRead]);\n\n  // Conversation'daki mesajları okundu olarak işaretle\n  const markConversationAsRead = async conversationId => {\n    try {\n      // Server'a okundu bilgisi gönder (mesajlar yüklenmeden önce de çalışır)\n      const response = await api.put(`/messages/conversations/${conversationId}/read`);\n      if (response.data.updatedCount > 0) {\n        console.log('📖 EXPERT: Marked', response.data.updatedCount, 'messages as read');\n\n        // Local state'i güncelle\n        setMessages(prev => prev.map(msg => msg.senderId !== user.id ? {\n          ...msg,\n          read: true\n        } : msg));\n\n        // Socket'e okundu bilgisi gönder\n        if (socket) {\n          socket.emit('mark_messages_read', {\n            conversationId,\n            messageIds: [] // Server zaten hangi mesajların okundu olduğunu biliyor\n          });\n        }\n      }\n    } catch (error) {\n      console.error('Mesajları okundu olarak işaretlerken hata:', error);\n    }\n  };\n\n  // Messages yükleme\n  const loadMessages = async conversationId => {\n    try {\n      const response = await api.get(`/messages/conversations/${conversationId}`);\n\n      // API verisini frontend formatına çevir\n      const formattedMessages = response.data.messages.map(message => ({\n        id: message.id,\n        senderId: message.senderId,\n        senderName: message.sender.name,\n        senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        text: message.content,\n        timestamp: message.createdAt,\n        read: message.isRead,\n        delivered: true,\n        // Veritabanından gelen mesajlar delivered olarak kabul edilir\n        attachments: message.attachments\n      }));\n      setMessages(formattedMessages);\n\n      // Scroll to bottom\n      setTimeout(() => {\n        var _messagesEndRef$curre2;\n        (_messagesEndRef$curre2 = messagesEndRef.current) === null || _messagesEndRef$curre2 === void 0 ? void 0 : _messagesEndRef$curre2.scrollIntoView({\n          behavior: 'smooth'\n        });\n      }, 100);\n    } catch (error) {\n      console.error('Mesajlar yüklenirken hata:', error);\n      toast.error('Mesajlar yüklenemedi');\n    }\n  };\n  // Mesaj gönderme\n  const sendMessage = async () => {\n    if (!messageText.trim() || !selectedConversation) return;\n    console.log('📤 Mesaj gönderiliyor:', {\n      receiverId: selectedConversation.clientId,\n      content: messageText.trim()\n    });\n    try {\n      const response = await api.post('/messages/send', {\n        receiverId: selectedConversation.clientId,\n        content: messageText.trim()\n      });\n      console.log('✅ Mesaj gönderildi:', response.data);\n\n      // Sadece mesaj input'unu temizle - mesaj socket'ten gelecek\n      setMessageText('');\n    } catch (error) {\n      console.error('Mesaj gönderilirken hata:', error);\n      toast.error('Mesaj gönderilemedi');\n    }\n  };\n  useEffect(() => {\n    // Mesajları otomatik kaydır\n    if (messagesEndRef.current && messagesContainerRef.current) {\n      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;\n    }\n  }, [messages]);\n\n  // Mesaj gönderme\n  const handleSendMessage = e => {\n    e.preventDefault();\n    sendMessage();\n  };\n\n  // Konuşma seçme\n  const handleSelectConversation = conversation => {\n    setSelectedConversation(conversation);\n  };\n\n  // Tarih formatı\n  const formatMessageDate = dateString => {\n    const date = new Date(dateString);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    if (date.toDateString() === today.toDateString()) {\n      return format(date, 'HH:mm', {\n        locale: tr\n      });\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Dün ' + format(date, 'HH:mm', {\n        locale: tr\n      });\n    } else {\n      return format(date, 'dd MMM HH:mm', {\n        locale: tr\n      });\n    }\n  };\n\n  // Konuşmaları filtrele\n  const filteredConversations = conversations.filter(conv => {\n    // Arama filtresi\n    const matchesSearch = conv.clientName.toLowerCase().includes(searchTerm.toLowerCase()) || conv.lastMessage.toLowerCase().includes(searchTerm.toLowerCase());\n\n    // Durum filtresi\n    const matchesFilter = filter === 'all' || filter === 'unread' && conv.unread || filter === 'archived' && conv.archived || filter === 'starred' && conv.starred;\n    return matchesSearch && matchesFilter;\n  });\n\n  // Konuşmayı yıldızla\n  const toggleStar = id => {\n    setConversations(prevConversations => prevConversations.map(conv => conv.id === id ? {\n      ...conv,\n      starred: !conv.starred\n    } : conv));\n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({\n        ...prev,\n        starred: !prev.starred\n      }));\n    }\n  };\n\n  // Konuşmayı arşivle\n  const toggleArchive = id => {\n    setConversations(prevConversations => prevConversations.map(conv => conv.id === id ? {\n      ...conv,\n      archived: !conv.archived\n    } : conv));\n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({\n        ...prev,\n        archived: !prev.archived\n      }));\n    }\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 490,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mx-auto px-4 py-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-teal-500 to-teal-700 shadow-lg rounded-lg p-6 mb-6 text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold\",\n            children: \"Mesajlar\\u0131m\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-1 text-indigo-100\",\n            children: \"Dan\\u0131\\u015Fanlar\\u0131n\\u0131zla olan t\\xFCm yaz\\u0131\\u015Fmalar\\u0131n\\u0131z\\u0131 buradan y\\xF6netebilirsiniz.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 501,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-3 sm:mt-0 flex space-x-2\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-indigo-800 bg-white hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-300\",\n            children: [/*#__PURE__*/_jsxDEV(ChatBubbleLeftEllipsisIcon, {\n              className: \"-ml-1 mr-2 h-5 w-5\",\n              \"aria-hidden\": \"true\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 15\n            }, this), \"Yeni Mesaj\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 500,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 499,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-md overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-12 h-[75vh]\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-span-12 md:col-span-4 border-r border-gray-200 flex flex-col\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 border-b border-gray-200 bg-white\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-semibold text-gray-800 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(ChatBubbleLeftEllipsisIcon, {\n                className: \"h-6 w-6 text-indigo-600 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 17\n              }, this), \"Mesajlar\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3 relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Konu\\u015Fmalarda ara...\",\n                className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                className: \"h-5 w-5 text-gray-400 absolute left-3 top-2.5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3 flex space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: `px-3 py-1 text-sm rounded-full ${filter === 'all' ? 'bg-indigo-100 text-indigo-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                onClick: () => setFilter('all'),\n                children: \"T\\xFCm\\xFC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `px-3 py-1 text-sm rounded-full ${filter === 'unread' ? 'bg-indigo-100 text-indigo-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                onClick: () => setFilter('unread'),\n                children: \"Okunmam\\u0131\\u015F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `px-3 py-1 text-sm rounded-full ${filter === 'starred' ? 'bg-indigo-100 text-indigo-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                onClick: () => setFilter('starred'),\n                children: \"Y\\u0131ld\\u0131zl\\u0131\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `px-3 py-1 text-sm rounded-full ${filter === 'archived' ? 'bg-indigo-100 text-indigo-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                onClick: () => setFilter('archived'),\n                children: \"Ar\\u015Fiv\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: conversationsRef,\n            style: {\n              height: 'calc(75vh - 145px)',\n              overflowY: 'auto',\n              scrollbarWidth: 'thin',\n              scrollbarColor: '#D1D5DB #F3F4F6'\n            },\n            children: filteredConversations.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 text-center text-gray-500\",\n              children: \"Hi\\xE7 mesaj\\u0131n\\u0131z yok\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 17\n            }, this) : filteredConversations.map(conversation => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `p-3 cursor-pointer hover:bg-gray-50 transition-colors duration-200 ${(selectedConversation === null || selectedConversation === void 0 ? void 0 : selectedConversation.id) === conversation.id ? 'bg-indigo-50' : ''} ${conversation.unread ? 'bg-indigo-50 hover:bg-indigo-100' : ''}`,\n              onClick: () => handleSelectConversation(conversation),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative flex-shrink-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: conversation.avatar,\n                    alt: conversation.clientName,\n                    className: `h-10 w-10 rounded-full ${(selectedConversation === null || selectedConversation === void 0 ? void 0 : selectedConversation.id) === conversation.id ? 'ring-2 ring-indigo-600' : ''}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 595,\n                    columnNumber: 25\n                  }, this), conversation.status === 'online' && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 605,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 594,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: `text-sm font-medium ${conversation.unread ? 'text-gray-900' : 'text-gray-700'}`,\n                      children: conversation.clientName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 610,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-1\",\n                      children: [conversation.starred && /*#__PURE__*/_jsxDEV(StarIcon, {\n                        className: \"h-4 w-4 text-yellow-400 fill-current\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 615,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-gray-500\",\n                        children: formatMessageDate(conversation.timestamp)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 617,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 613,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 609,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: `text-sm truncate mt-1 ${conversation.unread ? 'text-gray-900 font-medium' : 'text-gray-500'}`,\n                    children: conversation.lastMessage\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 622,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center mt-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-gray-500\",\n                      children: onlineUsers.has(conversation.clientId) ? /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-green-500 font-medium\",\n                        children: \"\\xC7evrimi\\xE7i\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 630,\n                        columnNumber: 33\n                      }, this) : conversation.lastSeen ? `Son görülme: ${conversation.lastSeen}` : ''\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 628,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex space-x-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: e => {\n                          e.stopPropagation();\n                          toggleStar(conversation.id);\n                        },\n                        className: \"text-gray-400 hover:text-yellow-400\",\n                        children: /*#__PURE__*/_jsxDEV(StarIcon, {\n                          className: `h-4 w-4 ${conversation.starred ? 'text-yellow-400 fill-current' : ''}`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 643,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 636,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: e => {\n                          e.stopPropagation();\n                          toggleArchive(conversation.id);\n                        },\n                        className: \"text-gray-400 hover:text-gray-600\",\n                        children: /*#__PURE__*/_jsxDEV(ArchiveBoxIcon, {\n                          className: \"h-4 w-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 652,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 645,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 635,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 627,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 608,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 593,\n                columnNumber: 21\n              }, this), conversation.unread && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"inline-flex items-center ml-12 mt-1 px-2 py-0.5 rounded-full text-xs font-medium bg-indigo-600 text-white\",\n                children: \"Yeni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 659,\n                columnNumber: 23\n              }, this)]\n            }, conversation.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 571,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 520,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-span-12 md:col-span-8 flex flex-col\",\n          children: selectedConversation ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 border-b border-gray-200 bg-white flex justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative mr-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: selectedConversation.avatar,\n                    alt: selectedConversation.clientName,\n                    className: \"h-10 w-10 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 677,\n                    columnNumber: 23\n                  }, this), selectedConversation.status === 'online' && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 683,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 676,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-lg font-medium text-gray-800\",\n                    children: selectedConversation.clientName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 687,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500\",\n                    children: selectedConversation.status === 'online' ? 'Çevrimiçi' : selectedConversation.lastSeen ? `Son görülme: ${selectedConversation.lastSeen}` : ''\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 690,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 686,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 675,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                  children: /*#__PURE__*/_jsxDEV(PhoneIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 701,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 700,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                  children: /*#__PURE__*/_jsxDEV(VideoCameraIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 704,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 703,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                  children: /*#__PURE__*/_jsxDEV(InformationCircleIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 707,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 706,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                  children: /*#__PURE__*/_jsxDEV(EllipsisHorizontalIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 710,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 709,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 699,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 674,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: messagesContainerRef,\n              className: \"p-4 bg-gray-50\",\n              style: {\n                height: 'calc(75vh - 195px)',\n                overflowY: 'auto',\n                scrollbarWidth: 'thin',\n                scrollbarColor: '#D1D5DB #F3F4F6'\n              },\n              children: [messages.map((message, index) => {\n                const isSender = message.senderId === user.id;\n                const showAvatar = index === 0 || messages[index - 1].senderId !== message.senderId;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `flex ${isSender ? 'justify-end' : 'justify-start'} mb-4`,\n                  children: [!isSender && showAvatar && /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: message.senderAvatar,\n                    alt: message.senderName,\n                    className: \"h-8 w-8 rounded-full mr-2 mt-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 733,\n                    columnNumber: 27\n                  }, this), !isSender && !showAvatar && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 739,\n                    columnNumber: 54\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `max-w-xs md:max-w-md lg:max-w-lg rounded-lg px-4 py-2 ${isSender ? 'bg-indigo-600 text-white rounded-br-none' : 'bg-white text-gray-800 rounded-bl-none border border-gray-200'}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm\",\n                      children: message.text\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 747,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `text-xs mt-1 ${isSender ? 'text-indigo-200' : 'text-gray-500'} flex items-center justify-end`,\n                      children: [formatMessageDate(message.timestamp), isSender && /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                        className: `h-3 w-3 ml-1 ${message.read ? 'text-blue-400' : 'text-gray-400'}`,\n                        title: message.read ? 'Okundu' : 'İletildi'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 751,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 748,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 740,\n                    columnNumber: 25\n                  }, this), isSender && showAvatar && /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: message.senderAvatar,\n                    alt: message.senderName,\n                    className: \"h-8 w-8 rounded-full ml-2 mt-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 759,\n                    columnNumber: 27\n                  }, this), isSender && !showAvatar && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 ml-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 765,\n                    columnNumber: 53\n                  }, this)]\n                }, message.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 731,\n                  columnNumber: 23\n                }, this);\n              }), /*#__PURE__*/_jsxDEV(\"div\", {\n                ref: messagesEndRef\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 769,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 716,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 border-t border-gray-200 bg-white\",\n              children: /*#__PURE__*/_jsxDEV(\"form\", {\n                onSubmit: handleSendMessage,\n                className: \"flex items-end\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\",\n                  children: /*#__PURE__*/_jsxDEV(PaperClipIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 779,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 775,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    className: \"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none\",\n                    placeholder: \"Mesaj\\u0131n\\u0131z\\u0131 yaz\\u0131n...\",\n                    rows: \"2\",\n                    value: messageText,\n                    onChange: e => setMessageText(e.target.value),\n                    onKeyDown: e => {\n                      if (e.key === 'Enter' && !e.shiftKey) {\n                        e.preventDefault();\n                        sendMessage();\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 782,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 781,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\",\n                  children: /*#__PURE__*/_jsxDEV(FaceSmileIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 800,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 796,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"submit\",\n                  disabled: !messageText.trim(),\n                  className: `ml-2 p-2 rounded-full ${messageText.trim() ? 'bg-indigo-600 text-white hover:bg-indigo-700' : 'bg-gray-200 text-gray-400 cursor-not-allowed'} focus:outline-none`,\n                  children: /*#__PURE__*/_jsxDEV(PaperAirplaneIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 811,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 802,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 774,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 773,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) :\n          /*#__PURE__*/\n          // Mesaj seçilmediğinde\n          _jsxDEV(\"div\", {\n            className: \"flex-1 flex flex-col items-center justify-center p-6 bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full max-w-md text-center\",\n              children: [/*#__PURE__*/_jsxDEV(ChatBubbleLeftEllipsisIcon, {\n                className: \"h-16 w-16 text-gray-300 mb-4 mx-auto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 820,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-medium text-gray-800 mb-2\",\n                children: \"Mesajlar\\u0131n\\u0131z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 821,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-500 mx-auto\",\n                children: \"Mesajla\\u015Fmaya ba\\u015Flamak i\\xE7in sol taraftan bir konu\\u015Fma se\\xE7in veya yeni bir mesaj ba\\u015Flat\\u0131n.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 822,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 819,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 818,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 670,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 518,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 517,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 497,\n    columnNumber: 5\n  }, this);\n};\n_s(MessagesPage, \"rYHyKpN34Dv9IpuQWPT+V9L0EHM=\", false, function () {\n  return [useAuth];\n});\n_c = MessagesPage;\nexport default MessagesPage;\nvar _c;\n$RefreshReg$(_c, \"MessagesPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "useAuth", "api", "toast", "io", "ChatBubbleLeftEllipsisIcon", "MagnifyingGlassIcon", "PaperAirplaneIcon", "PaperClipIcon", "FaceSmileIcon", "EllipsisHorizontalIcon", "VideoCameraIcon", "CheckCircleIcon", "ArchiveBoxIcon", "StarIcon", "PhoneIcon", "InformationCircleIcon", "format", "tr", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MessagesPage", "_s", "user", "isLoading", "setIsLoading", "conversations", "setConversations", "selectedConversation", "setSelectedConversation", "messages", "setMessages", "messageText", "setMessageText", "searchTerm", "setSearchTerm", "filter", "setFilter", "socket", "setSocket", "onlineUsers", "setOnlineUsers", "Set", "messagesEndRef", "conversationsRef", "messagesContainerRef", "token", "localStorage", "getItem", "id", "console", "log", "socketConnection", "process", "env", "REACT_APP_API_URL", "auth", "transports", "reconnection", "reconnectionDelay", "reconnectionAttempts", "timeout", "on", "connected", "rooms", "emit", "reason", "error", "attemptNumber", "heartbeatInterval", "setInterval", "response", "clearInterval", "disconnect", "hasToken", "<PERSON><PERSON>ser", "handleNewMessage", "message", "senderId", "conversationId", "prev", "map", "conv", "lastMessage", "content", "timestamp", "createdAt", "unread", "currentSelected", "messageExists", "some", "msg", "sender<PERSON>ame", "sender", "name", "senderAvatar", "encodeURIComponent", "text", "read", "isRead", "delivered", "setTimeout", "markConversationAsRead", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleMessageSent", "handleUserStatusChange", "data", "newSet", "status", "add", "userId", "delete", "Array", "from", "handleOnlineUsersList", "userIds", "handleUserTyping", "handleUserStoppedTyping", "handleMessagesRead", "readBy", "messageIds", "includes", "off", "loadConversations", "joinedConversations", "setJoinedConversations", "length", "for<PERSON>ach", "conversation", "has", "get", "formattedConversations", "_conversation$lastMes", "_conversation$lastMes2", "clientId", "otherUser", "clientName", "avatar", "starred", "archived", "loadMessages", "put", "updatedCount", "formattedMessages", "attachments", "_messagesEndRef$curre2", "sendMessage", "trim", "receiverId", "post", "scrollTop", "scrollHeight", "handleSendMessage", "e", "preventDefault", "handleSelectConversation", "formatMessageDate", "dateString", "date", "Date", "today", "yesterday", "setDate", "getDate", "toDateString", "locale", "filteredConversations", "matchesSearch", "toLowerCase", "matchesFilter", "toggleStar", "prevConversations", "toggleArchive", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "value", "onChange", "target", "onClick", "ref", "style", "height", "overflowY", "scrollbarWidth", "scrollbarColor", "src", "alt", "lastSeen", "stopPropagation", "index", "isSender", "showAvatar", "title", "onSubmit", "rows", "onKeyDown", "key", "shift<PERSON>ey", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Projeler/kidgarden/burky_root_web/client/src/pages/expert/messages/MessagesPage.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback } from 'react';\nimport { useAuth } from '../../../hooks/useAuth';\nimport api from '../../../services/api';\nimport { toast } from 'react-hot-toast';\nimport io from 'socket.io-client';\nimport {\n  ChatBubbleLeftEllipsisIcon,\n  MagnifyingGlassIcon,\n  PaperAirplaneIcon,\n  PaperClipIcon,\n  FaceSmileIcon,\n  EllipsisHorizontalIcon,\n  VideoCameraIcon,\n  CheckCircleIcon,\n  ArchiveBoxIcon,\n  StarIcon,\n  PhoneIcon,\n  InformationCircleIcon\n} from '@heroicons/react/24/outline';\nimport { format } from 'date-fns';\nimport { tr } from 'date-fns/locale';\n\n/**\n * <PERSON><PERSON> mesajlaş<PERSON> sayfası\n */\nconst MessagesPage = () => {\n  const { user } = useAuth();\n  const [isLoading, setIsLoading] = useState(true);\n  const [conversations, setConversations] = useState([]);\n  const [selectedConversation, setSelectedConversation] = useState(null);\n  const [messages, setMessages] = useState([]);\n  const [messageText, setMessageText] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filter, setFilter] = useState('all'); // all, unread, archived, starred\n  const [socket, setSocket] = useState(null);\n  const [onlineUsers, setOnlineUsers] = useState(new Set());\n\n  const messagesEndRef = useRef(null);\n  const conversationsRef = useRef(null);\n  const messagesContainerRef = useRef(null);\n\n  // Socket.IO connection - ayrı useEffect\n  useEffect(() => {\n    const token = localStorage.getItem('accessToken');\n    if (token && user?.id) {\n      console.log('🔌 EXPERT: Socket.IO bağlantısı kuruluyor... User ID:', user.id);\n      const socketConnection = io(process.env.REACT_APP_API_URL || 'http://localhost:5000', {\n        auth: { token },\n        transports: ['websocket', 'polling'],\n        reconnection: true,\n        reconnectionDelay: 1000,\n        reconnectionAttempts: 5,\n        timeout: 20000\n      });\n\n      setSocket(socketConnection);\n\n      // Connection events\n      socketConnection.on('connect', () => {\n        console.log('✅ EXPERT: Socket.IO bağlantısı kuruldu:', socketConnection.id);\n        console.log('🔗 EXPERT: Socket connected:', socketConnection.connected);\n        console.log('🏠 EXPERT: Socket rooms:', socketConnection.rooms);\n        // Kullanıcının online olduğunu bildir\n        socketConnection.emit('user_online');\n        // Online kullanıcı listesini al\n        socketConnection.emit('get_online_users');\n      });\n\n      socketConnection.on('disconnect', (reason) => {\n        console.log('❌ EXPERT: Socket.IO bağlantısı kesildi:', reason);\n      });\n\n      socketConnection.on('connect_error', (error) => {\n        console.error('🔥 EXPERT: Socket.IO bağlantı hatası:', error);\n      });\n\n      socketConnection.on('reconnect', (attemptNumber) => {\n        console.log('🔄 EXPERT: Socket.IO yeniden bağlandı, deneme:', attemptNumber);\n      });\n\n      socketConnection.on('reconnect_error', (error) => {\n        console.error('🔥 EXPERT: Socket.IO yeniden bağlanma hatası:', error);\n      });\n\n      // Heartbeat mekanizması\n      const heartbeatInterval = setInterval(() => {\n        if (socketConnection.connected) {\n          socketConnection.emit('ping', (response) => {\n            if (response === 'pong') {\n              console.log('💓 EXPERT: Heartbeat OK');\n            }\n          });\n        }\n      }, 30000); // 30 saniyede bir ping gönder\n\n      return () => {\n        clearInterval(heartbeatInterval);\n        socketConnection.disconnect();\n      };\n    } else {\n      console.log('❌ EXPERT: Socket bağlantısı kurulamıyor - token veya user eksik:', {\n        hasToken: !!token,\n        hasUser: !!user?.id\n      });\n    }\n  }, [user?.id]);\n\n  // Socket event listeners - ayrı useEffect\n  useEffect(() => {\n    if (socket && user?.id) {\n      console.log('🎧 Socket event listeners kuruluyor...');\n\n      // Sayfa yüklendiğinde online users listesini al\n      if (socket.connected) {\n        console.log('🔄 EXPERT: Requesting online users list on page load');\n        socket.emit('get_online_users');\n      }\n\n      // Socket event listeners\n      const handleNewMessage = (message) => {\n        console.log('📨 EXPERT: Yeni mesaj alındı:', message);\n        console.log('👤 EXPERT: Current user ID:', user.id);\n        console.log('💬 EXPERT: Message sender ID:', message.senderId);\n        console.log('🏠 EXPERT: Message conversation ID:', message.conversationId);\n\n        // Conversation listesini ÖNCE güncelle (her zaman)\n        setConversations(prev => {\n          console.log('📋 EXPERT: Updating conversations list');\n          return prev.map(conv =>\n            conv.id === message.conversationId\n              ? { ...conv, lastMessage: message.content, timestamp: message.createdAt, unread: message.senderId !== user.id }\n              : conv\n          );\n        });\n\n        // Eğer şu anda bu conversation açıksa, mesajı da ekle\n        setSelectedConversation(currentSelected => {\n          console.log('🎯 EXPERT: Current selected conversation:', currentSelected?.id);\n          if (currentSelected && message.conversationId === currentSelected.id) {\n            console.log('✅ EXPERT: Adding message to current conversation');\n            setMessages(prev => {\n              // Mesajın zaten var olup olmadığını kontrol et\n              const messageExists = prev.some(msg => msg.id === message.id);\n              if (messageExists) {\n                console.log('⚠️ EXPERT: Message already exists, skipping:', message.id);\n                return prev;\n              }\n\n              return [...prev, {\n                id: message.id,\n                senderId: message.senderId,\n                senderName: message.sender.name,\n                senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n                text: message.content,\n                timestamp: message.createdAt,\n                read: message.isRead,\n                delivered: true // Socket'ten gelen mesajlar delivered olarak kabul edilir\n              }];\n            });\n\n            // Eğer mesaj başka birinden geliyorsa ve conversation açıksa, otomatik okundu işaretle\n            if (message.senderId !== user.id) {\n              console.log('📖 EXPERT: Auto-marking message as read since conversation is open');\n              setTimeout(() => {\n                markConversationAsRead(message.conversationId);\n              }, 500); // Kısa bir gecikme ile\n            }\n\n            // Scroll to bottom\n            setTimeout(() => {\n              messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n            }, 100);\n          } else {\n            console.log('❌ EXPERT: Not adding message - conversation not selected or different conversation');\n          }\n          return currentSelected;\n        });\n      };\n\n      const handleMessageSent = (message) => {\n        console.log('✅ Mesaj gönderildi onayı:', message);\n      };\n\n      const handleUserStatusChange = (data) => {\n        console.log('🔄 EXPERT: User status change:', data);\n        setOnlineUsers(prev => {\n          const newSet = new Set(prev);\n          if (data.status === 'online') {\n            newSet.add(data.userId);\n          } else {\n            newSet.delete(data.userId);\n          }\n          console.log('🟢 EXPERT: Online users updated:', Array.from(newSet));\n          return newSet;\n        });\n      };\n\n      const handleOnlineUsersList = (userIds) => {\n        console.log('📋 EXPERT: Online users list received:', userIds);\n        setOnlineUsers(new Set(userIds));\n      };\n\n      const handleUserTyping = (data) => {\n        // Typing indicator functionality can be implemented here\n        console.log('User typing:', data);\n      };\n\n      const handleUserStoppedTyping = (data) => {\n        // Stop typing indicator functionality can be implemented here\n        console.log('User stopped typing:', data);\n      };\n\n      const handleMessagesRead = (data) => {\n        console.log('👁️ EXPERT: Messages read event:', data);\n        console.log('👁️ EXPERT: Read by user:', data.readBy, 'Current user:', user.id);\n        console.log('👁️ EXPERT: Message IDs marked as read:', data.messageIds);\n\n        // Eğer başka bir kullanıcı mesajları okudu ise, kendi gönderdiğim mesajları okundu olarak işaretle\n        if (data.readBy !== user.id) {\n          setMessages(prev => prev.map(msg => {\n            // Kendi gönderdiğim mesajları ve conversation'daki tüm mesajları güncelle\n            if (msg.senderId === user.id && msg.conversationId === data.conversationId) {\n              console.log('👁️ EXPERT: Marking my message as read:', msg.id);\n              return { ...msg, read: true };\n            }\n            // Ayrıca messageIds listesindeki mesajları da güncelle\n            if (data.messageIds.includes(msg.id)) {\n              console.log('👁️ EXPERT: Marking specific message as read:', msg.id);\n              return { ...msg, read: true };\n            }\n            return msg;\n          }));\n\n          // Conversation listesindeki unread durumunu da güncelle\n          setConversations(prev => prev.map(conv =>\n            conv.id === data.conversationId\n              ? { ...conv, unread: false }\n              : conv\n          ));\n        }\n      };\n\n      // Event listener'ları ekle\n      socket.on('new_message', handleNewMessage);\n      socket.on('message_sent', handleMessageSent);\n      socket.on('user_status_change', handleUserStatusChange);\n      socket.on('user_typing', handleUserTyping);\n      socket.on('user_stopped_typing', handleUserStoppedTyping);\n      socket.on('messages_read', handleMessagesRead);\n      socket.on('online_users_list', handleOnlineUsersList);\n\n      // Cleanup function\n      return () => {\n        socket.off('new_message', handleNewMessage);\n        socket.off('message_sent', handleMessageSent);\n        socket.off('user_status_change', handleUserStatusChange);\n        socket.off('user_typing', handleUserTyping);\n        socket.off('user_stopped_typing', handleUserStoppedTyping);\n        socket.off('messages_read', handleMessagesRead);\n        socket.off('online_users_list', handleOnlineUsersList);\n      };\n    }\n  }, [socket, user.id]);\n\n  // Conversations yükleme - ayrı useEffect\n  useEffect(() => {\n    loadConversations();\n  }, []);\n\n  // Socket bağlandığında tüm conversation'lara join ol - sadece bir kez\n  const [joinedConversations, setJoinedConversations] = useState(new Set());\n\n  useEffect(() => {\n    if (socket && conversations.length > 0) {\n      console.log('🏠 EXPERT: Tüm conversation\\'lara katılıyor...', conversations.length, 'conversation');\n\n      conversations.forEach(conversation => {\n        if (!joinedConversations.has(conversation.id)) {\n          socket.emit('join_conversation', conversation.id);\n          console.log(`🏠 EXPERT: Conversation ${conversation.id} katıldı`);\n          setJoinedConversations(prev => new Set([...prev, conversation.id]));\n        }\n      });\n    }\n  }, [socket, conversations, joinedConversations]);\n\n  // Conversations yükleme\n  const loadConversations = async () => {\n    try {\n      setIsLoading(true);\n      const response = await api.get('/messages/conversations');\n\n      // API verisini frontend formatına çevir\n      const formattedConversations = response.data.conversations.map(conversation => ({\n        id: conversation.id,\n        clientId: conversation.otherUser.id,\n        clientName: conversation.otherUser.name,\n        lastMessage: conversation.lastMessage?.content || 'Henüz mesaj yok',\n        timestamp: conversation.lastMessage?.timestamp || conversation.createdAt,\n        unread: conversation.lastMessage ? !conversation.lastMessage.isRead && conversation.lastMessage.senderId !== user.id : false,\n        avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(conversation.otherUser.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        status: onlineUsers.has(conversation.otherUser.id) ? 'online' : 'offline',\n        starred: false,\n        archived: false\n      }));\n\n      setConversations(formattedConversations);\n    } catch (error) {\n      console.error('Konuşmalar yüklenirken hata:', error);\n      toast.error('Konuşmalar yüklenemedi');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Mesaj yükleme ve okundu işaretleme\n  useEffect(() => {\n    if (selectedConversation) {\n      loadMessages(selectedConversation.id);\n      markConversationAsRead(selectedConversation.id);\n\n      // Conversation'ı seçildiğinde unread durumunu false yap\n      setConversations(prev => prev.map(conv =>\n        conv.id === selectedConversation.id\n          ? { ...conv, unread: false }\n          : conv\n      ));\n    }\n  }, [selectedConversation, loadMessages, markConversationAsRead]);\n\n  // Conversation'daki mesajları okundu olarak işaretle\n  const markConversationAsRead = async (conversationId) => {\n    try {\n      // Server'a okundu bilgisi gönder (mesajlar yüklenmeden önce de çalışır)\n      const response = await api.put(`/messages/conversations/${conversationId}/read`);\n\n      if (response.data.updatedCount > 0) {\n        console.log('📖 EXPERT: Marked', response.data.updatedCount, 'messages as read');\n\n        // Local state'i güncelle\n        setMessages(prev => prev.map(msg =>\n          msg.senderId !== user.id ? { ...msg, read: true } : msg\n        ));\n\n        // Socket'e okundu bilgisi gönder\n        if (socket) {\n          socket.emit('mark_messages_read', {\n            conversationId,\n            messageIds: [] // Server zaten hangi mesajların okundu olduğunu biliyor\n          });\n        }\n      }\n    } catch (error) {\n      console.error('Mesajları okundu olarak işaretlerken hata:', error);\n    }\n  };\n\n  // Messages yükleme\n  const loadMessages = async (conversationId) => {\n    try {\n      const response = await api.get(`/messages/conversations/${conversationId}`);\n\n      // API verisini frontend formatına çevir\n      const formattedMessages = response.data.messages.map(message => ({\n        id: message.id,\n        senderId: message.senderId,\n        senderName: message.sender.name,\n        senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        text: message.content,\n        timestamp: message.createdAt,\n        read: message.isRead,\n        delivered: true, // Veritabanından gelen mesajlar delivered olarak kabul edilir\n        attachments: message.attachments\n      }));\n\n      setMessages(formattedMessages);\n\n      // Scroll to bottom\n      setTimeout(() => {\n        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n      }, 100);\n    } catch (error) {\n      console.error('Mesajlar yüklenirken hata:', error);\n      toast.error('Mesajlar yüklenemedi');\n    }\n  };\n  // Mesaj gönderme\n  const sendMessage = async () => {\n    if (!messageText.trim() || !selectedConversation) return;\n\n    console.log('📤 Mesaj gönderiliyor:', {\n      receiverId: selectedConversation.clientId,\n      content: messageText.trim()\n    });\n\n    try {\n      const response = await api.post('/messages/send', {\n        receiverId: selectedConversation.clientId,\n        content: messageText.trim()\n      });\n\n      console.log('✅ Mesaj gönderildi:', response.data);\n\n      // Sadece mesaj input'unu temizle - mesaj socket'ten gelecek\n      setMessageText('');\n\n    } catch (error) {\n      console.error('Mesaj gönderilirken hata:', error);\n      toast.error('Mesaj gönderilemedi');\n    }\n  };\n\n  useEffect(() => {\n    // Mesajları otomatik kaydır\n    if (messagesEndRef.current && messagesContainerRef.current) {\n      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;\n    }\n  }, [messages]);\n\n  // Mesaj gönderme\n  const handleSendMessage = (e) => {\n    e.preventDefault();\n    sendMessage();\n  };\n\n  // Konuşma seçme\n  const handleSelectConversation = (conversation) => {\n    setSelectedConversation(conversation);\n  };\n\n  // Tarih formatı\n  const formatMessageDate = (dateString) => {\n    const date = new Date(dateString);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    \n    if (date.toDateString() === today.toDateString()) {\n      return format(date, 'HH:mm', { locale: tr });\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Dün ' + format(date, 'HH:mm', { locale: tr });\n    } else {\n      return format(date, 'dd MMM HH:mm', { locale: tr });\n    }\n  };\n\n  // Konuşmaları filtrele\n  const filteredConversations = conversations.filter(conv => {\n    // Arama filtresi\n    const matchesSearch = conv.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         conv.lastMessage.toLowerCase().includes(searchTerm.toLowerCase());\n    \n    // Durum filtresi\n    const matchesFilter = filter === 'all' ||\n                         (filter === 'unread' && conv.unread) ||\n                         (filter === 'archived' && conv.archived) ||\n                         (filter === 'starred' && conv.starred);\n                        \n    return matchesSearch && matchesFilter;\n  });\n\n  // Konuşmayı yıldızla\n  const toggleStar = (id) => {\n    setConversations(prevConversations =>\n      prevConversations.map(conv =>\n        conv.id === id ? { ...conv, starred: !conv.starred } : conv\n      )\n    );\n    \n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({ ...prev, starred: !prev.starred }));\n    }\n  };\n\n  // Konuşmayı arşivle\n  const toggleArchive = (id) => {\n    setConversations(prevConversations =>\n      prevConversations.map(conv =>\n        conv.id === id ? { ...conv, archived: !conv.archived } : conv\n      )\n    );\n    \n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({ ...prev, archived: !prev.archived }));\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto px-4 py-6\">\n      {/* Sayfa Başlığı */}\n      <div className=\"bg-gradient-to-r from-teal-500 to-teal-700 shadow-lg rounded-lg p-6 mb-6 text-white\">\n        <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center\">\n          <div>\n            <h1 className=\"text-2xl font-bold\">Mesajlarım</h1>\n            <p className=\"mt-1 text-indigo-100\">\n              Danışanlarınızla olan tüm yazışmalarınızı buradan yönetebilirsiniz.\n            </p>\n          </div>\n          <div className=\"mt-3 sm:mt-0 flex space-x-2\">\n            <button className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-indigo-800 bg-white hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-300\">\n              <ChatBubbleLeftEllipsisIcon className=\"-ml-1 mr-2 h-5 w-5\" aria-hidden=\"true\" />\n              Yeni Mesaj\n            </button>\n          </div>\n        </div>\n      </div>\n      \n      {/* Mesajlaşma arayüzü */}\n      <div className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n        <div className=\"grid grid-cols-12 h-[75vh]\">\n          {/* Sol Kenar - Konuşma Listesi */}\n          <div className=\"col-span-12 md:col-span-4 border-r border-gray-200 flex flex-col\">\n            <div className=\"p-4 border-b border-gray-200 bg-white\">\n              <h1 className=\"text-xl font-semibold text-gray-800 flex items-center\">\n                <ChatBubbleLeftEllipsisIcon className=\"h-6 w-6 text-indigo-600 mr-2\" />\n                Mesajlar\n              </h1>\n              <div className=\"mt-3 relative\">\n                <input\n                  type=\"text\"\n                  placeholder=\"Konuşmalarda ara...\"\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                />\n                <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400 absolute left-3 top-2.5\" />\n              </div>\n              <div className=\"mt-3 flex space-x-2\">\n                <button\n                  className={`px-3 py-1 text-sm rounded-full ${filter === 'all' \n                    ? 'bg-indigo-100 text-indigo-800' \n                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                  onClick={() => setFilter('all')}\n                >\n                  Tümü\n                </button>\n                <button\n                  className={`px-3 py-1 text-sm rounded-full ${filter === 'unread' \n                    ? 'bg-indigo-100 text-indigo-800' \n                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                  onClick={() => setFilter('unread')}\n                >\n                  Okunmamış\n                </button>\n                <button\n                  className={`px-3 py-1 text-sm rounded-full ${filter === 'starred' \n                    ? 'bg-indigo-100 text-indigo-800' \n                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                  onClick={() => setFilter('starred')}\n                >\n                  Yıldızlı\n                </button>\n                <button\n                  className={`px-3 py-1 text-sm rounded-full ${filter === 'archived' \n                    ? 'bg-indigo-100 text-indigo-800' \n                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                  onClick={() => setFilter('archived')}\n                >\n                  Arşiv\n                </button>\n              </div>\n            </div>\n            <div \n              ref={conversationsRef}\n              style={{\n                height: 'calc(75vh - 145px)',\n                overflowY: 'auto',\n                scrollbarWidth: 'thin',\n                scrollbarColor: '#D1D5DB #F3F4F6'\n              }}\n            >\n              {filteredConversations.length === 0 ? (\n                <div className=\"p-4 text-center text-gray-500\">\n                  Hiç mesajınız yok\n                </div>\n              ) : (\n                filteredConversations.map(conversation => (\n                  <div\n                    key={conversation.id}\n                    className={`p-3 cursor-pointer hover:bg-gray-50 transition-colors duration-200 ${\n                      selectedConversation?.id === conversation.id ? 'bg-indigo-50' : ''\n                    } ${conversation.unread ? 'bg-indigo-50 hover:bg-indigo-100' : ''}`}\n                    onClick={() => handleSelectConversation(conversation)}\n                  >\n                    <div className=\"flex items-start space-x-3\">\n                      <div className=\"relative flex-shrink-0\">\n                        <img\n                          src={conversation.avatar}\n                          alt={conversation.clientName}\n                          className={`h-10 w-10 rounded-full ${\n                            selectedConversation?.id === conversation.id \n                              ? 'ring-2 ring-indigo-600' \n                              : ''\n                          }`}\n                        />\n                        {conversation.status === 'online' && (\n                          <span className=\"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"></span>\n                        )}\n                      </div>\n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex justify-between items-start\">\n                          <h3 className={`text-sm font-medium ${conversation.unread ? 'text-gray-900' : 'text-gray-700'}`}>\n                            {conversation.clientName}\n                          </h3>\n                          <div className=\"flex items-center space-x-1\">\n                            {conversation.starred && (\n                              <StarIcon className=\"h-4 w-4 text-yellow-400 fill-current\" />\n                            )}\n                            <span className=\"text-xs text-gray-500\">\n                              {formatMessageDate(conversation.timestamp)}\n                            </span>\n                          </div>\n                        </div>\n                        <p className={`text-sm truncate mt-1 ${\n                          conversation.unread ? 'text-gray-900 font-medium' : 'text-gray-500'\n                        }`}>\n                          {conversation.lastMessage}\n                        </p>\n                        <div className=\"flex justify-between items-center mt-1\">\n                          <span className=\"text-xs text-gray-500\">\n                            {onlineUsers.has(conversation.clientId)\n                              ? <span className=\"text-green-500 font-medium\">Çevrimiçi</span>\n                              : conversation.lastSeen\n                                ? `Son görülme: ${conversation.lastSeen}`\n                                : ''}\n                          </span>\n                          <div className=\"flex space-x-1\">\n                            <button \n                              onClick={(e) => {\n                                e.stopPropagation();\n                                toggleStar(conversation.id);\n                              }}\n                              className=\"text-gray-400 hover:text-yellow-400\"\n                            >\n                              <StarIcon className={`h-4 w-4 ${conversation.starred ? 'text-yellow-400 fill-current' : ''}`} />\n                            </button>\n                            <button \n                              onClick={(e) => {\n                                e.stopPropagation();\n                                toggleArchive(conversation.id);\n                              }}\n                              className=\"text-gray-400 hover:text-gray-600\"\n                            >\n                              <ArchiveBoxIcon className=\"h-4 w-4\" />\n                            </button>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {conversation.unread && (\n                      <span className=\"inline-flex items-center ml-12 mt-1 px-2 py-0.5 rounded-full text-xs font-medium bg-indigo-600 text-white\">\n                        Yeni\n                      </span>\n                    )}\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n\n          {/* Sağ Taraf - Mesaj Alanı */}\n          <div className=\"col-span-12 md:col-span-8 flex flex-col\">\n            {selectedConversation ? (\n              <>\n                {/* Mesajlaşma Başlığı */}\n                <div className=\"p-4 border-b border-gray-200 bg-white flex justify-between items-center\">\n                  <div className=\"flex items-center\">\n                    <div className=\"relative mr-3\">\n                      <img\n                        src={selectedConversation.avatar}\n                        alt={selectedConversation.clientName}\n                        className=\"h-10 w-10 rounded-full\"\n                      />\n                      {selectedConversation.status === 'online' && (\n                        <span className=\"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"></span>\n                      )}\n                    </div>\n                    <div>\n                      <h2 className=\"text-lg font-medium text-gray-800\">\n                        {selectedConversation.clientName}\n                      </h2>\n                      <p className=\"text-xs text-gray-500\">\n                        {selectedConversation.status === 'online' \n                          ? 'Çevrimiçi' \n                          : selectedConversation.lastSeen \n                            ? `Son görülme: ${selectedConversation.lastSeen}` \n                            : ''}\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <button className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\">\n                      <PhoneIcon className=\"h-5 w-5\" />\n                    </button>\n                    <button className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\">\n                      <VideoCameraIcon className=\"h-5 w-5\" />\n                    </button>\n                    <button className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\">\n                      <InformationCircleIcon className=\"h-5 w-5\" />\n                    </button>\n                    <button className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\">\n                      <EllipsisHorizontalIcon className=\"h-5 w-5\" />\n                    </button>\n                  </div>\n                </div>\n\n                {/* Mesaj Alanı */}\n                <div \n                  ref={messagesContainerRef}\n                  className=\"p-4 bg-gray-50\"\n                  style={{\n                    height: 'calc(75vh - 195px)',\n                    overflowY: 'auto',\n                    scrollbarWidth: 'thin',\n                    scrollbarColor: '#D1D5DB #F3F4F6'\n                  }}\n                >\n                  {messages.map((message, index) => {\n                    const isSender = message.senderId === user.id;\n                    const showAvatar = index === 0 || messages[index - 1].senderId !== message.senderId;\n                    \n                    return (\n                      <div key={message.id} className={`flex ${isSender ? 'justify-end' : 'justify-start'} mb-4`}>\n                        {!isSender && showAvatar && (\n                          <img \n                            src={message.senderAvatar} \n                            alt={message.senderName}\n                            className=\"h-8 w-8 rounded-full mr-2 mt-1\"\n                          />\n                        )}\n                        {!isSender && !showAvatar && <div className=\"w-8 mr-2\"></div>}\n                        <div \n                          className={`max-w-xs md:max-w-md lg:max-w-lg rounded-lg px-4 py-2 ${\n                            isSender \n                              ? 'bg-indigo-600 text-white rounded-br-none' \n                              : 'bg-white text-gray-800 rounded-bl-none border border-gray-200'\n                          }`}\n                        >\n                          <p className=\"text-sm\">{message.text}</p>\n                          <div className={`text-xs mt-1 ${isSender ? 'text-indigo-200' : 'text-gray-500'} flex items-center justify-end`}>\n                            {formatMessageDate(message.timestamp)}\n                            {isSender && (\n                              <CheckCircleIcon\n                                className={`h-3 w-3 ml-1 ${message.read ? 'text-blue-400' : 'text-gray-400'}`}\n                                title={message.read ? 'Okundu' : 'İletildi'}\n                              />\n                            )}\n                          </div>\n                        </div>\n                        {isSender && showAvatar && (\n                          <img \n                            src={message.senderAvatar}\n                            alt={message.senderName}\n                            className=\"h-8 w-8 rounded-full ml-2 mt-1\"\n                          />\n                        )}\n                        {isSender && !showAvatar && <div className=\"w-8 ml-2\"></div>}\n                      </div>\n                    );\n                  })}\n                  <div ref={messagesEndRef} />\n                </div>\n\n                {/* Mesaj Giriş Alanı */}\n                <div className=\"p-3 border-t border-gray-200 bg-white\">\n                  <form onSubmit={handleSendMessage} className=\"flex items-end\">\n                    <button\n                      type=\"button\"\n                      className=\"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\"\n                    >\n                      <PaperClipIcon className=\"h-5 w-5\" />\n                    </button>\n                    <div className=\"flex-1 mx-2\">\n                      <textarea\n                        className=\"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none\"\n                        placeholder=\"Mesajınızı yazın...\"\n                        rows=\"2\"\n                        value={messageText}\n                        onChange={(e) => setMessageText(e.target.value)}\n                        onKeyDown={(e) => {\n                          if (e.key === 'Enter' && !e.shiftKey) {\n                            e.preventDefault();\n                            sendMessage();\n                          }\n                        }}\n                      ></textarea>\n                    </div>\n                    <button\n                      type=\"button\"\n                      className=\"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\"\n                    >\n                      <FaceSmileIcon className=\"h-5 w-5\" />\n                    </button>\n                    <button\n                      type=\"submit\"\n                      disabled={!messageText.trim()}\n                      className={`ml-2 p-2 rounded-full ${\n                        messageText.trim() \n                          ? 'bg-indigo-600 text-white hover:bg-indigo-700' \n                          : 'bg-gray-200 text-gray-400 cursor-not-allowed'\n                      } focus:outline-none`}\n                    >\n                      <PaperAirplaneIcon className=\"h-5 w-5\" />\n                    </button>\n                  </form>\n                </div>\n              </>\n            ) : (\n              // Mesaj seçilmediğinde\n              <div className=\"flex-1 flex flex-col items-center justify-center p-6 bg-gray-50\">\n                <div className=\"w-full max-w-md text-center\">\n                  <ChatBubbleLeftEllipsisIcon className=\"h-16 w-16 text-gray-300 mb-4 mx-auto\" />\n                  <h3 className=\"text-xl font-medium text-gray-800 mb-2\">Mesajlarınız</h3>\n                  <p className=\"text-gray-500 mx-auto\">\n                    Mesajlaşmaya başlamak için sol taraftan bir konuşma seçin veya yeni bir mesaj başlatın.\n                  </p>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default MessagesPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AACvE,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,GAAG,MAAM,uBAAuB;AACvC,SAASC,KAAK,QAAQ,iBAAiB;AACvC,OAAOC,EAAE,MAAM,kBAAkB;AACjC,SACEC,0BAA0B,EAC1BC,mBAAmB,EACnBC,iBAAiB,EACjBC,aAAa,EACbC,aAAa,EACbC,sBAAsB,EACtBC,eAAe,EACfC,eAAe,EACfC,cAAc,EACdC,QAAQ,EACRC,SAAS,EACTC,qBAAqB,QAChB,6BAA6B;AACpC,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,EAAE,QAAQ,iBAAiB;;AAEpC;AACA;AACA;AAFA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGA,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC;EAAK,CAAC,GAAGxB,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC+B,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqC,WAAW,EAAEC,cAAc,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuC,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyC,MAAM,EAAEC,SAAS,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC2C,MAAM,EAAEC,SAAS,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAAC6C,WAAW,EAAEC,cAAc,CAAC,GAAG9C,QAAQ,CAAC,IAAI+C,GAAG,CAAC,CAAC,CAAC;EAEzD,MAAMC,cAAc,GAAG9C,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM+C,gBAAgB,GAAG/C,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMgD,oBAAoB,GAAGhD,MAAM,CAAC,IAAI,CAAC;;EAEzC;EACAD,SAAS,CAAC,MAAM;IACd,MAAMkD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACjD,IAAIF,KAAK,IAAIvB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0B,EAAE,EAAE;MACrBC,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAE5B,IAAI,CAAC0B,EAAE,CAAC;MAC7E,MAAMG,gBAAgB,GAAGlD,EAAE,CAACmD,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB,EAAE;QACpFC,IAAI,EAAE;UAAEV;QAAM,CAAC;QACfW,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;QACpCC,YAAY,EAAE,IAAI;QAClBC,iBAAiB,EAAE,IAAI;QACvBC,oBAAoB,EAAE,CAAC;QACvBC,OAAO,EAAE;MACX,CAAC,CAAC;MAEFtB,SAAS,CAACa,gBAAgB,CAAC;;MAE3B;MACAA,gBAAgB,CAACU,EAAE,CAAC,SAAS,EAAE,MAAM;QACnCZ,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEC,gBAAgB,CAACH,EAAE,CAAC;QAC3EC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEC,gBAAgB,CAACW,SAAS,CAAC;QACvEb,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEC,gBAAgB,CAACY,KAAK,CAAC;QAC/D;QACAZ,gBAAgB,CAACa,IAAI,CAAC,aAAa,CAAC;QACpC;QACAb,gBAAgB,CAACa,IAAI,CAAC,kBAAkB,CAAC;MAC3C,CAAC,CAAC;MAEFb,gBAAgB,CAACU,EAAE,CAAC,YAAY,EAAGI,MAAM,IAAK;QAC5ChB,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEe,MAAM,CAAC;MAChE,CAAC,CAAC;MAEFd,gBAAgB,CAACU,EAAE,CAAC,eAAe,EAAGK,KAAK,IAAK;QAC9CjB,OAAO,CAACiB,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC/D,CAAC,CAAC;MAEFf,gBAAgB,CAACU,EAAE,CAAC,WAAW,EAAGM,aAAa,IAAK;QAClDlB,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEiB,aAAa,CAAC;MAC9E,CAAC,CAAC;MAEFhB,gBAAgB,CAACU,EAAE,CAAC,iBAAiB,EAAGK,KAAK,IAAK;QAChDjB,OAAO,CAACiB,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACvE,CAAC,CAAC;;MAEF;MACA,MAAME,iBAAiB,GAAGC,WAAW,CAAC,MAAM;QAC1C,IAAIlB,gBAAgB,CAACW,SAAS,EAAE;UAC9BX,gBAAgB,CAACa,IAAI,CAAC,MAAM,EAAGM,QAAQ,IAAK;YAC1C,IAAIA,QAAQ,KAAK,MAAM,EAAE;cACvBrB,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;YACxC;UACF,CAAC,CAAC;QACJ;MACF,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;MAEX,OAAO,MAAM;QACXqB,aAAa,CAACH,iBAAiB,CAAC;QAChCjB,gBAAgB,CAACqB,UAAU,CAAC,CAAC;MAC/B,CAAC;IACH,CAAC,MAAM;MACLvB,OAAO,CAACC,GAAG,CAAC,kEAAkE,EAAE;QAC9EuB,QAAQ,EAAE,CAAC,CAAC5B,KAAK;QACjB6B,OAAO,EAAE,CAAC,EAACpD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0B,EAAE;MACrB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC1B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0B,EAAE,CAAC,CAAC;;EAEd;EACArD,SAAS,CAAC,MAAM;IACd,IAAI0C,MAAM,IAAIf,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0B,EAAE,EAAE;MACtBC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;MAErD;MACA,IAAIb,MAAM,CAACyB,SAAS,EAAE;QACpBb,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;QACnEb,MAAM,CAAC2B,IAAI,CAAC,kBAAkB,CAAC;MACjC;;MAEA;MACA,MAAMW,gBAAgB,GAAIC,OAAO,IAAK;QACpC3B,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE0B,OAAO,CAAC;QACrD3B,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE5B,IAAI,CAAC0B,EAAE,CAAC;QACnDC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE0B,OAAO,CAACC,QAAQ,CAAC;QAC9D5B,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE0B,OAAO,CAACE,cAAc,CAAC;;QAE1E;QACApD,gBAAgB,CAACqD,IAAI,IAAI;UACvB9B,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;UACrD,OAAO6B,IAAI,CAACC,GAAG,CAACC,IAAI,IAClBA,IAAI,CAACjC,EAAE,KAAK4B,OAAO,CAACE,cAAc,GAC9B;YAAE,GAAGG,IAAI;YAAEC,WAAW,EAAEN,OAAO,CAACO,OAAO;YAAEC,SAAS,EAAER,OAAO,CAACS,SAAS;YAAEC,MAAM,EAAEV,OAAO,CAACC,QAAQ,KAAKvD,IAAI,CAAC0B;UAAG,CAAC,GAC7GiC,IACN,CAAC;QACH,CAAC,CAAC;;QAEF;QACArD,uBAAuB,CAAC2D,eAAe,IAAI;UACzCtC,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEqC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEvC,EAAE,CAAC;UAC7E,IAAIuC,eAAe,IAAIX,OAAO,CAACE,cAAc,KAAKS,eAAe,CAACvC,EAAE,EAAE;YACpEC,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;YAC/DpB,WAAW,CAACiD,IAAI,IAAI;cAClB;cACA,MAAMS,aAAa,GAAGT,IAAI,CAACU,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC1C,EAAE,KAAK4B,OAAO,CAAC5B,EAAE,CAAC;cAC7D,IAAIwC,aAAa,EAAE;gBACjBvC,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE0B,OAAO,CAAC5B,EAAE,CAAC;gBACvE,OAAO+B,IAAI;cACb;cAEA,OAAO,CAAC,GAAGA,IAAI,EAAE;gBACf/B,EAAE,EAAE4B,OAAO,CAAC5B,EAAE;gBACd6B,QAAQ,EAAED,OAAO,CAACC,QAAQ;gBAC1Bc,UAAU,EAAEf,OAAO,CAACgB,MAAM,CAACC,IAAI;gBAC/BC,YAAY,EAAE,oCAAoCC,kBAAkB,CAACnB,OAAO,CAACgB,MAAM,CAACC,IAAI,CAAC,qDAAqD;gBAC9IG,IAAI,EAAEpB,OAAO,CAACO,OAAO;gBACrBC,SAAS,EAAER,OAAO,CAACS,SAAS;gBAC5BY,IAAI,EAAErB,OAAO,CAACsB,MAAM;gBACpBC,SAAS,EAAE,IAAI,CAAC;cAClB,CAAC,CAAC;YACJ,CAAC,CAAC;;YAEF;YACA,IAAIvB,OAAO,CAACC,QAAQ,KAAKvD,IAAI,CAAC0B,EAAE,EAAE;cAChCC,OAAO,CAACC,GAAG,CAAC,oEAAoE,CAAC;cACjFkD,UAAU,CAAC,MAAM;gBACfC,sBAAsB,CAACzB,OAAO,CAACE,cAAc,CAAC;cAChD,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;YACX;;YAEA;YACAsB,UAAU,CAAC,MAAM;cAAA,IAAAE,qBAAA;cACf,CAAAA,qBAAA,GAAA5D,cAAc,CAAC6D,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;gBAAEC,QAAQ,EAAE;cAAS,CAAC,CAAC;YAChE,CAAC,EAAE,GAAG,CAAC;UACT,CAAC,MAAM;YACLxD,OAAO,CAACC,GAAG,CAAC,oFAAoF,CAAC;UACnG;UACA,OAAOqC,eAAe;QACxB,CAAC,CAAC;MACJ,CAAC;MAED,MAAMmB,iBAAiB,GAAI9B,OAAO,IAAK;QACrC3B,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE0B,OAAO,CAAC;MACnD,CAAC;MAED,MAAM+B,sBAAsB,GAAIC,IAAI,IAAK;QACvC3D,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE0D,IAAI,CAAC;QACnDpE,cAAc,CAACuC,IAAI,IAAI;UACrB,MAAM8B,MAAM,GAAG,IAAIpE,GAAG,CAACsC,IAAI,CAAC;UAC5B,IAAI6B,IAAI,CAACE,MAAM,KAAK,QAAQ,EAAE;YAC5BD,MAAM,CAACE,GAAG,CAACH,IAAI,CAACI,MAAM,CAAC;UACzB,CAAC,MAAM;YACLH,MAAM,CAACI,MAAM,CAACL,IAAI,CAACI,MAAM,CAAC;UAC5B;UACA/D,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEgE,KAAK,CAACC,IAAI,CAACN,MAAM,CAAC,CAAC;UACnE,OAAOA,MAAM;QACf,CAAC,CAAC;MACJ,CAAC;MAED,MAAMO,qBAAqB,GAAIC,OAAO,IAAK;QACzCpE,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEmE,OAAO,CAAC;QAC9D7E,cAAc,CAAC,IAAIC,GAAG,CAAC4E,OAAO,CAAC,CAAC;MAClC,CAAC;MAED,MAAMC,gBAAgB,GAAIV,IAAI,IAAK;QACjC;QACA3D,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE0D,IAAI,CAAC;MACnC,CAAC;MAED,MAAMW,uBAAuB,GAAIX,IAAI,IAAK;QACxC;QACA3D,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE0D,IAAI,CAAC;MAC3C,CAAC;MAED,MAAMY,kBAAkB,GAAIZ,IAAI,IAAK;QACnC3D,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE0D,IAAI,CAAC;QACrD3D,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE0D,IAAI,CAACa,MAAM,EAAE,eAAe,EAAEnG,IAAI,CAAC0B,EAAE,CAAC;QAC/EC,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE0D,IAAI,CAACc,UAAU,CAAC;;QAEvE;QACA,IAAId,IAAI,CAACa,MAAM,KAAKnG,IAAI,CAAC0B,EAAE,EAAE;UAC3BlB,WAAW,CAACiD,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACU,GAAG,IAAI;YAClC;YACA,IAAIA,GAAG,CAACb,QAAQ,KAAKvD,IAAI,CAAC0B,EAAE,IAAI0C,GAAG,CAACZ,cAAc,KAAK8B,IAAI,CAAC9B,cAAc,EAAE;cAC1E7B,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEwC,GAAG,CAAC1C,EAAE,CAAC;cAC9D,OAAO;gBAAE,GAAG0C,GAAG;gBAAEO,IAAI,EAAE;cAAK,CAAC;YAC/B;YACA;YACA,IAAIW,IAAI,CAACc,UAAU,CAACC,QAAQ,CAACjC,GAAG,CAAC1C,EAAE,CAAC,EAAE;cACpCC,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAEwC,GAAG,CAAC1C,EAAE,CAAC;cACpE,OAAO;gBAAE,GAAG0C,GAAG;gBAAEO,IAAI,EAAE;cAAK,CAAC;YAC/B;YACA,OAAOP,GAAG;UACZ,CAAC,CAAC,CAAC;;UAEH;UACAhE,gBAAgB,CAACqD,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IACpCA,IAAI,CAACjC,EAAE,KAAK4D,IAAI,CAAC9B,cAAc,GAC3B;YAAE,GAAGG,IAAI;YAAEK,MAAM,EAAE;UAAM,CAAC,GAC1BL,IACN,CAAC,CAAC;QACJ;MACF,CAAC;;MAED;MACA5C,MAAM,CAACwB,EAAE,CAAC,aAAa,EAAEc,gBAAgB,CAAC;MAC1CtC,MAAM,CAACwB,EAAE,CAAC,cAAc,EAAE6C,iBAAiB,CAAC;MAC5CrE,MAAM,CAACwB,EAAE,CAAC,oBAAoB,EAAE8C,sBAAsB,CAAC;MACvDtE,MAAM,CAACwB,EAAE,CAAC,aAAa,EAAEyD,gBAAgB,CAAC;MAC1CjF,MAAM,CAACwB,EAAE,CAAC,qBAAqB,EAAE0D,uBAAuB,CAAC;MACzDlF,MAAM,CAACwB,EAAE,CAAC,eAAe,EAAE2D,kBAAkB,CAAC;MAC9CnF,MAAM,CAACwB,EAAE,CAAC,mBAAmB,EAAEuD,qBAAqB,CAAC;;MAErD;MACA,OAAO,MAAM;QACX/E,MAAM,CAACuF,GAAG,CAAC,aAAa,EAAEjD,gBAAgB,CAAC;QAC3CtC,MAAM,CAACuF,GAAG,CAAC,cAAc,EAAElB,iBAAiB,CAAC;QAC7CrE,MAAM,CAACuF,GAAG,CAAC,oBAAoB,EAAEjB,sBAAsB,CAAC;QACxDtE,MAAM,CAACuF,GAAG,CAAC,aAAa,EAAEN,gBAAgB,CAAC;QAC3CjF,MAAM,CAACuF,GAAG,CAAC,qBAAqB,EAAEL,uBAAuB,CAAC;QAC1DlF,MAAM,CAACuF,GAAG,CAAC,eAAe,EAAEJ,kBAAkB,CAAC;QAC/CnF,MAAM,CAACuF,GAAG,CAAC,mBAAmB,EAAER,qBAAqB,CAAC;MACxD,CAAC;IACH;EACF,CAAC,EAAE,CAAC/E,MAAM,EAAEf,IAAI,CAAC0B,EAAE,CAAC,CAAC;;EAErB;EACArD,SAAS,CAAC,MAAM;IACdkI,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrI,QAAQ,CAAC,IAAI+C,GAAG,CAAC,CAAC,CAAC;EAEzE9C,SAAS,CAAC,MAAM;IACd,IAAI0C,MAAM,IAAIZ,aAAa,CAACuG,MAAM,GAAG,CAAC,EAAE;MACtC/E,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEzB,aAAa,CAACuG,MAAM,EAAE,cAAc,CAAC;MAEnGvG,aAAa,CAACwG,OAAO,CAACC,YAAY,IAAI;QACpC,IAAI,CAACJ,mBAAmB,CAACK,GAAG,CAACD,YAAY,CAAClF,EAAE,CAAC,EAAE;UAC7CX,MAAM,CAAC2B,IAAI,CAAC,mBAAmB,EAAEkE,YAAY,CAAClF,EAAE,CAAC;UACjDC,OAAO,CAACC,GAAG,CAAC,2BAA2BgF,YAAY,CAAClF,EAAE,UAAU,CAAC;UACjE+E,sBAAsB,CAAChD,IAAI,IAAI,IAAItC,GAAG,CAAC,CAAC,GAAGsC,IAAI,EAAEmD,YAAY,CAAClF,EAAE,CAAC,CAAC,CAAC;QACrE;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACX,MAAM,EAAEZ,aAAa,EAAEqG,mBAAmB,CAAC,CAAC;;EAEhD;EACA,MAAMD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFrG,YAAY,CAAC,IAAI,CAAC;MAClB,MAAM8C,QAAQ,GAAG,MAAMvE,GAAG,CAACqI,GAAG,CAAC,yBAAyB,CAAC;;MAEzD;MACA,MAAMC,sBAAsB,GAAG/D,QAAQ,CAACsC,IAAI,CAACnF,aAAa,CAACuD,GAAG,CAACkD,YAAY;QAAA,IAAAI,qBAAA,EAAAC,sBAAA;QAAA,OAAK;UAC9EvF,EAAE,EAAEkF,YAAY,CAAClF,EAAE;UACnBwF,QAAQ,EAAEN,YAAY,CAACO,SAAS,CAACzF,EAAE;UACnC0F,UAAU,EAAER,YAAY,CAACO,SAAS,CAAC5C,IAAI;UACvCX,WAAW,EAAE,EAAAoD,qBAAA,GAAAJ,YAAY,CAAChD,WAAW,cAAAoD,qBAAA,uBAAxBA,qBAAA,CAA0BnD,OAAO,KAAI,iBAAiB;UACnEC,SAAS,EAAE,EAAAmD,sBAAA,GAAAL,YAAY,CAAChD,WAAW,cAAAqD,sBAAA,uBAAxBA,sBAAA,CAA0BnD,SAAS,KAAI8C,YAAY,CAAC7C,SAAS;UACxEC,MAAM,EAAE4C,YAAY,CAAChD,WAAW,GAAG,CAACgD,YAAY,CAAChD,WAAW,CAACgB,MAAM,IAAIgC,YAAY,CAAChD,WAAW,CAACL,QAAQ,KAAKvD,IAAI,CAAC0B,EAAE,GAAG,KAAK;UAC5H2F,MAAM,EAAE,oCAAoC5C,kBAAkB,CAACmC,YAAY,CAACO,SAAS,CAAC5C,IAAI,CAAC,qDAAqD;UAChJiB,MAAM,EAAEvE,WAAW,CAAC4F,GAAG,CAACD,YAAY,CAACO,SAAS,CAACzF,EAAE,CAAC,GAAG,QAAQ,GAAG,SAAS;UACzE4F,OAAO,EAAE,KAAK;UACdC,QAAQ,EAAE;QACZ,CAAC;MAAA,CAAC,CAAC;MAEHnH,gBAAgB,CAAC2G,sBAAsB,CAAC;IAC1C,CAAC,CAAC,OAAOnE,KAAK,EAAE;MACdjB,OAAO,CAACiB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDlE,KAAK,CAACkE,KAAK,CAAC,wBAAwB,CAAC;IACvC,CAAC,SAAS;MACR1C,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA7B,SAAS,CAAC,MAAM;IACd,IAAIgC,oBAAoB,EAAE;MACxBmH,YAAY,CAACnH,oBAAoB,CAACqB,EAAE,CAAC;MACrCqD,sBAAsB,CAAC1E,oBAAoB,CAACqB,EAAE,CAAC;;MAE/C;MACAtB,gBAAgB,CAACqD,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IACpCA,IAAI,CAACjC,EAAE,KAAKrB,oBAAoB,CAACqB,EAAE,GAC/B;QAAE,GAAGiC,IAAI;QAAEK,MAAM,EAAE;MAAM,CAAC,GAC1BL,IACN,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACtD,oBAAoB,EAAEmH,YAAY,EAAEzC,sBAAsB,CAAC,CAAC;;EAEhE;EACA,MAAMA,sBAAsB,GAAG,MAAOvB,cAAc,IAAK;IACvD,IAAI;MACF;MACA,MAAMR,QAAQ,GAAG,MAAMvE,GAAG,CAACgJ,GAAG,CAAC,2BAA2BjE,cAAc,OAAO,CAAC;MAEhF,IAAIR,QAAQ,CAACsC,IAAI,CAACoC,YAAY,GAAG,CAAC,EAAE;QAClC/F,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEoB,QAAQ,CAACsC,IAAI,CAACoC,YAAY,EAAE,kBAAkB,CAAC;;QAEhF;QACAlH,WAAW,CAACiD,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACU,GAAG,IAC9BA,GAAG,CAACb,QAAQ,KAAKvD,IAAI,CAAC0B,EAAE,GAAG;UAAE,GAAG0C,GAAG;UAAEO,IAAI,EAAE;QAAK,CAAC,GAAGP,GACtD,CAAC,CAAC;;QAEF;QACA,IAAIrD,MAAM,EAAE;UACVA,MAAM,CAAC2B,IAAI,CAAC,oBAAoB,EAAE;YAChCc,cAAc;YACd4C,UAAU,EAAE,EAAE,CAAC;UACjB,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC,OAAOxD,KAAK,EAAE;MACdjB,OAAO,CAACiB,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;IACpE;EACF,CAAC;;EAED;EACA,MAAM4E,YAAY,GAAG,MAAOhE,cAAc,IAAK;IAC7C,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMvE,GAAG,CAACqI,GAAG,CAAC,2BAA2BtD,cAAc,EAAE,CAAC;;MAE3E;MACA,MAAMmE,iBAAiB,GAAG3E,QAAQ,CAACsC,IAAI,CAAC/E,QAAQ,CAACmD,GAAG,CAACJ,OAAO,KAAK;QAC/D5B,EAAE,EAAE4B,OAAO,CAAC5B,EAAE;QACd6B,QAAQ,EAAED,OAAO,CAACC,QAAQ;QAC1Bc,UAAU,EAAEf,OAAO,CAACgB,MAAM,CAACC,IAAI;QAC/BC,YAAY,EAAE,oCAAoCC,kBAAkB,CAACnB,OAAO,CAACgB,MAAM,CAACC,IAAI,CAAC,qDAAqD;QAC9IG,IAAI,EAAEpB,OAAO,CAACO,OAAO;QACrBC,SAAS,EAAER,OAAO,CAACS,SAAS;QAC5BY,IAAI,EAAErB,OAAO,CAACsB,MAAM;QACpBC,SAAS,EAAE,IAAI;QAAE;QACjB+C,WAAW,EAAEtE,OAAO,CAACsE;MACvB,CAAC,CAAC,CAAC;MAEHpH,WAAW,CAACmH,iBAAiB,CAAC;;MAE9B;MACA7C,UAAU,CAAC,MAAM;QAAA,IAAA+C,sBAAA;QACf,CAAAA,sBAAA,GAAAzG,cAAc,CAAC6D,OAAO,cAAA4C,sBAAA,uBAAtBA,sBAAA,CAAwB3C,cAAc,CAAC;UAAEC,QAAQ,EAAE;QAAS,CAAC,CAAC;MAChE,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAOvC,KAAK,EAAE;MACdjB,OAAO,CAACiB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDlE,KAAK,CAACkE,KAAK,CAAC,sBAAsB,CAAC;IACrC;EACF,CAAC;EACD;EACA,MAAMkF,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAACrH,WAAW,CAACsH,IAAI,CAAC,CAAC,IAAI,CAAC1H,oBAAoB,EAAE;IAElDsB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;MACpCoG,UAAU,EAAE3H,oBAAoB,CAAC6G,QAAQ;MACzCrD,OAAO,EAAEpD,WAAW,CAACsH,IAAI,CAAC;IAC5B,CAAC,CAAC;IAEF,IAAI;MACF,MAAM/E,QAAQ,GAAG,MAAMvE,GAAG,CAACwJ,IAAI,CAAC,gBAAgB,EAAE;QAChDD,UAAU,EAAE3H,oBAAoB,CAAC6G,QAAQ;QACzCrD,OAAO,EAAEpD,WAAW,CAACsH,IAAI,CAAC;MAC5B,CAAC,CAAC;MAEFpG,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEoB,QAAQ,CAACsC,IAAI,CAAC;;MAEjD;MACA5E,cAAc,CAAC,EAAE,CAAC;IAEpB,CAAC,CAAC,OAAOkC,KAAK,EAAE;MACdjB,OAAO,CAACiB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDlE,KAAK,CAACkE,KAAK,CAAC,qBAAqB,CAAC;IACpC;EACF,CAAC;EAEDvE,SAAS,CAAC,MAAM;IACd;IACA,IAAI+C,cAAc,CAAC6D,OAAO,IAAI3D,oBAAoB,CAAC2D,OAAO,EAAE;MAC1D3D,oBAAoB,CAAC2D,OAAO,CAACiD,SAAS,GAAG5G,oBAAoB,CAAC2D,OAAO,CAACkD,YAAY;IACpF;EACF,CAAC,EAAE,CAAC5H,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAM6H,iBAAiB,GAAIC,CAAC,IAAK;IAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBR,WAAW,CAAC,CAAC;EACf,CAAC;;EAED;EACA,MAAMS,wBAAwB,GAAI3B,YAAY,IAAK;IACjDtG,uBAAuB,CAACsG,YAAY,CAAC;EACvC,CAAC;;EAED;EACA,MAAM4B,iBAAiB,GAAIC,UAAU,IAAK;IACxC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,KAAK,GAAG,IAAID,IAAI,CAAC,CAAC;IACxB,MAAME,SAAS,GAAG,IAAIF,IAAI,CAACC,KAAK,CAAC;IACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IAE1C,IAAIL,IAAI,CAACM,YAAY,CAAC,CAAC,KAAKJ,KAAK,CAACI,YAAY,CAAC,CAAC,EAAE;MAChD,OAAOxJ,MAAM,CAACkJ,IAAI,EAAE,OAAO,EAAE;QAAEO,MAAM,EAAExJ;MAAG,CAAC,CAAC;IAC9C,CAAC,MAAM,IAAIiJ,IAAI,CAACM,YAAY,CAAC,CAAC,KAAKH,SAAS,CAACG,YAAY,CAAC,CAAC,EAAE;MAC3D,OAAO,MAAM,GAAGxJ,MAAM,CAACkJ,IAAI,EAAE,OAAO,EAAE;QAAEO,MAAM,EAAExJ;MAAG,CAAC,CAAC;IACvD,CAAC,MAAM;MACL,OAAOD,MAAM,CAACkJ,IAAI,EAAE,cAAc,EAAE;QAAEO,MAAM,EAAExJ;MAAG,CAAC,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAMyJ,qBAAqB,GAAG/I,aAAa,CAACU,MAAM,CAAC8C,IAAI,IAAI;IACzD;IACA,MAAMwF,aAAa,GAAGxF,IAAI,CAACyD,UAAU,CAACgC,WAAW,CAAC,CAAC,CAAC/C,QAAQ,CAAC1F,UAAU,CAACyI,WAAW,CAAC,CAAC,CAAC,IACjEzF,IAAI,CAACC,WAAW,CAACwF,WAAW,CAAC,CAAC,CAAC/C,QAAQ,CAAC1F,UAAU,CAACyI,WAAW,CAAC,CAAC,CAAC;;IAEtF;IACA,MAAMC,aAAa,GAAGxI,MAAM,KAAK,KAAK,IAChBA,MAAM,KAAK,QAAQ,IAAI8C,IAAI,CAACK,MAAO,IACnCnD,MAAM,KAAK,UAAU,IAAI8C,IAAI,CAAC4D,QAAS,IACvC1G,MAAM,KAAK,SAAS,IAAI8C,IAAI,CAAC2D,OAAQ;IAE3D,OAAO6B,aAAa,IAAIE,aAAa;EACvC,CAAC,CAAC;;EAEF;EACA,MAAMC,UAAU,GAAI5H,EAAE,IAAK;IACzBtB,gBAAgB,CAACmJ,iBAAiB,IAChCA,iBAAiB,CAAC7F,GAAG,CAACC,IAAI,IACxBA,IAAI,CAACjC,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGiC,IAAI;MAAE2D,OAAO,EAAE,CAAC3D,IAAI,CAAC2D;IAAQ,CAAC,GAAG3D,IACzD,CACF,CAAC;IAED,IAAItD,oBAAoB,IAAIA,oBAAoB,CAACqB,EAAE,KAAKA,EAAE,EAAE;MAC1DpB,uBAAuB,CAACmD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE6D,OAAO,EAAE,CAAC7D,IAAI,CAAC6D;MAAQ,CAAC,CAAC,CAAC;IACxE;EACF,CAAC;;EAED;EACA,MAAMkC,aAAa,GAAI9H,EAAE,IAAK;IAC5BtB,gBAAgB,CAACmJ,iBAAiB,IAChCA,iBAAiB,CAAC7F,GAAG,CAACC,IAAI,IACxBA,IAAI,CAACjC,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGiC,IAAI;MAAE4D,QAAQ,EAAE,CAAC5D,IAAI,CAAC4D;IAAS,CAAC,GAAG5D,IAC3D,CACF,CAAC;IAED,IAAItD,oBAAoB,IAAIA,oBAAoB,CAACqB,EAAE,KAAKA,EAAE,EAAE;MAC1DpB,uBAAuB,CAACmD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE8D,QAAQ,EAAE,CAAC9D,IAAI,CAAC8D;MAAS,CAAC,CAAC,CAAC;IAC1E;EACF,CAAC;EAED,IAAItH,SAAS,EAAE;IACb,oBACEN,OAAA;MAAK8J,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5D/J,OAAA;QAAK8J,SAAS,EAAC;MAA6E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChG,CAAC;EAEV;EAEA,oBACEnK,OAAA;IAAK8J,SAAS,EAAC,6BAA6B;IAAAC,QAAA,gBAE1C/J,OAAA;MAAK8J,SAAS,EAAC,qFAAqF;MAAAC,QAAA,eAClG/J,OAAA;QAAK8J,SAAS,EAAC,uEAAuE;QAAAC,QAAA,gBACpF/J,OAAA;UAAA+J,QAAA,gBACE/J,OAAA;YAAI8J,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClDnK,OAAA;YAAG8J,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAC;UAEpC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNnK,OAAA;UAAK8J,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAC1C/J,OAAA;YAAQ8J,SAAS,EAAC,6NAA6N;YAAAC,QAAA,gBAC7O/J,OAAA,CAACf,0BAA0B;cAAC6K,SAAS,EAAC,oBAAoB;cAAC,eAAY;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cAElF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnK,OAAA;MAAK8J,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5D/J,OAAA;QAAK8J,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBAEzC/J,OAAA;UAAK8J,SAAS,EAAC,kEAAkE;UAAAC,QAAA,gBAC/E/J,OAAA;YAAK8J,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD/J,OAAA;cAAI8J,SAAS,EAAC,uDAAuD;cAAAC,QAAA,gBACnE/J,OAAA,CAACf,0BAA0B;gBAAC6K,SAAS,EAAC;cAA8B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAEzE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLnK,OAAA;cAAK8J,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B/J,OAAA;gBACEoK,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,0BAAqB;gBACjCP,SAAS,EAAC,qHAAqH;gBAC/HQ,KAAK,EAAEtJ,UAAW;gBAClBuJ,QAAQ,EAAG7B,CAAC,IAAKzH,aAAa,CAACyH,CAAC,CAAC8B,MAAM,CAACF,KAAK;cAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACFnK,OAAA,CAACd,mBAAmB;gBAAC4K,SAAS,EAAC;cAA+C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC,eACNnK,OAAA;cAAK8J,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAClC/J,OAAA;gBACE8J,SAAS,EAAE,kCAAkC5I,MAAM,KAAK,KAAK,GACzD,+BAA+B,GAC/B,6CAA6C,EAAG;gBACpDuJ,OAAO,EAAEA,CAAA,KAAMtJ,SAAS,CAAC,KAAK,CAAE;gBAAA4I,QAAA,EACjC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTnK,OAAA;gBACE8J,SAAS,EAAE,kCAAkC5I,MAAM,KAAK,QAAQ,GAC5D,+BAA+B,GAC/B,6CAA6C,EAAG;gBACpDuJ,OAAO,EAAEA,CAAA,KAAMtJ,SAAS,CAAC,QAAQ,CAAE;gBAAA4I,QAAA,EACpC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTnK,OAAA;gBACE8J,SAAS,EAAE,kCAAkC5I,MAAM,KAAK,SAAS,GAC7D,+BAA+B,GAC/B,6CAA6C,EAAG;gBACpDuJ,OAAO,EAAEA,CAAA,KAAMtJ,SAAS,CAAC,SAAS,CAAE;gBAAA4I,QAAA,EACrC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTnK,OAAA;gBACE8J,SAAS,EAAE,kCAAkC5I,MAAM,KAAK,UAAU,GAC9D,+BAA+B,GAC/B,6CAA6C,EAAG;gBACpDuJ,OAAO,EAAEA,CAAA,KAAMtJ,SAAS,CAAC,UAAU,CAAE;gBAAA4I,QAAA,EACtC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNnK,OAAA;YACE0K,GAAG,EAAEhJ,gBAAiB;YACtBiJ,KAAK,EAAE;cACLC,MAAM,EAAE,oBAAoB;cAC5BC,SAAS,EAAE,MAAM;cACjBC,cAAc,EAAE,MAAM;cACtBC,cAAc,EAAE;YAClB,CAAE;YAAAhB,QAAA,EAEDR,qBAAqB,CAACxC,MAAM,KAAK,CAAC,gBACjC/G,OAAA;cAAK8J,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAE/C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GAENZ,qBAAqB,CAACxF,GAAG,CAACkD,YAAY,iBACpCjH,OAAA;cAEE8J,SAAS,EAAE,sEACT,CAAApJ,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEqB,EAAE,MAAKkF,YAAY,CAAClF,EAAE,GAAG,cAAc,GAAG,EAAE,IAChEkF,YAAY,CAAC5C,MAAM,GAAG,kCAAkC,GAAG,EAAE,EAAG;cACpEoG,OAAO,EAAEA,CAAA,KAAM7B,wBAAwB,CAAC3B,YAAY,CAAE;cAAA8C,QAAA,gBAEtD/J,OAAA;gBAAK8J,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzC/J,OAAA;kBAAK8J,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrC/J,OAAA;oBACEgL,GAAG,EAAE/D,YAAY,CAACS,MAAO;oBACzBuD,GAAG,EAAEhE,YAAY,CAACQ,UAAW;oBAC7BqC,SAAS,EAAE,0BACT,CAAApJ,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEqB,EAAE,MAAKkF,YAAY,CAAClF,EAAE,GACxC,wBAAwB,GACxB,EAAE;kBACL;oBAAAiI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,EACDlD,YAAY,CAACpB,MAAM,KAAK,QAAQ,iBAC/B7F,OAAA;oBAAM8J,SAAS,EAAC;kBAAyF;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CACjH;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACNnK,OAAA;kBAAK8J,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7B/J,OAAA;oBAAK8J,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,gBAC/C/J,OAAA;sBAAI8J,SAAS,EAAE,uBAAuB7C,YAAY,CAAC5C,MAAM,GAAG,eAAe,GAAG,eAAe,EAAG;sBAAA0F,QAAA,EAC7F9C,YAAY,CAACQ;oBAAU;sBAAAuC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB,CAAC,eACLnK,OAAA;sBAAK8J,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,GACzC9C,YAAY,CAACU,OAAO,iBACnB3H,OAAA,CAACN,QAAQ;wBAACoK,SAAS,EAAC;sBAAsC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAC7D,eACDnK,OAAA;wBAAM8J,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EACpClB,iBAAiB,CAAC5B,YAAY,CAAC9C,SAAS;sBAAC;wBAAA6F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNnK,OAAA;oBAAG8J,SAAS,EAAE,yBACZ7C,YAAY,CAAC5C,MAAM,GAAG,2BAA2B,GAAG,eAAe,EAClE;oBAAA0F,QAAA,EACA9C,YAAY,CAAChD;kBAAW;oBAAA+F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,eACJnK,OAAA;oBAAK8J,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrD/J,OAAA;sBAAM8J,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EACpCzI,WAAW,CAAC4F,GAAG,CAACD,YAAY,CAACM,QAAQ,CAAC,gBACnCvH,OAAA;wBAAM8J,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,EAAC;sBAAS;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,GAC7DlD,YAAY,CAACiE,QAAQ,GACnB,gBAAgBjE,YAAY,CAACiE,QAAQ,EAAE,GACvC;oBAAE;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACPnK,OAAA;sBAAK8J,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,gBAC7B/J,OAAA;wBACEyK,OAAO,EAAG/B,CAAC,IAAK;0BACdA,CAAC,CAACyC,eAAe,CAAC,CAAC;0BACnBxB,UAAU,CAAC1C,YAAY,CAAClF,EAAE,CAAC;wBAC7B,CAAE;wBACF+H,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,eAE/C/J,OAAA,CAACN,QAAQ;0BAACoK,SAAS,EAAE,WAAW7C,YAAY,CAACU,OAAO,GAAG,8BAA8B,GAAG,EAAE;wBAAG;0BAAAqC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1F,CAAC,eACTnK,OAAA;wBACEyK,OAAO,EAAG/B,CAAC,IAAK;0BACdA,CAAC,CAACyC,eAAe,CAAC,CAAC;0BACnBtB,aAAa,CAAC5C,YAAY,CAAClF,EAAE,CAAC;wBAChC,CAAE;wBACF+H,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,eAE7C/J,OAAA,CAACP,cAAc;0BAACqK,SAAS,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACLlD,YAAY,CAAC5C,MAAM,iBAClBrE,OAAA;gBAAM8J,SAAS,EAAC,2GAA2G;gBAAAC,QAAA,EAAC;cAE5H;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP;YAAA,GA3EIlD,YAAY,CAAClF,EAAE;cAAAiI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA4EjB,CACN;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnK,OAAA;UAAK8J,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EACrDrJ,oBAAoB,gBACnBV,OAAA,CAAAE,SAAA;YAAA6J,QAAA,gBAEE/J,OAAA;cAAK8J,SAAS,EAAC,yEAAyE;cAAAC,QAAA,gBACtF/J,OAAA;gBAAK8J,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC/J,OAAA;kBAAK8J,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5B/J,OAAA;oBACEgL,GAAG,EAAEtK,oBAAoB,CAACgH,MAAO;oBACjCuD,GAAG,EAAEvK,oBAAoB,CAAC+G,UAAW;oBACrCqC,SAAS,EAAC;kBAAwB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC,EACDzJ,oBAAoB,CAACmF,MAAM,KAAK,QAAQ,iBACvC7F,OAAA;oBAAM8J,SAAS,EAAC;kBAAyF;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CACjH;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACNnK,OAAA;kBAAA+J,QAAA,gBACE/J,OAAA;oBAAI8J,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAC9CrJ,oBAAoB,CAAC+G;kBAAU;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACLnK,OAAA;oBAAG8J,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EACjCrJ,oBAAoB,CAACmF,MAAM,KAAK,QAAQ,GACrC,WAAW,GACXnF,oBAAoB,CAACwK,QAAQ,GAC3B,gBAAgBxK,oBAAoB,CAACwK,QAAQ,EAAE,GAC/C;kBAAE;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNnK,OAAA;gBAAK8J,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1C/J,OAAA;kBAAQ8J,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,eAClE/J,OAAA,CAACL,SAAS;oBAACmK,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACTnK,OAAA;kBAAQ8J,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,eAClE/J,OAAA,CAACT,eAAe;oBAACuK,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACTnK,OAAA;kBAAQ8J,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,eAClE/J,OAAA,CAACJ,qBAAqB;oBAACkK,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACTnK,OAAA;kBAAQ8J,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,eAClE/J,OAAA,CAACV,sBAAsB;oBAACwK,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNnK,OAAA;cACE0K,GAAG,EAAE/I,oBAAqB;cAC1BmI,SAAS,EAAC,gBAAgB;cAC1Ba,KAAK,EAAE;gBACLC,MAAM,EAAE,oBAAoB;gBAC5BC,SAAS,EAAE,MAAM;gBACjBC,cAAc,EAAE,MAAM;gBACtBC,cAAc,EAAE;cAClB,CAAE;cAAAhB,QAAA,GAEDnJ,QAAQ,CAACmD,GAAG,CAAC,CAACJ,OAAO,EAAEyH,KAAK,KAAK;gBAChC,MAAMC,QAAQ,GAAG1H,OAAO,CAACC,QAAQ,KAAKvD,IAAI,CAAC0B,EAAE;gBAC7C,MAAMuJ,UAAU,GAAGF,KAAK,KAAK,CAAC,IAAIxK,QAAQ,CAACwK,KAAK,GAAG,CAAC,CAAC,CAACxH,QAAQ,KAAKD,OAAO,CAACC,QAAQ;gBAEnF,oBACE5D,OAAA;kBAAsB8J,SAAS,EAAE,QAAQuB,QAAQ,GAAG,aAAa,GAAG,eAAe,OAAQ;kBAAAtB,QAAA,GACxF,CAACsB,QAAQ,IAAIC,UAAU,iBACtBtL,OAAA;oBACEgL,GAAG,EAAErH,OAAO,CAACkB,YAAa;oBAC1BoG,GAAG,EAAEtH,OAAO,CAACe,UAAW;oBACxBoF,SAAS,EAAC;kBAAgC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CACF,EACA,CAACkB,QAAQ,IAAI,CAACC,UAAU,iBAAItL,OAAA;oBAAK8J,SAAS,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7DnK,OAAA;oBACE8J,SAAS,EAAE,yDACTuB,QAAQ,GACJ,0CAA0C,GAC1C,+DAA+D,EAClE;oBAAAtB,QAAA,gBAEH/J,OAAA;sBAAG8J,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAEpG,OAAO,CAACoB;oBAAI;sBAAAiF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzCnK,OAAA;sBAAK8J,SAAS,EAAE,gBAAgBuB,QAAQ,GAAG,iBAAiB,GAAG,eAAe,gCAAiC;sBAAAtB,QAAA,GAC5GlB,iBAAiB,CAAClF,OAAO,CAACQ,SAAS,CAAC,EACpCkH,QAAQ,iBACPrL,OAAA,CAACR,eAAe;wBACdsK,SAAS,EAAE,gBAAgBnG,OAAO,CAACqB,IAAI,GAAG,eAAe,GAAG,eAAe,EAAG;wBAC9EuG,KAAK,EAAE5H,OAAO,CAACqB,IAAI,GAAG,QAAQ,GAAG;sBAAW;wBAAAgF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7C,CACF;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EACLkB,QAAQ,IAAIC,UAAU,iBACrBtL,OAAA;oBACEgL,GAAG,EAAErH,OAAO,CAACkB,YAAa;oBAC1BoG,GAAG,EAAEtH,OAAO,CAACe,UAAW;oBACxBoF,SAAS,EAAC;kBAAgC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CACF,EACAkB,QAAQ,IAAI,CAACC,UAAU,iBAAItL,OAAA;oBAAK8J,SAAS,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,GAlCpDxG,OAAO,CAAC5B,EAAE;kBAAAiI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAmCf,CAAC;cAEV,CAAC,CAAC,eACFnK,OAAA;gBAAK0K,GAAG,EAAEjJ;cAAe;gBAAAuI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eAGNnK,OAAA;cAAK8J,SAAS,EAAC,uCAAuC;cAAAC,QAAA,eACpD/J,OAAA;gBAAMwL,QAAQ,EAAE/C,iBAAkB;gBAACqB,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC3D/J,OAAA;kBACEoK,IAAI,EAAC,QAAQ;kBACbN,SAAS,EAAC,uEAAuE;kBAAAC,QAAA,eAEjF/J,OAAA,CAACZ,aAAa;oBAAC0K,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACTnK,OAAA;kBAAK8J,SAAS,EAAC,aAAa;kBAAAC,QAAA,eAC1B/J,OAAA;oBACE8J,SAAS,EAAC,qHAAqH;oBAC/HO,WAAW,EAAC,yCAAqB;oBACjCoB,IAAI,EAAC,GAAG;oBACRnB,KAAK,EAAExJ,WAAY;oBACnByJ,QAAQ,EAAG7B,CAAC,IAAK3H,cAAc,CAAC2H,CAAC,CAAC8B,MAAM,CAACF,KAAK,CAAE;oBAChDoB,SAAS,EAAGhD,CAAC,IAAK;sBAChB,IAAIA,CAAC,CAACiD,GAAG,KAAK,OAAO,IAAI,CAACjD,CAAC,CAACkD,QAAQ,EAAE;wBACpClD,CAAC,CAACC,cAAc,CAAC,CAAC;wBAClBR,WAAW,CAAC,CAAC;sBACf;oBACF;kBAAE;oBAAA6B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACNnK,OAAA;kBACEoK,IAAI,EAAC,QAAQ;kBACbN,SAAS,EAAC,uEAAuE;kBAAAC,QAAA,eAEjF/J,OAAA,CAACX,aAAa;oBAACyK,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACTnK,OAAA;kBACEoK,IAAI,EAAC,QAAQ;kBACbyB,QAAQ,EAAE,CAAC/K,WAAW,CAACsH,IAAI,CAAC,CAAE;kBAC9B0B,SAAS,EAAE,yBACThJ,WAAW,CAACsH,IAAI,CAAC,CAAC,GACd,8CAA8C,GAC9C,8CAA8C,qBAC9B;kBAAA2B,QAAA,eAEtB/J,OAAA,CAACb,iBAAiB;oBAAC2K,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,eACN,CAAC;UAAA;UAEH;UACAnK,OAAA;YAAK8J,SAAS,EAAC,iEAAiE;YAAAC,QAAA,eAC9E/J,OAAA;cAAK8J,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1C/J,OAAA,CAACf,0BAA0B;gBAAC6K,SAAS,EAAC;cAAsC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/EnK,OAAA;gBAAI8J,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxEnK,OAAA;gBAAG8J,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAErC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/J,EAAA,CAvyBID,YAAY;EAAA,QACCtB,OAAO;AAAA;AAAAiN,EAAA,GADpB3L,YAAY;AAyyBlB,eAAeA,YAAY;AAAC,IAAA2L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}