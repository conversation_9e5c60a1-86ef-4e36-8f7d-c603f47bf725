{"ast": null, "code": "import getUTCWeekYear from \"../getUTCWeekYear/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nimport startOfUTCWeek from \"../startOfUTCWeek/index.js\";\nimport toInteger from \"../toInteger/index.js\";\nimport { getDefaultOptions } from \"../defaultOptions/index.js\";\nexport default function startOfUTCWeekYear(dirtyDate, options) {\n  var _ref, _ref2, _ref3, _options$firstWeekCon, _options$locale, _options$locale$optio, _defaultOptions$local, _defaultOptions$local2;\n  requiredArgs(1, arguments);\n  var defaultOptions = getDefaultOptions();\n  var firstWeekContainsDate = toInteger((_ref = (_ref2 = (_ref3 = (_options$firstWeekCon = options === null || options === void 0 ? void 0 : options.firstWeekContainsDate) !== null && _options$firstWeekCon !== void 0 ? _options$firstWeekCon : options === null || options === void 0 ? void 0 : (_options$locale = options.locale) === null || _options$locale === void 0 ? void 0 : (_options$locale$optio = _options$locale.options) === null || _options$locale$optio === void 0 ? void 0 : _options$locale$optio.firstWeekContainsDate) !== null && _ref3 !== void 0 ? _ref3 : defaultOptions.firstWeekContainsDate) !== null && _ref2 !== void 0 ? _ref2 : (_defaultOptions$local = defaultOptions.locale) === null || _defaultOptions$local === void 0 ? void 0 : (_defaultOptions$local2 = _defaultOptions$local.options) === null || _defaultOptions$local2 === void 0 ? void 0 : _defaultOptions$local2.firstWeekContainsDate) !== null && _ref !== void 0 ? _ref : 1);\n  var year = getUTCWeekYear(dirtyDate, options);\n  var firstWeek = new Date(0);\n  firstWeek.setUTCFullYear(year, 0, firstWeekContainsDate);\n  firstWeek.setUTCHours(0, 0, 0, 0);\n  var date = startOfUTCWeek(firstWeek, options);\n  return date;\n}", "map": {"version": 3, "names": ["getUTCWeekYear", "requiredArgs", "startOfUTCWeek", "toInteger", "getDefaultOptions", "startOfUTCWeekYear", "dirtyDate", "options", "_ref", "_ref2", "_ref3", "_options$firstWeekCon", "_options$locale", "_options$locale$optio", "_defaultOptions$local", "_defaultOptions$local2", "arguments", "defaultOptions", "firstWeekContainsDate", "locale", "year", "firstWeek", "Date", "setUTCFullYear", "setUTCHours", "date"], "sources": ["C:/Projeler/kidgarden/burky_root_web/node_modules/date-fns/esm/_lib/startOfUTCWeekYear/index.js"], "sourcesContent": ["import getUTCWeekYear from \"../getUTCWeekYear/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nimport startOfUTCWeek from \"../startOfUTCWeek/index.js\";\nimport toInteger from \"../toInteger/index.js\";\nimport { getDefaultOptions } from \"../defaultOptions/index.js\";\nexport default function startOfUTCWeekYear(dirtyDate, options) {\n  var _ref, _ref2, _ref3, _options$firstWeekCon, _options$locale, _options$locale$optio, _defaultOptions$local, _defaultOptions$local2;\n  requiredArgs(1, arguments);\n  var defaultOptions = getDefaultOptions();\n  var firstWeekContainsDate = toInteger((_ref = (_ref2 = (_ref3 = (_options$firstWeekCon = options === null || options === void 0 ? void 0 : options.firstWeekContainsDate) !== null && _options$firstWeekCon !== void 0 ? _options$firstWeekCon : options === null || options === void 0 ? void 0 : (_options$locale = options.locale) === null || _options$locale === void 0 ? void 0 : (_options$locale$optio = _options$locale.options) === null || _options$locale$optio === void 0 ? void 0 : _options$locale$optio.firstWeekContainsDate) !== null && _ref3 !== void 0 ? _ref3 : defaultOptions.firstWeekContainsDate) !== null && _ref2 !== void 0 ? _ref2 : (_defaultOptions$local = defaultOptions.locale) === null || _defaultOptions$local === void 0 ? void 0 : (_defaultOptions$local2 = _defaultOptions$local.options) === null || _defaultOptions$local2 === void 0 ? void 0 : _defaultOptions$local2.firstWeekContainsDate) !== null && _ref !== void 0 ? _ref : 1);\n  var year = getUTCWeekYear(dirtyDate, options);\n  var firstWeek = new Date(0);\n  firstWeek.setUTCFullYear(year, 0, firstWeekContainsDate);\n  firstWeek.setUTCHours(0, 0, 0, 0);\n  var date = startOfUTCWeek(firstWeek, options);\n  return date;\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,4BAA4B;AACvD,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,eAAe,SAASC,kBAAkBA,CAACC,SAAS,EAAEC,OAAO,EAAE;EAC7D,IAAIC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,qBAAqB,EAAEC,eAAe,EAAEC,qBAAqB,EAAEC,qBAAqB,EAAEC,sBAAsB;EACpId,YAAY,CAAC,CAAC,EAAEe,SAAS,CAAC;EAC1B,IAAIC,cAAc,GAAGb,iBAAiB,CAAC,CAAC;EACxC,IAAIc,qBAAqB,GAAGf,SAAS,CAAC,CAACK,IAAI,GAAG,CAACC,KAAK,GAAG,CAACC,KAAK,GAAG,CAACC,qBAAqB,GAAGJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACW,qBAAqB,MAAM,IAAI,IAAIP,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACK,eAAe,GAAGL,OAAO,CAACY,MAAM,MAAM,IAAI,IAAIP,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,qBAAqB,GAAGD,eAAe,CAACL,OAAO,MAAM,IAAI,IAAIM,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACK,qBAAqB,MAAM,IAAI,IAAIR,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGO,cAAc,CAACC,qBAAqB,MAAM,IAAI,IAAIT,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACK,qBAAqB,GAAGG,cAAc,CAACE,MAAM,MAAM,IAAI,IAAIL,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,sBAAsB,GAAGD,qBAAqB,CAACP,OAAO,MAAM,IAAI,IAAIQ,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACG,qBAAqB,MAAM,IAAI,IAAIV,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;EACl7B,IAAIY,IAAI,GAAGpB,cAAc,CAACM,SAAS,EAAEC,OAAO,CAAC;EAC7C,IAAIc,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAAC;EAC3BD,SAAS,CAACE,cAAc,CAACH,IAAI,EAAE,CAAC,EAAEF,qBAAqB,CAAC;EACxDG,SAAS,CAACG,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACjC,IAAIC,IAAI,GAAGvB,cAAc,CAACmB,SAAS,EAAEd,OAAO,CAAC;EAC7C,OAAOkB,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}