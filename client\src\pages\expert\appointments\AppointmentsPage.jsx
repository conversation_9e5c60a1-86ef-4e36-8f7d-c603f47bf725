import React, { useState, useEffect, useMemo } from 'react';
import { useAuth } from '../../../hooks/useAuth';
import {
  VideoCamera,
  ClockIcon,
  CalendarIcon,
  UserIcon,
  CheckCircleIcon,
  XCircleIcon,
  BellIcon,
  ChartBarIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  ArrowDownTrayIcon,
  PlayCircleIcon,
  PaperClipIcon,
  DocumentArrowDownIcon
} from '@heroicons/react/24/outline';
import { format, parseISO } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Link } from 'react-router-dom';
import api from '../../../services/api';
import toast from 'react-hot-toast';

/**
 * <PERSON><PERSON> randevular sayfası
 */
const AppointmentsPage = () => {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [appointments, setAppointments] = useState([]);
  const [activeTab, setActiveTab] = useState('upcoming'); // upcoming, past, all
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [isUpdating, setIsUpdating] = useState(false);

  // Randevu durumları
  const appointmentStatuses = {
    pending: "Onay Bekliyor",
    confirmed: "Onaylandı",
    cancelled: "İptal Edildi",
    rejected: "Reddedildi",
    rescheduled: "Yeniden Planlandı",
    completed: "Tamamlandı"
  };

  useEffect(() => {
    loadAppointments();
  }, []);

  const loadAppointments = async () => {
    try {
      setIsLoading(true);
      const response = await api.get('/experts/appointments');

      // API verisini frontend formatına çevir
      const formattedAppointments = response.data.appointments.map(appointment => ({
        id: appointment.AppointmentID,
        clientId: appointment.ClientID,
        clientName: `${appointment.ClientFirstName} ${appointment.ClientLastName}`,
        date: appointment.AppointmentDate.split('T')[0],
        time: new Date(appointment.AppointmentDate).toTimeString().slice(0, 5),
        duration: 50,
        status: appointment.Status.toLowerCase(),
        type: 'video',
        notes: appointment.Notes || '',
        clientAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(appointment.ClientFirstName)}+${encodeURIComponent(appointment.ClientLastName)}&background=4f46e5&color=fff&size=200&font-size=0.6`,
        clientEmail: appointment.ClientEmail,
        clientPhone: appointment.ClientPhone,
        createdAt: appointment.CreatedAt,
        endTime: appointment.EndTime
      }));

      setAppointments(formattedAppointments);
    } catch (error) {
      console.error('Randevular yüklenirken hata:', error);
      toast.error('Randevular yüklenemedi');
      setAppointments([]);
    } finally {
      setIsLoading(false);
    }
  };



  // Filtreleme useMemo ile optimize edildi
  const filteredAppointments = useMemo(() => {
    const today = new Date();

    return appointments.filter(appointment => {
      const appointmentDate = parseISO(appointment.date);

      // Tab filtresi
      if (activeTab === 'upcoming' && appointmentDate >= today && (appointment.status === 'confirmed' || appointment.status === 'pending' || appointment.status === 'rescheduled')) {
        // Gelecek randevular
      } else if (activeTab === 'past' && (appointmentDate < today || appointment.status === 'completed' || appointment.status === 'cancelled' || appointment.status === 'rejected')) {
        // Geçmiş randevular
      } else if (activeTab === 'all') {
        // Tüm randevular
      } else if (activeTab !== 'all') {
        return false;
      }

      // Durum filtresi - rejected'ı cancelled olarak treat et
      if (filterStatus !== 'all') {
        if (filterStatus === 'cancelled' && (appointment.status === 'cancelled' || appointment.status === 'rejected')) {
          // Cancelled filter'ında hem cancelled hem rejected göster
        } else if (appointment.status !== filterStatus) {
          return false;
        }
      }

      // Arama filtresi
      if (searchTerm && !appointment.clientName.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false;
      }

      return true;
    });
  }, [appointments, activeTab, filterStatus, searchTerm]);

  // İstatistik hesaplamaları
  const stats = {
    total: appointments.length,
    pending: appointments.filter(a => a.status === 'pending').length,
    confirmed: appointments.filter(a => a.status === 'confirmed').length,
    cancelled: appointments.filter(a => a.status === 'cancelled' || a.status === 'rejected').length,
    rescheduled: appointments.filter(a => a.status === 'rescheduled').length,
    completed: appointments.filter(a => a.status === 'completed').length
  };

  // Randevu onaylama
  const handleApproveAppointment = async (appointmentId) => {
    try {
      setIsUpdating(true);
      await api.put(`/experts/appointments/${appointmentId}/status`, {
        status: 'Confirmed'
      });

      toast.success('Randevu onaylandı!');
      loadAppointments(); // Listeyi yenile
    } catch (error) {
      console.error('Randevu onaylama hatası:', error);
      toast.error('Randevu onaylanamadı');
    } finally {
      setIsUpdating(false);
    }
  };

  // Randevu reddetme
  const handleRejectAppointment = async (appointmentId, reason = '') => {
    try {
      setIsUpdating(true);
      await api.put(`/experts/appointments/${appointmentId}/status`, {
        status: 'Rejected',
        rejectionReason: reason
      });

      toast.success('Randevu reddedildi');
      loadAppointments(); // Listeyi yenile
    } catch (error) {
      console.error('Randevu reddetme hatası:', error);
      toast.error('Randevu reddedilemedi');
    } finally {
      setIsUpdating(false);
    }
  };



  // Tarihe göre sırala - useMemo ile optimize edildi
  const sortedAppointments = useMemo(() => {
    return [...filteredAppointments].sort((a, b) => {
      // Önce tarihleri karşılaştır
      const dateComparison = new Date(b.date) - new Date(a.date);
      if (dateComparison !== 0) return dateComparison;

      // Tarihler aynıysa başlama saatini karşılaştır
      return a.time.localeCompare(b.time);
    });
  }, [filteredAppointments]);

  // Durum badge renkleri
  const getStatusBadge = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'rescheduled':
        return 'bg-orange-100 text-orange-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Yükleniyor durumu
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen pb-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-8">
        {/* Başlık ve Üst Kısım */}
        <div className="bg-gradient-to-r from-emerald-500 to-emerald-700 shadow-lg rounded-lg p-6 mb-6 text-white">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
            <div>
              <h1 className="text-2xl font-bold">Randevularım</h1>
              <p className="mt-1 text-indigo-100">
                Bekleyen ve onaylanan tüm randevularınızı yönetin
              </p>
            </div>
            <div className="mt-3 sm:mt-0 flex space-x-2">
              <Link
                to="/expert/sessions"
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-indigo-800 bg-white hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-300"
              >
                <CheckCircleIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
                Seanslarım
              </Link>
              <button className="inline-flex items-center px-3 py-2 border border-white text-sm font-medium rounded-md shadow-sm text-white bg-indigo-700 bg-opacity-50 hover:bg-opacity-75 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white">
                <ArrowDownTrayIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
                Rapor İndir
              </button>
              <button className="relative p-1 rounded-full bg-indigo-700 bg-opacity-50 text-indigo-100 hover:text-white focus:outline-none">
                <BellIcon className="h-6 w-6" />
                <span className="absolute top-0 right-0 block h-2.5 w-2.5 rounded-full bg-red-400 ring-2 ring-indigo-700"></span>
              </button>
            </div>
          </div>
        </div>

        {/* Özet İstatistikler */}
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
          <div 
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-indigo-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'all' && filterStatus === 'all' ? 'ring-2 ring-indigo-500' : ''}`}
            onClick={() => {
              setActiveTab('all');
              setFilterStatus('all');
            }}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Toplam</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.total}</span>
          </div>
          
          <div
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-yellow-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'all' && filterStatus === 'pending' ? 'ring-2 ring-yellow-500' : ''}`}
            onClick={() => {
              setActiveTab('all');
              setFilterStatus('pending');
            }}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Bekleyen</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.pending}</span>
          </div>
          
          <div
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-blue-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'all' && filterStatus === 'confirmed' ? 'ring-2 ring-blue-500' : ''}`}
            onClick={() => {
              setActiveTab('all');
              setFilterStatus('confirmed');
            }}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Onaylanan</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.confirmed}</span>
          </div>
          
          <div
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-orange-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'all' && filterStatus === 'rescheduled' ? 'ring-2 ring-orange-500' : ''}`}
            {...(stats.rescheduled > 0 && {
              onClick: () => {
                setActiveTab('all');
                setFilterStatus('rescheduled');
              }
            })}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Ertelenen</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.rescheduled}</span>
          </div>
          
          <div
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-green-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'all' && filterStatus === 'completed' ? 'ring-2 ring-green-500' : ''}`}
            {...(stats.completed > 0 && {
              onClick: () => {
                setActiveTab('all');
                setFilterStatus('completed');
              }
            })}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Tamamlanan</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.completed}</span>
          </div>
          
          <div
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-red-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'all' && filterStatus === 'cancelled' ? 'ring-2 ring-red-500' : ''}`}
            {...(stats.cancelled > 0 && {
              onClick: () => {
                setActiveTab('all');
                setFilterStatus('cancelled');
              }
            })}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">İptal Edilen</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.cancelled}</span>
          </div>
        </div>

        {/* Ana Sekme Navigasyonu */}
        <div className="flex border-b border-gray-200 mb-6">
          <button
            onClick={() => {
              setActiveTab('upcoming');
              setFilterStatus('all');
            }}
            className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
              activeTab === 'upcoming'
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center">
              <CalendarIcon className="h-5 w-5 mr-2" />
              <span>Yaklaşan Randevular</span>
            </div>
          </button>
          <button
            onClick={() => setActiveTab('past')}
            className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
              activeTab === 'past'
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center">
              <CheckCircleIcon className="h-5 w-5 mr-2" />
              <span>Geçmiş Randevular</span>
            </div>
          </button>
          <button
            onClick={() => {
              setActiveTab('all');
              setFilterStatus('all');
            }}
            className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
              activeTab === 'all'
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center">
              <DocumentTextIcon className="h-5 w-5 mr-2" />
              <span>Tüm Randevular</span>
            </div>
          </button>
        </div>

        {/* Arama */}
        <div className="bg-white shadow rounded-lg mb-6">
          <div className="px-4 py-5 sm:p-6">
            <div className="max-w-lg">
              <div className="relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                  </svg>
                </div>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="Danışan adına göre ara..."
                />
              </div>
            </div>
          </div>
        </div>

        {/* Randevular Listesi */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">
              {activeTab === 'upcoming' ? 'Yaklaşan Randevular' :
               activeTab === 'past' ? 'Geçmiş Randevular' : 'Tüm Randevular'}
              {filterStatus !== 'all' && ` - ${appointmentStatuses[filterStatus]}`}
            </h2>
          </div>

          {sortedAppointments.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {sortedAppointments.map((appointment) => (
                <div key={appointment.id} className="p-6 hover:bg-gray-50 transition duration-150">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <img
                          className="h-10 w-10 rounded-full border border-gray-200"
                          src={appointment.clientAvatar}
                          alt={appointment.clientName}
                        />
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-900">{appointment.clientName}</h3>
                        <div className="flex space-x-2 text-xs text-gray-500">
                          <span>{format(parseISO(appointment.date), 'EEEE', { locale: tr })}</span>
                          <span>•</span>
                          <span>{format(parseISO(appointment.date), 'd MMMM yyyy', { locale: tr })}</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadge(appointment.status)}`}>
                        {appointmentStatuses[appointment.status]}
                      </span>
                      <span className="text-xs text-gray-500">{appointment.packageName}</span>
                    </div>
                  </div>

                  <div className="mt-4 flex items-center justify-between">
                    <div className="flex space-x-6 text-sm text-gray-500">
                      <div className="flex items-center">
                        <ClockIcon className="h-4 w-4 text-gray-400 mr-1.5" />
                        <span>{appointment.time} ({appointment.duration} dk)</span>
                      </div>
                    </div>

                    <div className="flex space-x-2">
                      {(appointment.status === 'pending') && (
                        <>
                          <button
                            type="button"
                            onClick={() => handleApproveAppointment(appointment.id)}
                            disabled={isUpdating}
                            className="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            <CheckCircleIcon className="-ml-0.5 mr-1 h-4 w-4" />
                            {isUpdating ? 'Onaylanıyor...' : 'Onayla'}
                          </button>
                          <button
                            type="button"
                            onClick={() => handleRejectAppointment(appointment.id, 'Uzman tarafından reddedildi')}
                            disabled={isUpdating}
                            className="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            <XCircleIcon className="-ml-0.5 mr-1 h-4 w-4" />
                            {isUpdating ? 'Reddediliyor...' : 'Reddet'}
                          </button>
                        </>
                      )}
                      
                      {(appointment.status === 'confirmed' || appointment.status === 'rescheduled') && (
                        <button
                          type="button"
                          className="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        >
                          <CalendarIcon className="-ml-0.5 mr-1 h-4 w-4" />
                          Takvime Ekle
                        </button>
                      )}
                      
                      <button
                        type="button"
                        className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                      >
                        <ChatBubbleLeftRightIcon className="-ml-0.5 mr-1 h-4 w-4" />
                        Danışana Mesaj
                      </button>
                    </div>
                  </div>

                  {appointment.notes && (
                    <div className="mt-2 text-xs text-gray-500 bg-gray-50 px-3 py-1.5 rounded-md">
                      <span className="font-medium">Not:</span> {appointment.notes}
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="py-12 text-center">
              <CalendarIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Randevu Bulunamadı</h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm || filterStatus !== 'all'
                  ? 'Arama kriterlerinize uygun randevu bulunamadı.'
                  : 'Henüz bir randevu bulunmuyor.'}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AppointmentsPage; 