{"ast": null, "code": "import { useMemo as t } from \"react\";\nimport { getOwnerDocument as o } from '../utils/owner.js';\nfunction n(...e) {\n  return t(() => o(...e), [...e]);\n}\nexport { n as useOwnerDocument };", "map": {"version": 3, "names": ["useMemo", "t", "getOwnerDocument", "o", "n", "e", "useOwnerDocument"], "sources": ["C:/burky root/burky_root_web/client/node_modules/@headlessui/react/dist/hooks/use-owner.js"], "sourcesContent": ["import{useMemo as t}from\"react\";import{getOwnerDocument as o}from'../utils/owner.js';function n(...e){return t(()=>o(...e),[...e])}export{n as useOwnerDocument};\n"], "mappings": "AAAA,SAAOA,OAAO,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAASC,CAACA,CAAC,GAAGC,CAAC,EAAC;EAAC,OAAOJ,CAAC,CAAC,MAAIE,CAAC,CAAC,GAAGE,CAAC,CAAC,EAAC,CAAC,GAAGA,CAAC,CAAC,CAAC;AAAA;AAAC,SAAOD,CAAC,IAAIE,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}