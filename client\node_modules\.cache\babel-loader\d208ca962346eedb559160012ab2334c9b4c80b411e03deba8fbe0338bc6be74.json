{"ast": null, "code": "/**\n * API Service\n * Configures axios with interceptors for authentication and error handling\n */\n\nimport axios from 'axios';\nimport toast from 'react-hot-toast';\n\n// Create API instance\nconst api = axios.create({\n  baseURL: '/api',\n  // Proxy ile çalışacak şekilde sadece path'i kullan \n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Add request interceptor\napi.interceptors.request.use(config => {\n  // Get token from localStorage\n  const token = localStorage.getItem('accessToken');\n\n  // Add token to headers if it exists\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Yanıt interceptor'ı - başarılı ve hatalı yanıtları loglama\napi.interceptors.response.use(response => {\n  // Başarılı yanıtları logla\n  console.log(`API Yanıt - ${response.config.url}:`, {\n    url: response.config.url,\n    durum: response.status,\n    durumMetni: response.statusText,\n    veri: response.data\n  });\n  return response;\n}, async error => {\n  var _error$config, _error$response, _error$response2, _error$response3, _error$config2, _error$response4, _error$response5, _error$response5$data;\n  // API yanıt hatalarını logla\n  console.error(`API Hata - ${((_error$config = error.config) === null || _error$config === void 0 ? void 0 : _error$config.url) || 'bilinmeyen url'}:`, {\n    hata: error.message,\n    durum: (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status,\n    durumMetni: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.statusText,\n    veri: (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data,\n    url: (_error$config2 = error.config) === null || _error$config2 === void 0 ? void 0 : _error$config2.url\n  });\n\n  // Token yenileme işlemi\n  if (((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.status) === 401) {\n    const refreshToken = localStorage.getItem('refreshToken');\n    if (refreshToken) {\n      try {\n        // Token yenileme isteği\n        const refreshResponse = await axios.post(`/api/auth/refresh-token`, {\n          refreshToken\n        });\n        if (refreshResponse.data.accessToken) {\n          localStorage.setItem('accessToken', refreshResponse.data.accessToken);\n\n          // Orijinal isteği yeni token ile tekrarla\n          const originalRequest = error.config;\n          originalRequest.headers['Authorization'] = `Bearer ${refreshResponse.data.accessToken}`;\n          return axios(originalRequest);\n        }\n      } catch (refreshError) {\n        console.error('Token yenileme hatası:', refreshError);\n        // Token yenilenemedi, kullanıcıyı çıkış yap\n        localStorage.removeItem('accessToken');\n        localStorage.removeItem('refreshToken');\n        toast.error('Oturumunuz sona erdi, lütfen tekrar giriş yapın.');\n        window.location.href = '/login';\n      }\n    }\n  }\n\n  // 401 dışındaki hatalar için bildirim göster\n  if ((_error$response5 = error.response) !== null && _error$response5 !== void 0 && (_error$response5$data = _error$response5.data) !== null && _error$response5$data !== void 0 && _error$response5$data.message) {\n    toast.error(error.response.data.message);\n  } else {\n    toast.error('Bir hata oluştu.');\n  }\n  return Promise.reject(error);\n});\n\n// Uzman müsaitlik endpoint'leri\nexport const expertAvailabilityApi = {\n  // Uzmanın müsaitlik bilgilerini getir\n  getAvailability: () => api.get('/experts/availability/me'),\n  // Belirli bir uzmanın müsaitlik bilgilerini getir (public)\n  getExpertAvailability: expertId => api.get(`/experts/${expertId}/availability`),\n  // Yeni müsaitlik ekle\n  addAvailability: data => api.post('/experts/availability', data),\n  // Müsaitlik güncelle\n  updateAvailability: (availabilityId, data) => api.put(`/experts/availability/${availabilityId}`, data),\n  // Müsaitlik sil\n  deleteAvailability: availabilityId => api.delete(`/experts/availability/${availabilityId}`),\n  // Tekrarlı müsaitlik ayarı oluştur\n  createRecurringAvailability: data => api.post('/experts/availability/recurring', data),\n  // Belirli bir tarihe özel müsaitlik ayarı oluştur\n  createSpecificDateAvailability: data => api.post('/experts/availability/specific-date', data),\n  // Tarih aralığı için müsaitlik getir\n  getAvailabilityByDateRange: (startDate, endDate) => api.get(`/experts/availability/range?startDate=${startDate}&endDate=${endDate}`)\n};\n\n// Sessions API endpoints\nexport const sessionsApi = {\n  // Client sessions\n  getClientSessions: (filters = {}) => {\n    const params = new URLSearchParams();\n    if (filters.status && filters.status !== 'all') {\n      params.append('status', filters.status);\n    }\n    if (filters.search) {\n      params.append('search', filters.search);\n    }\n    const queryString = params.toString();\n    return api.get(`/sessions/client${queryString ? `?${queryString}` : ''}`);\n  },\n  // Expert sessions\n  getExpertSessions: (filters = {}) => {\n    const params = new URLSearchParams();\n    if (filters.status && filters.status !== 'all') {\n      params.append('status', filters.status);\n    }\n    if (filters.search) {\n      params.append('search', filters.search);\n    }\n    const queryString = params.toString();\n    return api.get(`/sessions/expert${queryString ? `?${queryString}` : ''}`);\n  },\n  // Get session by ID\n  getSessionById: sessionId => api.get(`/sessions/${sessionId}`),\n  // Update session notes (Expert only)\n  updateSessionNotes: (sessionId, notes) => api.put(`/sessions/${sessionId}/notes`, {\n    notes\n  }),\n  // Update session status (Expert only)\n  updateSessionStatus: (sessionId, status) => api.put(`/sessions/${sessionId}/status`, {\n    status\n  })\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "toast", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "console", "log", "url", "durum", "status", "durumMetni", "statusText", "veri", "data", "_error$config", "_error$response", "_error$response2", "_error$response3", "_error$config2", "_error$response4", "_error$response5", "_error$response5$data", "hata", "message", "refreshToken", "refreshResponse", "post", "accessToken", "setItem", "originalRequest", "refreshError", "removeItem", "window", "location", "href", "expertAvailabilityApi", "getAvailability", "get", "getExpertAvailability", "expertId", "addAvailability", "updateAvailability", "availabilityId", "put", "deleteAvailability", "delete", "createRecurringAvailability", "createSpecificDateAvailability", "getAvailabilityByDateRange", "startDate", "endDate", "sessionsApi", "getClientSessions", "filters", "params", "URLSearchParams", "append", "search", "queryString", "toString", "getExpertSessions", "getSessionById", "sessionId", "updateSessionNotes", "notes", "updateSessionStatus"], "sources": ["C:/Projeler/kidgarden/burky_root_web/client/src/services/api.js"], "sourcesContent": ["/**\n * API Service\n * Configures axios with interceptors for authentication and error handling\n */\n\nimport axios from 'axios';\nimport toast from 'react-hot-toast';\n\n// Create API instance\nconst api = axios.create({\n  baseURL: '/api', // Proxy ile çalışacak şekilde sadece path'i kullan \n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Add request interceptor\napi.interceptors.request.use(\n  (config) => {\n    // Get token from localStorage\n    const token = localStorage.getItem('accessToken');\n    \n    // Add token to headers if it exists\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    \n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Yanıt interceptor'ı - başarılı ve hatalı yanıtları loglama\napi.interceptors.response.use(\n  (response) => {\n    // Başarılı yanıtları logla\n    console.log(`API Yanıt - ${response.config.url}:`, {\n      url: response.config.url,\n      durum: response.status,\n      durumMetni: response.statusText,\n      veri: response.data,\n    });\n    return response;\n  },\n  async (error) => {\n    // API yanıt hatalarını logla\n    console.error(`API Hata - ${error.config?.url || 'bilinmeyen url'}:`, {\n      hata: error.message,\n      durum: error.response?.status,\n      durumMetni: error.response?.statusText,\n      veri: error.response?.data,\n      url: error.config?.url,\n    });\n\n    // Token yenileme işlemi\n    if (error.response?.status === 401) {\n      const refreshToken = localStorage.getItem('refreshToken');\n      \n      if (refreshToken) {\n        try {\n          // Token yenileme isteği\n          const refreshResponse = await axios.post(`/api/auth/refresh-token`, {\n            refreshToken\n          });\n          \n          if (refreshResponse.data.accessToken) {\n            localStorage.setItem('accessToken', refreshResponse.data.accessToken);\n            \n            // Orijinal isteği yeni token ile tekrarla\n            const originalRequest = error.config;\n            originalRequest.headers['Authorization'] = `Bearer ${refreshResponse.data.accessToken}`;\n            return axios(originalRequest);\n          }\n        } catch (refreshError) {\n          console.error('Token yenileme hatası:', refreshError);\n          // Token yenilenemedi, kullanıcıyı çıkış yap\n          localStorage.removeItem('accessToken');\n          localStorage.removeItem('refreshToken');\n          \n          toast.error('Oturumunuz sona erdi, lütfen tekrar giriş yapın.');\n          window.location.href = '/login';\n        }\n      }\n    }\n    \n    // 401 dışındaki hatalar için bildirim göster\n    if (error.response?.data?.message) {\n      toast.error(error.response.data.message);\n    } else {\n      toast.error('Bir hata oluştu.');\n    }\n    \n    return Promise.reject(error);\n  }\n);\n\n// Uzman müsaitlik endpoint'leri\nexport const expertAvailabilityApi = {\n  // Uzmanın müsaitlik bilgilerini getir\n  getAvailability: () => api.get('/experts/availability/me'),\n  \n  // Belirli bir uzmanın müsaitlik bilgilerini getir (public)\n  getExpertAvailability: (expertId) => api.get(`/experts/${expertId}/availability`),\n  \n  // Yeni müsaitlik ekle\n  addAvailability: (data) => api.post('/experts/availability', data),\n  \n  // Müsaitlik güncelle\n  updateAvailability: (availabilityId, data) => api.put(`/experts/availability/${availabilityId}`, data),\n  \n  // Müsaitlik sil\n  deleteAvailability: (availabilityId) => api.delete(`/experts/availability/${availabilityId}`),\n  \n  // Tekrarlı müsaitlik ayarı oluştur\n  createRecurringAvailability: (data) => api.post('/experts/availability/recurring', data),\n  \n  // Belirli bir tarihe özel müsaitlik ayarı oluştur\n  createSpecificDateAvailability: (data) => api.post('/experts/availability/specific-date', data),\n  \n  // Tarih aralığı için müsaitlik getir\n  getAvailabilityByDateRange: (startDate, endDate) =>\n    api.get(`/experts/availability/range?startDate=${startDate}&endDate=${endDate}`)\n};\n\n// Sessions API endpoints\nexport const sessionsApi = {\n  // Client sessions\n  getClientSessions: (filters = {}) => {\n    const params = new URLSearchParams();\n    if (filters.status && filters.status !== 'all') {\n      params.append('status', filters.status);\n    }\n    if (filters.search) {\n      params.append('search', filters.search);\n    }\n    const queryString = params.toString();\n    return api.get(`/sessions/client${queryString ? `?${queryString}` : ''}`);\n  },\n\n  // Expert sessions\n  getExpertSessions: (filters = {}) => {\n    const params = new URLSearchParams();\n    if (filters.status && filters.status !== 'all') {\n      params.append('status', filters.status);\n    }\n    if (filters.search) {\n      params.append('search', filters.search);\n    }\n    const queryString = params.toString();\n    return api.get(`/sessions/expert${queryString ? `?${queryString}` : ''}`);\n  },\n\n  // Get session by ID\n  getSessionById: (sessionId) => api.get(`/sessions/${sessionId}`),\n\n  // Update session notes (Expert only)\n  updateSessionNotes: (sessionId, notes) => api.put(`/sessions/${sessionId}/notes`, { notes }),\n\n  // Update session status (Expert only)\n  updateSessionStatus: (sessionId, status) => api.put(`/sessions/${sessionId}/status`, { status })\n};\n\nexport default api;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,iBAAiB;;AAEnC;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAE,MAAM;EAAE;EACjBC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV;EACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;;EAEjD;EACA,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EAEA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAZ,GAAG,CAACI,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAK;EACZ;EACAC,OAAO,CAACC,GAAG,CAAC,eAAeF,QAAQ,CAACR,MAAM,CAACW,GAAG,GAAG,EAAE;IACjDA,GAAG,EAAEH,QAAQ,CAACR,MAAM,CAACW,GAAG;IACxBC,KAAK,EAAEJ,QAAQ,CAACK,MAAM;IACtBC,UAAU,EAAEN,QAAQ,CAACO,UAAU;IAC/BC,IAAI,EAAER,QAAQ,CAACS;EACjB,CAAC,CAAC;EACF,OAAOT,QAAQ;AACjB,CAAC,EACD,MAAOH,KAAK,IAAK;EAAA,IAAAa,aAAA,EAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,cAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA;EACf;EACAhB,OAAO,CAACJ,KAAK,CAAC,cAAc,EAAAa,aAAA,GAAAb,KAAK,CAACL,MAAM,cAAAkB,aAAA,uBAAZA,aAAA,CAAcP,GAAG,KAAI,gBAAgB,GAAG,EAAE;IACpEe,IAAI,EAAErB,KAAK,CAACsB,OAAO;IACnBf,KAAK,GAAAO,eAAA,GAAEd,KAAK,CAACG,QAAQ,cAAAW,eAAA,uBAAdA,eAAA,CAAgBN,MAAM;IAC7BC,UAAU,GAAAM,gBAAA,GAAEf,KAAK,CAACG,QAAQ,cAAAY,gBAAA,uBAAdA,gBAAA,CAAgBL,UAAU;IACtCC,IAAI,GAAAK,gBAAA,GAAEhB,KAAK,CAACG,QAAQ,cAAAa,gBAAA,uBAAdA,gBAAA,CAAgBJ,IAAI;IAC1BN,GAAG,GAAAW,cAAA,GAAEjB,KAAK,CAACL,MAAM,cAAAsB,cAAA,uBAAZA,cAAA,CAAcX;EACrB,CAAC,CAAC;;EAEF;EACA,IAAI,EAAAY,gBAAA,GAAAlB,KAAK,CAACG,QAAQ,cAAAe,gBAAA,uBAAdA,gBAAA,CAAgBV,MAAM,MAAK,GAAG,EAAE;IAClC,MAAMe,YAAY,GAAG1B,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IAEzD,IAAIyB,YAAY,EAAE;MAChB,IAAI;QACF;QACA,MAAMC,eAAe,GAAG,MAAMtC,KAAK,CAACuC,IAAI,CAAC,yBAAyB,EAAE;UAClEF;QACF,CAAC,CAAC;QAEF,IAAIC,eAAe,CAACZ,IAAI,CAACc,WAAW,EAAE;UACpC7B,YAAY,CAAC8B,OAAO,CAAC,aAAa,EAAEH,eAAe,CAACZ,IAAI,CAACc,WAAW,CAAC;;UAErE;UACA,MAAME,eAAe,GAAG5B,KAAK,CAACL,MAAM;UACpCiC,eAAe,CAACrC,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUiC,eAAe,CAACZ,IAAI,CAACc,WAAW,EAAE;UACvF,OAAOxC,KAAK,CAAC0C,eAAe,CAAC;QAC/B;MACF,CAAC,CAAC,OAAOC,YAAY,EAAE;QACrBzB,OAAO,CAACJ,KAAK,CAAC,wBAAwB,EAAE6B,YAAY,CAAC;QACrD;QACAhC,YAAY,CAACiC,UAAU,CAAC,aAAa,CAAC;QACtCjC,YAAY,CAACiC,UAAU,CAAC,cAAc,CAAC;QAEvC3C,KAAK,CAACa,KAAK,CAAC,kDAAkD,CAAC;QAC/D+B,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;MACjC;IACF;EACF;;EAEA;EACA,KAAAd,gBAAA,GAAInB,KAAK,CAACG,QAAQ,cAAAgB,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBP,IAAI,cAAAQ,qBAAA,eAApBA,qBAAA,CAAsBE,OAAO,EAAE;IACjCnC,KAAK,CAACa,KAAK,CAACA,KAAK,CAACG,QAAQ,CAACS,IAAI,CAACU,OAAO,CAAC;EAC1C,CAAC,MAAM;IACLnC,KAAK,CAACa,KAAK,CAAC,kBAAkB,CAAC;EACjC;EAEA,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMkC,qBAAqB,GAAG;EACnC;EACAC,eAAe,EAAEA,CAAA,KAAM/C,GAAG,CAACgD,GAAG,CAAC,0BAA0B,CAAC;EAE1D;EACAC,qBAAqB,EAAGC,QAAQ,IAAKlD,GAAG,CAACgD,GAAG,CAAC,YAAYE,QAAQ,eAAe,CAAC;EAEjF;EACAC,eAAe,EAAG3B,IAAI,IAAKxB,GAAG,CAACqC,IAAI,CAAC,uBAAuB,EAAEb,IAAI,CAAC;EAElE;EACA4B,kBAAkB,EAAEA,CAACC,cAAc,EAAE7B,IAAI,KAAKxB,GAAG,CAACsD,GAAG,CAAC,yBAAyBD,cAAc,EAAE,EAAE7B,IAAI,CAAC;EAEtG;EACA+B,kBAAkB,EAAGF,cAAc,IAAKrD,GAAG,CAACwD,MAAM,CAAC,yBAAyBH,cAAc,EAAE,CAAC;EAE7F;EACAI,2BAA2B,EAAGjC,IAAI,IAAKxB,GAAG,CAACqC,IAAI,CAAC,iCAAiC,EAAEb,IAAI,CAAC;EAExF;EACAkC,8BAA8B,EAAGlC,IAAI,IAAKxB,GAAG,CAACqC,IAAI,CAAC,qCAAqC,EAAEb,IAAI,CAAC;EAE/F;EACAmC,0BAA0B,EAAEA,CAACC,SAAS,EAAEC,OAAO,KAC7C7D,GAAG,CAACgD,GAAG,CAAC,yCAAyCY,SAAS,YAAYC,OAAO,EAAE;AACnF,CAAC;;AAED;AACA,OAAO,MAAMC,WAAW,GAAG;EACzB;EACAC,iBAAiB,EAAEA,CAACC,OAAO,GAAG,CAAC,CAAC,KAAK;IACnC,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IACpC,IAAIF,OAAO,CAAC5C,MAAM,IAAI4C,OAAO,CAAC5C,MAAM,KAAK,KAAK,EAAE;MAC9C6C,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAEH,OAAO,CAAC5C,MAAM,CAAC;IACzC;IACA,IAAI4C,OAAO,CAACI,MAAM,EAAE;MAClBH,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAEH,OAAO,CAACI,MAAM,CAAC;IACzC;IACA,MAAMC,WAAW,GAAGJ,MAAM,CAACK,QAAQ,CAAC,CAAC;IACrC,OAAOtE,GAAG,CAACgD,GAAG,CAAC,mBAAmBqB,WAAW,GAAG,IAAIA,WAAW,EAAE,GAAG,EAAE,EAAE,CAAC;EAC3E,CAAC;EAED;EACAE,iBAAiB,EAAEA,CAACP,OAAO,GAAG,CAAC,CAAC,KAAK;IACnC,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IACpC,IAAIF,OAAO,CAAC5C,MAAM,IAAI4C,OAAO,CAAC5C,MAAM,KAAK,KAAK,EAAE;MAC9C6C,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAEH,OAAO,CAAC5C,MAAM,CAAC;IACzC;IACA,IAAI4C,OAAO,CAACI,MAAM,EAAE;MAClBH,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAEH,OAAO,CAACI,MAAM,CAAC;IACzC;IACA,MAAMC,WAAW,GAAGJ,MAAM,CAACK,QAAQ,CAAC,CAAC;IACrC,OAAOtE,GAAG,CAACgD,GAAG,CAAC,mBAAmBqB,WAAW,GAAG,IAAIA,WAAW,EAAE,GAAG,EAAE,EAAE,CAAC;EAC3E,CAAC;EAED;EACAG,cAAc,EAAGC,SAAS,IAAKzE,GAAG,CAACgD,GAAG,CAAC,aAAayB,SAAS,EAAE,CAAC;EAEhE;EACAC,kBAAkB,EAAEA,CAACD,SAAS,EAAEE,KAAK,KAAK3E,GAAG,CAACsD,GAAG,CAAC,aAAamB,SAAS,QAAQ,EAAE;IAAEE;EAAM,CAAC,CAAC;EAE5F;EACAC,mBAAmB,EAAEA,CAACH,SAAS,EAAErD,MAAM,KAAKpB,GAAG,CAACsD,GAAG,CAAC,aAAamB,SAAS,SAAS,EAAE;IAAErD;EAAO,CAAC;AACjG,CAAC;AAED,eAAepB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}