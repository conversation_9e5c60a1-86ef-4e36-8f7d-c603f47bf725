{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: \"'წინა' eeee p'-ზე'\",\n  yesterday: \"'გუშინ' p'-ზე'\",\n  today: \"'დღეს' p'-ზე'\",\n  tomorrow: \"'ხვალ' p'-ზე'\",\n  nextWeek: \"'შემდეგი' eeee p'-ზე'\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/burky root/burky_root_web/node_modules/date-fns/esm/locale/ka/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: \"'წინა' eeee p'-ზე'\",\n  yesterday: \"'გუშინ' p'-ზე'\",\n  today: \"'დღეს' p'-ზე'\",\n  tomorrow: \"'ხვალ' p'-ზე'\",\n  nextWeek: \"'შემდეგი' eeee p'-ზე'\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,oBAAoB;EAC9BC,SAAS,EAAE,gBAAgB;EAC3BC,KAAK,EAAE,eAAe;EACtBC,QAAQ,EAAE,eAAe;EACzBC,QAAQ,EAAE,uBAAuB;EACjCC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EAC9E,OAAOX,oBAAoB,CAACQ,KAAK,CAAC;AACpC,CAAC;AACD,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}