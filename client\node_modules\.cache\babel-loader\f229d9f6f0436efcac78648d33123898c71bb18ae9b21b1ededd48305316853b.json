{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useContext } from 'react';\nimport { AuthContext } from '../contexts/AuthContext';\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");", "map": {"version": 3, "names": ["useContext", "AuthContext", "useAuth", "_s", "context", "Error"], "sources": ["C:/burky root/burky_root_web/client/src/hooks/useAuth.js"], "sourcesContent": ["import { useContext } from 'react';\nimport { AuthContext } from '../contexts/AuthContext';\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  \n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  \n  return context;\n};\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,WAAW,QAAQ,yBAAyB;AAErD,OAAO,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGJ,UAAU,CAACC,WAAW,CAAC;EAEvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EAEA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CARWD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}