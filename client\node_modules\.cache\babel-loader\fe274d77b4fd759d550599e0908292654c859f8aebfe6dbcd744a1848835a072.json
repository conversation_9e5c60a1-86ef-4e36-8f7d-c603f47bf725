{"ast": null, "code": "var _jsxFileName = \"C:\\\\burky root\\\\burky_root_web\\\\client\\\\src\\\\pages\\\\auth\\\\LoginPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { useForm } from 'react-hook-form';\nimport { useAuth } from '../../hooks/useAuth';\nimport { FormInput } from '../../components/ui';\n\n// İkon importları\nimport { UserIcon, LockClosedIcon, EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoginPage = () => {\n  _s();\n  var _errors$username, _errors$password;\n  const {\n    login\n  } = useAuth();\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const {\n    register,\n    handleSubmit,\n    formState: {\n      errors\n    }\n  } = useForm();\n  const onSubmit = async data => {\n    setIsLoading(true);\n    setError('');\n    try {\n      const success = await login(data.username, data.password);\n      if (!success) {\n        setError('Kullanıcı adı veya şifre geçersiz');\n      }\n    } catch (error) {\n      setError('Giriş sırasında bir hata oluştu');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Password göster/gizle işlevi\n  const togglePasswordVisibility = () => setShowPassword(!showPassword);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-center text-2xl font-bold leading-9 tracking-tight text-gray-900\",\n      children: \"Hesab\\u0131n\\u0131za Giri\\u015F Yap\\u0131n\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4 rounded-md bg-red-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"ml-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-sm font-medium text-red-800\",\n            children: \"Hata\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2 text-sm text-red-700\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      className: \"space-y-6 mt-8\",\n      onSubmit: handleSubmit(onSubmit),\n      children: [/*#__PURE__*/_jsxDEV(FormInput, {\n        id: \"username\",\n        name: \"username\",\n        label: \"Kullan\\u0131c\\u0131 Ad\\u0131 veya E-posta\",\n        placeholder: \"Kullan\\u0131c\\u0131 ad\\u0131n\\u0131z\\u0131 veya e-postan\\u0131z\\u0131 girin\",\n        icon: /*#__PURE__*/_jsxDEV(UserIcon, {\n          className: \"h-5 w-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 17\n        }, this),\n        error: (_errors$username = errors.username) === null || _errors$username === void 0 ? void 0 : _errors$username.message,\n        variant: \"filled\",\n        ...register('username', {\n          required: 'Kullanıcı adı gereklidir'\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormInput, {\n        id: \"password\",\n        name: \"password\",\n        label: \"\\u015Eifre\",\n        type: showPassword ? 'text' : 'password',\n        placeholder: \"\\u015Eifrenizi girin\",\n        icon: /*#__PURE__*/_jsxDEV(LockClosedIcon, {\n          className: \"h-5 w-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 17\n        }, this),\n        rightIcon: showPassword ? /*#__PURE__*/_jsxDEV(EyeSlashIcon, {\n          className: \"h-5 w-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 37\n        }, this) : /*#__PURE__*/_jsxDEV(EyeIcon, {\n          className: \"h-5 w-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 76\n        }, this),\n        onIconClick: togglePasswordVisibility,\n        error: (_errors$password = errors.password) === null || _errors$password === void 0 ? void 0 : _errors$password.message,\n        variant: \"filled\",\n        helperText: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"flex justify-end\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/forgot-password\",\n            className: \"text-xs font-medium text-primary-600 hover:text-primary-500\",\n            children: \"\\u015Eifremi unuttum\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 13\n        }, this),\n        ...register('password', {\n          required: 'Şifre gereklidir'\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: isLoading,\n          className: \"flex w-full justify-center rounded-md bg-primary-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-primary-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600 disabled:opacity-50 disabled:cursor-not-allowed\",\n          children: isLoading ? 'Giriş yapılıyor...' : 'Giriş Yap'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"mt-10 text-center text-sm text-gray-500\",\n      children: [\"Hesab\\u0131n\\u0131z yok mu?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/register\",\n        className: \"font-semibold leading-6 text-primary-600 hover:text-primary-500\",\n        children: \"Buradan kaydolun\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 43,\n    columnNumber: 5\n  }, this);\n};\n_s(LoginPage, \"QJTxji9s+hgfC3zRgLY05adtL/k=\", false, function () {\n  return [useAuth, useForm];\n});\n_c = LoginPage;\nexport default LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useForm", "useAuth", "FormInput", "UserIcon", "LockClosedIcon", "EyeIcon", "EyeSlashIcon", "jsxDEV", "_jsxDEV", "LoginPage", "_s", "_errors$username", "_errors$password", "login", "isLoading", "setIsLoading", "error", "setError", "showPassword", "setShowPassword", "register", "handleSubmit", "formState", "errors", "onSubmit", "data", "success", "username", "password", "togglePasswordVisibility", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "name", "label", "placeholder", "icon", "message", "variant", "required", "type", "rightIcon", "onIconClick", "helperText", "to", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/burky root/burky_root_web/client/src/pages/auth/LoginPage.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { useForm } from 'react-hook-form';\nimport { useAuth } from '../../hooks/useAuth';\nimport { FormInput } from '../../components/ui';\n\n// İkon importları\nimport { UserIcon, LockClosedIcon, EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';\n\nconst LoginPage = () => {\n  const { login } = useAuth();\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  \n  const {\n    register,\n    handleSubmit,\n    formState: { errors }\n  } = useForm();\n  \n  const onSubmit = async (data) => {\n    setIsLoading(true);\n    setError('');\n    \n    try {\n      const success = await login(data.username, data.password);\n      \n      if (!success) {\n        setError('<PERSON><PERSON>ı<PERSON><PERSON> adı veya ş<PERSON>re geçersiz');\n      }\n    } catch (error) {\n      setError('<PERSON><PERSON><PERSON> sırasında bir hata oluştu');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  \n  // Password göster/gizle işlevi\n  const togglePasswordVisibility = () => setShowPassword(!showPassword);\n  \n  return (\n    <div>\n      <h2 className=\"text-center text-2xl font-bold leading-9 tracking-tight text-gray-900\">\n        Hesabınıza Giriş Yapın\n      </h2>\n      \n      {error && (\n        <div className=\"mt-4 rounded-md bg-red-50 p-4\">\n          <div className=\"flex\">\n            <div className=\"ml-3\">\n              <h3 className=\"text-sm font-medium text-red-800\">Hata</h3>\n              <div className=\"mt-2 text-sm text-red-700\">\n                {error}\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n      \n      <form className=\"space-y-6 mt-8\" onSubmit={handleSubmit(onSubmit)}>\n        <FormInput\n          id=\"username\"\n          name=\"username\"\n          label=\"Kullanıcı Adı veya E-posta\"\n          placeholder=\"Kullanıcı adınızı veya e-postanızı girin\"\n          icon={<UserIcon className=\"h-5 w-5\" />}\n          error={errors.username?.message}\n          variant=\"filled\"\n          {...register('username', { \n            required: 'Kullanıcı adı gereklidir' \n          })}\n        />\n\n        <FormInput\n          id=\"password\"\n          name=\"password\"\n          label=\"Şifre\"\n          type={showPassword ? 'text' : 'password'}\n          placeholder=\"Şifrenizi girin\"\n          icon={<LockClosedIcon className=\"h-5 w-5\" />}\n          rightIcon={showPassword ? <EyeSlashIcon className=\"h-5 w-5\" /> : <EyeIcon className=\"h-5 w-5\" />}\n          onIconClick={togglePasswordVisibility}\n          error={errors.password?.message}\n          variant=\"filled\"\n          helperText={\n            <span className=\"flex justify-end\">\n              <Link \n                to=\"/forgot-password\" \n                className=\"text-xs font-medium text-primary-600 hover:text-primary-500\"\n              >\n                Şifremi unuttum\n              </Link>\n            </span>\n          }\n          {...register('password', { \n            required: 'Şifre gereklidir' \n          })}\n        />\n\n        <div>\n          <button\n            type=\"submit\"\n            disabled={isLoading}\n            className=\"flex w-full justify-center rounded-md bg-primary-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-primary-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {isLoading ? 'Giriş yapılıyor...' : 'Giriş Yap'}\n          </button>\n        </div>\n      </form>\n\n      <p className=\"mt-10 text-center text-sm text-gray-500\">\n        Hesabınız yok mu?{' '}\n        <Link to=\"/register\" className=\"font-semibold leading-6 text-primary-600 hover:text-primary-500\">\n          Buradan kaydolun\n        </Link>\n      </p>\n    </div>\n  );\n};\n\nexport default LoginPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,SAAS,QAAQ,qBAAqB;;AAE/C;AACA,SAASC,QAAQ,EAAEC,cAAc,EAAEC,OAAO,EAAEC,YAAY,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9F,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,gBAAA,EAAAC,gBAAA;EACtB,MAAM;IAAEC;EAAM,CAAC,GAAGZ,OAAO,CAAC,CAAC;EAC3B,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAM;IACJsB,QAAQ;IACRC,YAAY;IACZC,SAAS,EAAE;MAAEC;IAAO;EACtB,CAAC,GAAGvB,OAAO,CAAC,CAAC;EAEb,MAAMwB,QAAQ,GAAG,MAAOC,IAAI,IAAK;IAC/BV,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMS,OAAO,GAAG,MAAMb,KAAK,CAACY,IAAI,CAACE,QAAQ,EAAEF,IAAI,CAACG,QAAQ,CAAC;MAEzD,IAAI,CAACF,OAAO,EAAE;QACZT,QAAQ,CAAC,mCAAmC,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdC,QAAQ,CAAC,iCAAiC,CAAC;IAC7C,CAAC,SAAS;MACRF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMc,wBAAwB,GAAGA,CAAA,KAAMV,eAAe,CAAC,CAACD,YAAY,CAAC;EAErE,oBACEV,OAAA;IAAAsB,QAAA,gBACEtB,OAAA;MAAIuB,SAAS,EAAC,uEAAuE;MAAAD,QAAA,EAAC;IAEtF;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAEJnB,KAAK,iBACJR,OAAA;MAAKuB,SAAS,EAAC,+BAA+B;MAAAD,QAAA,eAC5CtB,OAAA;QAAKuB,SAAS,EAAC,MAAM;QAAAD,QAAA,eACnBtB,OAAA;UAAKuB,SAAS,EAAC,MAAM;UAAAD,QAAA,gBACnBtB,OAAA;YAAIuB,SAAS,EAAC,kCAAkC;YAAAD,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1D3B,OAAA;YAAKuB,SAAS,EAAC,2BAA2B;YAAAD,QAAA,EACvCd;UAAK;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED3B,OAAA;MAAMuB,SAAS,EAAC,gBAAgB;MAACP,QAAQ,EAAEH,YAAY,CAACG,QAAQ,CAAE;MAAAM,QAAA,gBAChEtB,OAAA,CAACN,SAAS;QACRkC,EAAE,EAAC,UAAU;QACbC,IAAI,EAAC,UAAU;QACfC,KAAK,EAAC,2CAA4B;QAClCC,WAAW,EAAC,6EAA0C;QACtDC,IAAI,eAAEhC,OAAA,CAACL,QAAQ;UAAC4B,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvCnB,KAAK,GAAAL,gBAAA,GAAEY,MAAM,CAACI,QAAQ,cAAAhB,gBAAA,uBAAfA,gBAAA,CAAiB8B,OAAQ;QAChCC,OAAO,EAAC,QAAQ;QAAA,GACZtB,QAAQ,CAAC,UAAU,EAAE;UACvBuB,QAAQ,EAAE;QACZ,CAAC;MAAC;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEF3B,OAAA,CAACN,SAAS;QACRkC,EAAE,EAAC,UAAU;QACbC,IAAI,EAAC,UAAU;QACfC,KAAK,EAAC,YAAO;QACbM,IAAI,EAAE1B,YAAY,GAAG,MAAM,GAAG,UAAW;QACzCqB,WAAW,EAAC,sBAAiB;QAC7BC,IAAI,eAAEhC,OAAA,CAACJ,cAAc;UAAC2B,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7CU,SAAS,EAAE3B,YAAY,gBAAGV,OAAA,CAACF,YAAY;UAACyB,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG3B,OAAA,CAACH,OAAO;UAAC0B,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACjGW,WAAW,EAAEjB,wBAAyB;QACtCb,KAAK,GAAAJ,gBAAA,GAAEW,MAAM,CAACK,QAAQ,cAAAhB,gBAAA,uBAAfA,gBAAA,CAAiB6B,OAAQ;QAChCC,OAAO,EAAC,QAAQ;QAChBK,UAAU,eACRvC,OAAA;UAAMuB,SAAS,EAAC,kBAAkB;UAAAD,QAAA,eAChCtB,OAAA,CAACT,IAAI;YACHiD,EAAE,EAAC,kBAAkB;YACrBjB,SAAS,EAAC,6DAA6D;YAAAD,QAAA,EACxE;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACP;QAAA,GACGf,QAAQ,CAAC,UAAU,EAAE;UACvBuB,QAAQ,EAAE;QACZ,CAAC;MAAC;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEF3B,OAAA;QAAAsB,QAAA,eACEtB,OAAA;UACEoC,IAAI,EAAC,QAAQ;UACbK,QAAQ,EAAEnC,SAAU;UACpBiB,SAAS,EAAC,2SAA2S;UAAAD,QAAA,EAEpThB,SAAS,GAAG,oBAAoB,GAAG;QAAW;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEP3B,OAAA;MAAGuB,SAAS,EAAC,yCAAyC;MAAAD,QAAA,GAAC,6BACpC,EAAC,GAAG,eACrBtB,OAAA,CAACT,IAAI;QAACiD,EAAE,EAAC,WAAW;QAACjB,SAAS,EAAC,iEAAiE;QAAAD,QAAA,EAAC;MAEjG;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEV,CAAC;AAACzB,EAAA,CA9GID,SAAS;EAAA,QACKR,OAAO,EASrBD,OAAO;AAAA;AAAAkD,EAAA,GAVPzC,SAAS;AAgHf,eAAeA,SAAS;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}