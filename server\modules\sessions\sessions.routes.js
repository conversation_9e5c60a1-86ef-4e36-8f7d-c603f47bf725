/**
 * Sessions Routes
 * Defines API endpoints for session management
 */

const express = require('express');
const { body } = require('express-validator');
const router = express.Router();
const sessionsController = require('./sessions.controller');
const auth = require('../../middleware/auth');
const { checkPermission } = require('../../middleware/permission');

/**
 * @route   GET /api/sessions/client
 * @desc    Get client sessions
 * @access  Private (Client only)
 */
router.get('/client',
  auth,
  checkPermission('/client/sessions', 'READ'),
  sessionsController.getClientSessions
);

/**
 * @route   GET /api/sessions/expert
 * @desc    Get expert sessions
 * @access  Private (Expert only)
 */
router.get('/expert',
  auth,
  checkPermission('/expert/sessions', 'READ'),
  sessionsController.getExpertSessions
);

/**
 * @route   GET /api/sessions/:id
 * @desc    Get session by ID
 * @access  Private
 */
router.get('/:id',
  auth,
  sessionsController.getSessionById
);

/**
 * @route   PUT /api/sessions/:id/notes
 * @desc    Update session notes
 * @access  Private (Expert only)
 */
router.put('/:id/notes', [
  auth,
  checkPermission('/expert/sessions', 'UPDATE'),
  body('notes').notEmpty().withMessage('Notes are required')
    .isLength({ max: 2000 }).withMessage('Notes cannot exceed 2000 characters')
], sessionsController.updateSessionNotes);

/**
 * @route   PUT /api/sessions/:id/status
 * @desc    Update session status
 * @access  Private (Expert only)
 */
router.put('/:id/status', [
  auth,
  checkPermission('/expert/sessions', 'UPDATE'),
  body('status').isIn(['scheduled', 'completed', 'cancelled', 'missed'])
    .withMessage('Invalid status')
], sessionsController.updateSessionStatus);

module.exports = router;
