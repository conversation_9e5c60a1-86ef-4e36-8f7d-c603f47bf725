{"ast": null, "code": "var _jsxFileName = \"C:\\\\Projeler\\\\kidgarden\\\\burky_root_web\\\\client\\\\src\\\\pages\\\\client\\\\messages\\\\ClientMessagesPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '../../../hooks/useAuth';\nimport api from '../../../services/api';\nimport { toast } from 'react-hot-toast';\nimport io from 'socket.io-client';\nimport { ChatBubbleLeftEllipsisIcon, MagnifyingGlassIcon, UserIcon, PaperAirplaneIcon, PaperClipIcon, FaceSmileIcon, UserCircleIcon, EllipsisHorizontalIcon, PhoneIcon, VideoCameraIcon, InformationCircleIcon, ClockIcon, CheckCircleIcon, XMarkIcon, TrashIcon, ArchiveBoxIcon, StarIcon, UserGroupIcon, CalendarIcon } from '@heroicons/react/24/outline';\nimport { format } from 'date-fns';\nimport { tr } from 'date-fns/locale';\nimport { Link } from 'react-router-dom';\n\n/**\n * <PERSON><PERSON><PERSON><PERSON> me<PERSON>jlaş<PERSON> sayfası\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ClientMessagesPage = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [isLoading, setIsLoading] = useState(true);\n  const [conversations, setConversations] = useState([]);\n  const [selectedConversation, setSelectedConversation] = useState(null);\n  const [messages, setMessages] = useState([]);\n  const [messageText, setMessageText] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filter, setFilter] = useState('all'); // all, unread, archived, starred\n  const [socket, setSocket] = useState(null);\n  const [onlineUsers, setOnlineUsers] = useState(new Set());\n  const [typingUsers, setTypingUsers] = useState(new Set());\n  const messagesEndRef = useRef(null);\n  const conversationsRef = useRef(null);\n  const messagesContainerRef = useRef(null);\n\n  // Socket.IO connection - ayrı useEffect\n  useEffect(() => {\n    const token = localStorage.getItem('accessToken');\n    if (token && user !== null && user !== void 0 && user.id) {\n      console.log('🔌 CLIENT: Socket.IO bağlantısı kuruluyor... User ID:', user.id);\n      const socketConnection = io(process.env.REACT_APP_API_URL || 'http://localhost:5000', {\n        auth: {\n          token\n        },\n        transports: ['websocket', 'polling'],\n        reconnection: true,\n        reconnectionDelay: 1000,\n        reconnectionAttempts: 5,\n        timeout: 20000\n      });\n      setSocket(socketConnection);\n\n      // Connection events\n      socketConnection.on('connect', () => {\n        console.log('✅ CLIENT: Socket.IO bağlantısı kuruldu:', socketConnection.id);\n        console.log('🔗 CLIENT: Socket connected:', socketConnection.connected);\n        console.log('🏠 CLIENT: Socket rooms:', socketConnection.rooms);\n        // Kullanıcının online olduğunu bildir\n        socketConnection.emit('user_online');\n      });\n      socketConnection.on('disconnect', reason => {\n        console.log('❌ CLIENT: Socket.IO bağlantısı kesildi:', reason);\n      });\n      socketConnection.on('connect_error', error => {\n        console.error('🔥 CLIENT: Socket.IO bağlantı hatası:', error);\n      });\n      socketConnection.on('reconnect', attemptNumber => {\n        console.log('🔄 CLIENT: Socket.IO yeniden bağlandı, deneme:', attemptNumber);\n      });\n      socketConnection.on('reconnect_error', error => {\n        console.error('🔥 CLIENT: Socket.IO yeniden bağlanma hatası:', error);\n      });\n\n      // Heartbeat mekanizması\n      const heartbeatInterval = setInterval(() => {\n        if (socketConnection.connected) {\n          socketConnection.emit('ping', response => {\n            if (response === 'pong') {\n              console.log('💓 CLIENT: Heartbeat OK');\n            }\n          });\n        }\n      }, 30000); // 30 saniyede bir ping gönder\n\n      return () => {\n        clearInterval(heartbeatInterval);\n        socketConnection.disconnect();\n      };\n    } else {\n      console.log('❌ CLIENT: Socket bağlantısı kurulamıyor - token veya user eksik:', {\n        hasToken: !!token,\n        hasUser: !!(user !== null && user !== void 0 && user.id)\n      });\n    }\n  }, [user === null || user === void 0 ? void 0 : user.id]);\n\n  // Socket event listeners - ayrı useEffect\n  useEffect(() => {\n    if (socket && user !== null && user !== void 0 && user.id) {\n      console.log('🎧 Socket event listeners kuruluyor...');\n\n      // Socket event listeners\n      const handleNewMessage = message => {\n        console.log('📨 CLIENT: Yeni mesaj alındı:', message);\n        console.log('👤 CLIENT: Current user ID:', user.id);\n        console.log('💬 CLIENT: Message sender ID:', message.senderId);\n        console.log('🏠 CLIENT: Message conversation ID:', message.conversationId);\n\n        // Conversation listesini ÖNCE güncelle (her zaman)\n        setConversations(prev => {\n          console.log('📋 CLIENT: Updating conversations list');\n          return prev.map(conv => conv.id === message.conversationId ? {\n            ...conv,\n            lastMessage: message.content,\n            timestamp: message.createdAt,\n            unread: message.senderId !== user.id\n          } : conv);\n        });\n\n        // Eğer şu anda bu conversation açıksa, mesajı da ekle\n        setSelectedConversation(currentSelected => {\n          console.log('🎯 CLIENT: Current selected conversation:', currentSelected === null || currentSelected === void 0 ? void 0 : currentSelected.id);\n          if (currentSelected && message.conversationId === currentSelected.id) {\n            console.log('✅ CLIENT: Adding message to current conversation');\n            setMessages(prev => {\n              // Mesajın zaten var olup olmadığını kontrol et\n              const messageExists = prev.some(msg => msg.id === message.id);\n              if (messageExists) {\n                console.log('⚠️ CLIENT: Message already exists, skipping:', message.id);\n                return prev;\n              }\n              return [...prev, {\n                id: message.id,\n                senderId: message.senderId,\n                senderName: message.sender.name,\n                senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n                text: message.content,\n                timestamp: message.createdAt,\n                read: message.isRead,\n                delivered: true // Socket'ten gelen mesajlar delivered olarak kabul edilir\n              }];\n            });\n\n            // Scroll to bottom\n            setTimeout(() => {\n              var _messagesEndRef$curre;\n              (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n                behavior: 'smooth'\n              });\n            }, 100);\n          } else {\n            console.log('❌ CLIENT: Not adding message - conversation not selected or different conversation');\n          }\n          return currentSelected;\n        });\n      };\n      const handleMessageSent = message => {\n        console.log('✅ Mesaj gönderildi onayı:', message);\n      };\n      const handleUserStatusChange = data => {\n        setOnlineUsers(prev => {\n          const newSet = new Set(prev);\n          if (data.status === 'online') {\n            newSet.add(data.userId);\n          } else {\n            newSet.delete(data.userId);\n          }\n          return newSet;\n        });\n      };\n      const handleUserTyping = data => {\n        setSelectedConversation(currentSelected => {\n          if (currentSelected && data.conversationId === currentSelected.id) {\n            setTypingUsers(prev => new Set([...prev, data.userId]));\n          }\n          return currentSelected;\n        });\n      };\n      const handleUserStoppedTyping = data => {\n        setTypingUsers(prev => {\n          const newSet = new Set(prev);\n          newSet.delete(data.userId);\n          return newSet;\n        });\n      };\n      const handleMessagesRead = data => {\n        console.log('👁️ CLIENT: Messages read event:', data);\n        // Mesajları okundu olarak işaretle\n        setMessages(prev => prev.map(msg => data.messageIds.includes(msg.id) ? {\n          ...msg,\n          read: true\n        } : msg));\n      };\n\n      // Event listener'ları ekle\n      console.log('🎧 CLIENT: Adding socket event listeners...');\n      socket.on('new_message', handleNewMessage);\n      socket.on('message_sent', handleMessageSent);\n      socket.on('user_status_change', handleUserStatusChange);\n      socket.on('user_typing', handleUserTyping);\n      socket.on('user_stopped_typing', handleUserStoppedTyping);\n      socket.on('messages_read', handleMessagesRead);\n      console.log('✅ CLIENT: Socket event listeners added');\n\n      // Cleanup function\n      return () => {\n        socket.off('new_message', handleNewMessage);\n        socket.off('message_sent', handleMessageSent);\n        socket.off('user_status_change', handleUserStatusChange);\n        socket.off('user_typing', handleUserTyping);\n        socket.off('user_stopped_typing', handleUserStoppedTyping);\n      };\n    }\n  }, [socket, user.id]);\n\n  // Conversations yükleme - ayrı useEffect\n  useEffect(() => {\n    loadConversations();\n  }, []);\n\n  // Socket bağlandığında tüm conversation'lara join ol - sadece bir kez\n  const [joinedConversations, setJoinedConversations] = useState(new Set());\n  useEffect(() => {\n    if (socket && conversations.length > 0) {\n      console.log('🏠 CLIENT: Tüm conversation\\'lara katılıyor...', conversations.length, 'conversation');\n      conversations.forEach(conversation => {\n        if (!joinedConversations.has(conversation.id)) {\n          socket.emit('join_conversation', conversation.id);\n          console.log(`🏠 CLIENT: Conversation ${conversation.id} katıldı`);\n          setJoinedConversations(prev => new Set([...prev, conversation.id]));\n        }\n      });\n    }\n  }, [socket, conversations, joinedConversations]);\n\n  // Conversations yükleme\n  const loadConversations = async () => {\n    try {\n      setIsLoading(true);\n      const response = await api.get('/messages/conversations');\n\n      // API verisini frontend formatına çevir\n      const formattedConversations = response.data.conversations.map(conversation => {\n        var _conversation$lastMes, _conversation$lastMes2;\n        return {\n          id: conversation.id,\n          expertId: conversation.otherUser.id,\n          expertName: conversation.otherUser.name,\n          expertTitle: conversation.otherUser.role === 'Expert' ? 'Uzman' : conversation.otherUser.role,\n          lastMessage: ((_conversation$lastMes = conversation.lastMessage) === null || _conversation$lastMes === void 0 ? void 0 : _conversation$lastMes.content) || 'Henüz mesaj yok',\n          timestamp: ((_conversation$lastMes2 = conversation.lastMessage) === null || _conversation$lastMes2 === void 0 ? void 0 : _conversation$lastMes2.timestamp) || conversation.createdAt,\n          unread: conversation.lastMessage ? !conversation.lastMessage.isRead && conversation.lastMessage.senderId !== user.id : false,\n          avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(conversation.otherUser.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n          status: onlineUsers.has(conversation.otherUser.id) ? 'online' : 'offline',\n          starred: false,\n          archived: false\n        };\n      });\n      setConversations(formattedConversations);\n    } catch (error) {\n      console.error('Konuşmalar yüklenirken hata:', error);\n      toast.error('Konuşmalar yüklenemedi');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Mesaj yükleme ve okundu işaretleme\n  useEffect(() => {\n    if (selectedConversation) {\n      loadMessages(selectedConversation.id);\n      markConversationAsRead(selectedConversation.id);\n    }\n  }, [selectedConversation]);\n\n  // Conversation'daki mesajları okundu olarak işaretle\n  const markConversationAsRead = async conversationId => {\n    try {\n      // Okunmamış mesajları bul\n      const unreadMessages = messages.filter(msg => msg.senderId !== user.id && !msg.read);\n      if (unreadMessages.length > 0) {\n        // Server'a okundu bilgisi gönder\n        await api.put(`/messages/conversations/${conversationId}/read`);\n\n        // Local state'i güncelle\n        setMessages(prev => prev.map(msg => msg.senderId !== user.id ? {\n          ...msg,\n          read: true\n        } : msg));\n\n        // Socket'e okundu bilgisi gönder\n        if (socket) {\n          socket.emit('mark_messages_read', {\n            conversationId,\n            messageIds: unreadMessages.map(msg => msg.id)\n          });\n        }\n      }\n    } catch (error) {\n      console.error('Mesajları okundu olarak işaretlerken hata:', error);\n    }\n  };\n\n  // Conversations yükleme\n  const loadMessages = async conversationId => {\n    try {\n      const response = await api.get(`/messages/conversations/${conversationId}`);\n\n      // API verisini frontend formatına çevir\n      const formattedMessages = response.data.messages.map(message => ({\n        id: message.id,\n        senderId: message.senderId,\n        senderName: message.sender.name,\n        senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        text: message.content,\n        timestamp: message.createdAt,\n        read: message.isRead,\n        delivered: true,\n        // Veritabanından gelen mesajlar delivered olarak kabul edilir\n        attachments: message.attachments\n      }));\n      setMessages(formattedMessages);\n\n      // Scroll to bottom\n      setTimeout(() => {\n        var _messagesEndRef$curre2;\n        (_messagesEndRef$curre2 = messagesEndRef.current) === null || _messagesEndRef$curre2 === void 0 ? void 0 : _messagesEndRef$curre2.scrollIntoView({\n          behavior: 'smooth'\n        });\n      }, 100);\n    } catch (error) {\n      console.error('Mesajlar yüklenirken hata:', error);\n      toast.error('Mesajlar yüklenemedi');\n    }\n  };\n  // Mesaj gönderme\n  const sendMessage = async () => {\n    if (!messageText.trim() || !selectedConversation) return;\n    console.log('📤 Mesaj gönderiliyor:', {\n      receiverId: selectedConversation.expertId,\n      content: messageText.trim()\n    });\n    try {\n      const response = await api.post('/messages/send', {\n        receiverId: selectedConversation.expertId,\n        content: messageText.trim()\n      });\n      console.log('✅ Mesaj gönderildi:', response.data);\n\n      // Sadece mesaj input'unu temizle - mesaj socket'ten gelecek\n      setMessageText('');\n    } catch (error) {\n      console.error('Mesaj gönderilirken hata:', error);\n      toast.error('Mesaj gönderilemedi');\n    }\n  };\n  useEffect(() => {\n    // Mesajları otomatik kaydır\n    if (messagesEndRef.current && messagesContainerRef.current) {\n      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;\n    }\n  }, [messages]);\n\n  // Mesaj gönderme\n  const handleSendMessage = e => {\n    e.preventDefault();\n    sendMessage();\n  };\n\n  // Konuşma seçme\n  const handleSelectConversation = conversation => {\n    setSelectedConversation(conversation);\n  };\n\n  // Tarih formatı\n  const formatMessageDate = dateString => {\n    const date = new Date(dateString);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    if (date.toDateString() === today.toDateString()) {\n      return format(date, 'HH:mm', {\n        locale: tr\n      });\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Dün ' + format(date, 'HH:mm', {\n        locale: tr\n      });\n    } else {\n      return format(date, 'dd MMM HH:mm', {\n        locale: tr\n      });\n    }\n  };\n\n  // Konuşmaları filtrele\n  const filteredConversations = conversations.filter(conv => {\n    // Arama filtresi\n    const matchesSearch = conv.expertName.toLowerCase().includes(searchTerm.toLowerCase()) || conv.lastMessage.toLowerCase().includes(searchTerm.toLowerCase());\n\n    // Durum filtresi\n    const matchesFilter = filter === 'all' || filter === 'unread' && conv.unread || filter === 'archived' && conv.archived || filter === 'starred' && conv.starred;\n    return matchesSearch && matchesFilter;\n  });\n\n  // Konuşmayı yıldızla\n  const toggleStar = id => {\n    setConversations(prevConversations => prevConversations.map(conv => conv.id === id ? {\n      ...conv,\n      starred: !conv.starred\n    } : conv));\n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({\n        ...prev,\n        starred: !prev.starred\n      }));\n    }\n  };\n\n  // Konuşmayı arşivle\n  const toggleArchive = id => {\n    setConversations(prevConversations => prevConversations.map(conv => conv.id === id ? {\n      ...conv,\n      archived: !conv.archived\n    } : conv));\n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({\n        ...prev,\n        archived: !prev.archived\n      }));\n    }\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 457,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 456,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-gray-50 min-h-screen pb-12\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-pink-500 to-pink-600 shadow-lg rounded-lg p-6 mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-white\",\n              children: \"Mesajlar\\u0131m\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-pink-100\",\n              children: \"Uzmanlar\\u0131n\\u0131zla g\\xFCvenli bir \\u015Fekilde ileti\\u015Fim kurun\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-3 sm:mt-0 flex space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/client/experts\",\n              className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-pink-600 bg-white hover:bg-pink-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-colors duration-150\",\n              children: [/*#__PURE__*/_jsxDEV(UserGroupIcon, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 17\n              }, this), \"Uzmanlar\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/client/appointments\",\n              className: \"inline-flex items-center px-4 py-2 border border-white text-sm font-medium rounded-md shadow-sm text-white hover:bg-pink-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-colors duration-150\",\n              children: [/*#__PURE__*/_jsxDEV(CalendarIcon, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 17\n              }, this), \"Randevular\\u0131m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 466,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-md overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-12 h-[75vh]\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-span-12 md:col-span-4 border-r border-gray-200 flex flex-col\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 border-b border-gray-200 bg-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-xl font-semibold text-gray-800 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(ChatBubbleLeftEllipsisIcon, {\n                  className: \"h-6 w-6 text-teal-600 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 19\n                }, this), \"Mesajlar\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 499,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-3 relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Konu\\u015Fmalarda ara...\",\n                  className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500\",\n                  value: searchTerm,\n                  onChange: e => setSearchTerm(e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 504,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                  className: \"h-5 w-5 text-gray-400 absolute left-3 top-2.5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-3 flex space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `px-3 py-1 text-sm rounded-full ${filter === 'all' ? 'bg-teal-100 text-teal-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                  onClick: () => setFilter('all'),\n                  children: \"T\\xFCm\\xFC\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 514,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `px-3 py-1 text-sm rounded-full ${filter === 'unread' ? 'bg-teal-100 text-teal-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                  onClick: () => setFilter('unread'),\n                  children: \"Okunmam\\u0131\\u015F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 522,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `px-3 py-1 text-sm rounded-full ${filter === 'starred' ? 'bg-teal-100 text-teal-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                  onClick: () => setFilter('starred'),\n                  children: \"Y\\u0131ld\\u0131zl\\u0131\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 530,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `px-3 py-1 text-sm rounded-full ${filter === 'archived' ? 'bg-teal-100 text-teal-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                  onClick: () => setFilter('archived'),\n                  children: \"Ar\\u015Fiv\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 538,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: conversationsRef,\n              style: {\n                height: 'calc(75vh - 145px)',\n                overflowY: 'auto',\n                scrollbarWidth: 'thin',\n                scrollbarColor: '#D1D5DB #F3F4F6'\n              },\n              children: filteredConversations.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 text-center text-gray-500\",\n                children: \"Hi\\xE7 mesaj\\u0131n\\u0131z yok\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 19\n              }, this) : filteredConversations.map(conversation => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `p-3 cursor-pointer hover:bg-gray-50 transition-colors duration-200 ${(selectedConversation === null || selectedConversation === void 0 ? void 0 : selectedConversation.id) === conversation.id ? 'bg-teal-50' : ''} ${conversation.unread ? 'bg-teal-50 hover:bg-teal-100' : ''}`,\n                onClick: () => handleSelectConversation(conversation),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative flex-shrink-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: conversation.avatar,\n                      alt: conversation.expertName,\n                      className: `h-10 w-10 rounded-full ${(selectedConversation === null || selectedConversation === void 0 ? void 0 : selectedConversation.id) === conversation.id ? 'ring-2 ring-teal-600' : ''}`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 572,\n                      columnNumber: 27\n                    }, this), conversation.status === 'online' && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 582,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 571,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-start\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: `text-sm font-medium ${conversation.unread ? 'text-gray-900' : 'text-gray-700'}`,\n                        children: conversation.expertName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 587,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [conversation.starred && /*#__PURE__*/_jsxDEV(StarIcon, {\n                          className: \"h-4 w-4 text-yellow-400 fill-current\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 592,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-xs text-gray-500\",\n                          children: formatMessageDate(conversation.timestamp)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 594,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 590,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 586,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500\",\n                      children: conversation.expertTitle\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 599,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: `text-sm truncate mt-1 ${conversation.unread ? 'text-gray-900 font-medium' : 'text-gray-500'}`,\n                      children: conversation.lastMessage\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 600,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-center mt-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-gray-500\",\n                        children: conversation.status === 'online' ? 'Çevrimiçi' : conversation.lastSeen ? `Son görülme: ${conversation.lastSeen}` : ''\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 606,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex space-x-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: e => {\n                            e.stopPropagation();\n                            toggleStar(conversation.id);\n                          },\n                          className: \"text-gray-400 hover:text-yellow-400\",\n                          children: /*#__PURE__*/_jsxDEV(StarIcon, {\n                            className: `h-4 w-4 ${conversation.starred ? 'text-yellow-400 fill-current' : ''}`\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 621,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 614,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: e => {\n                            e.stopPropagation();\n                            toggleArchive(conversation.id);\n                          },\n                          className: \"text-gray-400 hover:text-gray-600\",\n                          children: /*#__PURE__*/_jsxDEV(ArchiveBoxIcon, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 630,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 623,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 613,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 605,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 585,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 23\n                }, this), conversation.unread && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"inline-flex items-center ml-12 mt-1 px-2 py-0.5 rounded-full text-xs font-medium bg-teal-600 text-white\",\n                  children: \"Yeni\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 637,\n                  columnNumber: 25\n                }, this)]\n              }, conversation.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-span-12 md:col-span-8 flex flex-col\",\n            children: selectedConversation ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 border-b border-gray-200 bg-white flex justify-between items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative mr-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: selectedConversation.avatar,\n                      alt: selectedConversation.expertName,\n                      className: \"h-10 w-10 rounded-full\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 654,\n                      columnNumber: 25\n                    }, this), selectedConversation.status === 'online' && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 660,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 653,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-lg font-medium text-gray-800\",\n                      children: selectedConversation.expertName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 664,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500\",\n                      children: [selectedConversation.expertTitle, \" \", ' • ', selectedConversation.status === 'online' ? 'Çevrimiçi' : selectedConversation.lastSeen ? `Son görülme: ${selectedConversation.lastSeen}` : '']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 667,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 663,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 652,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Link, {\n                    to: `/client/experts/${selectedConversation.expertId}`,\n                    className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                    children: /*#__PURE__*/_jsxDEV(UserIcon, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 682,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 678,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Link, {\n                    to: `/client/appointments?expert=${selectedConversation.expertId}`,\n                    className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                    children: /*#__PURE__*/_jsxDEV(ClockIcon, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 688,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 684,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Link, {\n                    to: `/client/sessions?expert=${selectedConversation.expertId}`,\n                    className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                    children: /*#__PURE__*/_jsxDEV(VideoCameraIcon, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 694,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 690,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                    children: /*#__PURE__*/_jsxDEV(EllipsisHorizontalIcon, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 697,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 696,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 677,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 651,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                ref: messagesContainerRef,\n                className: \"p-4 bg-gray-50\",\n                style: {\n                  height: 'calc(75vh - 195px)',\n                  overflowY: 'auto',\n                  scrollbarWidth: 'thin',\n                  scrollbarColor: '#D1D5DB #F3F4F6'\n                },\n                children: [messages.map((message, index) => {\n                  const isSender = message.senderId === user.id;\n                  const showAvatar = index === 0 || messages[index - 1].senderId !== message.senderId;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `flex ${isSender ? 'justify-end' : 'justify-start'} mb-4`,\n                    children: [!isSender && showAvatar && /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: message.senderAvatar,\n                      alt: message.senderName,\n                      className: \"h-8 w-8 rounded-full mr-2 mt-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 720,\n                      columnNumber: 29\n                    }, this), !isSender && !showAvatar && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-8 mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 726,\n                      columnNumber: 56\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `max-w-xs md:max-w-md lg:max-w-lg rounded-lg px-4 py-2 ${isSender ? 'bg-teal-600 text-white rounded-br-none' : 'bg-white text-gray-800 rounded-bl-none border border-gray-200'}`,\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm\",\n                        children: message.text\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 734,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `text-xs mt-1 ${isSender ? 'text-teal-200' : 'text-gray-500'} flex items-center justify-end`,\n                        children: [formatMessageDate(message.timestamp), isSender && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center ml-1\",\n                          children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                            className: `h-3 w-3 ${message.read ? 'text-blue-400' : 'text-gray-400'}`,\n                            title: message.delivered ? 'İletildi' : 'Gönderiliyor'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 740,\n                            columnNumber: 35\n                          }, this), message.read && /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                            className: \"h-3 w-3 -ml-1 text-blue-400\",\n                            title: \"Okundu\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 746,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 738,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 735,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 727,\n                      columnNumber: 27\n                    }, this), isSender && showAvatar && /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: message.senderAvatar,\n                      alt: message.senderName,\n                      className: \"h-8 w-8 rounded-full ml-2 mt-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 756,\n                      columnNumber: 29\n                    }, this), isSender && !showAvatar && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-8 ml-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 762,\n                      columnNumber: 55\n                    }, this)]\n                  }, message.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 718,\n                    columnNumber: 25\n                  }, this);\n                }), /*#__PURE__*/_jsxDEV(\"div\", {\n                  ref: messagesEndRef\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 766,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 703,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3 border-t border-gray-200 bg-white\",\n                children: /*#__PURE__*/_jsxDEV(\"form\", {\n                  onSubmit: handleSendMessage,\n                  className: \"flex items-end\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    className: \"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\",\n                    children: /*#__PURE__*/_jsxDEV(PaperClipIcon, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 776,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 772,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n                      className: \"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 resize-none\",\n                      placeholder: \"Mesaj\\u0131n\\u0131z\\u0131 yaz\\u0131n...\",\n                      rows: \"2\",\n                      value: messageText,\n                      onChange: e => setMessageText(e.target.value),\n                      onKeyDown: e => {\n                        if (e.key === 'Enter' && !e.shiftKey) {\n                          e.preventDefault();\n                          sendMessage();\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 779,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 778,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    className: \"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\",\n                    children: /*#__PURE__*/_jsxDEV(FaceSmileIcon, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 797,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 793,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"submit\",\n                    disabled: !messageText.trim(),\n                    className: `ml-2 p-2 rounded-full ${messageText.trim() ? 'bg-teal-600 text-white hover:bg-teal-700' : 'bg-gray-200 text-gray-400 cursor-not-allowed'} focus:outline-none`,\n                    children: /*#__PURE__*/_jsxDEV(PaperAirplaneIcon, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 808,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 799,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 771,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 770,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) :\n            /*#__PURE__*/\n            // Mesaj seçilmediğinde\n            _jsxDEV(\"div\", {\n              className: \"flex-1 flex flex-col items-center justify-center p-6 bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full max-w-md text-center\",\n                children: [/*#__PURE__*/_jsxDEV(ChatBubbleLeftEllipsisIcon, {\n                  className: \"h-16 w-16 text-gray-300 mb-4 mx-auto\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 817,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-medium text-gray-800 mb-2\",\n                  children: \"Mesajlar\\u0131n\\u0131z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 818,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-500 mx-auto\",\n                  children: \"Mesajla\\u015Fmaya ba\\u015Flamak i\\xE7in sol taraftan bir konu\\u015Fma se\\xE7in veya yeni bir uzmanla ileti\\u015Fime ge\\xE7in.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 819,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-6\",\n                  children: /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/client/experts\",\n                    className: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500\",\n                    children: [/*#__PURE__*/_jsxDEV(UserIcon, {\n                      className: \"-ml-1 mr-2 h-5 w-5\",\n                      \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 827,\n                      columnNumber: 25\n                    }, this), \"Uzmanlar\\u0131 Ke\\u015Ffedin\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 823,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 822,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 816,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 815,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 647,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 494,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 464,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 463,\n    columnNumber: 5\n  }, this);\n};\n_s(ClientMessagesPage, \"8B7RxxYStSTw62EYx5HVIfMSfbM=\", false, function () {\n  return [useAuth];\n});\n_c = ClientMessagesPage;\nexport default ClientMessagesPage;\nvar _c;\n$RefreshReg$(_c, \"ClientMessagesPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useAuth", "api", "toast", "io", "ChatBubbleLeftEllipsisIcon", "MagnifyingGlassIcon", "UserIcon", "PaperAirplaneIcon", "PaperClipIcon", "FaceSmileIcon", "UserCircleIcon", "EllipsisHorizontalIcon", "PhoneIcon", "VideoCameraIcon", "InformationCircleIcon", "ClockIcon", "CheckCircleIcon", "XMarkIcon", "TrashIcon", "ArchiveBoxIcon", "StarIcon", "UserGroupIcon", "CalendarIcon", "format", "tr", "Link", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ClientMessagesPage", "_s", "user", "isLoading", "setIsLoading", "conversations", "setConversations", "selectedConversation", "setSelectedConversation", "messages", "setMessages", "messageText", "setMessageText", "searchTerm", "setSearchTerm", "filter", "setFilter", "socket", "setSocket", "onlineUsers", "setOnlineUsers", "Set", "typingUsers", "setTypingUsers", "messagesEndRef", "conversationsRef", "messagesContainerRef", "token", "localStorage", "getItem", "id", "console", "log", "socketConnection", "process", "env", "REACT_APP_API_URL", "auth", "transports", "reconnection", "reconnectionDelay", "reconnectionAttempts", "timeout", "on", "connected", "rooms", "emit", "reason", "error", "attemptNumber", "heartbeatInterval", "setInterval", "response", "clearInterval", "disconnect", "hasToken", "<PERSON><PERSON>ser", "handleNewMessage", "message", "senderId", "conversationId", "prev", "map", "conv", "lastMessage", "content", "timestamp", "createdAt", "unread", "currentSelected", "messageExists", "some", "msg", "sender<PERSON>ame", "sender", "name", "senderAvatar", "encodeURIComponent", "text", "read", "isRead", "delivered", "setTimeout", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleMessageSent", "handleUserStatusChange", "data", "newSet", "status", "add", "userId", "delete", "handleUserTyping", "handleUserStoppedTyping", "handleMessagesRead", "messageIds", "includes", "off", "loadConversations", "joinedConversations", "setJoinedConversations", "length", "for<PERSON>ach", "conversation", "has", "get", "formattedConversations", "_conversation$lastMes", "_conversation$lastMes2", "expertId", "otherUser", "expertName", "expert<PERSON><PERSON>le", "role", "avatar", "starred", "archived", "loadMessages", "markConversationAsRead", "unreadMessages", "put", "formattedMessages", "attachments", "_messagesEndRef$curre2", "sendMessage", "trim", "receiverId", "post", "scrollTop", "scrollHeight", "handleSendMessage", "e", "preventDefault", "handleSelectConversation", "formatMessageDate", "dateString", "date", "Date", "today", "yesterday", "setDate", "getDate", "toDateString", "locale", "filteredConversations", "matchesSearch", "toLowerCase", "matchesFilter", "toggleStar", "prevConversations", "toggleArchive", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "type", "placeholder", "value", "onChange", "target", "onClick", "ref", "style", "height", "overflowY", "scrollbarWidth", "scrollbarColor", "src", "alt", "lastSeen", "stopPropagation", "index", "isSender", "showAvatar", "title", "onSubmit", "rows", "onKeyDown", "key", "shift<PERSON>ey", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Projeler/kidgarden/burky_root_web/client/src/pages/client/messages/ClientMessagesPage.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '../../../hooks/useAuth';\nimport api from '../../../services/api';\nimport { toast } from 'react-hot-toast';\nimport io from 'socket.io-client';\nimport {\n  ChatBubbleLeftEllipsisIcon,\n  MagnifyingGlassIcon,\n  UserIcon,\n  PaperAirplaneIcon,\n  PaperClipIcon,\n  FaceSmileIcon,\n  UserCircleIcon,\n  EllipsisHorizontalIcon,\n  PhoneIcon,\n  VideoCameraIcon,\n  InformationCircleIcon,\n  ClockIcon,\n  CheckCircleIcon,\n  XMarkIcon,\n  TrashIcon,\n  ArchiveBoxIcon,\n  StarIcon,\n  UserGroupIcon,\n  CalendarIcon\n} from '@heroicons/react/24/outline';\nimport { format } from 'date-fns';\nimport { tr } from 'date-fns/locale';\nimport { Link } from 'react-router-dom';\n\n/**\n * Danışan mesajlaşma sayfası\n */\nconst ClientMessagesPage = () => {\n  const { user } = useAuth();\n  const [isLoading, setIsLoading] = useState(true);\n  const [conversations, setConversations] = useState([]);\n  const [selectedConversation, setSelectedConversation] = useState(null);\n  const [messages, setMessages] = useState([]);\n  const [messageText, setMessageText] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filter, setFilter] = useState('all'); // all, unread, archived, starred\n  const [socket, setSocket] = useState(null);\n  const [onlineUsers, setOnlineUsers] = useState(new Set());\n  const [typingUsers, setTypingUsers] = useState(new Set());\n  const messagesEndRef = useRef(null);\n  const conversationsRef = useRef(null);\n  const messagesContainerRef = useRef(null);\n\n  // Socket.IO connection - ayrı useEffect\n  useEffect(() => {\n    const token = localStorage.getItem('accessToken');\n    if (token && user?.id) {\n      console.log('🔌 CLIENT: Socket.IO bağlantısı kuruluyor... User ID:', user.id);\n      const socketConnection = io(process.env.REACT_APP_API_URL || 'http://localhost:5000', {\n        auth: { token },\n        transports: ['websocket', 'polling'],\n        reconnection: true,\n        reconnectionDelay: 1000,\n        reconnectionAttempts: 5,\n        timeout: 20000\n      });\n\n      setSocket(socketConnection);\n\n      // Connection events\n      socketConnection.on('connect', () => {\n        console.log('✅ CLIENT: Socket.IO bağlantısı kuruldu:', socketConnection.id);\n        console.log('🔗 CLIENT: Socket connected:', socketConnection.connected);\n        console.log('🏠 CLIENT: Socket rooms:', socketConnection.rooms);\n        // Kullanıcının online olduğunu bildir\n        socketConnection.emit('user_online');\n      });\n\n      socketConnection.on('disconnect', (reason) => {\n        console.log('❌ CLIENT: Socket.IO bağlantısı kesildi:', reason);\n      });\n\n      socketConnection.on('connect_error', (error) => {\n        console.error('🔥 CLIENT: Socket.IO bağlantı hatası:', error);\n      });\n\n      socketConnection.on('reconnect', (attemptNumber) => {\n        console.log('🔄 CLIENT: Socket.IO yeniden bağlandı, deneme:', attemptNumber);\n      });\n\n      socketConnection.on('reconnect_error', (error) => {\n        console.error('🔥 CLIENT: Socket.IO yeniden bağlanma hatası:', error);\n      });\n\n      // Heartbeat mekanizması\n      const heartbeatInterval = setInterval(() => {\n        if (socketConnection.connected) {\n          socketConnection.emit('ping', (response) => {\n            if (response === 'pong') {\n              console.log('💓 CLIENT: Heartbeat OK');\n            }\n          });\n        }\n      }, 30000); // 30 saniyede bir ping gönder\n\n      return () => {\n        clearInterval(heartbeatInterval);\n        socketConnection.disconnect();\n      };\n    } else {\n      console.log('❌ CLIENT: Socket bağlantısı kurulamıyor - token veya user eksik:', {\n        hasToken: !!token,\n        hasUser: !!user?.id\n      });\n    }\n  }, [user?.id]);\n\n  // Socket event listeners - ayrı useEffect\n  useEffect(() => {\n    if (socket && user?.id) {\n      console.log('🎧 Socket event listeners kuruluyor...');\n\n      // Socket event listeners\n      const handleNewMessage = (message) => {\n        console.log('📨 CLIENT: Yeni mesaj alındı:', message);\n        console.log('👤 CLIENT: Current user ID:', user.id);\n        console.log('💬 CLIENT: Message sender ID:', message.senderId);\n        console.log('🏠 CLIENT: Message conversation ID:', message.conversationId);\n\n        // Conversation listesini ÖNCE güncelle (her zaman)\n        setConversations(prev => {\n          console.log('📋 CLIENT: Updating conversations list');\n          return prev.map(conv =>\n            conv.id === message.conversationId\n              ? { ...conv, lastMessage: message.content, timestamp: message.createdAt, unread: message.senderId !== user.id }\n              : conv\n          );\n        });\n\n        // Eğer şu anda bu conversation açıksa, mesajı da ekle\n        setSelectedConversation(currentSelected => {\n          console.log('🎯 CLIENT: Current selected conversation:', currentSelected?.id);\n          if (currentSelected && message.conversationId === currentSelected.id) {\n            console.log('✅ CLIENT: Adding message to current conversation');\n            setMessages(prev => {\n              // Mesajın zaten var olup olmadığını kontrol et\n              const messageExists = prev.some(msg => msg.id === message.id);\n              if (messageExists) {\n                console.log('⚠️ CLIENT: Message already exists, skipping:', message.id);\n                return prev;\n              }\n\n              return [...prev, {\n                id: message.id,\n                senderId: message.senderId,\n                senderName: message.sender.name,\n                senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n                text: message.content,\n                timestamp: message.createdAt,\n                read: message.isRead,\n                delivered: true // Socket'ten gelen mesajlar delivered olarak kabul edilir\n              }];\n            });\n\n            // Scroll to bottom\n            setTimeout(() => {\n              messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n            }, 100);\n          } else {\n            console.log('❌ CLIENT: Not adding message - conversation not selected or different conversation');\n          }\n          return currentSelected;\n        });\n      };\n\n      const handleMessageSent = (message) => {\n        console.log('✅ Mesaj gönderildi onayı:', message);\n      };\n\n      const handleUserStatusChange = (data) => {\n        setOnlineUsers(prev => {\n          const newSet = new Set(prev);\n          if (data.status === 'online') {\n            newSet.add(data.userId);\n          } else {\n            newSet.delete(data.userId);\n          }\n          return newSet;\n        });\n      };\n\n      const handleUserTyping = (data) => {\n        setSelectedConversation(currentSelected => {\n          if (currentSelected && data.conversationId === currentSelected.id) {\n            setTypingUsers(prev => new Set([...prev, data.userId]));\n          }\n          return currentSelected;\n        });\n      };\n\n      const handleUserStoppedTyping = (data) => {\n        setTypingUsers(prev => {\n          const newSet = new Set(prev);\n          newSet.delete(data.userId);\n          return newSet;\n        });\n      };\n\n      const handleMessagesRead = (data) => {\n        console.log('👁️ CLIENT: Messages read event:', data);\n        // Mesajları okundu olarak işaretle\n        setMessages(prev => prev.map(msg =>\n          data.messageIds.includes(msg.id) ? { ...msg, read: true } : msg\n        ));\n      };\n\n      // Event listener'ları ekle\n      console.log('🎧 CLIENT: Adding socket event listeners...');\n      socket.on('new_message', handleNewMessage);\n      socket.on('message_sent', handleMessageSent);\n      socket.on('user_status_change', handleUserStatusChange);\n      socket.on('user_typing', handleUserTyping);\n      socket.on('user_stopped_typing', handleUserStoppedTyping);\n      socket.on('messages_read', handleMessagesRead);\n      console.log('✅ CLIENT: Socket event listeners added');\n\n      // Cleanup function\n      return () => {\n        socket.off('new_message', handleNewMessage);\n        socket.off('message_sent', handleMessageSent);\n        socket.off('user_status_change', handleUserStatusChange);\n        socket.off('user_typing', handleUserTyping);\n        socket.off('user_stopped_typing', handleUserStoppedTyping);\n      };\n    }\n  }, [socket, user.id]);\n\n  // Conversations yükleme - ayrı useEffect\n  useEffect(() => {\n    loadConversations();\n  }, []);\n\n  // Socket bağlandığında tüm conversation'lara join ol - sadece bir kez\n  const [joinedConversations, setJoinedConversations] = useState(new Set());\n\n  useEffect(() => {\n    if (socket && conversations.length > 0) {\n      console.log('🏠 CLIENT: Tüm conversation\\'lara katılıyor...', conversations.length, 'conversation');\n\n      conversations.forEach(conversation => {\n        if (!joinedConversations.has(conversation.id)) {\n          socket.emit('join_conversation', conversation.id);\n          console.log(`🏠 CLIENT: Conversation ${conversation.id} katıldı`);\n          setJoinedConversations(prev => new Set([...prev, conversation.id]));\n        }\n      });\n    }\n  }, [socket, conversations, joinedConversations]);\n\n  // Conversations yükleme\n  const loadConversations = async () => {\n    try {\n      setIsLoading(true);\n      const response = await api.get('/messages/conversations');\n\n      // API verisini frontend formatına çevir\n      const formattedConversations = response.data.conversations.map(conversation => ({\n        id: conversation.id,\n        expertId: conversation.otherUser.id,\n        expertName: conversation.otherUser.name,\n        expertTitle: conversation.otherUser.role === 'Expert' ? 'Uzman' : conversation.otherUser.role,\n        lastMessage: conversation.lastMessage?.content || 'Henüz mesaj yok',\n        timestamp: conversation.lastMessage?.timestamp || conversation.createdAt,\n        unread: conversation.lastMessage ? !conversation.lastMessage.isRead && conversation.lastMessage.senderId !== user.id : false,\n        avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(conversation.otherUser.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        status: onlineUsers.has(conversation.otherUser.id) ? 'online' : 'offline',\n        starred: false,\n        archived: false\n      }));\n\n      setConversations(formattedConversations);\n    } catch (error) {\n      console.error('Konuşmalar yüklenirken hata:', error);\n      toast.error('Konuşmalar yüklenemedi');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Mesaj yükleme ve okundu işaretleme\n  useEffect(() => {\n    if (selectedConversation) {\n      loadMessages(selectedConversation.id);\n      markConversationAsRead(selectedConversation.id);\n    }\n  }, [selectedConversation]);\n\n  // Conversation'daki mesajları okundu olarak işaretle\n  const markConversationAsRead = async (conversationId) => {\n    try {\n      // Okunmamış mesajları bul\n      const unreadMessages = messages.filter(msg =>\n        msg.senderId !== user.id && !msg.read\n      );\n\n      if (unreadMessages.length > 0) {\n        // Server'a okundu bilgisi gönder\n        await api.put(`/messages/conversations/${conversationId}/read`);\n\n        // Local state'i güncelle\n        setMessages(prev => prev.map(msg =>\n          msg.senderId !== user.id ? { ...msg, read: true } : msg\n        ));\n\n        // Socket'e okundu bilgisi gönder\n        if (socket) {\n          socket.emit('mark_messages_read', {\n            conversationId,\n            messageIds: unreadMessages.map(msg => msg.id)\n          });\n        }\n      }\n    } catch (error) {\n      console.error('Mesajları okundu olarak işaretlerken hata:', error);\n    }\n  };\n\n  // Conversations yükleme\n  const loadMessages = async (conversationId) => {\n    try {\n      const response = await api.get(`/messages/conversations/${conversationId}`);\n\n      // API verisini frontend formatına çevir\n      const formattedMessages = response.data.messages.map(message => ({\n        id: message.id,\n        senderId: message.senderId,\n        senderName: message.sender.name,\n        senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        text: message.content,\n        timestamp: message.createdAt,\n        read: message.isRead,\n        delivered: true, // Veritabanından gelen mesajlar delivered olarak kabul edilir\n        attachments: message.attachments\n      }));\n\n      setMessages(formattedMessages);\n\n      // Scroll to bottom\n      setTimeout(() => {\n        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n      }, 100);\n    } catch (error) {\n      console.error('Mesajlar yüklenirken hata:', error);\n      toast.error('Mesajlar yüklenemedi');\n    }\n  };\n  // Mesaj gönderme\n  const sendMessage = async () => {\n    if (!messageText.trim() || !selectedConversation) return;\n\n    console.log('📤 Mesaj gönderiliyor:', {\n      receiverId: selectedConversation.expertId,\n      content: messageText.trim()\n    });\n\n    try {\n      const response = await api.post('/messages/send', {\n        receiverId: selectedConversation.expertId,\n        content: messageText.trim()\n      });\n\n      console.log('✅ Mesaj gönderildi:', response.data);\n\n      // Sadece mesaj input'unu temizle - mesaj socket'ten gelecek\n      setMessageText('');\n\n    } catch (error) {\n      console.error('Mesaj gönderilirken hata:', error);\n      toast.error('Mesaj gönderilemedi');\n    }\n  };\n\n  useEffect(() => {\n    // Mesajları otomatik kaydır\n    if (messagesEndRef.current && messagesContainerRef.current) {\n      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;\n    }\n  }, [messages]);\n\n  // Mesaj gönderme\n  const handleSendMessage = (e) => {\n    e.preventDefault();\n    sendMessage();\n  };\n\n  // Konuşma seçme\n  const handleSelectConversation = (conversation) => {\n    setSelectedConversation(conversation);\n  };\n\n  // Tarih formatı\n  const formatMessageDate = (dateString) => {\n    const date = new Date(dateString);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    \n    if (date.toDateString() === today.toDateString()) {\n      return format(date, 'HH:mm', { locale: tr });\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Dün ' + format(date, 'HH:mm', { locale: tr });\n    } else {\n      return format(date, 'dd MMM HH:mm', { locale: tr });\n    }\n  };\n\n  // Konuşmaları filtrele\n  const filteredConversations = conversations.filter(conv => {\n    // Arama filtresi\n    const matchesSearch = conv.expertName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         conv.lastMessage.toLowerCase().includes(searchTerm.toLowerCase());\n    \n    // Durum filtresi\n    const matchesFilter = filter === 'all' ||\n                         (filter === 'unread' && conv.unread) ||\n                         (filter === 'archived' && conv.archived) ||\n                         (filter === 'starred' && conv.starred);\n                        \n    return matchesSearch && matchesFilter;\n  });\n\n  // Konuşmayı yıldızla\n  const toggleStar = (id) => {\n    setConversations(prevConversations =>\n      prevConversations.map(conv =>\n        conv.id === id ? { ...conv, starred: !conv.starred } : conv\n      )\n    );\n    \n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({ ...prev, starred: !prev.starred }));\n    }\n  };\n\n  // Konuşmayı arşivle\n  const toggleArchive = (id) => {\n    setConversations(prevConversations =>\n      prevConversations.map(conv =>\n        conv.id === id ? { ...conv, archived: !conv.archived } : conv\n      )\n    );\n    \n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({ ...prev, archived: !prev.archived }));\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-gray-50 min-h-screen pb-12\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-8\">\n        {/* Başlık ve Üst Kısım */}\n        <div className=\"bg-gradient-to-r from-pink-500 to-pink-600 shadow-lg rounded-lg p-6 mb-6\">\n          <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-white\">Mesajlarım</h1>\n              <p className=\"mt-1 text-pink-100\">\n                Uzmanlarınızla güvenli bir şekilde iletişim kurun\n              </p>\n            </div>\n            <div className=\"mt-3 sm:mt-0 flex space-x-2\">\n              <Link\n                to=\"/client/experts\"\n                className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-pink-600 bg-white hover:bg-pink-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-colors duration-150\"\n              >\n                <UserGroupIcon className=\"h-4 w-4 mr-2\" />\n                Uzmanlar\n              </Link>\n              <Link\n                to=\"/client/appointments\"\n                className=\"inline-flex items-center px-4 py-2 border border-white text-sm font-medium rounded-md shadow-sm text-white hover:bg-pink-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-colors duration-150\"\n              >\n                <CalendarIcon className=\"h-4 w-4 mr-2\" />\n                Randevularım\n              </Link>\n            </div>\n          </div>\n        </div>\n        \n        {/* Mesajlaşma arayüzü */}\n        <div className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n          <div className=\"grid grid-cols-12 h-[75vh]\">\n            {/* Sol Kenar - Konuşma Listesi */}\n            <div className=\"col-span-12 md:col-span-4 border-r border-gray-200 flex flex-col\">\n              <div className=\"p-4 border-b border-gray-200 bg-white\">\n                <h1 className=\"text-xl font-semibold text-gray-800 flex items-center\">\n                  <ChatBubbleLeftEllipsisIcon className=\"h-6 w-6 text-teal-600 mr-2\" />\n                  Mesajlar\n                </h1>\n                <div className=\"mt-3 relative\">\n                  <input\n                    type=\"text\"\n                    placeholder=\"Konuşmalarda ara...\"\n                    className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                  />\n                  <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400 absolute left-3 top-2.5\" />\n                </div>\n                <div className=\"mt-3 flex space-x-2\">\n                  <button\n                    className={`px-3 py-1 text-sm rounded-full ${filter === 'all' \n                      ? 'bg-teal-100 text-teal-800' \n                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                    onClick={() => setFilter('all')}\n                  >\n                    Tümü\n                  </button>\n                  <button\n                    className={`px-3 py-1 text-sm rounded-full ${filter === 'unread' \n                      ? 'bg-teal-100 text-teal-800' \n                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                    onClick={() => setFilter('unread')}\n                  >\n                    Okunmamış\n                  </button>\n                  <button\n                    className={`px-3 py-1 text-sm rounded-full ${filter === 'starred' \n                      ? 'bg-teal-100 text-teal-800' \n                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                    onClick={() => setFilter('starred')}\n                  >\n                    Yıldızlı\n                  </button>\n                  <button\n                    className={`px-3 py-1 text-sm rounded-full ${filter === 'archived' \n                      ? 'bg-teal-100 text-teal-800' \n                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                    onClick={() => setFilter('archived')}\n                  >\n                    Arşiv\n                  </button>\n                </div>\n              </div>\n              <div \n                ref={conversationsRef}\n                style={{\n                  height: 'calc(75vh - 145px)',\n                  overflowY: 'auto',\n                  scrollbarWidth: 'thin',\n                  scrollbarColor: '#D1D5DB #F3F4F6'\n                }}\n              >\n                {filteredConversations.length === 0 ? (\n                  <div className=\"p-4 text-center text-gray-500\">\n                    Hiç mesajınız yok\n                  </div>\n                ) : (\n                  filteredConversations.map(conversation => (\n                    <div\n                      key={conversation.id}\n                      className={`p-3 cursor-pointer hover:bg-gray-50 transition-colors duration-200 ${\n                        selectedConversation?.id === conversation.id ? 'bg-teal-50' : ''\n                      } ${conversation.unread ? 'bg-teal-50 hover:bg-teal-100' : ''}`}\n                      onClick={() => handleSelectConversation(conversation)}\n                    >\n                      <div className=\"flex items-start space-x-3\">\n                        <div className=\"relative flex-shrink-0\">\n                          <img\n                            src={conversation.avatar}\n                            alt={conversation.expertName}\n                            className={`h-10 w-10 rounded-full ${\n                              selectedConversation?.id === conversation.id \n                                ? 'ring-2 ring-teal-600' \n                                : ''\n                            }`}\n                          />\n                          {conversation.status === 'online' && (\n                            <span className=\"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"></span>\n                          )}\n                        </div>\n                        <div className=\"flex-1 min-w-0\">\n                          <div className=\"flex justify-between items-start\">\n                            <h3 className={`text-sm font-medium ${conversation.unread ? 'text-gray-900' : 'text-gray-700'}`}>\n                              {conversation.expertName}\n                            </h3>\n                            <div className=\"flex items-center space-x-1\">\n                              {conversation.starred && (\n                                <StarIcon className=\"h-4 w-4 text-yellow-400 fill-current\" />\n                              )}\n                              <span className=\"text-xs text-gray-500\">\n                                {formatMessageDate(conversation.timestamp)}\n                              </span>\n                            </div>\n                          </div>\n                          <p className=\"text-xs text-gray-500\">{conversation.expertTitle}</p>\n                          <p className={`text-sm truncate mt-1 ${\n                            conversation.unread ? 'text-gray-900 font-medium' : 'text-gray-500'\n                          }`}>\n                            {conversation.lastMessage}\n                          </p>\n                          <div className=\"flex justify-between items-center mt-1\">\n                            <span className=\"text-xs text-gray-500\">\n                              {conversation.status === 'online' \n                                ? 'Çevrimiçi' \n                                : conversation.lastSeen \n                                  ? `Son görülme: ${conversation.lastSeen}` \n                                  : ''}\n                            </span>\n                            <div className=\"flex space-x-1\">\n                              <button \n                                onClick={(e) => {\n                                  e.stopPropagation();\n                                  toggleStar(conversation.id);\n                                }}\n                                className=\"text-gray-400 hover:text-yellow-400\"\n                              >\n                                <StarIcon className={`h-4 w-4 ${conversation.starred ? 'text-yellow-400 fill-current' : ''}`} />\n                              </button>\n                              <button \n                                onClick={(e) => {\n                                  e.stopPropagation();\n                                  toggleArchive(conversation.id);\n                                }}\n                                className=\"text-gray-400 hover:text-gray-600\"\n                              >\n                                <ArchiveBoxIcon className=\"h-4 w-4\" />\n                              </button>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                      {conversation.unread && (\n                        <span className=\"inline-flex items-center ml-12 mt-1 px-2 py-0.5 rounded-full text-xs font-medium bg-teal-600 text-white\">\n                          Yeni\n                        </span>\n                      )}\n                    </div>\n                  ))\n                )}\n              </div>\n            </div>\n            {/* Sağ Taraf - Mesaj Alanı */}\n            <div className=\"col-span-12 md:col-span-8 flex flex-col\">\n              {selectedConversation ? (\n                <>\n                  {/* Mesajlaşma Başlığı */}\n                  <div className=\"p-4 border-b border-gray-200 bg-white flex justify-between items-center\">\n                    <div className=\"flex items-center\">\n                      <div className=\"relative mr-3\">\n                        <img\n                          src={selectedConversation.avatar}\n                          alt={selectedConversation.expertName}\n                          className=\"h-10 w-10 rounded-full\"\n                        />\n                        {selectedConversation.status === 'online' && (\n                          <span className=\"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"></span>\n                        )}\n                      </div>\n                      <div>\n                        <h2 className=\"text-lg font-medium text-gray-800\">\n                          {selectedConversation.expertName}\n                        </h2>\n                        <p className=\"text-xs text-gray-500\">\n                          {selectedConversation.expertTitle} {' • '}\n                          {selectedConversation.status === 'online' \n                            ? 'Çevrimiçi' \n                            : selectedConversation.lastSeen \n                              ? `Son görülme: ${selectedConversation.lastSeen}` \n                              : ''}\n                        </p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center space-x-3\">\n                      <Link \n                        to={`/client/experts/${selectedConversation.expertId}`}\n                        className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\"\n                      >\n                        <UserIcon className=\"h-5 w-5\" />\n                      </Link>\n                      <Link \n                        to={`/client/appointments?expert=${selectedConversation.expertId}`}\n                        className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\"\n                      >\n                        <ClockIcon className=\"h-5 w-5\" />\n                      </Link>\n                      <Link \n                        to={`/client/sessions?expert=${selectedConversation.expertId}`}\n                        className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\"\n                      >\n                        <VideoCameraIcon className=\"h-5 w-5\" />\n                      </Link>\n                      <button className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\">\n                        <EllipsisHorizontalIcon className=\"h-5 w-5\" />\n                      </button>\n                    </div>\n                  </div>\n\n                  {/* Mesaj Alanı */}\n                  <div \n                    ref={messagesContainerRef}\n                    className=\"p-4 bg-gray-50\"\n                    style={{\n                      height: 'calc(75vh - 195px)',\n                      overflowY: 'auto',\n                      scrollbarWidth: 'thin',\n                      scrollbarColor: '#D1D5DB #F3F4F6'\n                    }}\n                  >\n                    {messages.map((message, index) => {\n                      const isSender = message.senderId === user.id;\n                      const showAvatar = index === 0 || messages[index - 1].senderId !== message.senderId;\n                      \n                      return (\n                        <div key={message.id} className={`flex ${isSender ? 'justify-end' : 'justify-start'} mb-4`}>\n                          {!isSender && showAvatar && (\n                            <img \n                              src={message.senderAvatar} \n                              alt={message.senderName}\n                              className=\"h-8 w-8 rounded-full mr-2 mt-1\"\n                            />\n                          )}\n                          {!isSender && !showAvatar && <div className=\"w-8 mr-2\"></div>}\n                          <div \n                            className={`max-w-xs md:max-w-md lg:max-w-lg rounded-lg px-4 py-2 ${\n                              isSender \n                                ? 'bg-teal-600 text-white rounded-br-none' \n                                : 'bg-white text-gray-800 rounded-bl-none border border-gray-200'\n                            }`}\n                          >\n                            <p className=\"text-sm\">{message.text}</p>\n                            <div className={`text-xs mt-1 ${isSender ? 'text-teal-200' : 'text-gray-500'} flex items-center justify-end`}>\n                              {formatMessageDate(message.timestamp)}\n                              {isSender && (\n                                <div className=\"flex items-center ml-1\">\n                                  {/* Tek tik - İletildi */}\n                                  <CheckCircleIcon\n                                    className={`h-3 w-3 ${message.read ? 'text-blue-400' : 'text-gray-400'}`}\n                                    title={message.delivered ? 'İletildi' : 'Gönderiliyor'}\n                                  />\n                                  {/* Çift tik - Okundu */}\n                                  {message.read && (\n                                    <CheckCircleIcon\n                                      className=\"h-3 w-3 -ml-1 text-blue-400\"\n                                      title=\"Okundu\"\n                                    />\n                                  )}\n                                </div>\n                              )}\n                            </div>\n                          </div>\n                          {isSender && showAvatar && (\n                            <img \n                              src={message.senderAvatar}\n                              alt={message.senderName}\n                              className=\"h-8 w-8 rounded-full ml-2 mt-1\"\n                            />\n                          )}\n                          {isSender && !showAvatar && <div className=\"w-8 ml-2\"></div>}\n                        </div>\n                      );\n                    })}\n                    <div ref={messagesEndRef} />\n                  </div>\n\n                  {/* Mesaj Giriş Alanı */}\n                  <div className=\"p-3 border-t border-gray-200 bg-white\">\n                    <form onSubmit={handleSendMessage} className=\"flex items-end\">\n                      <button\n                        type=\"button\"\n                        className=\"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\"\n                      >\n                        <PaperClipIcon className=\"h-5 w-5\" />\n                      </button>\n                      <div className=\"flex-1 mx-2\">\n                        <textarea\n                          className=\"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 resize-none\"\n                          placeholder=\"Mesajınızı yazın...\"\n                          rows=\"2\"\n                          value={messageText}\n                          onChange={(e) => setMessageText(e.target.value)}\n                          onKeyDown={(e) => {\n                            if (e.key === 'Enter' && !e.shiftKey) {\n                              e.preventDefault();\n                              sendMessage();\n                            }\n                          }}\n                        ></textarea>\n                      </div>\n                      <button\n                        type=\"button\"\n                        className=\"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\"\n                      >\n                        <FaceSmileIcon className=\"h-5 w-5\" />\n                      </button>\n                      <button\n                        type=\"submit\"\n                        disabled={!messageText.trim()}\n                        className={`ml-2 p-2 rounded-full ${\n                          messageText.trim() \n                            ? 'bg-teal-600 text-white hover:bg-teal-700' \n                            : 'bg-gray-200 text-gray-400 cursor-not-allowed'\n                        } focus:outline-none`}\n                      >\n                        <PaperAirplaneIcon className=\"h-5 w-5\" />\n                      </button>\n                    </form>\n                  </div>\n                </>\n              ) : (\n                // Mesaj seçilmediğinde\n                <div className=\"flex-1 flex flex-col items-center justify-center p-6 bg-gray-50\">\n                  <div className=\"w-full max-w-md text-center\">\n                    <ChatBubbleLeftEllipsisIcon className=\"h-16 w-16 text-gray-300 mb-4 mx-auto\" />\n                    <h3 className=\"text-xl font-medium text-gray-800 mb-2\">Mesajlarınız</h3>\n                    <p className=\"text-gray-500 mx-auto\">\n                      Mesajlaşmaya başlamak için sol taraftan bir konuşma seçin veya yeni bir uzmanla iletişime geçin.\n                    </p>\n                    <div className=\"mt-6\">\n                      <Link\n                        to=\"/client/experts\"\n                        className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500\"\n                      >\n                        <UserIcon className=\"-ml-1 mr-2 h-5 w-5\" aria-hidden=\"true\" />\n                        Uzmanları Keşfedin\n                      </Link>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n} \n\nexport default ClientMessagesPage; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,GAAG,MAAM,uBAAuB;AACvC,SAASC,KAAK,QAAQ,iBAAiB;AACvC,OAAOC,EAAE,MAAM,kBAAkB;AACjC,SACEC,0BAA0B,EAC1BC,mBAAmB,EACnBC,QAAQ,EACRC,iBAAiB,EACjBC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,sBAAsB,EACtBC,SAAS,EACTC,eAAe,EACfC,qBAAqB,EACrBC,SAAS,EACTC,eAAe,EACfC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,QAAQ,EACRC,aAAa,EACbC,YAAY,QACP,6BAA6B;AACpC,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,EAAE,QAAQ,iBAAiB;AACpC,SAASC,IAAI,QAAQ,kBAAkB;;AAEvC;AACA;AACA;AAFA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM;IAAEC;EAAK,CAAC,GAAGhC,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACsC,aAAa,EAAEC,gBAAgB,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACwC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAAC0C,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC4C,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC8C,UAAU,EAAEC,aAAa,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgD,MAAM,EAAEC,SAAS,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACkD,MAAM,EAAEC,SAAS,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACoD,WAAW,EAAEC,cAAc,CAAC,GAAGrD,QAAQ,CAAC,IAAIsD,GAAG,CAAC,CAAC,CAAC;EACzD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGxD,QAAQ,CAAC,IAAIsD,GAAG,CAAC,CAAC,CAAC;EACzD,MAAMG,cAAc,GAAGvD,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMwD,gBAAgB,GAAGxD,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMyD,oBAAoB,GAAGzD,MAAM,CAAC,IAAI,CAAC;;EAEzC;EACAD,SAAS,CAAC,MAAM;IACd,MAAM2D,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACjD,IAAIF,KAAK,IAAIzB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE4B,EAAE,EAAE;MACrBC,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAE9B,IAAI,CAAC4B,EAAE,CAAC;MAC7E,MAAMG,gBAAgB,GAAG5D,EAAE,CAAC6D,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB,EAAE;QACpFC,IAAI,EAAE;UAAEV;QAAM,CAAC;QACfW,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;QACpCC,YAAY,EAAE,IAAI;QAClBC,iBAAiB,EAAE,IAAI;QACvBC,oBAAoB,EAAE,CAAC;QACvBC,OAAO,EAAE;MACX,CAAC,CAAC;MAEFxB,SAAS,CAACe,gBAAgB,CAAC;;MAE3B;MACAA,gBAAgB,CAACU,EAAE,CAAC,SAAS,EAAE,MAAM;QACnCZ,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEC,gBAAgB,CAACH,EAAE,CAAC;QAC3EC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEC,gBAAgB,CAACW,SAAS,CAAC;QACvEb,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEC,gBAAgB,CAACY,KAAK,CAAC;QAC/D;QACAZ,gBAAgB,CAACa,IAAI,CAAC,aAAa,CAAC;MACtC,CAAC,CAAC;MAEFb,gBAAgB,CAACU,EAAE,CAAC,YAAY,EAAGI,MAAM,IAAK;QAC5ChB,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEe,MAAM,CAAC;MAChE,CAAC,CAAC;MAEFd,gBAAgB,CAACU,EAAE,CAAC,eAAe,EAAGK,KAAK,IAAK;QAC9CjB,OAAO,CAACiB,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC/D,CAAC,CAAC;MAEFf,gBAAgB,CAACU,EAAE,CAAC,WAAW,EAAGM,aAAa,IAAK;QAClDlB,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEiB,aAAa,CAAC;MAC9E,CAAC,CAAC;MAEFhB,gBAAgB,CAACU,EAAE,CAAC,iBAAiB,EAAGK,KAAK,IAAK;QAChDjB,OAAO,CAACiB,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACvE,CAAC,CAAC;;MAEF;MACA,MAAME,iBAAiB,GAAGC,WAAW,CAAC,MAAM;QAC1C,IAAIlB,gBAAgB,CAACW,SAAS,EAAE;UAC9BX,gBAAgB,CAACa,IAAI,CAAC,MAAM,EAAGM,QAAQ,IAAK;YAC1C,IAAIA,QAAQ,KAAK,MAAM,EAAE;cACvBrB,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;YACxC;UACF,CAAC,CAAC;QACJ;MACF,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;MAEX,OAAO,MAAM;QACXqB,aAAa,CAACH,iBAAiB,CAAC;QAChCjB,gBAAgB,CAACqB,UAAU,CAAC,CAAC;MAC/B,CAAC;IACH,CAAC,MAAM;MACLvB,OAAO,CAACC,GAAG,CAAC,kEAAkE,EAAE;QAC9EuB,QAAQ,EAAE,CAAC,CAAC5B,KAAK;QACjB6B,OAAO,EAAE,CAAC,EAACtD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE4B,EAAE;MACrB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC5B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4B,EAAE,CAAC,CAAC;;EAEd;EACA9D,SAAS,CAAC,MAAM;IACd,IAAIiD,MAAM,IAAIf,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE4B,EAAE,EAAE;MACtBC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;MAErD;MACA,MAAMyB,gBAAgB,GAAIC,OAAO,IAAK;QACpC3B,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE0B,OAAO,CAAC;QACrD3B,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE9B,IAAI,CAAC4B,EAAE,CAAC;QACnDC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE0B,OAAO,CAACC,QAAQ,CAAC;QAC9D5B,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE0B,OAAO,CAACE,cAAc,CAAC;;QAE1E;QACAtD,gBAAgB,CAACuD,IAAI,IAAI;UACvB9B,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;UACrD,OAAO6B,IAAI,CAACC,GAAG,CAACC,IAAI,IAClBA,IAAI,CAACjC,EAAE,KAAK4B,OAAO,CAACE,cAAc,GAC9B;YAAE,GAAGG,IAAI;YAAEC,WAAW,EAAEN,OAAO,CAACO,OAAO;YAAEC,SAAS,EAAER,OAAO,CAACS,SAAS;YAAEC,MAAM,EAAEV,OAAO,CAACC,QAAQ,KAAKzD,IAAI,CAAC4B;UAAG,CAAC,GAC7GiC,IACN,CAAC;QACH,CAAC,CAAC;;QAEF;QACAvD,uBAAuB,CAAC6D,eAAe,IAAI;UACzCtC,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEqC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEvC,EAAE,CAAC;UAC7E,IAAIuC,eAAe,IAAIX,OAAO,CAACE,cAAc,KAAKS,eAAe,CAACvC,EAAE,EAAE;YACpEC,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;YAC/DtB,WAAW,CAACmD,IAAI,IAAI;cAClB;cACA,MAAMS,aAAa,GAAGT,IAAI,CAACU,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC1C,EAAE,KAAK4B,OAAO,CAAC5B,EAAE,CAAC;cAC7D,IAAIwC,aAAa,EAAE;gBACjBvC,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE0B,OAAO,CAAC5B,EAAE,CAAC;gBACvE,OAAO+B,IAAI;cACb;cAEA,OAAO,CAAC,GAAGA,IAAI,EAAE;gBACf/B,EAAE,EAAE4B,OAAO,CAAC5B,EAAE;gBACd6B,QAAQ,EAAED,OAAO,CAACC,QAAQ;gBAC1Bc,UAAU,EAAEf,OAAO,CAACgB,MAAM,CAACC,IAAI;gBAC/BC,YAAY,EAAE,oCAAoCC,kBAAkB,CAACnB,OAAO,CAACgB,MAAM,CAACC,IAAI,CAAC,qDAAqD;gBAC9IG,IAAI,EAAEpB,OAAO,CAACO,OAAO;gBACrBC,SAAS,EAAER,OAAO,CAACS,SAAS;gBAC5BY,IAAI,EAAErB,OAAO,CAACsB,MAAM;gBACpBC,SAAS,EAAE,IAAI,CAAC;cAClB,CAAC,CAAC;YACJ,CAAC,CAAC;;YAEF;YACAC,UAAU,CAAC,MAAM;cAAA,IAAAC,qBAAA;cACf,CAAAA,qBAAA,GAAA3D,cAAc,CAAC4D,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;gBAAEC,QAAQ,EAAE;cAAS,CAAC,CAAC;YAChE,CAAC,EAAE,GAAG,CAAC;UACT,CAAC,MAAM;YACLvD,OAAO,CAACC,GAAG,CAAC,oFAAoF,CAAC;UACnG;UACA,OAAOqC,eAAe;QACxB,CAAC,CAAC;MACJ,CAAC;MAED,MAAMkB,iBAAiB,GAAI7B,OAAO,IAAK;QACrC3B,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE0B,OAAO,CAAC;MACnD,CAAC;MAED,MAAM8B,sBAAsB,GAAIC,IAAI,IAAK;QACvCrE,cAAc,CAACyC,IAAI,IAAI;UACrB,MAAM6B,MAAM,GAAG,IAAIrE,GAAG,CAACwC,IAAI,CAAC;UAC5B,IAAI4B,IAAI,CAACE,MAAM,KAAK,QAAQ,EAAE;YAC5BD,MAAM,CAACE,GAAG,CAACH,IAAI,CAACI,MAAM,CAAC;UACzB,CAAC,MAAM;YACLH,MAAM,CAACI,MAAM,CAACL,IAAI,CAACI,MAAM,CAAC;UAC5B;UACA,OAAOH,MAAM;QACf,CAAC,CAAC;MACJ,CAAC;MAED,MAAMK,gBAAgB,GAAIN,IAAI,IAAK;QACjCjF,uBAAuB,CAAC6D,eAAe,IAAI;UACzC,IAAIA,eAAe,IAAIoB,IAAI,CAAC7B,cAAc,KAAKS,eAAe,CAACvC,EAAE,EAAE;YACjEP,cAAc,CAACsC,IAAI,IAAI,IAAIxC,GAAG,CAAC,CAAC,GAAGwC,IAAI,EAAE4B,IAAI,CAACI,MAAM,CAAC,CAAC,CAAC;UACzD;UACA,OAAOxB,eAAe;QACxB,CAAC,CAAC;MACJ,CAAC;MAED,MAAM2B,uBAAuB,GAAIP,IAAI,IAAK;QACxClE,cAAc,CAACsC,IAAI,IAAI;UACrB,MAAM6B,MAAM,GAAG,IAAIrE,GAAG,CAACwC,IAAI,CAAC;UAC5B6B,MAAM,CAACI,MAAM,CAACL,IAAI,CAACI,MAAM,CAAC;UAC1B,OAAOH,MAAM;QACf,CAAC,CAAC;MACJ,CAAC;MAED,MAAMO,kBAAkB,GAAIR,IAAI,IAAK;QACnC1D,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEyD,IAAI,CAAC;QACrD;QACA/E,WAAW,CAACmD,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACU,GAAG,IAC9BiB,IAAI,CAACS,UAAU,CAACC,QAAQ,CAAC3B,GAAG,CAAC1C,EAAE,CAAC,GAAG;UAAE,GAAG0C,GAAG;UAAEO,IAAI,EAAE;QAAK,CAAC,GAAGP,GAC9D,CAAC,CAAC;MACJ,CAAC;;MAED;MACAzC,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAC1Df,MAAM,CAAC0B,EAAE,CAAC,aAAa,EAAEc,gBAAgB,CAAC;MAC1CxC,MAAM,CAAC0B,EAAE,CAAC,cAAc,EAAE4C,iBAAiB,CAAC;MAC5CtE,MAAM,CAAC0B,EAAE,CAAC,oBAAoB,EAAE6C,sBAAsB,CAAC;MACvDvE,MAAM,CAAC0B,EAAE,CAAC,aAAa,EAAEoD,gBAAgB,CAAC;MAC1C9E,MAAM,CAAC0B,EAAE,CAAC,qBAAqB,EAAEqD,uBAAuB,CAAC;MACzD/E,MAAM,CAAC0B,EAAE,CAAC,eAAe,EAAEsD,kBAAkB,CAAC;MAC9ClE,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;MAErD;MACA,OAAO,MAAM;QACXf,MAAM,CAACmF,GAAG,CAAC,aAAa,EAAE3C,gBAAgB,CAAC;QAC3CxC,MAAM,CAACmF,GAAG,CAAC,cAAc,EAAEb,iBAAiB,CAAC;QAC7CtE,MAAM,CAACmF,GAAG,CAAC,oBAAoB,EAAEZ,sBAAsB,CAAC;QACxDvE,MAAM,CAACmF,GAAG,CAAC,aAAa,EAAEL,gBAAgB,CAAC;QAC3C9E,MAAM,CAACmF,GAAG,CAAC,qBAAqB,EAAEJ,uBAAuB,CAAC;MAC5D,CAAC;IACH;EACF,CAAC,EAAE,CAAC/E,MAAM,EAAEf,IAAI,CAAC4B,EAAE,CAAC,CAAC;;EAErB;EACA9D,SAAS,CAAC,MAAM;IACdqI,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGxI,QAAQ,CAAC,IAAIsD,GAAG,CAAC,CAAC,CAAC;EAEzErD,SAAS,CAAC,MAAM;IACd,IAAIiD,MAAM,IAAIZ,aAAa,CAACmG,MAAM,GAAG,CAAC,EAAE;MACtCzE,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE3B,aAAa,CAACmG,MAAM,EAAE,cAAc,CAAC;MAEnGnG,aAAa,CAACoG,OAAO,CAACC,YAAY,IAAI;QACpC,IAAI,CAACJ,mBAAmB,CAACK,GAAG,CAACD,YAAY,CAAC5E,EAAE,CAAC,EAAE;UAC7Cb,MAAM,CAAC6B,IAAI,CAAC,mBAAmB,EAAE4D,YAAY,CAAC5E,EAAE,CAAC;UACjDC,OAAO,CAACC,GAAG,CAAC,2BAA2B0E,YAAY,CAAC5E,EAAE,UAAU,CAAC;UACjEyE,sBAAsB,CAAC1C,IAAI,IAAI,IAAIxC,GAAG,CAAC,CAAC,GAAGwC,IAAI,EAAE6C,YAAY,CAAC5E,EAAE,CAAC,CAAC,CAAC;QACrE;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACb,MAAM,EAAEZ,aAAa,EAAEiG,mBAAmB,CAAC,CAAC;;EAEhD;EACA,MAAMD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFjG,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMgD,QAAQ,GAAG,MAAMjF,GAAG,CAACyI,GAAG,CAAC,yBAAyB,CAAC;;MAEzD;MACA,MAAMC,sBAAsB,GAAGzD,QAAQ,CAACqC,IAAI,CAACpF,aAAa,CAACyD,GAAG,CAAC4C,YAAY;QAAA,IAAAI,qBAAA,EAAAC,sBAAA;QAAA,OAAK;UAC9EjF,EAAE,EAAE4E,YAAY,CAAC5E,EAAE;UACnBkF,QAAQ,EAAEN,YAAY,CAACO,SAAS,CAACnF,EAAE;UACnCoF,UAAU,EAAER,YAAY,CAACO,SAAS,CAACtC,IAAI;UACvCwC,WAAW,EAAET,YAAY,CAACO,SAAS,CAACG,IAAI,KAAK,QAAQ,GAAG,OAAO,GAAGV,YAAY,CAACO,SAAS,CAACG,IAAI;UAC7FpD,WAAW,EAAE,EAAA8C,qBAAA,GAAAJ,YAAY,CAAC1C,WAAW,cAAA8C,qBAAA,uBAAxBA,qBAAA,CAA0B7C,OAAO,KAAI,iBAAiB;UACnEC,SAAS,EAAE,EAAA6C,sBAAA,GAAAL,YAAY,CAAC1C,WAAW,cAAA+C,sBAAA,uBAAxBA,sBAAA,CAA0B7C,SAAS,KAAIwC,YAAY,CAACvC,SAAS;UACxEC,MAAM,EAAEsC,YAAY,CAAC1C,WAAW,GAAG,CAAC0C,YAAY,CAAC1C,WAAW,CAACgB,MAAM,IAAI0B,YAAY,CAAC1C,WAAW,CAACL,QAAQ,KAAKzD,IAAI,CAAC4B,EAAE,GAAG,KAAK;UAC5HuF,MAAM,EAAE,oCAAoCxC,kBAAkB,CAAC6B,YAAY,CAACO,SAAS,CAACtC,IAAI,CAAC,qDAAqD;UAChJgB,MAAM,EAAExE,WAAW,CAACwF,GAAG,CAACD,YAAY,CAACO,SAAS,CAACnF,EAAE,CAAC,GAAG,QAAQ,GAAG,SAAS;UACzEwF,OAAO,EAAE,KAAK;UACdC,QAAQ,EAAE;QACZ,CAAC;MAAA,CAAC,CAAC;MAEHjH,gBAAgB,CAACuG,sBAAsB,CAAC;IAC1C,CAAC,CAAC,OAAO7D,KAAK,EAAE;MACdjB,OAAO,CAACiB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD5E,KAAK,CAAC4E,KAAK,CAAC,wBAAwB,CAAC;IACvC,CAAC,SAAS;MACR5C,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACApC,SAAS,CAAC,MAAM;IACd,IAAIuC,oBAAoB,EAAE;MACxBiH,YAAY,CAACjH,oBAAoB,CAACuB,EAAE,CAAC;MACrC2F,sBAAsB,CAAClH,oBAAoB,CAACuB,EAAE,CAAC;IACjD;EACF,CAAC,EAAE,CAACvB,oBAAoB,CAAC,CAAC;;EAE1B;EACA,MAAMkH,sBAAsB,GAAG,MAAO7D,cAAc,IAAK;IACvD,IAAI;MACF;MACA,MAAM8D,cAAc,GAAGjH,QAAQ,CAACM,MAAM,CAACyD,GAAG,IACxCA,GAAG,CAACb,QAAQ,KAAKzD,IAAI,CAAC4B,EAAE,IAAI,CAAC0C,GAAG,CAACO,IACnC,CAAC;MAED,IAAI2C,cAAc,CAAClB,MAAM,GAAG,CAAC,EAAE;QAC7B;QACA,MAAMrI,GAAG,CAACwJ,GAAG,CAAC,2BAA2B/D,cAAc,OAAO,CAAC;;QAE/D;QACAlD,WAAW,CAACmD,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACU,GAAG,IAC9BA,GAAG,CAACb,QAAQ,KAAKzD,IAAI,CAAC4B,EAAE,GAAG;UAAE,GAAG0C,GAAG;UAAEO,IAAI,EAAE;QAAK,CAAC,GAAGP,GACtD,CAAC,CAAC;;QAEF;QACA,IAAIvD,MAAM,EAAE;UACVA,MAAM,CAAC6B,IAAI,CAAC,oBAAoB,EAAE;YAChCc,cAAc;YACdsC,UAAU,EAAEwB,cAAc,CAAC5D,GAAG,CAACU,GAAG,IAAIA,GAAG,CAAC1C,EAAE;UAC9C,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdjB,OAAO,CAACiB,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;IACpE;EACF,CAAC;;EAED;EACA,MAAMwE,YAAY,GAAG,MAAO5D,cAAc,IAAK;IAC7C,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMjF,GAAG,CAACyI,GAAG,CAAC,2BAA2BhD,cAAc,EAAE,CAAC;;MAE3E;MACA,MAAMgE,iBAAiB,GAAGxE,QAAQ,CAACqC,IAAI,CAAChF,QAAQ,CAACqD,GAAG,CAACJ,OAAO,KAAK;QAC/D5B,EAAE,EAAE4B,OAAO,CAAC5B,EAAE;QACd6B,QAAQ,EAAED,OAAO,CAACC,QAAQ;QAC1Bc,UAAU,EAAEf,OAAO,CAACgB,MAAM,CAACC,IAAI;QAC/BC,YAAY,EAAE,oCAAoCC,kBAAkB,CAACnB,OAAO,CAACgB,MAAM,CAACC,IAAI,CAAC,qDAAqD;QAC9IG,IAAI,EAAEpB,OAAO,CAACO,OAAO;QACrBC,SAAS,EAAER,OAAO,CAACS,SAAS;QAC5BY,IAAI,EAAErB,OAAO,CAACsB,MAAM;QACpBC,SAAS,EAAE,IAAI;QAAE;QACjB4C,WAAW,EAAEnE,OAAO,CAACmE;MACvB,CAAC,CAAC,CAAC;MAEHnH,WAAW,CAACkH,iBAAiB,CAAC;;MAE9B;MACA1C,UAAU,CAAC,MAAM;QAAA,IAAA4C,sBAAA;QACf,CAAAA,sBAAA,GAAAtG,cAAc,CAAC4D,OAAO,cAAA0C,sBAAA,uBAAtBA,sBAAA,CAAwBzC,cAAc,CAAC;UAAEC,QAAQ,EAAE;QAAS,CAAC,CAAC;MAChE,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACdjB,OAAO,CAACiB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD5E,KAAK,CAAC4E,KAAK,CAAC,sBAAsB,CAAC;IACrC;EACF,CAAC;EACD;EACA,MAAM+E,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAACpH,WAAW,CAACqH,IAAI,CAAC,CAAC,IAAI,CAACzH,oBAAoB,EAAE;IAElDwB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;MACpCiG,UAAU,EAAE1H,oBAAoB,CAACyG,QAAQ;MACzC/C,OAAO,EAAEtD,WAAW,CAACqH,IAAI,CAAC;IAC5B,CAAC,CAAC;IAEF,IAAI;MACF,MAAM5E,QAAQ,GAAG,MAAMjF,GAAG,CAAC+J,IAAI,CAAC,gBAAgB,EAAE;QAChDD,UAAU,EAAE1H,oBAAoB,CAACyG,QAAQ;QACzC/C,OAAO,EAAEtD,WAAW,CAACqH,IAAI,CAAC;MAC5B,CAAC,CAAC;MAEFjG,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEoB,QAAQ,CAACqC,IAAI,CAAC;;MAEjD;MACA7E,cAAc,CAAC,EAAE,CAAC;IAEpB,CAAC,CAAC,OAAOoC,KAAK,EAAE;MACdjB,OAAO,CAACiB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD5E,KAAK,CAAC4E,KAAK,CAAC,qBAAqB,CAAC;IACpC;EACF,CAAC;EAEDhF,SAAS,CAAC,MAAM;IACd;IACA,IAAIwD,cAAc,CAAC4D,OAAO,IAAI1D,oBAAoB,CAAC0D,OAAO,EAAE;MAC1D1D,oBAAoB,CAAC0D,OAAO,CAAC+C,SAAS,GAAGzG,oBAAoB,CAAC0D,OAAO,CAACgD,YAAY;IACpF;EACF,CAAC,EAAE,CAAC3H,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAM4H,iBAAiB,GAAIC,CAAC,IAAK;IAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBR,WAAW,CAAC,CAAC;EACf,CAAC;;EAED;EACA,MAAMS,wBAAwB,GAAI9B,YAAY,IAAK;IACjDlG,uBAAuB,CAACkG,YAAY,CAAC;EACvC,CAAC;;EAED;EACA,MAAM+B,iBAAiB,GAAIC,UAAU,IAAK;IACxC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,KAAK,GAAG,IAAID,IAAI,CAAC,CAAC;IACxB,MAAME,SAAS,GAAG,IAAIF,IAAI,CAACC,KAAK,CAAC;IACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IAE1C,IAAIL,IAAI,CAACM,YAAY,CAAC,CAAC,KAAKJ,KAAK,CAACI,YAAY,CAAC,CAAC,EAAE;MAChD,OAAOxJ,MAAM,CAACkJ,IAAI,EAAE,OAAO,EAAE;QAAEO,MAAM,EAAExJ;MAAG,CAAC,CAAC;IAC9C,CAAC,MAAM,IAAIiJ,IAAI,CAACM,YAAY,CAAC,CAAC,KAAKH,SAAS,CAACG,YAAY,CAAC,CAAC,EAAE;MAC3D,OAAO,MAAM,GAAGxJ,MAAM,CAACkJ,IAAI,EAAE,OAAO,EAAE;QAAEO,MAAM,EAAExJ;MAAG,CAAC,CAAC;IACvD,CAAC,MAAM;MACL,OAAOD,MAAM,CAACkJ,IAAI,EAAE,cAAc,EAAE;QAAEO,MAAM,EAAExJ;MAAG,CAAC,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAMyJ,qBAAqB,GAAG9I,aAAa,CAACU,MAAM,CAACgD,IAAI,IAAI;IACzD;IACA,MAAMqF,aAAa,GAAGrF,IAAI,CAACmD,UAAU,CAACmC,WAAW,CAAC,CAAC,CAAClD,QAAQ,CAACtF,UAAU,CAACwI,WAAW,CAAC,CAAC,CAAC,IACjEtF,IAAI,CAACC,WAAW,CAACqF,WAAW,CAAC,CAAC,CAAClD,QAAQ,CAACtF,UAAU,CAACwI,WAAW,CAAC,CAAC,CAAC;;IAEtF;IACA,MAAMC,aAAa,GAAGvI,MAAM,KAAK,KAAK,IAChBA,MAAM,KAAK,QAAQ,IAAIgD,IAAI,CAACK,MAAO,IACnCrD,MAAM,KAAK,UAAU,IAAIgD,IAAI,CAACwD,QAAS,IACvCxG,MAAM,KAAK,SAAS,IAAIgD,IAAI,CAACuD,OAAQ;IAE3D,OAAO8B,aAAa,IAAIE,aAAa;EACvC,CAAC,CAAC;;EAEF;EACA,MAAMC,UAAU,GAAIzH,EAAE,IAAK;IACzBxB,gBAAgB,CAACkJ,iBAAiB,IAChCA,iBAAiB,CAAC1F,GAAG,CAACC,IAAI,IACxBA,IAAI,CAACjC,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGiC,IAAI;MAAEuD,OAAO,EAAE,CAACvD,IAAI,CAACuD;IAAQ,CAAC,GAAGvD,IACzD,CACF,CAAC;IAED,IAAIxD,oBAAoB,IAAIA,oBAAoB,CAACuB,EAAE,KAAKA,EAAE,EAAE;MAC1DtB,uBAAuB,CAACqD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEyD,OAAO,EAAE,CAACzD,IAAI,CAACyD;MAAQ,CAAC,CAAC,CAAC;IACxE;EACF,CAAC;;EAED;EACA,MAAMmC,aAAa,GAAI3H,EAAE,IAAK;IAC5BxB,gBAAgB,CAACkJ,iBAAiB,IAChCA,iBAAiB,CAAC1F,GAAG,CAACC,IAAI,IACxBA,IAAI,CAACjC,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGiC,IAAI;MAAEwD,QAAQ,EAAE,CAACxD,IAAI,CAACwD;IAAS,CAAC,GAAGxD,IAC3D,CACF,CAAC;IAED,IAAIxD,oBAAoB,IAAIA,oBAAoB,CAACuB,EAAE,KAAKA,EAAE,EAAE;MAC1DtB,uBAAuB,CAACqD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE0D,QAAQ,EAAE,CAAC1D,IAAI,CAAC0D;MAAS,CAAC,CAAC,CAAC;IAC1E;EACF,CAAC;EAED,IAAIpH,SAAS,EAAE;IACb,oBACEN,OAAA;MAAK6J,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5D9J,OAAA;QAAK6J,SAAS,EAAC;MAA2E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9F,CAAC;EAEV;EAEA,oBACElK,OAAA;IAAK6J,SAAS,EAAC,+BAA+B;IAAAC,QAAA,eAC5C9J,OAAA;MAAK6J,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1D9J,OAAA;QAAK6J,SAAS,EAAC,0EAA0E;QAAAC,QAAA,eACvF9J,OAAA;UAAK6J,SAAS,EAAC,uEAAuE;UAAAC,QAAA,gBACpF9J,OAAA;YAAA8J,QAAA,gBACE9J,OAAA;cAAI6J,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7DlK,OAAA;cAAG6J,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAElC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNlK,OAAA;YAAK6J,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C9J,OAAA,CAACF,IAAI;cACHqK,EAAE,EAAC,iBAAiB;cACpBN,SAAS,EAAC,sPAAsP;cAAAC,QAAA,gBAEhQ9J,OAAA,CAACN,aAAa;gBAACmK,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAE5C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPlK,OAAA,CAACF,IAAI;cACHqK,EAAE,EAAC,sBAAsB;cACzBN,SAAS,EAAC,qOAAqO;cAAAC,QAAA,gBAE/O9J,OAAA,CAACL,YAAY;gBAACkK,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,qBAE3C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlK,OAAA;QAAK6J,SAAS,EAAC,+CAA+C;QAAAC,QAAA,eAC5D9J,OAAA;UAAK6J,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBAEzC9J,OAAA;YAAK6J,SAAS,EAAC,kEAAkE;YAAAC,QAAA,gBAC/E9J,OAAA;cAAK6J,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD9J,OAAA;gBAAI6J,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,gBACnE9J,OAAA,CAACvB,0BAA0B;kBAACoL,SAAS,EAAC;gBAA4B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,YAEvE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLlK,OAAA;gBAAK6J,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B9J,OAAA;kBACEoK,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,0BAAqB;kBACjCR,SAAS,EAAC,iHAAiH;kBAC3HS,KAAK,EAAEtJ,UAAW;kBAClBuJ,QAAQ,EAAG9B,CAAC,IAAKxH,aAAa,CAACwH,CAAC,CAAC+B,MAAM,CAACF,KAAK;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACFlK,OAAA,CAACtB,mBAAmB;kBAACmL,SAAS,EAAC;gBAA+C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC,eACNlK,OAAA;gBAAK6J,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAClC9J,OAAA;kBACE6J,SAAS,EAAE,kCAAkC3I,MAAM,KAAK,KAAK,GACzD,2BAA2B,GAC3B,6CAA6C,EAAG;kBACpDuJ,OAAO,EAAEA,CAAA,KAAMtJ,SAAS,CAAC,KAAK,CAAE;kBAAA2I,QAAA,EACjC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlK,OAAA;kBACE6J,SAAS,EAAE,kCAAkC3I,MAAM,KAAK,QAAQ,GAC5D,2BAA2B,GAC3B,6CAA6C,EAAG;kBACpDuJ,OAAO,EAAEA,CAAA,KAAMtJ,SAAS,CAAC,QAAQ,CAAE;kBAAA2I,QAAA,EACpC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlK,OAAA;kBACE6J,SAAS,EAAE,kCAAkC3I,MAAM,KAAK,SAAS,GAC7D,2BAA2B,GAC3B,6CAA6C,EAAG;kBACpDuJ,OAAO,EAAEA,CAAA,KAAMtJ,SAAS,CAAC,SAAS,CAAE;kBAAA2I,QAAA,EACrC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlK,OAAA;kBACE6J,SAAS,EAAE,kCAAkC3I,MAAM,KAAK,UAAU,GAC9D,2BAA2B,GAC3B,6CAA6C,EAAG;kBACpDuJ,OAAO,EAAEA,CAAA,KAAMtJ,SAAS,CAAC,UAAU,CAAE;kBAAA2I,QAAA,EACtC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlK,OAAA;cACE0K,GAAG,EAAE9I,gBAAiB;cACtB+I,KAAK,EAAE;gBACLC,MAAM,EAAE,oBAAoB;gBAC5BC,SAAS,EAAE,MAAM;gBACjBC,cAAc,EAAE,MAAM;gBACtBC,cAAc,EAAE;cAClB,CAAE;cAAAjB,QAAA,EAEDR,qBAAqB,CAAC3C,MAAM,KAAK,CAAC,gBACjC3G,OAAA;gBAAK6J,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,GAENZ,qBAAqB,CAACrF,GAAG,CAAC4C,YAAY,iBACpC7G,OAAA;gBAEE6J,SAAS,EAAE,sEACT,CAAAnJ,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEuB,EAAE,MAAK4E,YAAY,CAAC5E,EAAE,GAAG,YAAY,GAAG,EAAE,IAC9D4E,YAAY,CAACtC,MAAM,GAAG,8BAA8B,GAAG,EAAE,EAAG;gBAChEkG,OAAO,EAAEA,CAAA,KAAM9B,wBAAwB,CAAC9B,YAAY,CAAE;gBAAAiD,QAAA,gBAEtD9J,OAAA;kBAAK6J,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzC9J,OAAA;oBAAK6J,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrC9J,OAAA;sBACEgL,GAAG,EAAEnE,YAAY,CAACW,MAAO;sBACzByD,GAAG,EAAEpE,YAAY,CAACQ,UAAW;sBAC7BwC,SAAS,EAAE,0BACT,CAAAnJ,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEuB,EAAE,MAAK4E,YAAY,CAAC5E,EAAE,GACxC,sBAAsB,GACtB,EAAE;oBACL;sBAAA8H,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,EACDrD,YAAY,CAACf,MAAM,KAAK,QAAQ,iBAC/B9F,OAAA;sBAAM6J,SAAS,EAAC;oBAAyF;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CACjH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACNlK,OAAA;oBAAK6J,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC7B9J,OAAA;sBAAK6J,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,gBAC/C9J,OAAA;wBAAI6J,SAAS,EAAE,uBAAuBhD,YAAY,CAACtC,MAAM,GAAG,eAAe,GAAG,eAAe,EAAG;wBAAAuF,QAAA,EAC7FjD,YAAY,CAACQ;sBAAU;wBAAA0C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtB,CAAC,eACLlK,OAAA;wBAAK6J,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,GACzCjD,YAAY,CAACY,OAAO,iBACnBzH,OAAA,CAACP,QAAQ;0BAACoK,SAAS,EAAC;wBAAsC;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAC7D,eACDlK,OAAA;0BAAM6J,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,EACpClB,iBAAiB,CAAC/B,YAAY,CAACxC,SAAS;wBAAC;0BAAA0F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNlK,OAAA;sBAAG6J,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEjD,YAAY,CAACS;oBAAW;sBAAAyC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnElK,OAAA;sBAAG6J,SAAS,EAAE,yBACZhD,YAAY,CAACtC,MAAM,GAAG,2BAA2B,GAAG,eAAe,EAClE;sBAAAuF,QAAA,EACAjD,YAAY,CAAC1C;oBAAW;sBAAA4F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC,eACJlK,OAAA;sBAAK6J,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrD9J,OAAA;wBAAM6J,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EACpCjD,YAAY,CAACf,MAAM,KAAK,QAAQ,GAC7B,WAAW,GACXe,YAAY,CAACqE,QAAQ,GACnB,gBAAgBrE,YAAY,CAACqE,QAAQ,EAAE,GACvC;sBAAE;wBAAAnB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACPlK,OAAA;wBAAK6J,SAAS,EAAC,gBAAgB;wBAAAC,QAAA,gBAC7B9J,OAAA;0BACEyK,OAAO,EAAGhC,CAAC,IAAK;4BACdA,CAAC,CAAC0C,eAAe,CAAC,CAAC;4BACnBzB,UAAU,CAAC7C,YAAY,CAAC5E,EAAE,CAAC;0BAC7B,CAAE;0BACF4H,SAAS,EAAC,qCAAqC;0BAAAC,QAAA,eAE/C9J,OAAA,CAACP,QAAQ;4BAACoK,SAAS,EAAE,WAAWhD,YAAY,CAACY,OAAO,GAAG,8BAA8B,GAAG,EAAE;0BAAG;4BAAAsC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1F,CAAC,eACTlK,OAAA;0BACEyK,OAAO,EAAGhC,CAAC,IAAK;4BACdA,CAAC,CAAC0C,eAAe,CAAC,CAAC;4BACnBvB,aAAa,CAAC/C,YAAY,CAAC5E,EAAE,CAAC;0BAChC,CAAE;0BACF4H,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,eAE7C9J,OAAA,CAACR,cAAc;4BAACqK,SAAS,EAAC;0BAAS;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EACLrD,YAAY,CAACtC,MAAM,iBAClBvE,OAAA;kBAAM6J,SAAS,EAAC,yGAAyG;kBAAAC,QAAA,EAAC;gBAE1H;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACP;cAAA,GA5EIrD,YAAY,CAAC5E,EAAE;gBAAA8H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6EjB,CACN;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlK,OAAA;YAAK6J,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EACrDpJ,oBAAoB,gBACnBV,OAAA,CAAAE,SAAA;cAAA4J,QAAA,gBAEE9J,OAAA;gBAAK6J,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,gBACtF9J,OAAA;kBAAK6J,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC9J,OAAA;oBAAK6J,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5B9J,OAAA;sBACEgL,GAAG,EAAEtK,oBAAoB,CAAC8G,MAAO;sBACjCyD,GAAG,EAAEvK,oBAAoB,CAAC2G,UAAW;sBACrCwC,SAAS,EAAC;oBAAwB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC,EACDxJ,oBAAoB,CAACoF,MAAM,KAAK,QAAQ,iBACvC9F,OAAA;sBAAM6J,SAAS,EAAC;oBAAyF;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CACjH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACNlK,OAAA;oBAAA8J,QAAA,gBACE9J,OAAA;sBAAI6J,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAC9CpJ,oBAAoB,CAAC2G;oBAAU;sBAAA0C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9B,CAAC,eACLlK,OAAA;sBAAG6J,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GACjCpJ,oBAAoB,CAAC4G,WAAW,EAAC,GAAC,EAAC,KAAK,EACxC5G,oBAAoB,CAACoF,MAAM,KAAK,QAAQ,GACrC,WAAW,GACXpF,oBAAoB,CAACwK,QAAQ,GAC3B,gBAAgBxK,oBAAoB,CAACwK,QAAQ,EAAE,GAC/C,EAAE;oBAAA;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlK,OAAA;kBAAK6J,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C9J,OAAA,CAACF,IAAI;oBACHqK,EAAE,EAAE,mBAAmBzJ,oBAAoB,CAACyG,QAAQ,EAAG;oBACvD0C,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,eAE5D9J,OAAA,CAACrB,QAAQ;sBAACkL,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACPlK,OAAA,CAACF,IAAI;oBACHqK,EAAE,EAAE,+BAA+BzJ,oBAAoB,CAACyG,QAAQ,EAAG;oBACnE0C,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,eAE5D9J,OAAA,CAACZ,SAAS;sBAACyK,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC,eACPlK,OAAA,CAACF,IAAI;oBACHqK,EAAE,EAAE,2BAA2BzJ,oBAAoB,CAACyG,QAAQ,EAAG;oBAC/D0C,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,eAE5D9J,OAAA,CAACd,eAAe;sBAAC2K,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC,eACPlK,OAAA;oBAAQ6J,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,eAClE9J,OAAA,CAAChB,sBAAsB;sBAAC6K,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNlK,OAAA;gBACE0K,GAAG,EAAE7I,oBAAqB;gBAC1BgI,SAAS,EAAC,gBAAgB;gBAC1Bc,KAAK,EAAE;kBACLC,MAAM,EAAE,oBAAoB;kBAC5BC,SAAS,EAAE,MAAM;kBACjBC,cAAc,EAAE,MAAM;kBACtBC,cAAc,EAAE;gBAClB,CAAE;gBAAAjB,QAAA,GAEDlJ,QAAQ,CAACqD,GAAG,CAAC,CAACJ,OAAO,EAAEuH,KAAK,KAAK;kBAChC,MAAMC,QAAQ,GAAGxH,OAAO,CAACC,QAAQ,KAAKzD,IAAI,CAAC4B,EAAE;kBAC7C,MAAMqJ,UAAU,GAAGF,KAAK,KAAK,CAAC,IAAIxK,QAAQ,CAACwK,KAAK,GAAG,CAAC,CAAC,CAACtH,QAAQ,KAAKD,OAAO,CAACC,QAAQ;kBAEnF,oBACE9D,OAAA;oBAAsB6J,SAAS,EAAE,QAAQwB,QAAQ,GAAG,aAAa,GAAG,eAAe,OAAQ;oBAAAvB,QAAA,GACxF,CAACuB,QAAQ,IAAIC,UAAU,iBACtBtL,OAAA;sBACEgL,GAAG,EAAEnH,OAAO,CAACkB,YAAa;sBAC1BkG,GAAG,EAAEpH,OAAO,CAACe,UAAW;sBACxBiF,SAAS,EAAC;oBAAgC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C,CACF,EACA,CAACmB,QAAQ,IAAI,CAACC,UAAU,iBAAItL,OAAA;sBAAK6J,SAAS,EAAC;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC7DlK,OAAA;sBACE6J,SAAS,EAAE,yDACTwB,QAAQ,GACJ,wCAAwC,GACxC,+DAA+D,EAClE;sBAAAvB,QAAA,gBAEH9J,OAAA;wBAAG6J,SAAS,EAAC,SAAS;wBAAAC,QAAA,EAAEjG,OAAO,CAACoB;sBAAI;wBAAA8E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACzClK,OAAA;wBAAK6J,SAAS,EAAE,gBAAgBwB,QAAQ,GAAG,eAAe,GAAG,eAAe,gCAAiC;wBAAAvB,QAAA,GAC1GlB,iBAAiB,CAAC/E,OAAO,CAACQ,SAAS,CAAC,EACpCgH,QAAQ,iBACPrL,OAAA;0BAAK6J,SAAS,EAAC,wBAAwB;0BAAAC,QAAA,gBAErC9J,OAAA,CAACX,eAAe;4BACdwK,SAAS,EAAE,WAAWhG,OAAO,CAACqB,IAAI,GAAG,eAAe,GAAG,eAAe,EAAG;4BACzEqG,KAAK,EAAE1H,OAAO,CAACuB,SAAS,GAAG,UAAU,GAAG;0BAAe;4BAAA2E,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxD,CAAC,EAEDrG,OAAO,CAACqB,IAAI,iBACXlF,OAAA,CAACX,eAAe;4BACdwK,SAAS,EAAC,6BAA6B;4BACvC0B,KAAK,EAAC;0BAAQ;4BAAAxB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACf,CACF;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,EACLmB,QAAQ,IAAIC,UAAU,iBACrBtL,OAAA;sBACEgL,GAAG,EAAEnH,OAAO,CAACkB,YAAa;sBAC1BkG,GAAG,EAAEpH,OAAO,CAACe,UAAW;sBACxBiF,SAAS,EAAC;oBAAgC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C,CACF,EACAmB,QAAQ,IAAI,CAACC,UAAU,iBAAItL,OAAA;sBAAK6J,SAAS,EAAC;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA,GA5CpDrG,OAAO,CAAC5B,EAAE;oBAAA8H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OA6Cf,CAAC;gBAEV,CAAC,CAAC,eACFlK,OAAA;kBAAK0K,GAAG,EAAE/I;gBAAe;kBAAAoI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eAGNlK,OAAA;gBAAK6J,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,eACpD9J,OAAA;kBAAMwL,QAAQ,EAAEhD,iBAAkB;kBAACqB,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC3D9J,OAAA;oBACEoK,IAAI,EAAC,QAAQ;oBACbP,SAAS,EAAC,uEAAuE;oBAAAC,QAAA,eAEjF9J,OAAA,CAACnB,aAAa;sBAACgL,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACTlK,OAAA;oBAAK6J,SAAS,EAAC,aAAa;oBAAAC,QAAA,eAC1B9J,OAAA;sBACE6J,SAAS,EAAC,iHAAiH;sBAC3HQ,WAAW,EAAC,yCAAqB;sBACjCoB,IAAI,EAAC,GAAG;sBACRnB,KAAK,EAAExJ,WAAY;sBACnByJ,QAAQ,EAAG9B,CAAC,IAAK1H,cAAc,CAAC0H,CAAC,CAAC+B,MAAM,CAACF,KAAK,CAAE;sBAChDoB,SAAS,EAAGjD,CAAC,IAAK;wBAChB,IAAIA,CAAC,CAACkD,GAAG,KAAK,OAAO,IAAI,CAAClD,CAAC,CAACmD,QAAQ,EAAE;0BACpCnD,CAAC,CAACC,cAAc,CAAC,CAAC;0BAClBR,WAAW,CAAC,CAAC;wBACf;sBACF;oBAAE;sBAAA6B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACNlK,OAAA;oBACEoK,IAAI,EAAC,QAAQ;oBACbP,SAAS,EAAC,uEAAuE;oBAAAC,QAAA,eAEjF9J,OAAA,CAAClB,aAAa;sBAAC+K,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACTlK,OAAA;oBACEoK,IAAI,EAAC,QAAQ;oBACbyB,QAAQ,EAAE,CAAC/K,WAAW,CAACqH,IAAI,CAAC,CAAE;oBAC9B0B,SAAS,EAAE,yBACT/I,WAAW,CAACqH,IAAI,CAAC,CAAC,GACd,0CAA0C,GAC1C,8CAA8C,qBAC9B;oBAAA2B,QAAA,eAEtB9J,OAAA,CAACpB,iBAAiB;sBAACiL,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,eACN,CAAC;YAAA;YAEH;YACAlK,OAAA;cAAK6J,SAAS,EAAC,iEAAiE;cAAAC,QAAA,eAC9E9J,OAAA;gBAAK6J,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1C9J,OAAA,CAACvB,0BAA0B;kBAACoL,SAAS,EAAC;gBAAsC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/ElK,OAAA;kBAAI6J,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxElK,OAAA;kBAAG6J,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAErC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJlK,OAAA;kBAAK6J,SAAS,EAAC,MAAM;kBAAAC,QAAA,eACnB9J,OAAA,CAACF,IAAI;oBACHqK,EAAE,EAAC,iBAAiB;oBACpBN,SAAS,EAAC,wNAAwN;oBAAAC,QAAA,gBAElO9J,OAAA,CAACrB,QAAQ;sBAACkL,SAAS,EAAC,oBAAoB;sBAAC,eAAY;oBAAM;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gCAEhE;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAA9J,EAAA,CAtyBKD,kBAAkB;EAAA,QACL9B,OAAO;AAAA;AAAAyN,EAAA,GADpB3L,kBAAkB;AAwyBxB,eAAeA,kBAAkB;AAAC,IAAA2L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}