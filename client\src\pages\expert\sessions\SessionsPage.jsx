import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../hooks/useAuth';
import { 
  VideoCamera, 
  ClockIcon, 
  CalendarIcon,
  UserIcon,
  CheckCircleIcon,
  XCircleIcon,
  BellIcon,
  ChartBarIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  ArrowDownTrayIcon,
  StarIcon,
  PlayCircleIcon,
  PaperClipIcon,
  DocumentArrowDownIcon
} from '@heroicons/react/24/outline';
import { format, parseISO } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Link } from 'react-router-dom';

/**
 * <PERSON>zman görüşmeleri sayfası
 */
const SessionsPage = () => {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [sessions, setSessions] = useState([]);
  const [activeTab, setActiveTab] = useState('upcoming'); // upcoming, past, all
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  // Mock görüşme durumları
  const sessionStatuses = {
    scheduled: "Planlandı",
    inProgress: "Devam Ediyor",
    completed: "Tamamlandı",
    missed: "Kaçırıldı",
    cancelled: "İptal Edildi",
  };

  useEffect(() => {
    // Gerçek uygulamada API'den veri çekeceğiz
    // Bu mockup veri sadece gösterim amaçlıdır
    const mockSessions = [
      {
        id: 1,
        clientId: 101,
        clientName: 'Ahmet Yılmaz',
        date: '2025-03-25',
        startTime: '14:00',
        endTime: '14:50',
        duration: 50,
        status: 'scheduled',
        type: 'video',
        notes: 'Anksiyete terapisi - devam seansı',
        recordingAvailable: false,
        sessionsCompleted: 3,
        clientAvatar: 'https://randomuser.me/api/portraits/men/32.jpg'
      },
      {
        id: 2,
        clientId: 102,
        clientName: 'Ayşe Demir',
        date: '2025-03-26',
        startTime: '15:30',
        endTime: '16:20',
        duration: 50,
        status: 'scheduled',
        type: 'video',
        notes: 'İlişki danışmanlığı - ilk seans',
        recordingAvailable: false,
        sessionsCompleted: 0,
        clientAvatar: 'https://randomuser.me/api/portraits/women/12.jpg'
      },
      {
        id: 3,
        clientId: 103,
        clientName: 'Mehmet Kaya',
        date: '2025-03-27',
        startTime: '10:00',
        endTime: '10:50',
        duration: 50,
        status: 'scheduled',
        type: 'video',
        notes: 'Stres yönetimi - devam seansı',
        recordingAvailable: false,
        sessionsCompleted: 5,
        clientAvatar: 'https://randomuser.me/api/portraits/men/22.jpg'
      },
      {
        id: 4,
        clientId: 104,
        clientName: 'Zeynep Öztürk',
        date: '2025-03-24',
        startTime: '11:30',
        endTime: '12:20',
        duration: 50,
        status: 'completed',
        type: 'video',
        notes: 'Depresyon terapisi - devam seansı',
        recordingAvailable: true,
        sessionsCompleted: 7,
        clientAvatar: 'https://randomuser.me/api/portraits/women/8.jpg'
      },
      {
        id: 5,
        clientId: 105,
        clientName: 'Ali Yıldız',
        date: '2025-03-23',
        startTime: '09:00',
        endTime: '09:50',
        duration: 50,
        status: 'missed',
        type: 'video',
        notes: 'Danışan katılmadı',
        recordingAvailable: false,
        sessionsCompleted: 2,
        clientAvatar: 'https://randomuser.me/api/portraits/men/42.jpg'
      },
      {
        id: 6,
        clientId: 106,
        clientName: 'Gizem Aksoy',
        date: '2025-03-22',
        startTime: '16:00',
        endTime: '16:50',
        duration: 50,
        status: 'cancelled',
        type: 'video',
        notes: 'Danışan iptal etti - kişisel neden',
        recordingAvailable: false,
        sessionsCompleted: 4,
        clientAvatar: 'https://randomuser.me/api/portraits/women/22.jpg'
      },
      {
        id: 7,
        clientId: 107,
        clientName: 'Emre Demir',
        date: '2025-03-21',
        startTime: '13:30',
        endTime: '14:20',
        duration: 50,
        status: 'completed',
        type: 'video',
        notes: 'Kaygı terapisi - devam seansı',
        recordingAvailable: true,
        sessionsCompleted: 6,
        clientAvatar: 'https://randomuser.me/api/portraits/men/12.jpg'
      },
      {
        id: 8,
        clientId: 108,
        clientName: 'Deniz Şahin',
        date: '2025-03-29',
        startTime: '12:00',
        endTime: '12:50',
        duration: 50,
        status: 'scheduled',
        type: 'video',
        notes: 'Aile danışmanlığı - ilk seans',
        recordingAvailable: false,
        sessionsCompleted: 0,
        clientAvatar: 'https://randomuser.me/api/portraits/women/33.jpg'
      }
    ];

    setTimeout(() => {
      setSessions(mockSessions);
      setIsLoading(false);
    }, 1000);
  }, []);

  // İstatistik hesaplamaları
  const stats = {
    total: sessions.length,
    upcoming: sessions.filter(s => s.status === 'scheduled').length,
    completed: sessions.filter(s => s.status === 'completed').length,
    missed: sessions.filter(s => s.status === 'missed').length,
    cancelled: sessions.filter(s => s.status === 'cancelled').length
  };

  // Bugünün tarihi
  const today = new Date();
  
  // Görüşmeleri filtrele
  const filteredSessions = sessions.filter(session => {
    const sessionDate = parseISO(session.date);

    // Tab filtresi
    if (activeTab === 'upcoming' && sessionDate >= today && session.status === 'scheduled') {
      // Gelecek görüşmeler
    } else if (activeTab === 'past' && (sessionDate < today || session.status === 'completed' || session.status === 'missed' || session.status === 'cancelled')) {
      // Geçmiş görüşmeler
    } else if (activeTab === 'all') {
      // Tüm görüşmeler
    } else if (activeTab !== 'all') {
      return false;
    }
    
    // Durum filtresi
    if (filterStatus !== 'all' && session.status !== filterStatus) {
      return false;
    }

    // Arama filtresi
    if (searchTerm && !session.clientName.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }

    return true;
  });

  // Tarihe göre sırala
  const sortedSessions = [...filteredSessions].sort((a, b) => {
    // Önce tarihleri karşılaştır
    const dateComparison = new Date(b.date) - new Date(a.date);
    if (dateComparison !== 0) return dateComparison;
    
    // Tarihler aynıysa başlama saatini karşılaştır
    return a.startTime.localeCompare(b.startTime);
  });

  // Durum badge renkleri
  const getStatusBadge = (status) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'missed':
        return 'bg-amber-100 text-amber-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Yükleniyor durumu
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen pb-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-8">
        {/* Başlık ve Üst Kısım */}
        <div className="bg-gradient-to-r from-orange-500 to-orange-700 shadow-lg rounded-lg p-6 mb-6 text-white">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
            <div>
              <h1 className="text-2xl font-bold">Terapist Seanslarım</h1>
              <p className="mt-1 text-purple-100">
                Gerçekleşecek ve gerçekleşmiş tüm terapist seanslarınızı yönetin
              </p>
            </div>
            <div className="mt-3 sm:mt-0 flex space-x-2">
              <Link
                to="/expert/appointments"
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-purple-800 bg-white hover:bg-purple-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-300"
              >
                <CalendarIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
                Randevularım
              </Link>
              <button className="inline-flex items-center px-3 py-2 border border-white text-sm font-medium rounded-md shadow-sm text-white bg-purple-700 bg-opacity-50 hover:bg-opacity-75 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white">
                <ArrowDownTrayIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
                Rapor İndir
              </button>
              <button className="relative p-1 rounded-full bg-purple-700 bg-opacity-50 text-purple-100 hover:text-white focus:outline-none">
                <BellIcon className="h-6 w-6" />
                <span className="absolute top-0 right-0 block h-2.5 w-2.5 rounded-full bg-red-400 ring-2 ring-purple-700"></span>
              </button>
            </div>
          </div>
        </div>

        {/* Özet İstatistikler */}
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-4 mb-6">
          <div 
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-purple-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'all' && filterStatus === 'all' ? 'ring-2 ring-purple-500' : ''}`}
            onClick={() => {
              setActiveTab('all');
              setFilterStatus('all');
            }}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Toplam</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.total}</span>
          </div>
          
          <div 
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-blue-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'upcoming' ? 'ring-2 ring-blue-500' : ''}`}
            onClick={() => {
              setActiveTab('upcoming');
              setFilterStatus('scheduled');
            }}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Planlanan</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.upcoming}</span>
          </div>
          
          <div 
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-green-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'all' && filterStatus === 'completed' ? 'ring-2 ring-green-500' : ''}`}
            onClick={() => {
              setActiveTab('all');
              setFilterStatus('completed');
            }}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Tamamlanan</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.completed}</span>
          </div>
          
          <div 
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-amber-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'all' && filterStatus === 'missed' ? 'ring-2 ring-amber-500' : ''}`}
            onClick={() => {
              setActiveTab('all');
              setFilterStatus('missed');
            }}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Kaçırılan</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.missed}</span>
          </div>
          
          <div 
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-red-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'all' && filterStatus === 'cancelled' ? 'ring-2 ring-red-500' : ''}`}
            onClick={() => {
              setActiveTab('all');
              setFilterStatus('cancelled');
            }}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">İptal Edilen</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.cancelled}</span>
          </div>
        </div>

        {/* Ana Sekme Navigasyonu */}
        <div className="flex border-b border-gray-200 mb-6">
          <button
            onClick={() => {
              setActiveTab('upcoming');
              setFilterStatus('scheduled');
            }}
            className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
              activeTab === 'upcoming'
                ? 'border-purple-500 text-purple-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center">
              <CalendarIcon className="h-5 w-5 mr-2" />
              <span>Yaklaşan Seanslar</span>
            </div>
          </button>
          <button
            onClick={() => setActiveTab('past')}
            className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
              activeTab === 'past'
                ? 'border-purple-500 text-purple-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center">
              <CheckCircleIcon className="h-5 w-5 mr-2" />
              <span>Geçmiş Seanslar</span>
            </div>
          </button>
          <button
            onClick={() => {
              setActiveTab('all');
              setFilterStatus('all');
            }}
            className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
              activeTab === 'all'
                ? 'border-purple-500 text-purple-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center">
              <DocumentTextIcon className="h-5 w-5 mr-2" />
              <span>Tüm Seanslar</span>
            </div>
          </button>
        </div>

        {/* Arama */}
        <div className="bg-white shadow rounded-lg mb-6">
          <div className="px-4 py-5 sm:p-6">
            <div className="max-w-lg">
              <div className="relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                  </svg>
                </div>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                  placeholder="Danışan adına göre ara..."
                />
              </div>
            </div>
          </div>
        </div>

        {/* Görüşmeler Listesi */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">
              {activeTab === 'upcoming' ? 'Yaklaşan Seanslar' :
               activeTab === 'past' ? 'Geçmiş Seanslar' : 'Tüm Seanslar'}
              {filterStatus !== 'all' && ` - ${sessionStatuses[filterStatus]}`}
            </h2>
          </div>

          {sortedSessions.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {sortedSessions.map((session) => (
                <div key={session.id} className="p-6 hover:bg-gray-50 transition duration-150">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <img
                          className="h-10 w-10 rounded-full border border-gray-200"
                          src={session.clientAvatar}
                          alt={session.clientName}
                        />
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-900">{session.clientName}</h3>
                        <div className="flex space-x-2 text-xs text-gray-500">
                          <span>{format(parseISO(session.date), 'EEEE', { locale: tr })}</span>
                          <span>•</span>
                          <span>{format(parseISO(session.date), 'd MMMM yyyy', { locale: tr })}</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadge(session.status)}`}>
                        {sessionStatuses[session.status]}
                      </span>
                    </div>
                  </div>

                  <div className="mt-4 flex items-center justify-between">
                    <div className="flex space-x-6 text-sm text-gray-500">
                      <div className="flex items-center">
                        <ClockIcon className="h-4 w-4 text-gray-400 mr-1.5" />
                        <span>{session.startTime} - {session.endTime}</span>
                      </div>
                      <div className="flex items-center">
                        <UserIcon className="h-4 w-4 text-gray-400 mr-1.5" />
                        <span>Seans #{session.sessionsCompleted + 1}</span>
                      </div>
                    </div>

                    <div className="flex space-x-2">
                      {session.status === 'scheduled' && (
                        <button
                          type="button"
                          className="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                        >
                          <PlayCircleIcon className="-ml-0.5 mr-1 h-4 w-4" />
                          Seansı Başlat
                        </button>
                      )}
                      
                      {session.recordingAvailable && (
                        <button
                          type="button"
                          className="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-purple-700 bg-purple-100 hover:bg-purple-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                        >
                          <DocumentArrowDownIcon className="-ml-0.5 mr-1 h-4 w-4" />
                          Kaydı İndir
                        </button>
                      )}
                      
                      <button
                        type="button"
                        className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                      >
                        <DocumentTextIcon className="-ml-0.5 mr-1 h-4 w-4" />
                        Seans Notları
                      </button>
                      
                      <button
                        type="button"
                        className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                      >
                        <ChatBubbleLeftRightIcon className="-ml-0.5 mr-1 h-4 w-4" />
                        Danışana Mesaj
                      </button>
                    </div>
                  </div>

                  {session.notes && (
                    <div className="mt-2 text-xs text-gray-500 bg-gray-50 px-3 py-1.5 rounded-md">
                      <span className="font-medium">Not:</span> {session.notes}
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="py-12 text-center">
              <CalendarIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Seans Bulunamadı</h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm || filterStatus !== 'all'
                  ? 'Arama kriterlerinize uygun seans bulunamadı.'
                  : 'Henüz bir seansınız bulunmuyor.'}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SessionsPage; 