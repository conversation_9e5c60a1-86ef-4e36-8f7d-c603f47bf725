/**
 * Express Application
 * Configures middleware and routes
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const { notFound, errorHandler } = require('./middleware/error');
const fs = require('fs');
const path = require('path');

// Create Express app
const app = express();

// Middleware
app.use(helmet()); // Security headers
app.use(cors()); // CORS
app.use(express.json()); // Parse JSON body
app.use(express.urlencoded({ extended: true })); // Parse URL-encoded body

// Serve static files (uploaded images) with CORS headers
app.use('/uploads', (req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  next();
}, express.static(path.join(__dirname, 'uploads')));

// API Routes - Load modules safely
let authModule, usersModule, rolesModule, permissionsModule, auditLogsModule, expertsModule, clientsModule, messagesModule;

try {
  console.log('Loading API modules...');

  // Load modules
  console.log('Loading auth module...');
  authModule = require('./modules/auth');

  console.log('Loading users module...');
  usersModule = require('./modules/users');

  console.log('Loading roles module...');
  rolesModule = require('./modules/roles');

  console.log('Loading permissions module...');
  permissionsModule = require('./modules/permissions');

  console.log('Loading audit logs module...');
  auditLogsModule = require('./modules/audit-logs');

  console.log('Loading experts module...');
  expertsModule = require('./modules/experts');

  console.log('Loading clients module...');
  clientsModule = require('./modules/clients');

  console.log('Loading messages module...');
  messagesModule = require('./modules/messages');

} catch (error) {
  console.error('Error loading modules:', error);
}

// Basic route for health check
app.get('/', (req, res) => {
  res.json({ message: 'Burky Root Web API is running' });
});

app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date() });
});

// Public routes
console.log('Registering auth routes...');
app.use('/api/auth', authModule.routes);

// Protected routes
const authMiddleware = require('./middleware/auth');

console.log('Registering users routes...');
app.use('/api/users', authMiddleware, usersModule.routes);

console.log('Registering roles routes...');
app.use('/api/roles', authMiddleware, rolesModule.routes);

console.log('Registering permissions routes...');
app.use('/api/permissions', authMiddleware, permissionsModule.routes);

console.log('Registering audit logs routes...');
app.use('/api/audit-logs', authMiddleware, auditLogsModule.routes);

console.log('Registering experts routes...');
app.use('/api/experts', authMiddleware, expertsModule.routes);

console.log('Registering clients routes...');
app.use('/api/clients', authMiddleware, clientsModule.routes);

console.log('Registering messages routes...');
app.use('/api/messages', authMiddleware, messagesModule.routes);

// Error handling
app.use(notFound);
app.use(errorHandler);

module.exports = app;
