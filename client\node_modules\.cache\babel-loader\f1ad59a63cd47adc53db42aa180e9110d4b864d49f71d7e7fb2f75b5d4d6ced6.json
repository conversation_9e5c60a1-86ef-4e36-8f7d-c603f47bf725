{"ast": null, "code": "\"use client\";\n\n// src/index.ts\nimport * as Devtools from \"./ReactQueryDevtools.js\";\nimport * as DevtoolsPanel from \"./ReactQueryDevtoolsPanel.js\";\nvar ReactQueryDevtools2 = process.env.NODE_ENV !== \"development\" ? function () {\n  return null;\n} : Devtools.ReactQueryDevtools;\nvar ReactQueryDevtoolsPanel2 = process.env.NODE_ENV !== \"development\" ? function () {\n  return null;\n} : DevtoolsPanel.ReactQueryDevtoolsPanel;\nexport { ReactQueryDevtools2 as ReactQueryDevtools, ReactQueryDevtoolsPanel2 as ReactQueryDevtoolsPanel };", "map": {"version": 3, "names": ["Devtools", "DevtoolsPanel", "ReactQueryDevtools2", "process", "env", "NODE_ENV", "ReactQueryDevtools", "ReactQueryDevtoolsPanel2", "ReactQueryDevtoolsPanel"], "sources": ["C:\\Projeler\\kidgarden\\burky_root_web\\client\\node_modules\\@tanstack\\react-query-devtools\\src\\index.ts"], "sourcesContent": ["'use client'\n\nimport * as Devtools from './ReactQueryDevtools'\nimport * as DevtoolsPanel from './ReactQueryDevtoolsPanel'\n\nexport const ReactQueryDevtools: (typeof Devtools)['ReactQueryDevtools'] =\n  process.env.NODE_ENV !== 'development'\n    ? function () {\n        return null\n      }\n    : Devtools.ReactQueryDevtools\n\nexport const ReactQueryDevtoolsPanel: (typeof DevtoolsPanel)['ReactQueryDevtoolsPanel'] =\n  process.env.NODE_ENV !== 'development'\n    ? function () {\n        return null\n      }\n    : DevtoolsPanel.ReactQueryDevtoolsPanel\n"], "mappings": ";;;AAEA,YAAYA,QAAA,MAAc;AAC1B,YAAYC,aAAA,MAAmB;AAExB,IAAMC,mBAAA,GACXC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBACrB,YAAY;EACV,OAAO;AACT,IACSL,QAAA,CAAAM,kBAAA;AAER,IAAMC,wBAAA,GACXJ,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBACrB,YAAY;EACV,OAAO;AACT,IACcJ,aAAA,CAAAO,uBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}