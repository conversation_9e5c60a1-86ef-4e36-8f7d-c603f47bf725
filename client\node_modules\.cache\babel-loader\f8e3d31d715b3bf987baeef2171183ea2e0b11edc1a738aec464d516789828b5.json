{"ast": null, "code": "// src/query.ts\nimport { ensureQueryFn, noop, replaceData, resolveEnabled, skipToken, timeUntilStale } from \"./utils.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { canFetch, createRetryer, isCancelledError } from \"./retryer.js\";\nimport { Removable } from \"./removable.js\";\nvar Query = class extends Removable {\n  #initialState;\n  #revertState;\n  #cache;\n  #client;\n  #retryer;\n  #defaultOptions;\n  #abortSignalConsumed;\n  constructor(config) {\n    super();\n    this.#abortSignalConsumed = false;\n    this.#defaultOptions = config.defaultOptions;\n    this.setOptions(config.options);\n    this.observers = [];\n    this.#client = config.client;\n    this.#cache = this.#client.getQueryCache();\n    this.queryKey = config.queryKey;\n    this.queryHash = config.queryHash;\n    this.#initialState = getDefaultState(this.options);\n    this.state = config.state ?? this.#initialState;\n    this.scheduleGc();\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  get promise() {\n    return this.#retryer?.promise;\n  }\n  setOptions(options) {\n    this.options = {\n      ...this.#defaultOptions,\n      ...options\n    };\n    this.updateGcTime(this.options.gcTime);\n  }\n  optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === \"idle\") {\n      this.#cache.remove(this);\n    }\n  }\n  setData(newData, options) {\n    const data = replaceData(this.state.data, newData, this.options);\n    this.#dispatch({\n      data,\n      type: \"success\",\n      dataUpdatedAt: options?.updatedAt,\n      manual: options?.manual\n    });\n    return data;\n  }\n  setState(state, setStateOptions) {\n    this.#dispatch({\n      type: \"setState\",\n      state,\n      setStateOptions\n    });\n  }\n  cancel(options) {\n    const promise = this.#retryer?.promise;\n    this.#retryer?.cancel(options);\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve();\n  }\n  destroy() {\n    super.destroy();\n    this.cancel({\n      silent: true\n    });\n  }\n  reset() {\n    this.destroy();\n    this.setState(this.#initialState);\n  }\n  isActive() {\n    return this.observers.some(observer => resolveEnabled(observer.options.enabled, this) !== false);\n  }\n  isDisabled() {\n    if (this.getObserversCount() > 0) {\n      return !this.isActive();\n    }\n    return this.options.queryFn === skipToken || this.state.dataUpdateCount + this.state.errorUpdateCount === 0;\n  }\n  isStale() {\n    if (this.state.isInvalidated) {\n      return true;\n    }\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(observer => observer.getCurrentResult().isStale);\n    }\n    return this.state.data === void 0;\n  }\n  isStaleByTime(staleTime = 0) {\n    return this.state.isInvalidated || this.state.data === void 0 || !timeUntilStale(this.state.dataUpdatedAt, staleTime);\n  }\n  onFocus() {\n    const observer = this.observers.find(x => x.shouldFetchOnWindowFocus());\n    observer?.refetch({\n      cancelRefetch: false\n    });\n    this.#retryer?.continue();\n  }\n  onOnline() {\n    const observer = this.observers.find(x => x.shouldFetchOnReconnect());\n    observer?.refetch({\n      cancelRefetch: false\n    });\n    this.#retryer?.continue();\n  }\n  addObserver(observer) {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer);\n      this.clearGcTimeout();\n      this.#cache.notify({\n        type: \"observerAdded\",\n        query: this,\n        observer\n      });\n    }\n  }\n  removeObserver(observer) {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter(x => x !== observer);\n      if (!this.observers.length) {\n        if (this.#retryer) {\n          if (this.#abortSignalConsumed) {\n            this.#retryer.cancel({\n              revert: true\n            });\n          } else {\n            this.#retryer.cancelRetry();\n          }\n        }\n        this.scheduleGc();\n      }\n      this.#cache.notify({\n        type: \"observerRemoved\",\n        query: this,\n        observer\n      });\n    }\n  }\n  getObserversCount() {\n    return this.observers.length;\n  }\n  invalidate() {\n    if (!this.state.isInvalidated) {\n      this.#dispatch({\n        type: \"invalidate\"\n      });\n    }\n  }\n  fetch(options, fetchOptions) {\n    if (this.state.fetchStatus !== \"idle\") {\n      if (this.state.data !== void 0 && fetchOptions?.cancelRefetch) {\n        this.cancel({\n          silent: true\n        });\n      } else if (this.#retryer) {\n        this.#retryer.continueRetry();\n        return this.#retryer.promise;\n      }\n    }\n    if (options) {\n      this.setOptions(options);\n    }\n    if (!this.options.queryFn) {\n      const observer = this.observers.find(x => x.options.queryFn);\n      if (observer) {\n        this.setOptions(observer.options);\n      }\n    }\n    if (process.env.NODE_ENV !== \"production\") {\n      if (!Array.isArray(this.options.queryKey)) {\n        console.error(`As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`);\n      }\n    }\n    const abortController = new AbortController();\n    const addSignalProperty = object => {\n      Object.defineProperty(object, \"signal\", {\n        enumerable: true,\n        get: () => {\n          this.#abortSignalConsumed = true;\n          return abortController.signal;\n        }\n      });\n    };\n    const fetchFn = () => {\n      const queryFn = ensureQueryFn(this.options, fetchOptions);\n      const queryFnContext = {\n        client: this.#client,\n        queryKey: this.queryKey,\n        meta: this.meta\n      };\n      addSignalProperty(queryFnContext);\n      this.#abortSignalConsumed = false;\n      if (this.options.persister) {\n        return this.options.persister(queryFn, queryFnContext, this);\n      }\n      return queryFn(queryFnContext);\n    };\n    const context = {\n      fetchOptions,\n      options: this.options,\n      queryKey: this.queryKey,\n      client: this.#client,\n      state: this.state,\n      fetchFn\n    };\n    addSignalProperty(context);\n    this.options.behavior?.onFetch(context, this);\n    this.#revertState = this.state;\n    if (this.state.fetchStatus === \"idle\" || this.state.fetchMeta !== context.fetchOptions?.meta) {\n      this.#dispatch({\n        type: \"fetch\",\n        meta: context.fetchOptions?.meta\n      });\n    }\n    const onError = error => {\n      if (!(isCancelledError(error) && error.silent)) {\n        this.#dispatch({\n          type: \"error\",\n          error\n        });\n      }\n      if (!isCancelledError(error)) {\n        this.#cache.config.onError?.(error, this);\n        this.#cache.config.onSettled?.(this.state.data, error, this);\n      }\n      this.scheduleGc();\n    };\n    this.#retryer = createRetryer({\n      initialPromise: fetchOptions?.initialPromise,\n      fn: context.fetchFn,\n      abort: abortController.abort.bind(abortController),\n      onSuccess: data => {\n        if (data === void 0) {\n          if (process.env.NODE_ENV !== \"production\") {\n            console.error(`Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`);\n          }\n          onError(new Error(`${this.queryHash} data is undefined`));\n          return;\n        }\n        try {\n          this.setData(data);\n        } catch (error) {\n          onError(error);\n          return;\n        }\n        this.#cache.config.onSuccess?.(data, this);\n        this.#cache.config.onSettled?.(data, this.state.error, this);\n        this.scheduleGc();\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.#dispatch({\n          type: \"failed\",\n          failureCount,\n          error\n        });\n      },\n      onPause: () => {\n        this.#dispatch({\n          type: \"pause\"\n        });\n      },\n      onContinue: () => {\n        this.#dispatch({\n          type: \"continue\"\n        });\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode,\n      canRun: () => true\n    });\n    return this.#retryer.start();\n  }\n  #dispatch(action) {\n    const reducer = state => {\n      switch (action.type) {\n        case \"failed\":\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error\n          };\n        case \"pause\":\n          return {\n            ...state,\n            fetchStatus: \"paused\"\n          };\n        case \"continue\":\n          return {\n            ...state,\n            fetchStatus: \"fetching\"\n          };\n        case \"fetch\":\n          return {\n            ...state,\n            ...fetchState(state.data, this.options),\n            fetchMeta: action.meta ?? null\n          };\n        case \"success\":\n          return {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: \"success\",\n            ...(!action.manual && {\n              fetchStatus: \"idle\",\n              fetchFailureCount: 0,\n              fetchFailureReason: null\n            })\n          };\n        case \"error\":\n          const error = action.error;\n          if (isCancelledError(error) && error.revert && this.#revertState) {\n            return {\n              ...this.#revertState,\n              fetchStatus: \"idle\"\n            };\n          }\n          return {\n            ...state,\n            error,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error,\n            fetchStatus: \"idle\",\n            status: \"error\"\n          };\n        case \"invalidate\":\n          return {\n            ...state,\n            isInvalidated: true\n          };\n        case \"setState\":\n          return {\n            ...state,\n            ...action.state\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    notifyManager.batch(() => {\n      this.observers.forEach(observer => {\n        observer.onQueryUpdate();\n      });\n      this.#cache.notify({\n        query: this,\n        type: \"updated\",\n        action\n      });\n    });\n  }\n};\nfunction fetchState(data, options) {\n  return {\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchStatus: canFetch(options.networkMode) ? \"fetching\" : \"paused\",\n    ...(data === void 0 && {\n      error: null,\n      status: \"pending\"\n    })\n  };\n}\nfunction getDefaultState(options) {\n  const data = typeof options.initialData === \"function\" ? options.initialData() : options.initialData;\n  const hasData = data !== void 0;\n  const initialDataUpdatedAt = hasData ? typeof options.initialDataUpdatedAt === \"function\" ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? initialDataUpdatedAt ?? Date.now() : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? \"success\" : \"pending\",\n    fetchStatus: \"idle\"\n  };\n}\nexport { Query, fetchState };", "map": {"version": 3, "names": ["ensureQueryFn", "noop", "replaceData", "resolveEnabled", "skipToken", "timeUntilStale", "notify<PERSON><PERSON>ger", "canFetch", "createRetryer", "isCancelledError", "Removable", "Query", "initialState", "revertState", "cache", "client", "retryer", "defaultOptions", "abortSignalConsumed", "constructor", "config", "setOptions", "options", "observers", "get<PERSON><PERSON><PERSON><PERSON>ache", "query<PERSON><PERSON>", "queryHash", "getDefaultState", "state", "scheduleGc", "meta", "promise", "updateGcTime", "gcTime", "optionalRemove", "length", "fetchStatus", "remove", "setData", "newData", "data", "dispatch", "type", "dataUpdatedAt", "updatedAt", "manual", "setState", "setStateOptions", "cancel", "then", "catch", "Promise", "resolve", "destroy", "silent", "reset", "isActive", "some", "observer", "enabled", "isDisabled", "getObserversCount", "queryFn", "dataUpdateCount", "errorUpdateCount", "isStale", "isInvalidated", "getCurrentResult", "isStaleByTime", "staleTime", "onFocus", "find", "x", "shouldFetchOnWindowFocus", "refetch", "cancelRefetch", "continue", "onOnline", "shouldFetchOnReconnect", "addObserver", "includes", "push", "clearGcTimeout", "notify", "query", "removeObserver", "filter", "revert", "cancelRetry", "invalidate", "fetch", "fetchOptions", "continueRetry", "process", "env", "NODE_ENV", "Array", "isArray", "console", "error", "abortController", "AbortController", "addSignalProperty", "object", "Object", "defineProperty", "enumerable", "get", "signal", "fetchFn", "queryFnContext", "persister", "context", "behavior", "onFetch", "fetchMeta", "onError", "onSettled", "initialPromise", "fn", "abort", "bind", "onSuccess", "Error", "onFail", "failureCount", "onPause", "onContinue", "retry", "retry<PERSON><PERSON><PERSON>", "networkMode", "canRun", "start", "#dispatch", "action", "reducer", "fetchFailureCount", "fetchFailureReason", "fetchState", "Date", "now", "status", "errorUpdatedAt", "batch", "for<PERSON>ach", "onQueryUpdate", "initialData", "hasData", "initialDataUpdatedAt"], "sources": ["C:\\burky root\\burky_root_web\\client\\node_modules\\@tanstack\\query-core\\src\\query.ts"], "sourcesContent": ["import {\n  ensureQueryFn,\n  noop,\n  replaceData,\n  resolveEnabled,\n  skipToken,\n  timeUntilStale,\n} from './utils'\nimport { notifyManager } from './notifyManager'\nimport { canFetch, createRetryer, isCancelledError } from './retryer'\nimport { Removable } from './removable'\nimport type { QueryCache } from './queryCache'\nimport type { QueryClient } from './queryClient'\nimport type {\n  CancelOptions,\n  DefaultError,\n  FetchStatus,\n  InitialDataFunction,\n  OmitKeyof,\n  QueryFunction,\n  QueryFunctionContext,\n  QueryKey,\n  QueryMeta,\n  QueryOptions,\n  QueryStatus,\n  SetDataOptions,\n} from './types'\nimport type { QueryObserver } from './queryObserver'\nimport type { Retryer } from './retryer'\n\n// TYPES\n\ninterface QueryConfig<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  client: QueryClient\n  queryKey: TQuery<PERSON><PERSON>\n  queryHash: string\n  options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  state?: QueryState<TData, TError>\n}\n\nexport interface QueryState<TData = unknown, TError = DefaultError> {\n  data: TData | undefined\n  dataUpdateCount: number\n  dataUpdatedAt: number\n  error: TError | null\n  errorUpdateCount: number\n  errorUpdatedAt: number\n  fetchFailureCount: number\n  fetchFailureReason: TError | null\n  fetchMeta: FetchMeta | null\n  isInvalidated: boolean\n  status: QueryStatus\n  fetchStatus: FetchStatus\n}\n\nexport interface FetchContext<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  fetchFn: () => unknown | Promise<unknown>\n  fetchOptions?: FetchOptions\n  signal: AbortSignal\n  options: QueryOptions<TQueryFnData, TError, TData, any>\n  client: QueryClient\n  queryKey: TQueryKey\n  state: QueryState<TData, TError>\n}\n\nexport interface QueryBehavior<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  onFetch: (\n    context: FetchContext<TQueryFnData, TError, TData, TQueryKey>,\n    query: Query,\n  ) => void\n}\n\nexport type FetchDirection = 'forward' | 'backward'\n\nexport interface FetchMeta {\n  fetchMore?: { direction: FetchDirection }\n}\n\nexport interface FetchOptions<TData = unknown> {\n  cancelRefetch?: boolean\n  meta?: FetchMeta\n  initialPromise?: Promise<TData>\n}\n\ninterface FailedAction<TError> {\n  type: 'failed'\n  failureCount: number\n  error: TError\n}\n\ninterface FetchAction {\n  type: 'fetch'\n  meta?: FetchMeta\n}\n\ninterface SuccessAction<TData> {\n  data: TData | undefined\n  type: 'success'\n  dataUpdatedAt?: number\n  manual?: boolean\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface InvalidateAction {\n  type: 'invalidate'\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\ninterface SetStateAction<TData, TError> {\n  type: 'setState'\n  state: Partial<QueryState<TData, TError>>\n  setStateOptions?: SetStateOptions\n}\n\nexport type Action<TData, TError> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction<TError>\n  | FetchAction\n  | InvalidateAction\n  | PauseAction\n  | SetStateAction<TData, TError>\n  | SuccessAction<TData>\n\nexport interface SetStateOptions {\n  meta?: any\n}\n\n// CLASS\n\nexport class Query<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends Removable {\n  queryKey: TQueryKey\n  queryHash: string\n  options!: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  state: QueryState<TData, TError>\n\n  #initialState: QueryState<TData, TError>\n  #revertState?: QueryState<TData, TError>\n  #cache: QueryCache\n  #client: QueryClient\n  #retryer?: Retryer<TData>\n  observers: Array<QueryObserver<any, any, any, any, any>>\n  #defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  #abortSignalConsumed: boolean\n\n  constructor(config: QueryConfig<TQueryFnData, TError, TData, TQueryKey>) {\n    super()\n\n    this.#abortSignalConsumed = false\n    this.#defaultOptions = config.defaultOptions\n    this.setOptions(config.options)\n    this.observers = []\n    this.#client = config.client\n    this.#cache = this.#client.getQueryCache()\n    this.queryKey = config.queryKey\n    this.queryHash = config.queryHash\n    this.#initialState = getDefaultState(this.options)\n    this.state = config.state ?? this.#initialState\n    this.scheduleGc()\n  }\n  get meta(): QueryMeta | undefined {\n    return this.options.meta\n  }\n\n  get promise(): Promise<TData> | undefined {\n    return this.#retryer?.promise\n  }\n\n  setOptions(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): void {\n    this.options = { ...this.#defaultOptions, ...options }\n\n    this.updateGcTime(this.options.gcTime)\n  }\n\n  protected optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === 'idle') {\n      this.#cache.remove(this)\n    }\n  }\n\n  setData(\n    newData: TData,\n    options?: SetDataOptions & { manual: boolean },\n  ): TData {\n    const data = replaceData(this.state.data, newData, this.options)\n\n    // Set data and mark it as cached\n    this.#dispatch({\n      data,\n      type: 'success',\n      dataUpdatedAt: options?.updatedAt,\n      manual: options?.manual,\n    })\n\n    return data\n  }\n\n  setState(\n    state: Partial<QueryState<TData, TError>>,\n    setStateOptions?: SetStateOptions,\n  ): void {\n    this.#dispatch({ type: 'setState', state, setStateOptions })\n  }\n\n  cancel(options?: CancelOptions): Promise<void> {\n    const promise = this.#retryer?.promise\n    this.#retryer?.cancel(options)\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve()\n  }\n\n  destroy(): void {\n    super.destroy()\n\n    this.cancel({ silent: true })\n  }\n\n  reset(): void {\n    this.destroy()\n    this.setState(this.#initialState)\n  }\n\n  isActive(): boolean {\n    return this.observers.some(\n      (observer) => resolveEnabled(observer.options.enabled, this) !== false,\n    )\n  }\n\n  isDisabled(): boolean {\n    if (this.getObserversCount() > 0) {\n      return !this.isActive()\n    }\n    // if a query has no observers, it should still be considered disabled if it never attempted a fetch\n    return (\n      this.options.queryFn === skipToken ||\n      this.state.dataUpdateCount + this.state.errorUpdateCount === 0\n    )\n  }\n\n  isStale(): boolean {\n    if (this.state.isInvalidated) {\n      return true\n    }\n\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(\n        (observer) => observer.getCurrentResult().isStale,\n      )\n    }\n\n    return this.state.data === undefined\n  }\n\n  isStaleByTime(staleTime = 0): boolean {\n    return (\n      this.state.isInvalidated ||\n      this.state.data === undefined ||\n      !timeUntilStale(this.state.dataUpdatedAt, staleTime)\n    )\n  }\n\n  onFocus(): void {\n    const observer = this.observers.find((x) => x.shouldFetchOnWindowFocus())\n\n    observer?.refetch({ cancelRefetch: false })\n\n    // Continue fetch if currently paused\n    this.#retryer?.continue()\n  }\n\n  onOnline(): void {\n    const observer = this.observers.find((x) => x.shouldFetchOnReconnect())\n\n    observer?.refetch({ cancelRefetch: false })\n\n    // Continue fetch if currently paused\n    this.#retryer?.continue()\n  }\n\n  addObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer)\n\n      // Stop the query from being garbage collected\n      this.clearGcTimeout()\n\n      this.#cache.notify({ type: 'observerAdded', query: this, observer })\n    }\n  }\n\n  removeObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter((x) => x !== observer)\n\n      if (!this.observers.length) {\n        // If the transport layer does not support cancellation\n        // we'll let the query continue so the result can be cached\n        if (this.#retryer) {\n          if (this.#abortSignalConsumed) {\n            this.#retryer.cancel({ revert: true })\n          } else {\n            this.#retryer.cancelRetry()\n          }\n        }\n\n        this.scheduleGc()\n      }\n\n      this.#cache.notify({ type: 'observerRemoved', query: this, observer })\n    }\n  }\n\n  getObserversCount(): number {\n    return this.observers.length\n  }\n\n  invalidate(): void {\n    if (!this.state.isInvalidated) {\n      this.#dispatch({ type: 'invalidate' })\n    }\n  }\n\n  fetch(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    fetchOptions?: FetchOptions<TQueryFnData>,\n  ): Promise<TData> {\n    if (this.state.fetchStatus !== 'idle') {\n      if (this.state.data !== undefined && fetchOptions?.cancelRefetch) {\n        // Silently cancel current fetch if the user wants to cancel refetch\n        this.cancel({ silent: true })\n      } else if (this.#retryer) {\n        // make sure that retries that were potentially cancelled due to unmounts can continue\n        this.#retryer.continueRetry()\n        // Return current promise if we are already fetching\n        return this.#retryer.promise\n      }\n    }\n\n    // Update config if passed, otherwise the config from the last execution is used\n    if (options) {\n      this.setOptions(options)\n    }\n\n    // Use the options from the first observer with a query function if no function is found.\n    // This can happen when the query is hydrated or created with setQueryData.\n    if (!this.options.queryFn) {\n      const observer = this.observers.find((x) => x.options.queryFn)\n      if (observer) {\n        this.setOptions(observer.options)\n      }\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (!Array.isArray(this.options.queryKey)) {\n        console.error(\n          `As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`,\n        )\n      }\n    }\n\n    const abortController = new AbortController()\n\n    // Adds an enumerable signal property to the object that\n    // which sets abortSignalConsumed to true when the signal\n    // is read.\n    const addSignalProperty = (object: unknown) => {\n      Object.defineProperty(object, 'signal', {\n        enumerable: true,\n        get: () => {\n          this.#abortSignalConsumed = true\n          return abortController.signal\n        },\n      })\n    }\n\n    // Create fetch function\n    const fetchFn = () => {\n      const queryFn = ensureQueryFn(this.options, fetchOptions)\n\n      // Create query function context\n      const queryFnContext: OmitKeyof<\n        QueryFunctionContext<TQueryKey>,\n        'signal'\n      > = {\n        client: this.#client,\n        queryKey: this.queryKey,\n        meta: this.meta,\n      }\n\n      addSignalProperty(queryFnContext)\n\n      this.#abortSignalConsumed = false\n      if (this.options.persister) {\n        return this.options.persister(\n          queryFn as QueryFunction<any>,\n          queryFnContext as QueryFunctionContext<TQueryKey>,\n          this as unknown as Query,\n        )\n      }\n\n      return queryFn(queryFnContext as QueryFunctionContext<TQueryKey>)\n    }\n\n    // Trigger behavior hook\n    const context: OmitKeyof<\n      FetchContext<TQueryFnData, TError, TData, TQueryKey>,\n      'signal'\n    > = {\n      fetchOptions,\n      options: this.options,\n      queryKey: this.queryKey,\n      client: this.#client,\n      state: this.state,\n      fetchFn,\n    }\n\n    addSignalProperty(context)\n\n    this.options.behavior?.onFetch(\n      context as FetchContext<TQueryFnData, TError, TData, TQueryKey>,\n      this as unknown as Query,\n    )\n\n    // Store state in case the current fetch needs to be reverted\n    this.#revertState = this.state\n\n    // Set to fetching state if not already in it\n    if (\n      this.state.fetchStatus === 'idle' ||\n      this.state.fetchMeta !== context.fetchOptions?.meta\n    ) {\n      this.#dispatch({ type: 'fetch', meta: context.fetchOptions?.meta })\n    }\n\n    const onError = (error: TError | { silent?: boolean }) => {\n      // Optimistically update state if needed\n      if (!(isCancelledError(error) && error.silent)) {\n        this.#dispatch({\n          type: 'error',\n          error: error as TError,\n        })\n      }\n\n      if (!isCancelledError(error)) {\n        // Notify cache callback\n        this.#cache.config.onError?.(\n          error as any,\n          this as Query<any, any, any, any>,\n        )\n        this.#cache.config.onSettled?.(\n          this.state.data,\n          error as any,\n          this as Query<any, any, any, any>,\n        )\n      }\n\n      // Schedule query gc after fetching\n      this.scheduleGc()\n    }\n\n    // Try to fetch the data\n    this.#retryer = createRetryer({\n      initialPromise: fetchOptions?.initialPromise as\n        | Promise<TData>\n        | undefined,\n      fn: context.fetchFn as () => Promise<TData>,\n      abort: abortController.abort.bind(abortController),\n      onSuccess: (data) => {\n        if (data === undefined) {\n          if (process.env.NODE_ENV !== 'production') {\n            console.error(\n              `Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`,\n            )\n          }\n          onError(new Error(`${this.queryHash} data is undefined`) as any)\n          return\n        }\n\n        try {\n          this.setData(data)\n        } catch (error) {\n          onError(error as TError)\n          return\n        }\n\n        // Notify cache callback\n        this.#cache.config.onSuccess?.(data, this as Query<any, any, any, any>)\n        this.#cache.config.onSettled?.(\n          data,\n          this.state.error as any,\n          this as Query<any, any, any, any>,\n        )\n\n        // Schedule query gc after fetching\n        this.scheduleGc()\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: 'failed', failureCount, error })\n      },\n      onPause: () => {\n        this.#dispatch({ type: 'pause' })\n      },\n      onContinue: () => {\n        this.#dispatch({ type: 'continue' })\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode,\n      canRun: () => true,\n    })\n\n    return this.#retryer.start()\n  }\n\n  #dispatch(action: Action<TData, TError>): void {\n    const reducer = (\n      state: QueryState<TData, TError>,\n    ): QueryState<TData, TError> => {\n      switch (action.type) {\n        case 'failed':\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error,\n          }\n        case 'pause':\n          return {\n            ...state,\n            fetchStatus: 'paused',\n          }\n        case 'continue':\n          return {\n            ...state,\n            fetchStatus: 'fetching',\n          }\n        case 'fetch':\n          return {\n            ...state,\n            ...fetchState(state.data, this.options),\n            fetchMeta: action.meta ?? null,\n          }\n        case 'success':\n          return {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: 'success',\n            ...(!action.manual && {\n              fetchStatus: 'idle',\n              fetchFailureCount: 0,\n              fetchFailureReason: null,\n            }),\n          }\n        case 'error':\n          const error = action.error\n\n          if (isCancelledError(error) && error.revert && this.#revertState) {\n            return { ...this.#revertState, fetchStatus: 'idle' }\n          }\n\n          return {\n            ...state,\n            error,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error,\n            fetchStatus: 'idle',\n            status: 'error',\n          }\n        case 'invalidate':\n          return {\n            ...state,\n            isInvalidated: true,\n          }\n        case 'setState':\n          return {\n            ...state,\n            ...action.state,\n          }\n      }\n    }\n\n    this.state = reducer(this.state)\n\n    notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onQueryUpdate()\n      })\n\n      this.#cache.notify({ query: this, type: 'updated', action })\n    })\n  }\n}\n\nexport function fetchState<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey,\n>(\n  data: TData | undefined,\n  options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n) {\n  return {\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchStatus: canFetch(options.networkMode) ? 'fetching' : 'paused',\n    ...(data === undefined &&\n      ({\n        error: null,\n        status: 'pending',\n      } as const)),\n  } as const\n}\n\nfunction getDefaultState<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey,\n>(\n  options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n): QueryState<TData, TError> {\n  const data =\n    typeof options.initialData === 'function'\n      ? (options.initialData as InitialDataFunction<TData>)()\n      : options.initialData\n\n  const hasData = data !== undefined\n\n  const initialDataUpdatedAt = hasData\n    ? typeof options.initialDataUpdatedAt === 'function'\n      ? (options.initialDataUpdatedAt as () => number | undefined)()\n      : options.initialDataUpdatedAt\n    : 0\n\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? (initialDataUpdatedAt ?? Date.now()) : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? 'success' : 'pending',\n    fetchStatus: 'idle',\n  }\n}\n"], "mappings": ";AAAA,SACEA,aAAA,EACAC,IAAA,EACAC,WAAA,EACAC,cAAA,EACAC,SAAA,EACAC,cAAA,QACK;AACP,SAASC,aAAA,QAAqB;AAC9B,SAASC,QAAA,EAAUC,aAAA,EAAeC,gBAAA,QAAwB;AAC1D,SAASC,SAAA,QAAiB;AAmJnB,IAAMC,KAAA,GAAN,cAKGD,SAAA,CAAU;EAMlB,CAAAE,YAAA;EACA,CAAAC,WAAA;EACA,CAAAC,KAAA;EACA,CAAAC,MAAA;EACA,CAAAC,OAAA;EAEA,CAAAC,cAAA;EACA,CAAAC,mBAAA;EAEAC,YAAYC,MAAA,EAA6D;IACvE,MAAM;IAEN,KAAK,CAAAF,mBAAA,GAAuB;IAC5B,KAAK,CAAAD,cAAA,GAAkBG,MAAA,CAAOH,cAAA;IAC9B,KAAKI,UAAA,CAAWD,MAAA,CAAOE,OAAO;IAC9B,KAAKC,SAAA,GAAY,EAAC;IAClB,KAAK,CAAAR,MAAA,GAAUK,MAAA,CAAOL,MAAA;IACtB,KAAK,CAAAD,KAAA,GAAS,KAAK,CAAAC,MAAA,CAAQS,aAAA,CAAc;IACzC,KAAKC,QAAA,GAAWL,MAAA,CAAOK,QAAA;IACvB,KAAKC,SAAA,GAAYN,MAAA,CAAOM,SAAA;IACxB,KAAK,CAAAd,YAAA,GAAgBe,eAAA,CAAgB,KAAKL,OAAO;IACjD,KAAKM,KAAA,GAAQR,MAAA,CAAOQ,KAAA,IAAS,KAAK,CAAAhB,YAAA;IAClC,KAAKiB,UAAA,CAAW;EAClB;EACA,IAAIC,KAAA,EAA8B;IAChC,OAAO,KAAKR,OAAA,CAAQQ,IAAA;EACtB;EAEA,IAAIC,QAAA,EAAsC;IACxC,OAAO,KAAK,CAAAf,OAAA,EAAUe,OAAA;EACxB;EAEAV,WACEC,OAAA,EACM;IACN,KAAKA,OAAA,GAAU;MAAE,GAAG,KAAK,CAAAL,cAAA;MAAiB,GAAGK;IAAQ;IAErD,KAAKU,YAAA,CAAa,KAAKV,OAAA,CAAQW,MAAM;EACvC;EAEUC,eAAA,EAAiB;IACzB,IAAI,CAAC,KAAKX,SAAA,CAAUY,MAAA,IAAU,KAAKP,KAAA,CAAMQ,WAAA,KAAgB,QAAQ;MAC/D,KAAK,CAAAtB,KAAA,CAAOuB,MAAA,CAAO,IAAI;IACzB;EACF;EAEAC,QACEC,OAAA,EACAjB,OAAA,EACO;IACP,MAAMkB,IAAA,GAAOtC,WAAA,CAAY,KAAK0B,KAAA,CAAMY,IAAA,EAAMD,OAAA,EAAS,KAAKjB,OAAO;IAG/D,KAAK,CAAAmB,QAAA,CAAU;MACbD,IAAA;MACAE,IAAA,EAAM;MACNC,aAAA,EAAerB,OAAA,EAASsB,SAAA;MACxBC,MAAA,EAAQvB,OAAA,EAASuB;IACnB,CAAC;IAED,OAAOL,IAAA;EACT;EAEAM,SACElB,KAAA,EACAmB,eAAA,EACM;IACN,KAAK,CAAAN,QAAA,CAAU;MAAEC,IAAA,EAAM;MAAYd,KAAA;MAAOmB;IAAgB,CAAC;EAC7D;EAEAC,OAAO1B,OAAA,EAAwC;IAC7C,MAAMS,OAAA,GAAU,KAAK,CAAAf,OAAA,EAAUe,OAAA;IAC/B,KAAK,CAAAf,OAAA,EAAUgC,MAAA,CAAO1B,OAAO;IAC7B,OAAOS,OAAA,GAAUA,OAAA,CAAQkB,IAAA,CAAKhD,IAAI,EAAEiD,KAAA,CAAMjD,IAAI,IAAIkD,OAAA,CAAQC,OAAA,CAAQ;EACpE;EAEAC,QAAA,EAAgB;IACd,MAAMA,OAAA,CAAQ;IAEd,KAAKL,MAAA,CAAO;MAAEM,MAAA,EAAQ;IAAK,CAAC;EAC9B;EAEAC,MAAA,EAAc;IACZ,KAAKF,OAAA,CAAQ;IACb,KAAKP,QAAA,CAAS,KAAK,CAAAlC,YAAa;EAClC;EAEA4C,SAAA,EAAoB;IAClB,OAAO,KAAKjC,SAAA,CAAUkC,IAAA,CACnBC,QAAA,IAAavD,cAAA,CAAeuD,QAAA,CAASpC,OAAA,CAAQqC,OAAA,EAAS,IAAI,MAAM,KACnE;EACF;EAEAC,WAAA,EAAsB;IACpB,IAAI,KAAKC,iBAAA,CAAkB,IAAI,GAAG;MAChC,OAAO,CAAC,KAAKL,QAAA,CAAS;IACxB;IAEA,OACE,KAAKlC,OAAA,CAAQwC,OAAA,KAAY1D,SAAA,IACzB,KAAKwB,KAAA,CAAMmC,eAAA,GAAkB,KAAKnC,KAAA,CAAMoC,gBAAA,KAAqB;EAEjE;EAEAC,QAAA,EAAmB;IACjB,IAAI,KAAKrC,KAAA,CAAMsC,aAAA,EAAe;MAC5B,OAAO;IACT;IAEA,IAAI,KAAKL,iBAAA,CAAkB,IAAI,GAAG;MAChC,OAAO,KAAKtC,SAAA,CAAUkC,IAAA,CACnBC,QAAA,IAAaA,QAAA,CAASS,gBAAA,CAAiB,EAAEF,OAC5C;IACF;IAEA,OAAO,KAAKrC,KAAA,CAAMY,IAAA,KAAS;EAC7B;EAEA4B,cAAcC,SAAA,GAAY,GAAY;IACpC,OACE,KAAKzC,KAAA,CAAMsC,aAAA,IACX,KAAKtC,KAAA,CAAMY,IAAA,KAAS,UACpB,CAACnC,cAAA,CAAe,KAAKuB,KAAA,CAAMe,aAAA,EAAe0B,SAAS;EAEvD;EAEAC,QAAA,EAAgB;IACd,MAAMZ,QAAA,GAAW,KAAKnC,SAAA,CAAUgD,IAAA,CAAMC,CAAA,IAAMA,CAAA,CAAEC,wBAAA,CAAyB,CAAC;IAExEf,QAAA,EAAUgB,OAAA,CAAQ;MAAEC,aAAA,EAAe;IAAM,CAAC;IAG1C,KAAK,CAAA3D,OAAA,EAAU4D,QAAA,CAAS;EAC1B;EAEAC,SAAA,EAAiB;IACf,MAAMnB,QAAA,GAAW,KAAKnC,SAAA,CAAUgD,IAAA,CAAMC,CAAA,IAAMA,CAAA,CAAEM,sBAAA,CAAuB,CAAC;IAEtEpB,QAAA,EAAUgB,OAAA,CAAQ;MAAEC,aAAA,EAAe;IAAM,CAAC;IAG1C,KAAK,CAAA3D,OAAA,EAAU4D,QAAA,CAAS;EAC1B;EAEAG,YAAYrB,QAAA,EAAwD;IAClE,IAAI,CAAC,KAAKnC,SAAA,CAAUyD,QAAA,CAAStB,QAAQ,GAAG;MACtC,KAAKnC,SAAA,CAAU0D,IAAA,CAAKvB,QAAQ;MAG5B,KAAKwB,cAAA,CAAe;MAEpB,KAAK,CAAApE,KAAA,CAAOqE,MAAA,CAAO;QAAEzC,IAAA,EAAM;QAAiB0C,KAAA,EAAO;QAAM1B;MAAS,CAAC;IACrE;EACF;EAEA2B,eAAe3B,QAAA,EAAwD;IACrE,IAAI,KAAKnC,SAAA,CAAUyD,QAAA,CAAStB,QAAQ,GAAG;MACrC,KAAKnC,SAAA,GAAY,KAAKA,SAAA,CAAU+D,MAAA,CAAQd,CAAA,IAAMA,CAAA,KAAMd,QAAQ;MAE5D,IAAI,CAAC,KAAKnC,SAAA,CAAUY,MAAA,EAAQ;QAG1B,IAAI,KAAK,CAAAnB,OAAA,EAAU;UACjB,IAAI,KAAK,CAAAE,mBAAA,EAAsB;YAC7B,KAAK,CAAAF,OAAA,CAASgC,MAAA,CAAO;cAAEuC,MAAA,EAAQ;YAAK,CAAC;UACvC,OAAO;YACL,KAAK,CAAAvE,OAAA,CAASwE,WAAA,CAAY;UAC5B;QACF;QAEA,KAAK3D,UAAA,CAAW;MAClB;MAEA,KAAK,CAAAf,KAAA,CAAOqE,MAAA,CAAO;QAAEzC,IAAA,EAAM;QAAmB0C,KAAA,EAAO;QAAM1B;MAAS,CAAC;IACvE;EACF;EAEAG,kBAAA,EAA4B;IAC1B,OAAO,KAAKtC,SAAA,CAAUY,MAAA;EACxB;EAEAsD,WAAA,EAAmB;IACjB,IAAI,CAAC,KAAK7D,KAAA,CAAMsC,aAAA,EAAe;MAC7B,KAAK,CAAAzB,QAAA,CAAU;QAAEC,IAAA,EAAM;MAAa,CAAC;IACvC;EACF;EAEAgD,MACEpE,OAAA,EACAqE,YAAA,EACgB;IAChB,IAAI,KAAK/D,KAAA,CAAMQ,WAAA,KAAgB,QAAQ;MACrC,IAAI,KAAKR,KAAA,CAAMY,IAAA,KAAS,UAAamD,YAAA,EAAchB,aAAA,EAAe;QAEhE,KAAK3B,MAAA,CAAO;UAAEM,MAAA,EAAQ;QAAK,CAAC;MAC9B,WAAW,KAAK,CAAAtC,OAAA,EAAU;QAExB,KAAK,CAAAA,OAAA,CAAS4E,aAAA,CAAc;QAE5B,OAAO,KAAK,CAAA5E,OAAA,CAASe,OAAA;MACvB;IACF;IAGA,IAAIT,OAAA,EAAS;MACX,KAAKD,UAAA,CAAWC,OAAO;IACzB;IAIA,IAAI,CAAC,KAAKA,OAAA,CAAQwC,OAAA,EAAS;MACzB,MAAMJ,QAAA,GAAW,KAAKnC,SAAA,CAAUgD,IAAA,CAAMC,CAAA,IAAMA,CAAA,CAAElD,OAAA,CAAQwC,OAAO;MAC7D,IAAIJ,QAAA,EAAU;QACZ,KAAKrC,UAAA,CAAWqC,QAAA,CAASpC,OAAO;MAClC;IACF;IAEA,IAAIuE,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;MACzC,IAAI,CAACC,KAAA,CAAMC,OAAA,CAAQ,KAAK3E,OAAA,CAAQG,QAAQ,GAAG;QACzCyE,OAAA,CAAQC,KAAA,CACN,qIACF;MACF;IACF;IAEA,MAAMC,eAAA,GAAkB,IAAIC,eAAA,CAAgB;IAK5C,MAAMC,iBAAA,GAAqBC,MAAA,IAAoB;MAC7CC,MAAA,CAAOC,cAAA,CAAeF,MAAA,EAAQ,UAAU;QACtCG,UAAA,EAAY;QACZC,GAAA,EAAKA,CAAA,KAAM;UACT,KAAK,CAAAzF,mBAAA,GAAuB;UAC5B,OAAOkF,eAAA,CAAgBQ,MAAA;QACzB;MACF,CAAC;IACH;IAGA,MAAMC,OAAA,GAAUA,CAAA,KAAM;MACpB,MAAM/C,OAAA,GAAU9D,aAAA,CAAc,KAAKsB,OAAA,EAASqE,YAAY;MAGxD,MAAMmB,cAAA,GAGF;QACF/F,MAAA,EAAQ,KAAK,CAAAA,MAAA;QACbU,QAAA,EAAU,KAAKA,QAAA;QACfK,IAAA,EAAM,KAAKA;MACb;MAEAwE,iBAAA,CAAkBQ,cAAc;MAEhC,KAAK,CAAA5F,mBAAA,GAAuB;MAC5B,IAAI,KAAKI,OAAA,CAAQyF,SAAA,EAAW;QAC1B,OAAO,KAAKzF,OAAA,CAAQyF,SAAA,CAClBjD,OAAA,EACAgD,cAAA,EACA,IACF;MACF;MAEA,OAAOhD,OAAA,CAAQgD,cAAiD;IAClE;IAGA,MAAME,OAAA,GAGF;MACFrB,YAAA;MACArE,OAAA,EAAS,KAAKA,OAAA;MACdG,QAAA,EAAU,KAAKA,QAAA;MACfV,MAAA,EAAQ,KAAK,CAAAA,MAAA;MACba,KAAA,EAAO,KAAKA,KAAA;MACZiF;IACF;IAEAP,iBAAA,CAAkBU,OAAO;IAEzB,KAAK1F,OAAA,CAAQ2F,QAAA,EAAUC,OAAA,CACrBF,OAAA,EACA,IACF;IAGA,KAAK,CAAAnG,WAAA,GAAe,KAAKe,KAAA;IAGzB,IACE,KAAKA,KAAA,CAAMQ,WAAA,KAAgB,UAC3B,KAAKR,KAAA,CAAMuF,SAAA,KAAcH,OAAA,CAAQrB,YAAA,EAAc7D,IAAA,EAC/C;MACA,KAAK,CAAAW,QAAA,CAAU;QAAEC,IAAA,EAAM;QAASZ,IAAA,EAAMkF,OAAA,CAAQrB,YAAA,EAAc7D;MAAK,CAAC;IACpE;IAEA,MAAMsF,OAAA,GAAWjB,KAAA,IAAyC;MAExD,IAAI,EAAE1F,gBAAA,CAAiB0F,KAAK,KAAKA,KAAA,CAAM7C,MAAA,GAAS;QAC9C,KAAK,CAAAb,QAAA,CAAU;UACbC,IAAA,EAAM;UACNyD;QACF,CAAC;MACH;MAEA,IAAI,CAAC1F,gBAAA,CAAiB0F,KAAK,GAAG;QAE5B,KAAK,CAAArF,KAAA,CAAOM,MAAA,CAAOgG,OAAA,GACjBjB,KAAA,EACA,IACF;QACA,KAAK,CAAArF,KAAA,CAAOM,MAAA,CAAOiG,SAAA,GACjB,KAAKzF,KAAA,CAAMY,IAAA,EACX2D,KAAA,EACA,IACF;MACF;MAGA,KAAKtE,UAAA,CAAW;IAClB;IAGA,KAAK,CAAAb,OAAA,GAAWR,aAAA,CAAc;MAC5B8G,cAAA,EAAgB3B,YAAA,EAAc2B,cAAA;MAG9BC,EAAA,EAAIP,OAAA,CAAQH,OAAA;MACZW,KAAA,EAAOpB,eAAA,CAAgBoB,KAAA,CAAMC,IAAA,CAAKrB,eAAe;MACjDsB,SAAA,EAAYlF,IAAA,IAAS;QACnB,IAAIA,IAAA,KAAS,QAAW;UACtB,IAAIqD,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;YACzCG,OAAA,CAAQC,KAAA,CACN,yIAAyI,KAAKzE,SAAS,EACzJ;UACF;UACA0F,OAAA,CAAQ,IAAIO,KAAA,CAAM,GAAG,KAAKjG,SAAS,oBAAoB,CAAQ;UAC/D;QACF;QAEA,IAAI;UACF,KAAKY,OAAA,CAAQE,IAAI;QACnB,SAAS2D,KAAA,EAAO;UACdiB,OAAA,CAAQjB,KAAe;UACvB;QACF;QAGA,KAAK,CAAArF,KAAA,CAAOM,MAAA,CAAOsG,SAAA,GAAYlF,IAAA,EAAM,IAAiC;QACtE,KAAK,CAAA1B,KAAA,CAAOM,MAAA,CAAOiG,SAAA,GACjB7E,IAAA,EACA,KAAKZ,KAAA,CAAMuE,KAAA,EACX,IACF;QAGA,KAAKtE,UAAA,CAAW;MAClB;MACAuF,OAAA;MACAQ,MAAA,EAAQA,CAACC,YAAA,EAAc1B,KAAA,KAAU;QAC/B,KAAK,CAAA1D,QAAA,CAAU;UAAEC,IAAA,EAAM;UAAUmF,YAAA;UAAc1B;QAAM,CAAC;MACxD;MACA2B,OAAA,EAASA,CAAA,KAAM;QACb,KAAK,CAAArF,QAAA,CAAU;UAAEC,IAAA,EAAM;QAAQ,CAAC;MAClC;MACAqF,UAAA,EAAYA,CAAA,KAAM;QAChB,KAAK,CAAAtF,QAAA,CAAU;UAAEC,IAAA,EAAM;QAAW,CAAC;MACrC;MACAsF,KAAA,EAAOhB,OAAA,CAAQ1F,OAAA,CAAQ0G,KAAA;MACvBC,UAAA,EAAYjB,OAAA,CAAQ1F,OAAA,CAAQ2G,UAAA;MAC5BC,WAAA,EAAalB,OAAA,CAAQ1F,OAAA,CAAQ4G,WAAA;MAC7BC,MAAA,EAAQA,CAAA,KAAM;IAChB,CAAC;IAED,OAAO,KAAK,CAAAnH,OAAA,CAASoH,KAAA,CAAM;EAC7B;EAEA,CAAA3F,QAAA4F,CAAUC,MAAA,EAAqC;IAC7C,MAAMC,OAAA,GACJ3G,KAAA,IAC8B;MAC9B,QAAQ0G,MAAA,CAAO5F,IAAA;QACb,KAAK;UACH,OAAO;YACL,GAAGd,KAAA;YACH4G,iBAAA,EAAmBF,MAAA,CAAOT,YAAA;YAC1BY,kBAAA,EAAoBH,MAAA,CAAOnC;UAC7B;QACF,KAAK;UACH,OAAO;YACL,GAAGvE,KAAA;YACHQ,WAAA,EAAa;UACf;QACF,KAAK;UACH,OAAO;YACL,GAAGR,KAAA;YACHQ,WAAA,EAAa;UACf;QACF,KAAK;UACH,OAAO;YACL,GAAGR,KAAA;YACH,GAAG8G,UAAA,CAAW9G,KAAA,CAAMY,IAAA,EAAM,KAAKlB,OAAO;YACtC6F,SAAA,EAAWmB,MAAA,CAAOxG,IAAA,IAAQ;UAC5B;QACF,KAAK;UACH,OAAO;YACL,GAAGF,KAAA;YACHY,IAAA,EAAM8F,MAAA,CAAO9F,IAAA;YACbuB,eAAA,EAAiBnC,KAAA,CAAMmC,eAAA,GAAkB;YACzCpB,aAAA,EAAe2F,MAAA,CAAO3F,aAAA,IAAiBgG,IAAA,CAAKC,GAAA,CAAI;YAChDzC,KAAA,EAAO;YACPjC,aAAA,EAAe;YACf2E,MAAA,EAAQ;YACR,IAAI,CAACP,MAAA,CAAOzF,MAAA,IAAU;cACpBT,WAAA,EAAa;cACboG,iBAAA,EAAmB;cACnBC,kBAAA,EAAoB;YACtB;UACF;QACF,KAAK;UACH,MAAMtC,KAAA,GAAQmC,MAAA,CAAOnC,KAAA;UAErB,IAAI1F,gBAAA,CAAiB0F,KAAK,KAAKA,KAAA,CAAMZ,MAAA,IAAU,KAAK,CAAA1E,WAAA,EAAc;YAChE,OAAO;cAAE,GAAG,KAAK,CAAAA,WAAA;cAAcuB,WAAA,EAAa;YAAO;UACrD;UAEA,OAAO;YACL,GAAGR,KAAA;YACHuE,KAAA;YACAnC,gBAAA,EAAkBpC,KAAA,CAAMoC,gBAAA,GAAmB;YAC3C8E,cAAA,EAAgBH,IAAA,CAAKC,GAAA,CAAI;YACzBJ,iBAAA,EAAmB5G,KAAA,CAAM4G,iBAAA,GAAoB;YAC7CC,kBAAA,EAAoBtC,KAAA;YACpB/D,WAAA,EAAa;YACbyG,MAAA,EAAQ;UACV;QACF,KAAK;UACH,OAAO;YACL,GAAGjH,KAAA;YACHsC,aAAA,EAAe;UACjB;QACF,KAAK;UACH,OAAO;YACL,GAAGtC,KAAA;YACH,GAAG0G,MAAA,CAAO1G;UACZ;MACJ;IACF;IAEA,KAAKA,KAAA,GAAQ2G,OAAA,CAAQ,KAAK3G,KAAK;IAE/BtB,aAAA,CAAcyI,KAAA,CAAM,MAAM;MACxB,KAAKxH,SAAA,CAAUyH,OAAA,CAAStF,QAAA,IAAa;QACnCA,QAAA,CAASuF,aAAA,CAAc;MACzB,CAAC;MAED,KAAK,CAAAnI,KAAA,CAAOqE,MAAA,CAAO;QAAEC,KAAA,EAAO;QAAM1C,IAAA,EAAM;QAAW4F;MAAO,CAAC;IAC7D,CAAC;EACH;AACF;AAEO,SAASI,WAMdlG,IAAA,EACAlB,OAAA,EACA;EACA,OAAO;IACLkH,iBAAA,EAAmB;IACnBC,kBAAA,EAAoB;IACpBrG,WAAA,EAAa7B,QAAA,CAASe,OAAA,CAAQ4G,WAAW,IAAI,aAAa;IAC1D,IAAI1F,IAAA,KAAS,UACV;MACC2D,KAAA,EAAO;MACP0C,MAAA,EAAQ;IACV;EACJ;AACF;AAEA,SAASlH,gBAMPL,OAAA,EAC2B;EAC3B,MAAMkB,IAAA,GACJ,OAAOlB,OAAA,CAAQ4H,WAAA,KAAgB,aAC1B5H,OAAA,CAAQ4H,WAAA,CAA2C,IACpD5H,OAAA,CAAQ4H,WAAA;EAEd,MAAMC,OAAA,GAAU3G,IAAA,KAAS;EAEzB,MAAM4G,oBAAA,GAAuBD,OAAA,GACzB,OAAO7H,OAAA,CAAQ8H,oBAAA,KAAyB,aACrC9H,OAAA,CAAQ8H,oBAAA,CAAkD,IAC3D9H,OAAA,CAAQ8H,oBAAA,GACV;EAEJ,OAAO;IACL5G,IAAA;IACAuB,eAAA,EAAiB;IACjBpB,aAAA,EAAewG,OAAA,GAAWC,oBAAA,IAAwBT,IAAA,CAAKC,GAAA,CAAI,IAAK;IAChEzC,KAAA,EAAO;IACPnC,gBAAA,EAAkB;IAClB8E,cAAA,EAAgB;IAChBN,iBAAA,EAAmB;IACnBC,kBAAA,EAAoB;IACpBtB,SAAA,EAAW;IACXjD,aAAA,EAAe;IACf2E,MAAA,EAAQM,OAAA,GAAU,YAAY;IAC9B/G,WAAA,EAAa;EACf;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}