/**
 * Sessions Service
 * Handles database operations for sessions
 */

const { pool, sql } = require('../../config/db');
const logger = require('../../utils/logger');

/**
 * Get sessions for a client
 * @param {number} clientId - Client ID
 * @param {Object} filters - Filter options
 * @returns {Promise<Array>} Sessions array
 */
const getClientSessions = async (clientId, filters = {}) => {
  try {
    const request = pool.request();
    request.input('ClientID', sql.Int, clientId);

    let whereClause = 'WHERE a.ClientID = @ClientID';
    
    // Add status filter if provided
    if (filters.status && filters.status !== 'all') {
      request.input('Status', sql.NVarChar(20), filters.status);
      whereClause += ' AND s.Status = @Status';
    }

    // Add search filter if provided
    if (filters.search) {
      request.input('SearchTerm', sql.NVarChar(255), `%${filters.search}%`);
      whereClause += ` AND (
        u_expert.FirstName + ' ' + u_expert.LastName LIKE @SearchTerm OR
        e.Specialization LIKE @SearchTerm OR
        a.Title LIKE @SearchTerm OR
        s.Notes LIKE @SearchTerm
      )`;
    }

    const query = `
      SELECT 
        s.SessionID as id,
        s.AppointmentID,
        s.StartTime,
        s.EndTime,
        s.Duration,
        s.Status,
        s.Notes,
        s.VideoURL,
        s.FileAttachments,
        a.ClientID,
        a.ExpertID,
        a.AppointmentDate,
        a.Title,
        a.PaymentAmount,
        u_client.FirstName + ' ' + u_client.LastName as clientName,
        u_expert.FirstName + ' ' + u_expert.LastName as expertName,
        u_expert.FirstName + ' ' + u_expert.LastName as expertTitle,
        e.Specialization,
        e.ProfileImage as expertAvatar,
        e.HourlyRate,
        e.Rating,
        e.ReviewCount,
        -- Calculate sessions completed for this client-expert pair
        (SELECT COUNT(*) FROM Sessions s2 
         INNER JOIN Appointments a2 ON s2.AppointmentID = a2.AppointmentID 
         WHERE a2.ClientID = a.ClientID AND a2.ExpertID = a.ExpertID 
         AND s2.Status = 'completed') as sessionsCompleted,
        -- Format date and time
        FORMAT(s.StartTime, 'yyyy-MM-dd') as date,
        FORMAT(s.StartTime, 'HH:mm') as startTime,
        FORMAT(s.EndTime, 'HH:mm') as endTime,
        CASE 
          WHEN s.VideoURL IS NOT NULL THEN 1 
          ELSE 0 
        END as recordingAvailable,
        'video' as type,
        COALESCE(a.Title, e.Specialization, 'Danışmanlık Seansı') as packageName
      FROM Sessions s
      INNER JOIN Appointments a ON s.AppointmentID = a.AppointmentID
      INNER JOIN Clients c ON a.ClientID = c.ClientID
      INNER JOIN Users u_client ON c.UserID = u_client.UserID
      INNER JOIN Experts e ON a.ExpertID = e.ExpertID
      INNER JOIN Users u_expert ON e.UserID = u_expert.UserID
      ${whereClause}
      ORDER BY s.StartTime DESC
    `;

    const result = await request.query(query);
    return result.recordset;
  } catch (error) {
    logger.error('Error getting client sessions:', error);
    throw error;
  }
};

/**
 * Get sessions for an expert
 * @param {number} expertId - Expert ID
 * @param {Object} filters - Filter options
 * @returns {Promise<Array>} Sessions array
 */
const getExpertSessions = async (expertId, filters = {}) => {
  try {
    const request = pool.request();
    request.input('ExpertID', sql.Int, expertId);

    let whereClause = 'WHERE a.ExpertID = @ExpertID';
    
    // Add status filter if provided
    if (filters.status && filters.status !== 'all') {
      request.input('Status', sql.NVarChar(20), filters.status);
      whereClause += ' AND s.Status = @Status';
    }

    // Add search filter if provided
    if (filters.search) {
      request.input('SearchTerm', sql.NVarChar(255), `%${filters.search}%`);
      whereClause += ` AND (
        u_client.FirstName + ' ' + u_client.LastName LIKE @SearchTerm OR
        a.Title LIKE @SearchTerm OR
        s.Notes LIKE @SearchTerm
      )`;
    }

    const query = `
      SELECT 
        s.SessionID as id,
        s.AppointmentID,
        s.StartTime,
        s.EndTime,
        s.Duration,
        s.Status,
        s.Notes,
        s.VideoURL,
        s.FileAttachments,
        a.ClientID,
        a.ExpertID,
        a.AppointmentDate,
        a.Title,
        a.PaymentAmount,
        u_client.FirstName + ' ' + u_client.LastName as clientName,
        u_expert.FirstName + ' ' + u_expert.LastName as expertName,
        u_client.ProfileImage as clientAvatar,
        -- Calculate sessions completed for this client-expert pair
        (SELECT COUNT(*) FROM Sessions s2 
         INNER JOIN Appointments a2 ON s2.AppointmentID = a2.AppointmentID 
         WHERE a2.ClientID = a.ClientID AND a2.ExpertID = a.ExpertID 
         AND s2.Status = 'completed') as sessionsCompleted,
        -- Format date and time
        FORMAT(s.StartTime, 'yyyy-MM-dd') as date,
        FORMAT(s.StartTime, 'HH:mm') as startTime,
        FORMAT(s.EndTime, 'HH:mm') as endTime,
        CASE 
          WHEN s.VideoURL IS NOT NULL THEN 1 
          ELSE 0 
        END as recordingAvailable,
        'video' as type
      FROM Sessions s
      INNER JOIN Appointments a ON s.AppointmentID = a.AppointmentID
      INNER JOIN Clients c ON a.ClientID = c.ClientID
      INNER JOIN Users u_client ON c.UserID = u_client.UserID
      INNER JOIN Experts e ON a.ExpertID = e.ExpertID
      INNER JOIN Users u_expert ON e.UserID = u_expert.UserID
      ${whereClause}
      ORDER BY s.StartTime DESC
    `;

    const result = await request.query(query);
    return result.recordset;
  } catch (error) {
    logger.error('Error getting expert sessions:', error);
    throw error;
  }
};

/**
 * Get session by ID
 * @param {number} sessionId - Session ID
 * @returns {Promise<Object>} Session object
 */
const getSessionById = async (sessionId) => {
  try {
    const request = pool.request();
    request.input('SessionID', sql.Int, sessionId);

    const query = `
      SELECT 
        s.SessionID as id,
        s.AppointmentID,
        s.StartTime,
        s.EndTime,
        s.Duration,
        s.Status,
        s.Notes,
        s.Summary,
        s.VideoURL,
        s.FileAttachments,
        a.ClientID,
        a.ExpertID,
        a.AppointmentDate,
        a.Title,
        a.PaymentAmount,
        u_client.FirstName + ' ' + u_client.LastName as clientName,
        u_expert.FirstName + ' ' + u_expert.LastName as expertName,
        u_expert.FirstName + ' ' + u_expert.LastName as expertTitle,
        e.Specialization,
        e.ProfileImage as expertAvatar,
        u_client.ProfileImage as clientAvatar,
        -- Format date and time
        FORMAT(s.StartTime, 'yyyy-MM-dd') as date,
        FORMAT(s.StartTime, 'HH:mm') as startTime,
        FORMAT(s.EndTime, 'HH:mm') as endTime,
        CASE 
          WHEN s.VideoURL IS NOT NULL THEN 1 
          ELSE 0 
        END as recordingAvailable,
        'video' as type
      FROM Sessions s
      INNER JOIN Appointments a ON s.AppointmentID = a.AppointmentID
      INNER JOIN Clients c ON a.ClientID = c.ClientID
      INNER JOIN Users u_client ON c.UserID = u_client.UserID
      INNER JOIN Experts e ON a.ExpertID = e.ExpertID
      INNER JOIN Users u_expert ON e.UserID = u_expert.UserID
      WHERE s.SessionID = @SessionID
    `;

    const result = await request.query(query);
    return result.recordset[0];
  } catch (error) {
    logger.error('Error getting session by ID:', error);
    throw error;
  }
};

/**
 * Update session notes
 * @param {number} sessionId - Session ID
 * @param {string} notes - Session notes
 * @param {number} userId - User ID making the update
 * @returns {Promise<Object>} Updated session
 */
const updateSessionNotes = async (sessionId, notes, userId) => {
  try {
    const request = pool.request();
    request.input('SessionID', sql.Int, sessionId);
    request.input('Notes', sql.NVarChar(sql.MAX), notes);
    request.input('UpdatedAt', sql.DateTime, new Date());

    const query = `
      UPDATE Sessions 
      SET Notes = @Notes, UpdatedAt = @UpdatedAt
      WHERE SessionID = @SessionID
    `;

    await request.query(query);
    return await getSessionById(sessionId);
  } catch (error) {
    logger.error('Error updating session notes:', error);
    throw error;
  }
};

/**
 * Update session status
 * @param {number} sessionId - Session ID
 * @param {string} status - New status
 * @param {number} userId - User ID making the update
 * @returns {Promise<Object>} Updated session
 */
const updateSessionStatus = async (sessionId, status, userId) => {
  try {
    const request = pool.request();
    request.input('SessionID', sql.Int, sessionId);
    request.input('Status', sql.NVarChar(20), status);
    request.input('UpdatedAt', sql.DateTime, new Date());

    const query = `
      UPDATE Sessions 
      SET Status = @Status, UpdatedAt = @UpdatedAt
      WHERE SessionID = @SessionID
    `;

    await request.query(query);
    return await getSessionById(sessionId);
  } catch (error) {
    logger.error('Error updating session status:', error);
    throw error;
  }
};

module.exports = {
  getClientSessions,
  getExpertSessions,
  getSessionById,
  updateSessionNotes,
  updateSessionStatus
};
