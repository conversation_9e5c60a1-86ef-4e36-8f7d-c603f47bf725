2025-03-22 17:03:49 [ERROR]: uncaughtException: Cannot find module './modules/users'
Require stack:
- C:\claude\burky_root_web\server\app.js
- C:\claude\burky_root_web\server\index.js
Error: Cannot find module './modules/users'
Require stack:
- C:\claude\burky_root_web\server\app.js
- C:\claude\burky_root_web\server\index.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1244:15)
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (C:\claude\burky_root_web\server\app.js:22:21)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32) {
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "C:\\claude\\burky_root_web\\server\\app.js",
      "C:\\claude\\burky_root_web\\server\\index.js"
    ]
  },
  "stack": "Error: Cannot find module './modules/users'\nRequire stack:\n- C:\\claude\\burky_root_web\\server\\app.js\n- C:\\claude\\burky_root_web\\server\\index.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1244:15)\n    at Function._load (node:internal/modules/cjs/loader:1070:27)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n    at Module.require (node:internal/modules/cjs/loader:1335:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (C:\\claude\\burky_root_web\\server\\app.js:22:21)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object..js (node:internal/modules/cjs/loader:1699:10)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)",
  "exception": true,
  "date": "Sat Mar 22 2025 17:03:49 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 248,
    "uid": null,
    "gid": null,
    "cwd": "C:\\claude\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v22.13.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\claude\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 83152896,
      "heapTotal": 38379520,
      "heapUsed": 27425104,
      "external": 2366763,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 12213.234
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._resolveFilename",
      "line": 1244,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 27,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1070,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 21,
      "file": "C:\\claude\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 22,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    }
  ]
}
2025-03-22 17:05:13 [ERROR]: uncaughtException: Cannot find module './modules/users'
Require stack:
- C:\claude\burky_root_web\server\app.js
- C:\claude\burky_root_web\server\index.js
Error: Cannot find module './modules/users'
Require stack:
- C:\claude\burky_root_web\server\app.js
- C:\claude\burky_root_web\server\index.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1244:15)
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (C:\claude\burky_root_web\server\app.js:22:21)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32) {
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "C:\\claude\\burky_root_web\\server\\app.js",
      "C:\\claude\\burky_root_web\\server\\index.js"
    ]
  },
  "stack": "Error: Cannot find module './modules/users'\nRequire stack:\n- C:\\claude\\burky_root_web\\server\\app.js\n- C:\\claude\\burky_root_web\\server\\index.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1244:15)\n    at Function._load (node:internal/modules/cjs/loader:1070:27)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n    at Module.require (node:internal/modules/cjs/loader:1335:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (C:\\claude\\burky_root_web\\server\\app.js:22:21)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object..js (node:internal/modules/cjs/loader:1699:10)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)",
  "exception": true,
  "date": "Sat Mar 22 2025 17:05:13 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 9984,
    "uid": null,
    "gid": null,
    "cwd": "C:\\claude\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v22.13.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\claude\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 84410368,
      "heapTotal": 38117376,
      "heapUsed": 27392472,
      "external": 2366763,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 12297.859
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._resolveFilename",
      "line": 1244,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 27,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1070,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 21,
      "file": "C:\\claude\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 22,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    }
  ]
}
2025-03-22 17:05:24 [ERROR]: uncaughtException: Cannot find module './modules/users'
Require stack:
- C:\claude\burky_root_web\server\app.js
- C:\claude\burky_root_web\server\index.js
Error: Cannot find module './modules/users'
Require stack:
- C:\claude\burky_root_web\server\app.js
- C:\claude\burky_root_web\server\index.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1244:15)
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (C:\claude\burky_root_web\server\app.js:22:21)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32) {
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "C:\\claude\\burky_root_web\\server\\app.js",
      "C:\\claude\\burky_root_web\\server\\index.js"
    ]
  },
  "stack": "Error: Cannot find module './modules/users'\nRequire stack:\n- C:\\claude\\burky_root_web\\server\\app.js\n- C:\\claude\\burky_root_web\\server\\index.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1244:15)\n    at Function._load (node:internal/modules/cjs/loader:1070:27)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n    at Module.require (node:internal/modules/cjs/loader:1335:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (C:\\claude\\burky_root_web\\server\\app.js:22:21)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object..js (node:internal/modules/cjs/loader:1699:10)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)",
  "exception": true,
  "date": "Sat Mar 22 2025 17:05:24 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 8664,
    "uid": null,
    "gid": null,
    "cwd": "C:\\claude\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v22.13.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\claude\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 84799488,
      "heapTotal": 38379520,
      "heapUsed": 27421344,
      "external": 2366763,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 12308.953
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._resolveFilename",
      "line": 1244,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 27,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1070,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 21,
      "file": "C:\\claude\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 22,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    }
  ]
}
2025-03-23 13:19:10 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\claude\burky_root_web\server\app.js:68:51)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (C:\claude\burky_root_web\server\index.js:10:13) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\claude\\burky_root_web\\server\\app.js:68:51)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object..js (node:internal/modules/cjs/loader:1699:10)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n    at Module.require (node:internal/modules/cjs/loader:1335:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (C:\\claude\\burky_root_web\\server\\index.js:10:13)",
  "exception": true,
  "date": "Sun Mar 23 2025 13:19:10 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 5660,
    "uid": null,
    "gid": null,
    "cwd": "C:\\claude\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v22.13.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\claude\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 81182720,
      "heapTotal": 40738816,
      "heapUsed": 23791136,
      "external": 2366763,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 1204.328
  },
  "trace": [
    {
      "column": 51,
      "file": "C:\\claude\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 68,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\claude\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    }
  ]
}
2025-03-24 03:05:35 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\burky root\burky_root_web\server\app.js:59:33)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (C:\burky root\burky_root_web\server\index.js:10:13) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\burky root\\burky_root_web\\server\\app.js:59:33)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object..js (node:internal/modules/cjs/loader:1699:10)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n    at Module.require (node:internal/modules/cjs/loader:1335:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (C:\\burky root\\burky_root_web\\server\\index.js:10:13)",
  "exception": true,
  "date": "Mon Mar 24 2025 03:05:35 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 220,
    "uid": null,
    "gid": null,
    "cwd": "C:\\burky root\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v22.13.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\burky root\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 76091392,
      "heapTotal": 38117376,
      "heapUsed": 27874880,
      "external": 2366763,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 6205.171
  },
  "trace": [
    {
      "column": 33,
      "file": "C:\\burky root\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 59,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\burky root\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    }
  ]
}
2025-03-24 03:14:48 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1937:16)
    at listenInCluster (node:net:1994:12)
    at Server.listen (node:net:2099:7)
    at startServer (C:\burky root\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at startServer (C:\\burky root\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Mon Mar 24 2025 03:14:48 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 12164,
    "uid": null,
    "gid": null,
    "cwd": "C:\\burky root\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v22.13.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\burky root\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 79896576,
      "heapTotal": 41680896,
      "heapUsed": 27011144,
      "external": 2387492,
      "arrayBuffers": 18717
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 6757.953
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1937,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1994,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2099,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\burky root\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-03-25 17:28:14 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\burky root\burky_root_web\server\app.js:80:39)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (C:\burky root\burky_root_web\server\index.js:10:13) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\burky root\\burky_root_web\\server\\app.js:80:39)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object..js (node:internal/modules/cjs/loader:1699:10)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n    at Module.require (node:internal/modules/cjs/loader:1335:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (C:\\burky root\\burky_root_web\\server\\index.js:10:13)",
  "exception": true,
  "date": "Tue Mar 25 2025 17:28:14 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 5384,
    "uid": null,
    "gid": null,
    "cwd": "C:\\burky root\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v22.13.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\burky root\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 80924672,
      "heapTotal": 41525248,
      "heapUsed": 25797256,
      "external": 2366763,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 15235.375
  },
  "trace": [
    {
      "column": 39,
      "file": "C:\\burky root\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 80,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\burky root\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    }
  ]
}
2025-03-25 17:29:57 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\burky root\burky_root_web\server\app.js:80:55)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (C:\burky root\burky_root_web\server\index.js:10:13) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\burky root\\burky_root_web\\server\\app.js:80:55)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object..js (node:internal/modules/cjs/loader:1699:10)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n    at Module.require (node:internal/modules/cjs/loader:1335:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (C:\\burky root\\burky_root_web\\server\\index.js:10:13)",
  "exception": true,
  "date": "Tue Mar 25 2025 17:29:57 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 9220,
    "uid": null,
    "gid": null,
    "cwd": "C:\\burky root\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v22.13.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\burky root\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 81092608,
      "heapTotal": 41525248,
      "heapUsed": 25830688,
      "external": 2366763,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 15337.937
  },
  "trace": [
    {
      "column": 55,
      "file": "C:\\burky root\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 80,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\burky root\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    }
  ]
}
2025-03-25 17:30:31 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\burky root\burky_root_web\server\app.js:80:55)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (C:\burky root\burky_root_web\server\index.js:10:13) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\burky root\\burky_root_web\\server\\app.js:80:55)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object..js (node:internal/modules/cjs/loader:1699:10)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n    at Module.require (node:internal/modules/cjs/loader:1335:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (C:\\burky root\\burky_root_web\\server\\index.js:10:13)",
  "exception": true,
  "date": "Tue Mar 25 2025 17:30:31 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 10556,
    "uid": null,
    "gid": null,
    "cwd": "C:\\burky root\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v22.13.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\burky root\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 81039360,
      "heapTotal": 41263104,
      "heapUsed": 25687368,
      "external": 2366763,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 15372.156
  },
  "trace": [
    {
      "column": 55,
      "file": "C:\\burky root\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 80,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\burky root\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    }
  ]
}
2025-03-26 16:50:59 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\burky root\burky_root_web\server\app.js:80:55)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (C:\burky root\burky_root_web\server\index.js:10:13) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\burky root\\burky_root_web\\server\\app.js:80:55)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object..js (node:internal/modules/cjs/loader:1699:10)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n    at Module.require (node:internal/modules/cjs/loader:1335:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (C:\\burky root\\burky_root_web\\server\\index.js:10:13)",
  "exception": true,
  "date": "Wed Mar 26 2025 16:50:59 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 10368,
    "uid": null,
    "gid": null,
    "cwd": "C:\\burky root\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v22.13.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\burky root\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 80904192,
      "heapTotal": 41000960,
      "heapUsed": 27192088,
      "external": 2366763,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 17755.921
  },
  "trace": [
    {
      "column": 55,
      "file": "C:\\burky root\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 80,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\burky root\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    }
  ]
}
2025-03-26 16:51:33 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\burky root\burky_root_web\server\app.js:80:55)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (C:\burky root\burky_root_web\server\index.js:10:13) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\burky root\\burky_root_web\\server\\app.js:80:55)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object..js (node:internal/modules/cjs/loader:1699:10)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n    at Module.require (node:internal/modules/cjs/loader:1335:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (C:\\burky root\\burky_root_web\\server\\index.js:10:13)",
  "exception": true,
  "date": "Wed Mar 26 2025 16:51:33 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 12268,
    "uid": null,
    "gid": null,
    "cwd": "C:\\burky root\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v22.13.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\burky root\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 81137664,
      "heapTotal": 41263104,
      "heapUsed": 27115064,
      "external": 2366763,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 17790.468
  },
  "trace": [
    {
      "column": 55,
      "file": "C:\\burky root\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 80,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\burky root\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    }
  ]
}
2025-03-26 23:59:17 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\burky root\burky_root_web\server\app.js:89:55)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (C:\burky root\burky_root_web\server\index.js:10:13) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\burky root\\burky_root_web\\server\\app.js:89:55)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object..js (node:internal/modules/cjs/loader:1699:10)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n    at Module.require (node:internal/modules/cjs/loader:1335:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (C:\\burky root\\burky_root_web\\server\\index.js:10:13)",
  "exception": true,
  "date": "Wed Mar 26 2025 23:59:17 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 4540,
    "uid": null,
    "gid": null,
    "cwd": "C:\\burky root\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v22.13.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\burky root\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 77737984,
      "heapTotal": 41263104,
      "heapUsed": 27228536,
      "external": 2366763,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 2169.453
  },
  "trace": [
    {
      "column": 55,
      "file": "C:\\burky root\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 89,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\burky root\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    }
  ]
}
2025-03-26 23:59:30 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\burky root\burky_root_web\server\app.js:89:55)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (C:\burky root\burky_root_web\server\index.js:10:13) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\burky root\\burky_root_web\\server\\app.js:89:55)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object..js (node:internal/modules/cjs/loader:1699:10)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n    at Module.require (node:internal/modules/cjs/loader:1335:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (C:\\burky root\\burky_root_web\\server\\index.js:10:13)",
  "exception": true,
  "date": "Wed Mar 26 2025 23:59:30 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 6928,
    "uid": null,
    "gid": null,
    "cwd": "C:\\burky root\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v22.13.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\burky root\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 78303232,
      "heapTotal": 41263104,
      "heapUsed": 27037080,
      "external": 2366763,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 2181.843
  },
  "trace": [
    {
      "column": 55,
      "file": "C:\\burky root\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 89,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\burky root\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    }
  ]
}
2025-03-27 00:00:25 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\burky root\burky_root_web\server\app.js:89:55)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (C:\burky root\burky_root_web\server\index.js:10:13) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\burky root\\burky_root_web\\server\\app.js:89:55)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object..js (node:internal/modules/cjs/loader:1699:10)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n    at Module.require (node:internal/modules/cjs/loader:1335:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (C:\\burky root\\burky_root_web\\server\\index.js:10:13)",
  "exception": true,
  "date": "Thu Mar 27 2025 00:00:25 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 11396,
    "uid": null,
    "gid": null,
    "cwd": "C:\\burky root\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v22.13.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\burky root\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 79642624,
      "heapTotal": 41525248,
      "heapUsed": 27159400,
      "external": 2366763,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 2237.171
  },
  "trace": [
    {
      "column": 55,
      "file": "C:\\burky root\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 89,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\burky root\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    }
  ]
}
2025-03-27 00:01:02 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\burky root\burky_root_web\server\app.js:89:55)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (C:\burky root\burky_root_web\server\index.js:10:13) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\burky root\\burky_root_web\\server\\app.js:89:55)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object..js (node:internal/modules/cjs/loader:1699:10)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n    at Module.require (node:internal/modules/cjs/loader:1335:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (C:\\burky root\\burky_root_web\\server\\index.js:10:13)",
  "exception": true,
  "date": "Thu Mar 27 2025 00:01:02 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 5852,
    "uid": null,
    "gid": null,
    "cwd": "C:\\burky root\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v22.13.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\burky root\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 80617472,
      "heapTotal": 41000960,
      "heapUsed": 27275896,
      "external": 2366763,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 2274.281
  },
  "trace": [
    {
      "column": 55,
      "file": "C:\\burky root\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 89,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\burky root\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    }
  ]
}
2025-03-27 00:01:12 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\burky root\burky_root_web\server\app.js:89:55)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (C:\burky root\burky_root_web\server\index.js:10:13) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\burky root\\burky_root_web\\server\\app.js:89:55)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object..js (node:internal/modules/cjs/loader:1699:10)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n    at Module.require (node:internal/modules/cjs/loader:1335:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (C:\\burky root\\burky_root_web\\server\\index.js:10:13)",
  "exception": true,
  "date": "Thu Mar 27 2025 00:01:12 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 7824,
    "uid": null,
    "gid": null,
    "cwd": "C:\\burky root\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v22.13.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\burky root\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 80805888,
      "heapTotal": 41263104,
      "heapUsed": 27231664,
      "external": 2366763,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 2284.39
  },
  "trace": [
    {
      "column": 55,
      "file": "C:\\burky root\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 89,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\burky root\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    }
  ]
}
2025-03-27 00:49:56 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\burky root\burky_root_web\server\app.js:86:55)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (C:\burky root\burky_root_web\server\index.js:10:13) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\burky root\\burky_root_web\\server\\app.js:86:55)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object..js (node:internal/modules/cjs/loader:1699:10)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n    at Module.require (node:internal/modules/cjs/loader:1335:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (C:\\burky root\\burky_root_web\\server\\index.js:10:13)",
  "exception": true,
  "date": "Thu Mar 27 2025 00:49:56 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 13092,
    "uid": null,
    "gid": null,
    "cwd": "C:\\burky root\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v22.13.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\burky root\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 81129472,
      "heapTotal": 41000960,
      "heapUsed": 27218792,
      "external": 2366763,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 5207.687
  },
  "trace": [
    {
      "column": 55,
      "file": "C:\\burky root\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 86,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\burky root\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    }
  ]
}
2025-08-06 21:22:22 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Wed Aug 06 2025 21:22:22 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 10532,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 77770752,
      "heapTotal": 57348096,
      "heapUsed": 30316944,
      "external": 2328035,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 74697.453
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-06 21:22:58 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Wed Aug 06 2025 21:22:58 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 16376,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 77402112,
      "heapTotal": 57348096,
      "heapUsed": 30146400,
      "external": 2328035,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 74733.421
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-06 21:23:54 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Wed Aug 06 2025 21:23:54 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 2248,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 77824000,
      "heapTotal": 57348096,
      "heapUsed": 30023216,
      "external": 2328035,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 74789.171
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-06 21:47:07 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Wed Aug 06 2025 21:47:07 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 1824,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 77590528,
      "heapTotal": 57348096,
      "heapUsed": 30308072,
      "external": 2328035,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 76182.359
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-06 21:47:53 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Wed Aug 06 2025 21:47:53 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 13472,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 77750272,
      "heapTotal": 57348096,
      "heapUsed": 30136456,
      "external": 2328035,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 76227.546
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-06 21:54:22 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:83:55)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:83:55)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Wed Aug 06 2025 21:54:22 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 9012,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 76476416,
      "heapTotal": 56406016,
      "heapUsed": 26427696,
      "external": 2308342,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 76616.921
  },
  "trace": [
    {
      "column": 55,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 83,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-06 22:07:54 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Wed Aug 06 2025 22:07:54 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 2868,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 78082048,
      "heapTotal": 57085952,
      "heapUsed": 30411224,
      "external": 2328035,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 77428.703
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-06 22:08:14 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Wed Aug 06 2025 22:08:14 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 15212,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 78200832,
      "heapTotal": 57085952,
      "heapUsed": 30341560,
      "external": 2328035,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 77448.703
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-06 22:49:56 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Wed Aug 06 2025 22:49:56 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 6188,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 78467072,
      "heapTotal": 57610240,
      "heapUsed": 30224088,
      "external": 2328035,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 79950.937
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-06 22:50:37 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Wed Aug 06 2025 22:50:37 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 12596,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 78426112,
      "heapTotal": 57610240,
      "heapUsed": 30281664,
      "external": 2328035,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 79991.5
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-06 23:04:38 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:83:55)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:83:55)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Wed Aug 06 2025 23:04:38 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 10308,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 77594624,
      "heapTotal": 56930304,
      "heapUsed": 27317032,
      "external": 2306588,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 80833.156
  },
  "trace": [
    {
      "column": 55,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 83,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-06 23:05:10 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:83:55)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:83:55)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Wed Aug 06 2025 23:05:10 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 7804,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 84217856,
      "heapTotal": 56668160,
      "heapUsed": 27458176,
      "external": 2308342,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 80865.328
  },
  "trace": [
    {
      "column": 55,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 83,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-06 23:18:45 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Wed Aug 06 2025 23:18:45 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 14316,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 86106112,
      "heapTotal": 57348096,
      "heapUsed": 31344072,
      "external": 2328035,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 81680.218
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-06 23:19:27 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Wed Aug 06 2025 23:19:27 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 7892,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 86130688,
      "heapTotal": 57348096,
      "heapUsed": 31345872,
      "external": 2329789,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 81722.406
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-06 23:22:36 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Wed Aug 06 2025 23:22:36 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 16056,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 85950464,
      "heapTotal": 57348096,
      "heapUsed": 31135048,
      "external": 2328035,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 81911.031
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-06 23:26:16 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Wed Aug 06 2025 23:26:16 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 18332,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 86130688,
      "heapTotal": 57610240,
      "heapUsed": 31255056,
      "external": 2328035,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 82130.625
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-06 23:28:45 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Wed Aug 06 2025 23:28:45 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 13672,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 86278144,
      "heapTotal": 57348096,
      "heapUsed": 31253144,
      "external": 2329789,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 82279.781
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-06 23:31:20 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Wed Aug 06 2025 23:31:20 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 9176,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 85745664,
      "heapTotal": 57610240,
      "heapUsed": 31096544,
      "external": 2328035,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 82435
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-06 23:31:20 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Wed Aug 06 2025 23:31:20 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 9996,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 85557248,
      "heapTotal": 57348096,
      "heapUsed": 31070640,
      "external": 2328035,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 82435.312
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-06 23:34:42 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Wed Aug 06 2025 23:34:42 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 11572,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 85626880,
      "heapTotal": 57348096,
      "heapUsed": 31196328,
      "external": 2328035,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 82636.859
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-06 23:34:42 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Wed Aug 06 2025 23:34:42 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 5944,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 85843968,
      "heapTotal": 57348096,
      "heapUsed": 31166520,
      "external": 2328035,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 82636.968
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-06 23:35:12 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Wed Aug 06 2025 23:35:12 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 6636,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 85606400,
      "heapTotal": 57610240,
      "heapUsed": 30955304,
      "external": 2328035,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 82666.593
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-06 23:35:12 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Wed Aug 06 2025 23:35:12 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 12604,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 85372928,
      "heapTotal": 57348096,
      "heapUsed": 31144432,
      "external": 2328035,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 82666.687
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-06 23:35:55 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Wed Aug 06 2025 23:35:55 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 11708,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 85757952,
      "heapTotal": 57085952,
      "heapUsed": 31109648,
      "external": 2329789,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 82710.406
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-06 23:35:55 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Wed Aug 06 2025 23:35:55 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 15344,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 85757952,
      "heapTotal": 57348096,
      "heapUsed": 31181336,
      "external": 2328035,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 82710.484
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-06 23:36:55 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Wed Aug 06 2025 23:36:55 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 10356,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 85835776,
      "heapTotal": 57348096,
      "heapUsed": 31208016,
      "external": 2328035,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 82769.781
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-06 23:36:55 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Wed Aug 06 2025 23:36:55 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 13988,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 85594112,
      "heapTotal": 57348096,
      "heapUsed": 31203280,
      "external": 2328035,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 82769.875
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-06 23:37:25 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Wed Aug 06 2025 23:37:25 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 10268,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 85331968,
      "heapTotal": 57610240,
      "heapUsed": 30911600,
      "external": 2328035,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 82799.531
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-06 23:37:25 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Wed Aug 06 2025 23:37:25 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 10168,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 85909504,
      "heapTotal": 57348096,
      "heapUsed": 31079704,
      "external": 2328035,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 82799.593
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-06 23:51:15 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Wed Aug 06 2025 23:51:15 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 12512,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 85655552,
      "heapTotal": 57348096,
      "heapUsed": 31247648,
      "external": 2328035,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 83630.343
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-06 23:51:15 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Wed Aug 06 2025 23:51:15 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 16812,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 85733376,
      "heapTotal": 57348096,
      "heapUsed": 31096600,
      "external": 2328035,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 83630.453
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-06 23:53:39 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Wed Aug 06 2025 23:53:39 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 3616,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 85733376,
      "heapTotal": 57348096,
      "heapUsed": 31382912,
      "external": 2329789,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 83774.39
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-06 23:54:25 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Wed Aug 06 2025 23:54:25 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 13544,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 86171648,
      "heapTotal": 57610240,
      "heapUsed": 31444816,
      "external": 2329789,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 83819.921
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-07 01:01:57 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Thu Aug 07 2025 01:01:57 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 15972,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 85852160,
      "heapTotal": 57348096,
      "heapUsed": 31301896,
      "external": 2329789,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 87872.593
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-07 01:02:10 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Thu Aug 07 2025 01:02:10 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 16500,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 86147072,
      "heapTotal": 57348096,
      "heapUsed": 31304080,
      "external": 2328035,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 87884.796
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-07 01:12:19 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Thu Aug 07 2025 01:12:19 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 11708,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 86130688,
      "heapTotal": 57610240,
      "heapUsed": 31309064,
      "external": 2328035,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 88494.578
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-07 01:47:23 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Thu Aug 07 2025 01:47:23 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 12228,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 86413312,
      "heapTotal": 57610240,
      "heapUsed": 31309832,
      "external": 2328035,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 90597.734
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-07 01:48:01 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Thu Aug 07 2025 01:48:01 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 5804,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 86487040,
      "heapTotal": 57348096,
      "heapUsed": 31165144,
      "external": 2328035,
      "arrayBuffers": 19389
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 90636.281
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-07 01:48:13 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:73:33)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:73:33)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Thu Aug 07 2025 01:48:13 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 8132,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 84692992,
      "heapTotal": 56668160,
      "heapUsed": 26016816,
      "external": 2306588,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 90647.75
  },
  "trace": [
    {
      "column": 33,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 73,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-07 01:48:13 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:73:33)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:73:33)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Thu Aug 07 2025 01:48:13 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 10268,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 84398080,
      "heapTotal": 56668160,
      "heapUsed": 26317472,
      "external": 2306588,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 90647.796
  },
  "trace": [
    {
      "column": 33,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 73,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-07 01:48:44 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:73:33)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:73:33)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Thu Aug 07 2025 01:48:44 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 17596,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 83742720,
      "heapTotal": 56668160,
      "heapUsed": 26182968,
      "external": 2306588,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 90679.015
  },
  "trace": [
    {
      "column": 33,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 73,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-07 01:48:44 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:73:33)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:73:33)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Thu Aug 07 2025 01:48:44 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 10364,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 84238336,
      "heapTotal": 56668160,
      "heapUsed": 26131728,
      "external": 2306588,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 90679.046
  },
  "trace": [
    {
      "column": 33,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 73,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-07 01:49:09 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:73:33)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:73:33)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Thu Aug 07 2025 01:49:09 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 17652,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 83910656,
      "heapTotal": 56668160,
      "heapUsed": 26110520,
      "external": 2306588,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 90704.515
  },
  "trace": [
    {
      "column": 33,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 73,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-07 01:49:09 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:73:33)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:73:33)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Thu Aug 07 2025 01:49:09 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 3680,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 83615744,
      "heapTotal": 56406016,
      "heapUsed": 25820640,
      "external": 2306588,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 90704.515
  },
  "trace": [
    {
      "column": 33,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 73,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-07 01:49:42 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:73:33)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:73:33)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Thu Aug 07 2025 01:49:42 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 13028,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 83812352,
      "heapTotal": 56668160,
      "heapUsed": 26051120,
      "external": 2306588,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 90737.203
  },
  "trace": [
    {
      "column": 33,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 73,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-07 01:49:42 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:73:33)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:73:33)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Thu Aug 07 2025 01:49:42 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 10480,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 83922944,
      "heapTotal": 56406016,
      "heapUsed": 26391544,
      "external": 2306588,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 90737.234
  },
  "trace": [
    {
      "column": 33,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 73,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-07 01:53:37 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:73:33)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:73:33)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Thu Aug 07 2025 01:53:37 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 12540,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 67239936,
      "heapTotal": 30646272,
      "heapUsed": 19915776,
      "external": 2156509,
      "arrayBuffers": 16835
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 90971.765
  },
  "trace": [
    {
      "column": 33,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 73,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-07 01:53:37 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:73:33)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:73:33)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Thu Aug 07 2025 01:53:37 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 12760,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 66981888,
      "heapTotal": 30646272,
      "heapUsed": 19966664,
      "external": 2156509,
      "arrayBuffers": 16835
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 90971.765
  },
  "trace": [
    {
      "column": 33,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 73,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-07 01:59:23 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:73:33)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:73:33)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Thu Aug 07 2025 01:59:23 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 15008,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 66834432,
      "heapTotal": 30646272,
      "heapUsed": 19855216,
      "external": 2156509,
      "arrayBuffers": 16835
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 91318.156
  },
  "trace": [
    {
      "column": 33,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 73,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-07 01:59:52 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:73:33)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:73:33)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Thu Aug 07 2025 01:59:52 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 15788,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 67190784,
      "heapTotal": 30646272,
      "heapUsed": 20136048,
      "external": 2156509,
      "arrayBuffers": 16835
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 91347.203
  },
  "trace": [
    {
      "column": 33,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 73,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-07 01:59:57 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:73:33)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:73:33)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Thu Aug 07 2025 01:59:57 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 12268,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 66953216,
      "heapTotal": 30646272,
      "heapUsed": 20074256,
      "external": 2156509,
      "arrayBuffers": 16835
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 91352.25
  },
  "trace": [
    {
      "column": 33,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 73,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-08 21:51:37 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:91:55)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:91:55)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Fri Aug 08 2025 21:51:37 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 19256,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 77094912,
      "heapTotal": 56668160,
      "heapUsed": 28134384,
      "external": 2306588,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 249253.421
  },
  "trace": [
    {
      "column": 55,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 91,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-08 22:30:01 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:40:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:40:12)",
  "exception": true,
  "date": "Fri Aug 08 2025 22:30:01 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 3136,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 78942208,
      "heapTotal": 58134528,
      "heapUsed": 26970216,
      "external": 2304588,
      "arrayBuffers": 19213
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 251558.031
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 40,
      "method": null,
      "native": false
    }
  ]
}
2025-08-08 22:59:14 [ERROR]: uncaughtException: messagesModule is not defined
ReferenceError: messagesModule is not defined
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:100:42)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:11:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "ReferenceError: messagesModule is not defined\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:100:42)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:11:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Fri Aug 08 2025 22:59:14 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 14572,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 79818752,
      "heapTotal": 57454592,
      "heapUsed": 31200600,
      "external": 2308342,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 253310.546
  },
  "trace": [
    {
      "column": 42,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 100,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 11,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-08 22:59:32 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:100:57)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:11:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:100:57)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:11:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Fri Aug 08 2025 22:59:32 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 784,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 79712256,
      "heapTotal": 58241024,
      "heapUsed": 31166368,
      "external": 2308342,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 253329.015
  },
  "trace": [
    {
      "column": 57,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 100,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 11,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-08 23:04:14 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:100:57)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:11:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:100:57)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:11:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Fri Aug 08 2025 23:04:14 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 11380,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 79466496,
      "heapTotal": 57716736,
      "heapUsed": 30882000,
      "external": 2306588,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 253610.125
  },
  "trace": [
    {
      "column": 57,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 100,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 11,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-08 23:04:30 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:100:57)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:100:57)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Fri Aug 08 2025 23:04:30 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 1696,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 77946880,
      "heapTotal": 56406016,
      "heapUsed": 29686376,
      "external": 2306588,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 253626.187
  },
  "trace": [
    {
      "column": 57,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 100,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-08 23:04:44 [ERROR]: uncaughtException: messagesModule is not defined
ReferenceError: messagesModule is not defined
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:100:42)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "ReferenceError: messagesModule is not defined\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:100:42)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Fri Aug 08 2025 23:04:44 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 3360,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 78266368,
      "heapTotal": 56930304,
      "heapUsed": 30114008,
      "external": 2306588,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 253640.265
  },
  "trace": [
    {
      "column": 42,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 100,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-08 23:04:57 [ERROR]: uncaughtException: messagesModule is not defined
ReferenceError: messagesModule is not defined
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:97:42)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "ReferenceError: messagesModule is not defined\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:97:42)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Fri Aug 08 2025 23:04:57 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 5468,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 78245888,
      "heapTotal": 56668160,
      "heapUsed": 29906024,
      "external": 2306588,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 253653.218
  },
  "trace": [
    {
      "column": 42,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 97,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-08 23:12:24 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:100:57)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:100:57)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Fri Aug 08 2025 23:12:24 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 14044,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 78462976,
      "heapTotal": 56668160,
      "heapUsed": 30158128,
      "external": 2306588,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 254100.781
  },
  "trace": [
    {
      "column": 57,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 100,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-08 23:12:46 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:100:57)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:100:57)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Fri Aug 08 2025 23:12:46 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 9420,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 78467072,
      "heapTotal": 56668160,
      "heapUsed": 30141048,
      "external": 2306588,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 254123
  },
  "trace": [
    {
      "column": 57,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 100,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-08 23:12:57 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:100:57)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:100:57)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Fri Aug 08 2025 23:12:57 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 1296,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 78483456,
      "heapTotal": 56668160,
      "heapUsed": 29976384,
      "external": 2306588,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 254133.625
  },
  "trace": [
    {
      "column": 57,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 100,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-08 23:19:25 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:100:57)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:100:57)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Fri Aug 08 2025 23:19:25 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 3076,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 78479360,
      "heapTotal": 56406016,
      "heapUsed": 29824328,
      "external": 2281387,
      "arrayBuffers": 16943
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 254521.687
  },
  "trace": [
    {
      "column": 57,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 100,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-08 23:19:39 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:100:57)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:100:57)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Fri Aug 08 2025 23:19:39 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 12160,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 78450688,
      "heapTotal": 56668160,
      "heapUsed": 30167480,
      "external": 2281387,
      "arrayBuffers": 16943
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 254535.656
  },
  "trace": [
    {
      "column": 57,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 100,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-08 23:19:52 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:100:57)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:100:57)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Fri Aug 08 2025 23:19:52 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 4780,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 78372864,
      "heapTotal": 56668160,
      "heapUsed": 30012240,
      "external": 2281387,
      "arrayBuffers": 16943
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 254548.953
  },
  "trace": [
    {
      "column": 57,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 100,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-08 23:20:06 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:100:57)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:100:57)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Fri Aug 08 2025 23:20:06 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 10704,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 78479360,
      "heapTotal": 57716736,
      "heapUsed": 25897080,
      "external": 2283141,
      "arrayBuffers": 16943
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 254562.218
  },
  "trace": [
    {
      "column": 57,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 100,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-08 23:23:20 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:100:57)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:100:57)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Fri Aug 08 2025 23:23:20 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 10032,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 78340096,
      "heapTotal": 56406016,
      "heapUsed": 30067936,
      "external": 2281387,
      "arrayBuffers": 16943
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 254756.515
  },
  "trace": [
    {
      "column": 57,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 100,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-08 23:23:26 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:100:57)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:100:57)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Fri Aug 08 2025 23:23:26 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 16048,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 78430208,
      "heapTotal": 56930304,
      "heapUsed": 30148616,
      "external": 2306588,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 254762.765
  },
  "trace": [
    {
      "column": 57,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 100,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-08 23:23:47 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:100:57)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:100:57)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Fri Aug 08 2025 23:23:47 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 784,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 77885440,
      "heapTotal": 56668160,
      "heapUsed": 30013960,
      "external": 2306588,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 254783.25
  },
  "trace": [
    {
      "column": 57,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 100,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-08 23:30:58 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:47:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:47:12)",
  "exception": true,
  "date": "Fri Aug 08 2025 23:30:58 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 1616,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 83488768,
      "heapTotal": 57872384,
      "heapUsed": 35766992,
      "external": 2302834,
      "arrayBuffers": 19213
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 255214.39
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 47,
      "method": null,
      "native": false
    }
  ]
}
2025-08-08 23:31:14 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:47:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:47:12)",
  "exception": true,
  "date": "Fri Aug 08 2025 23:31:14 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 17248,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 79859712,
      "heapTotal": 58920960,
      "heapUsed": 31390744,
      "external": 2304588,
      "arrayBuffers": 19213
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 255230.281
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 47,
      "method": null,
      "native": false
    }
  ]
}
2025-08-08 23:32:09 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1898:16)
    at listenInCluster (node:net:1946:12)
    at Server.listen (node:net:2044:7)
    at startServer (C:\Projeler\kidgarden\burky_root_web\server\index.js:47:12) {
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 5000
  },
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at startServer (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:47:12)",
  "exception": true,
  "date": "Fri Aug 08 2025 23:32:09 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 9252,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 79818752,
      "heapTotal": 59183104,
      "heapUsed": 31377088,
      "external": 2304588,
      "arrayBuffers": 19213
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 255285.062
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1898,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1946,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2044,
      "method": "listen",
      "native": false
    },
    {
      "column": 12,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": "startServer",
      "line": 47,
      "method": null,
      "native": false
    }
  ]
}
2025-08-10 18:00:14 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:100:57)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:100:57)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Sun Aug 10 2025 18:00:14 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 15624,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 78561280,
      "heapTotal": 56668160,
      "heapUsed": 30528112,
      "external": 2306588,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 102722.406
  },
  "trace": [
    {
      "column": 57,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 100,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-10 23:04:31 [ERROR]: uncaughtException: Cannot find module '../../config/database'
Require stack:
- C:\Projeler\kidgarden\burky_root_web\server\modules\sessions\sessions.service.js
- C:\Projeler\kidgarden\burky_root_web\server\modules\sessions\sessions.controller.js
- C:\Projeler\kidgarden\burky_root_web\server\modules\sessions\sessions.routes.js
- C:\Projeler\kidgarden\burky_root_web\server\modules\sessions\index.js
- C:\Projeler\kidgarden\burky_root_web\server\app.js
- C:\Projeler\kidgarden\burky_root_web\server\index.js
Error: Cannot find module '../../config/database'
Require stack:
- C:\Projeler\kidgarden\burky_root_web\server\modules\sessions\sessions.service.js
- C:\Projeler\kidgarden\burky_root_web\server\modules\sessions\sessions.controller.js
- C:\Projeler\kidgarden\burky_root_web\server\modules\sessions\sessions.routes.js
- C:\Projeler\kidgarden\burky_root_web\server\modules\sessions\index.js
- C:\Projeler\kidgarden\burky_root_web\server\app.js
- C:\Projeler\kidgarden\burky_root_web\server\index.js
    at Module._resolveFilename (node:internal/modules/cjs/loader:1145:15)
    at Module._load (node:internal/modules/cjs/loader:986:27)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\modules\sessions\sessions.service.js:1:23)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19) {
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\sessions\\sessions.service.js",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\sessions\\sessions.controller.js",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\sessions\\sessions.routes.js",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\sessions\\index.js",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ]
  },
  "stack": "Error: Cannot find module '../../config/database'\nRequire stack:\n- C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\sessions\\sessions.service.js\n- C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\sessions\\sessions.controller.js\n- C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\sessions\\sessions.routes.js\n- C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\sessions\\index.js\n- C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js\n- C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1145:15)\n    at Module._load (node:internal/modules/cjs/loader:986:27)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\sessions\\sessions.service.js:1:23)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)",
  "exception": true,
  "date": "Sun Aug 10 2025 23:04:31 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 1500,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 78815232,
      "heapTotal": 56406016,
      "heapUsed": 30657600,
      "external": 2306588,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 120979.875
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._resolveFilename",
      "line": 1145,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 27,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 986,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 23,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\modules\\sessions\\sessions.service.js",
      "function": null,
      "line": 1,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    }
  ]
}
2025-08-11 12:21:48 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:106:57)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:106:57)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Mon Aug 11 2025 12:21:48 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 15212,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 78667776,
      "heapTotal": 56668160,
      "heapUsed": 31134752,
      "external": 2308342,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 168817.109
  },
  "trace": [
    {
      "column": 57,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 106,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
2025-08-11 12:26:36 [ERROR]: uncaughtException: Cannot read properties of undefined (reading 'routes')
TypeError: Cannot read properties of undefined (reading 'routes')
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\app.js:100:55)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)
    at Module.load (node:internal/modules/cjs/loader:1208:32)
    at Module._load (node:internal/modules/cjs/loader:1024:12)
    at Module.require (node:internal/modules/cjs/loader:1233:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (C:\Projeler\kidgarden\burky_root_web\server\index.js:10:13)
    at Module._compile (node:internal/modules/cjs/loader:1358:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'routes')\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js:100:55)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)\n    at Module.load (node:internal/modules/cjs/loader:1208:32)\n    at Module._load (node:internal/modules/cjs/loader:1024:12)\n    at Module.require (node:internal/modules/cjs/loader:1233:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js:10:13)\n    at Module._compile (node:internal/modules/cjs/loader:1358:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1416:10)",
  "exception": true,
  "date": "Mon Aug 11 2025 12:26:36 GMT+0300 (GMT+03:00)",
  "process": {
    "pid": 13884,
    "uid": null,
    "gid": null,
    "cwd": "C:\\Projeler\\kidgarden\\burky_root_web\\server",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.14.0",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js"
    ],
    "memoryUsage": {
      "rss": 77266944,
      "heapTotal": 56668160,
      "heapUsed": 29286008,
      "external": 2306588,
      "arrayBuffers": 17119
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 169105.281
  },
  "trace": [
    {
      "column": 55,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\app.js",
      "function": null,
      "line": 100,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1208,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1024,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1233,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "C:\\Projeler\\kidgarden\\burky_root_web\\server\\index.js",
      "function": null,
      "line": 10,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1358,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1416,
      "method": ".js",
      "native": false
    }
  ]
}
