{"ast": null, "code": "var _jsxFileName = \"C:\\\\Projeler\\\\kidgarden\\\\burky_root_web\\\\client\\\\src\\\\pages\\\\expert\\\\messages\\\\MessagesPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '../../../hooks/useAuth';\nimport api from '../../../services/api';\nimport { toast } from 'react-hot-toast';\nimport io from 'socket.io-client';\nimport { ChatBubbleLeftEllipsisIcon, MagnifyingGlassIcon, UserIcon, PaperAirplaneIcon, PaperClipIcon, FaceSmileIcon, UserCircleIcon, EllipsisHorizontalIcon, PhoneIcon, VideoCameraIcon, InformationCircleIcon, ClockIcon, CheckCircleIcon, XMarkIcon, TrashIcon, ArchiveBoxIcon, StarIcon } from '@heroicons/react/24/outline';\nimport { format } from 'date-fns';\nimport { tr } from 'date-fns/locale';\nimport { Link } from 'react-router-dom';\n\n/**\n * <PERSON><PERSON> mesaj<PERSON><PERSON> sayfası\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MessagesPage = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [isLoading, setIsLoading] = useState(true);\n  const [conversations, setConversations] = useState([]);\n  const [selectedConversation, setSelectedConversation] = useState(null);\n  const [messages, setMessages] = useState([]);\n  const [messageText, setMessageText] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filter, setFilter] = useState('all'); // all, unread, archived, starred\n  const [socket, setSocket] = useState(null);\n  const [onlineUsers, setOnlineUsers] = useState(new Set());\n  const [typingUsers, setTypingUsers] = useState(new Set());\n  const messagesEndRef = useRef(null);\n  const conversationsRef = useRef(null);\n  const messagesContainerRef = useRef(null);\n\n  // Socket.IO connection - ayrı useEffect\n  useEffect(() => {\n    const token = localStorage.getItem('accessToken');\n    if (token && user !== null && user !== void 0 && user.id) {\n      console.log('🔌 EXPERT: Socket.IO bağlantısı kuruluyor... User ID:', user.id);\n      const socketConnection = io(process.env.REACT_APP_API_URL || 'http://localhost:5000', {\n        auth: {\n          token\n        },\n        transports: ['websocket', 'polling'],\n        reconnection: true,\n        reconnectionDelay: 1000,\n        reconnectionAttempts: 5,\n        timeout: 20000\n      });\n      setSocket(socketConnection);\n\n      // Connection events\n      socketConnection.on('connect', () => {\n        console.log('✅ EXPERT: Socket.IO bağlantısı kuruldu:', socketConnection.id);\n        console.log('🔗 EXPERT: Socket connected:', socketConnection.connected);\n        console.log('🏠 EXPERT: Socket rooms:', socketConnection.rooms);\n        // Kullanıcının online olduğunu bildir\n        socketConnection.emit('user_online');\n      });\n      socketConnection.on('disconnect', reason => {\n        console.log('❌ EXPERT: Socket.IO bağlantısı kesildi:', reason);\n      });\n      socketConnection.on('connect_error', error => {\n        console.error('🔥 EXPERT: Socket.IO bağlantı hatası:', error);\n      });\n      socketConnection.on('reconnect', attemptNumber => {\n        console.log('🔄 EXPERT: Socket.IO yeniden bağlandı, deneme:', attemptNumber);\n      });\n      socketConnection.on('reconnect_error', error => {\n        console.error('🔥 EXPERT: Socket.IO yeniden bağlanma hatası:', error);\n      });\n\n      // Heartbeat mekanizması\n      const heartbeatInterval = setInterval(() => {\n        if (socketConnection.connected) {\n          socketConnection.emit('ping', response => {\n            if (response === 'pong') {\n              console.log('💓 EXPERT: Heartbeat OK');\n            }\n          });\n        }\n      }, 30000); // 30 saniyede bir ping gönder\n\n      return () => {\n        clearInterval(heartbeatInterval);\n        socketConnection.disconnect();\n      };\n    } else {\n      console.log('❌ EXPERT: Socket bağlantısı kurulamıyor - token veya user eksik:', {\n        hasToken: !!token,\n        hasUser: !!(user !== null && user !== void 0 && user.id)\n      });\n    }\n  }, [user === null || user === void 0 ? void 0 : user.id]);\n\n  // Socket event listeners - ayrı useEffect\n  useEffect(() => {\n    if (socket && user !== null && user !== void 0 && user.id) {\n      console.log('🎧 Socket event listeners kuruluyor...');\n\n      // Socket event listeners\n      const handleNewMessage = message => {\n        console.log('📨 EXPERT: Yeni mesaj alındı:', message);\n        console.log('👤 EXPERT: Current user ID:', user.id);\n        console.log('💬 EXPERT: Message sender ID:', message.senderId);\n        console.log('🏠 EXPERT: Message conversation ID:', message.conversationId);\n\n        // Conversation listesini ÖNCE güncelle (her zaman)\n        setConversations(prev => {\n          console.log('📋 EXPERT: Updating conversations list');\n          return prev.map(conv => conv.id === message.conversationId ? {\n            ...conv,\n            lastMessage: message.content,\n            timestamp: message.createdAt,\n            unread: message.senderId !== user.id\n          } : conv);\n        });\n\n        // Eğer şu anda bu conversation açıksa, mesajı da ekle\n        setSelectedConversation(currentSelected => {\n          console.log('🎯 EXPERT: Current selected conversation:', currentSelected === null || currentSelected === void 0 ? void 0 : currentSelected.id);\n          if (currentSelected && message.conversationId === currentSelected.id) {\n            console.log('✅ EXPERT: Adding message to current conversation');\n            setMessages(prev => {\n              // Mesajın zaten var olup olmadığını kontrol et\n              const messageExists = prev.some(msg => msg.id === message.id);\n              if (messageExists) {\n                console.log('⚠️ EXPERT: Message already exists, skipping:', message.id);\n                return prev;\n              }\n              return [...prev, {\n                id: message.id,\n                senderId: message.senderId,\n                senderName: message.sender.name,\n                senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n                text: message.content,\n                timestamp: message.createdAt,\n                read: message.isRead,\n                delivered: true // Socket'ten gelen mesajlar delivered olarak kabul edilir\n              }];\n            });\n\n            // Scroll to bottom\n            setTimeout(() => {\n              var _messagesEndRef$curre;\n              (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n                behavior: 'smooth'\n              });\n            }, 100);\n          } else {\n            console.log('❌ EXPERT: Not adding message - conversation not selected or different conversation');\n          }\n          return currentSelected;\n        });\n      };\n      const handleMessageSent = message => {\n        console.log('✅ Mesaj gönderildi onayı:', message);\n      };\n      const handleUserStatusChange = data => {\n        setOnlineUsers(prev => {\n          const newSet = new Set(prev);\n          if (data.status === 'online') {\n            newSet.add(data.userId);\n          } else {\n            newSet.delete(data.userId);\n          }\n          return newSet;\n        });\n      };\n      const handleUserTyping = data => {\n        setSelectedConversation(currentSelected => {\n          if (currentSelected && data.conversationId === currentSelected.id) {\n            setTypingUsers(prev => new Set([...prev, data.userId]));\n          }\n          return currentSelected;\n        });\n      };\n      const handleUserStoppedTyping = data => {\n        setTypingUsers(prev => {\n          const newSet = new Set(prev);\n          newSet.delete(data.userId);\n          return newSet;\n        });\n      };\n      const handleMessagesRead = data => {\n        console.log('👁️ EXPERT: Messages read event:', data);\n        // Mesajları okundu olarak işaretle\n        setMessages(prev => prev.map(msg => data.messageIds.includes(msg.id) ? {\n          ...msg,\n          read: true\n        } : msg));\n      };\n\n      // Event listener'ları ekle\n      socket.on('new_message', handleNewMessage);\n      socket.on('message_sent', handleMessageSent);\n      socket.on('user_status_change', handleUserStatusChange);\n      socket.on('user_typing', handleUserTyping);\n      socket.on('user_stopped_typing', handleUserStoppedTyping);\n      socket.on('messages_read', handleMessagesRead);\n\n      // Cleanup function\n      return () => {\n        socket.off('new_message', handleNewMessage);\n        socket.off('message_sent', handleMessageSent);\n        socket.off('user_status_change', handleUserStatusChange);\n        socket.off('user_typing', handleUserTyping);\n        socket.off('user_stopped_typing', handleUserStoppedTyping);\n        socket.off('messages_read', handleMessagesRead);\n      };\n    }\n  }, [socket, user.id]);\n\n  // Conversations yükleme - ayrı useEffect\n  useEffect(() => {\n    loadConversations();\n  }, []);\n\n  // Socket bağlandığında tüm conversation'lara join ol - sadece bir kez\n  const [joinedConversations, setJoinedConversations] = useState(new Set());\n  useEffect(() => {\n    if (socket && conversations.length > 0) {\n      console.log('🏠 EXPERT: Tüm conversation\\'lara katılıyor...', conversations.length, 'conversation');\n      conversations.forEach(conversation => {\n        if (!joinedConversations.has(conversation.id)) {\n          socket.emit('join_conversation', conversation.id);\n          console.log(`🏠 EXPERT: Conversation ${conversation.id} katıldı`);\n          setJoinedConversations(prev => new Set([...prev, conversation.id]));\n        }\n      });\n    }\n  }, [socket, conversations, joinedConversations]);\n\n  // Conversations yükleme\n  const loadConversations = async () => {\n    try {\n      setIsLoading(true);\n      const response = await api.get('/messages/conversations');\n\n      // API verisini frontend formatına çevir\n      const formattedConversations = response.data.conversations.map(conversation => {\n        var _conversation$lastMes, _conversation$lastMes2;\n        return {\n          id: conversation.id,\n          clientId: conversation.otherUser.id,\n          clientName: conversation.otherUser.name,\n          lastMessage: ((_conversation$lastMes = conversation.lastMessage) === null || _conversation$lastMes === void 0 ? void 0 : _conversation$lastMes.content) || 'Henüz mesaj yok',\n          timestamp: ((_conversation$lastMes2 = conversation.lastMessage) === null || _conversation$lastMes2 === void 0 ? void 0 : _conversation$lastMes2.timestamp) || conversation.createdAt,\n          unread: conversation.lastMessage ? !conversation.lastMessage.isRead && conversation.lastMessage.senderId !== user.id : false,\n          avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(conversation.otherUser.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n          status: onlineUsers.has(conversation.otherUser.id) ? 'online' : 'offline',\n          starred: false,\n          archived: false\n        };\n      });\n      setConversations(formattedConversations);\n    } catch (error) {\n      console.error('Konuşmalar yüklenirken hata:', error);\n      toast.error('Konuşmalar yüklenemedi');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Mesaj yükleme ve okundu işaretleme\n  useEffect(() => {\n    if (selectedConversation) {\n      loadMessages(selectedConversation.id);\n      markConversationAsRead(selectedConversation.id);\n    }\n  }, [selectedConversation]);\n\n  // Conversation'daki mesajları okundu olarak işaretle\n  const markConversationAsRead = async conversationId => {\n    try {\n      // Okunmamış mesajları bul\n      const unreadMessages = messages.filter(msg => msg.senderId !== user.id && !msg.read);\n      if (unreadMessages.length > 0) {\n        // Server'a okundu bilgisi gönder\n        await api.put(`/messages/conversations/${conversationId}/read`);\n\n        // Local state'i güncelle\n        setMessages(prev => prev.map(msg => msg.senderId !== user.id ? {\n          ...msg,\n          read: true\n        } : msg));\n\n        // Socket'e okundu bilgisi gönder\n        if (socket) {\n          socket.emit('mark_messages_read', {\n            conversationId,\n            messageIds: unreadMessages.map(msg => msg.id)\n          });\n        }\n      }\n    } catch (error) {\n      console.error('Mesajları okundu olarak işaretlerken hata:', error);\n    }\n  };\n\n  // Messages yükleme\n  const loadMessages = async conversationId => {\n    try {\n      const response = await api.get(`/messages/conversations/${conversationId}`);\n\n      // API verisini frontend formatına çevir\n      const formattedMessages = response.data.messages.map(message => ({\n        id: message.id,\n        senderId: message.senderId,\n        senderName: message.sender.name,\n        senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        text: message.content,\n        timestamp: message.createdAt,\n        read: message.isRead,\n        delivered: true,\n        // Veritabanından gelen mesajlar delivered olarak kabul edilir\n        attachments: message.attachments\n      }));\n      setMessages(formattedMessages);\n\n      // Scroll to bottom\n      setTimeout(() => {\n        var _messagesEndRef$curre2;\n        (_messagesEndRef$curre2 = messagesEndRef.current) === null || _messagesEndRef$curre2 === void 0 ? void 0 : _messagesEndRef$curre2.scrollIntoView({\n          behavior: 'smooth'\n        });\n      }, 100);\n    } catch (error) {\n      console.error('Mesajlar yüklenirken hata:', error);\n      toast.error('Mesajlar yüklenemedi');\n    }\n  };\n  // Mesaj gönderme\n  const sendMessage = async () => {\n    if (!messageText.trim() || !selectedConversation) return;\n    console.log('📤 Mesaj gönderiliyor:', {\n      receiverId: selectedConversation.clientId,\n      content: messageText.trim()\n    });\n    try {\n      const response = await api.post('/messages/send', {\n        receiverId: selectedConversation.clientId,\n        content: messageText.trim()\n      });\n      console.log('✅ Mesaj gönderildi:', response.data);\n\n      // Sadece mesaj input'unu temizle - mesaj socket'ten gelecek\n      setMessageText('');\n    } catch (error) {\n      console.error('Mesaj gönderilirken hata:', error);\n      toast.error('Mesaj gönderilemedi');\n    }\n  };\n  useEffect(() => {\n    // Mesajları otomatik kaydır\n    if (messagesEndRef.current && messagesContainerRef.current) {\n      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;\n    }\n  }, [messages]);\n\n  // Mesaj gönderme\n  const handleSendMessage = e => {\n    e.preventDefault();\n    sendMessage();\n  };\n\n  // Konuşma seçme\n  const handleSelectConversation = conversation => {\n    setSelectedConversation(conversation);\n  };\n\n  // Tarih formatı\n  const formatMessageDate = dateString => {\n    const date = new Date(dateString);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    if (date.toDateString() === today.toDateString()) {\n      return format(date, 'HH:mm', {\n        locale: tr\n      });\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Dün ' + format(date, 'HH:mm', {\n        locale: tr\n      });\n    } else {\n      return format(date, 'dd MMM HH:mm', {\n        locale: tr\n      });\n    }\n  };\n\n  // Konuşmaları filtrele\n  const filteredConversations = conversations.filter(conv => {\n    // Arama filtresi\n    const matchesSearch = conv.clientName.toLowerCase().includes(searchTerm.toLowerCase()) || conv.lastMessage.toLowerCase().includes(searchTerm.toLowerCase());\n\n    // Durum filtresi\n    const matchesFilter = filter === 'all' || filter === 'unread' && conv.unread || filter === 'archived' && conv.archived || filter === 'starred' && conv.starred;\n    return matchesSearch && matchesFilter;\n  });\n\n  // Konuşmayı yıldızla\n  const toggleStar = id => {\n    setConversations(prevConversations => prevConversations.map(conv => conv.id === id ? {\n      ...conv,\n      starred: !conv.starred\n    } : conv));\n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({\n        ...prev,\n        starred: !prev.starred\n      }));\n    }\n  };\n\n  // Konuşmayı arşivle\n  const toggleArchive = id => {\n    setConversations(prevConversations => prevConversations.map(conv => conv.id === id ? {\n      ...conv,\n      archived: !conv.archived\n    } : conv));\n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({\n        ...prev,\n        archived: !prev.archived\n      }));\n    }\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 453,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 452,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mx-auto px-4 py-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-teal-500 to-teal-700 shadow-lg rounded-lg p-6 mb-6 text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold\",\n            children: \"Mesajlar\\u0131m\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-1 text-indigo-100\",\n            children: \"Dan\\u0131\\u015Fanlar\\u0131n\\u0131zla olan t\\xFCm yaz\\u0131\\u015Fmalar\\u0131n\\u0131z\\u0131 buradan y\\xF6netebilirsiniz.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-3 sm:mt-0 flex space-x-2\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-indigo-800 bg-white hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-300\",\n            children: [/*#__PURE__*/_jsxDEV(ChatBubbleLeftEllipsisIcon, {\n              className: \"-ml-1 mr-2 h-5 w-5\",\n              \"aria-hidden\": \"true\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 15\n            }, this), \"Yeni Mesaj\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 461,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-md overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-12 h-[75vh]\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-span-12 md:col-span-4 border-r border-gray-200 flex flex-col\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 border-b border-gray-200 bg-white\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-semibold text-gray-800 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(ChatBubbleLeftEllipsisIcon, {\n                className: \"h-6 w-6 text-indigo-600 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 17\n              }, this), \"Mesajlar\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3 relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Konu\\u015Fmalarda ara...\",\n                className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                className: \"h-5 w-5 text-gray-400 absolute left-3 top-2.5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3 flex space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: `px-3 py-1 text-sm rounded-full ${filter === 'all' ? 'bg-indigo-100 text-indigo-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                onClick: () => setFilter('all'),\n                children: \"T\\xFCm\\xFC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 499,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `px-3 py-1 text-sm rounded-full ${filter === 'unread' ? 'bg-indigo-100 text-indigo-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                onClick: () => setFilter('unread'),\n                children: \"Okunmam\\u0131\\u015F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `px-3 py-1 text-sm rounded-full ${filter === 'starred' ? 'bg-indigo-100 text-indigo-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                onClick: () => setFilter('starred'),\n                children: \"Y\\u0131ld\\u0131zl\\u0131\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `px-3 py-1 text-sm rounded-full ${filter === 'archived' ? 'bg-indigo-100 text-indigo-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                onClick: () => setFilter('archived'),\n                children: \"Ar\\u015Fiv\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: conversationsRef,\n            style: {\n              height: 'calc(75vh - 145px)',\n              overflowY: 'auto',\n              scrollbarWidth: 'thin',\n              scrollbarColor: '#D1D5DB #F3F4F6'\n            },\n            children: filteredConversations.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 text-center text-gray-500\",\n              children: \"Hi\\xE7 mesaj\\u0131n\\u0131z yok\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 17\n            }, this) : filteredConversations.map(conversation => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `p-3 cursor-pointer hover:bg-gray-50 transition-colors duration-200 ${(selectedConversation === null || selectedConversation === void 0 ? void 0 : selectedConversation.id) === conversation.id ? 'bg-indigo-50' : ''} ${conversation.unread ? 'bg-indigo-50 hover:bg-indigo-100' : ''}`,\n              onClick: () => handleSelectConversation(conversation),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative flex-shrink-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: conversation.avatar,\n                    alt: conversation.clientName,\n                    className: `h-10 w-10 rounded-full ${(selectedConversation === null || selectedConversation === void 0 ? void 0 : selectedConversation.id) === conversation.id ? 'ring-2 ring-indigo-600' : ''}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 557,\n                    columnNumber: 25\n                  }, this), conversation.status === 'online' && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 567,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 556,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: `text-sm font-medium ${conversation.unread ? 'text-gray-900' : 'text-gray-700'}`,\n                      children: conversation.clientName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 572,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-1\",\n                      children: [conversation.starred && /*#__PURE__*/_jsxDEV(StarIcon, {\n                        className: \"h-4 w-4 text-yellow-400 fill-current\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 577,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-gray-500\",\n                        children: formatMessageDate(conversation.timestamp)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 579,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 575,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 571,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: `text-sm truncate mt-1 ${conversation.unread ? 'text-gray-900 font-medium' : 'text-gray-500'}`,\n                    children: conversation.lastMessage\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 584,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center mt-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-gray-500\",\n                      children: conversation.status === 'online' ? 'Çevrimiçi' : conversation.lastSeen ? `Son görülme: ${conversation.lastSeen}` : ''\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 590,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex space-x-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: e => {\n                          e.stopPropagation();\n                          toggleStar(conversation.id);\n                        },\n                        className: \"text-gray-400 hover:text-yellow-400\",\n                        children: /*#__PURE__*/_jsxDEV(StarIcon, {\n                          className: `h-4 w-4 ${conversation.starred ? 'text-yellow-400 fill-current' : ''}`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 605,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 598,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: e => {\n                          e.stopPropagation();\n                          toggleArchive(conversation.id);\n                        },\n                        className: \"text-gray-400 hover:text-gray-600\",\n                        children: /*#__PURE__*/_jsxDEV(ArchiveBoxIcon, {\n                          className: \"h-4 w-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 614,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 607,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 597,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 589,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 21\n              }, this), conversation.unread && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"inline-flex items-center ml-12 mt-1 px-2 py-0.5 rounded-full text-xs font-medium bg-indigo-600 text-white\",\n                children: \"Yeni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 621,\n                columnNumber: 23\n              }, this)]\n            }, conversation.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 482,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-span-12 md:col-span-8 flex flex-col\",\n          children: selectedConversation ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 border-b border-gray-200 bg-white flex justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative mr-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: selectedConversation.avatar,\n                    alt: selectedConversation.clientName,\n                    className: \"h-10 w-10 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 639,\n                    columnNumber: 23\n                  }, this), selectedConversation.status === 'online' && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 645,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 638,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-lg font-medium text-gray-800\",\n                    children: selectedConversation.clientName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 649,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500\",\n                    children: selectedConversation.status === 'online' ? 'Çevrimiçi' : selectedConversation.lastSeen ? `Son görülme: ${selectedConversation.lastSeen}` : ''\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 652,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 648,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 637,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                  children: /*#__PURE__*/_jsxDEV(PhoneIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 663,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 662,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                  children: /*#__PURE__*/_jsxDEV(VideoCameraIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 666,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 665,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                  children: /*#__PURE__*/_jsxDEV(InformationCircleIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 669,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 668,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                  children: /*#__PURE__*/_jsxDEV(EllipsisHorizontalIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 672,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 671,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 661,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 636,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: messagesContainerRef,\n              className: \"p-4 bg-gray-50\",\n              style: {\n                height: 'calc(75vh - 195px)',\n                overflowY: 'auto',\n                scrollbarWidth: 'thin',\n                scrollbarColor: '#D1D5DB #F3F4F6'\n              },\n              children: [messages.map((message, index) => {\n                const isSender = message.senderId === user.id;\n                const showAvatar = index === 0 || messages[index - 1].senderId !== message.senderId;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `flex ${isSender ? 'justify-end' : 'justify-start'} mb-4`,\n                  children: [!isSender && showAvatar && /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: message.senderAvatar,\n                    alt: message.senderName,\n                    className: \"h-8 w-8 rounded-full mr-2 mt-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 695,\n                    columnNumber: 27\n                  }, this), !isSender && !showAvatar && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 701,\n                    columnNumber: 54\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `max-w-xs md:max-w-md lg:max-w-lg rounded-lg px-4 py-2 ${isSender ? 'bg-indigo-600 text-white rounded-br-none' : 'bg-white text-gray-800 rounded-bl-none border border-gray-200'}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm\",\n                      children: message.text\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 709,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `text-xs mt-1 ${isSender ? 'text-indigo-200' : 'text-gray-500'} flex items-center justify-end`,\n                      children: [formatMessageDate(message.timestamp), isSender && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center ml-1\",\n                        children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                          className: `h-3 w-3 ${message.read ? 'text-blue-400' : 'text-gray-400'}`,\n                          title: message.delivered ? 'İletildi' : 'Gönderiliyor'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 715,\n                          columnNumber: 33\n                        }, this), message.read && /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                          className: \"h-3 w-3 -ml-1 text-blue-400\",\n                          title: \"Okundu\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 721,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 713,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 710,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 702,\n                    columnNumber: 25\n                  }, this), isSender && showAvatar && /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: message.senderAvatar,\n                    alt: message.senderName,\n                    className: \"h-8 w-8 rounded-full ml-2 mt-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 731,\n                    columnNumber: 27\n                  }, this), isSender && !showAvatar && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 ml-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 737,\n                    columnNumber: 53\n                  }, this)]\n                }, message.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 693,\n                  columnNumber: 23\n                }, this);\n              }), /*#__PURE__*/_jsxDEV(\"div\", {\n                ref: messagesEndRef\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 741,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 678,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 border-t border-gray-200 bg-white\",\n              children: /*#__PURE__*/_jsxDEV(\"form\", {\n                onSubmit: handleSendMessage,\n                className: \"flex items-end\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\",\n                  children: /*#__PURE__*/_jsxDEV(PaperClipIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 751,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 747,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    className: \"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none\",\n                    placeholder: \"Mesaj\\u0131n\\u0131z\\u0131 yaz\\u0131n...\",\n                    rows: \"2\",\n                    value: messageText,\n                    onChange: e => setMessageText(e.target.value),\n                    onKeyDown: e => {\n                      if (e.key === 'Enter' && !e.shiftKey) {\n                        e.preventDefault();\n                        sendMessage();\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 754,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 753,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\",\n                  children: /*#__PURE__*/_jsxDEV(FaceSmileIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 772,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 768,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"submit\",\n                  disabled: !messageText.trim(),\n                  className: `ml-2 p-2 rounded-full ${messageText.trim() ? 'bg-indigo-600 text-white hover:bg-indigo-700' : 'bg-gray-200 text-gray-400 cursor-not-allowed'} focus:outline-none`,\n                  children: /*#__PURE__*/_jsxDEV(PaperAirplaneIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 783,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 774,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 746,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 745,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) :\n          /*#__PURE__*/\n          // Mesaj seçilmediğinde\n          _jsxDEV(\"div\", {\n            className: \"flex-1 flex flex-col items-center justify-center p-6 bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full max-w-md text-center\",\n              children: [/*#__PURE__*/_jsxDEV(ChatBubbleLeftEllipsisIcon, {\n                className: \"h-16 w-16 text-gray-300 mb-4 mx-auto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 792,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-medium text-gray-800 mb-2\",\n                children: \"Mesajlar\\u0131n\\u0131z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 793,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-500 mx-auto\",\n                children: \"Mesajla\\u015Fmaya ba\\u015Flamak i\\xE7in sol taraftan bir konu\\u015Fma se\\xE7in veya yeni bir mesaj ba\\u015Flat\\u0131n.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 794,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 791,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 790,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 632,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 480,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 479,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 459,\n    columnNumber: 5\n  }, this);\n};\n_s(MessagesPage, \"8B7RxxYStSTw62EYx5HVIfMSfbM=\", false, function () {\n  return [useAuth];\n});\n_c = MessagesPage;\nexport default MessagesPage;\nvar _c;\n$RefreshReg$(_c, \"MessagesPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useAuth", "api", "toast", "io", "ChatBubbleLeftEllipsisIcon", "MagnifyingGlassIcon", "UserIcon", "PaperAirplaneIcon", "PaperClipIcon", "FaceSmileIcon", "UserCircleIcon", "EllipsisHorizontalIcon", "PhoneIcon", "VideoCameraIcon", "InformationCircleIcon", "ClockIcon", "CheckCircleIcon", "XMarkIcon", "TrashIcon", "ArchiveBoxIcon", "StarIcon", "format", "tr", "Link", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MessagesPage", "_s", "user", "isLoading", "setIsLoading", "conversations", "setConversations", "selectedConversation", "setSelectedConversation", "messages", "setMessages", "messageText", "setMessageText", "searchTerm", "setSearchTerm", "filter", "setFilter", "socket", "setSocket", "onlineUsers", "setOnlineUsers", "Set", "typingUsers", "setTypingUsers", "messagesEndRef", "conversationsRef", "messagesContainerRef", "token", "localStorage", "getItem", "id", "console", "log", "socketConnection", "process", "env", "REACT_APP_API_URL", "auth", "transports", "reconnection", "reconnectionDelay", "reconnectionAttempts", "timeout", "on", "connected", "rooms", "emit", "reason", "error", "attemptNumber", "heartbeatInterval", "setInterval", "response", "clearInterval", "disconnect", "hasToken", "<PERSON><PERSON>ser", "handleNewMessage", "message", "senderId", "conversationId", "prev", "map", "conv", "lastMessage", "content", "timestamp", "createdAt", "unread", "currentSelected", "messageExists", "some", "msg", "sender<PERSON>ame", "sender", "name", "senderAvatar", "encodeURIComponent", "text", "read", "isRead", "delivered", "setTimeout", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleMessageSent", "handleUserStatusChange", "data", "newSet", "status", "add", "userId", "delete", "handleUserTyping", "handleUserStoppedTyping", "handleMessagesRead", "messageIds", "includes", "off", "loadConversations", "joinedConversations", "setJoinedConversations", "length", "for<PERSON>ach", "conversation", "has", "get", "formattedConversations", "_conversation$lastMes", "_conversation$lastMes2", "clientId", "otherUser", "clientName", "avatar", "starred", "archived", "loadMessages", "markConversationAsRead", "unreadMessages", "put", "formattedMessages", "attachments", "_messagesEndRef$curre2", "sendMessage", "trim", "receiverId", "post", "scrollTop", "scrollHeight", "handleSendMessage", "e", "preventDefault", "handleSelectConversation", "formatMessageDate", "dateString", "date", "Date", "today", "yesterday", "setDate", "getDate", "toDateString", "locale", "filteredConversations", "matchesSearch", "toLowerCase", "matchesFilter", "toggleStar", "prevConversations", "toggleArchive", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "value", "onChange", "target", "onClick", "ref", "style", "height", "overflowY", "scrollbarWidth", "scrollbarColor", "src", "alt", "lastSeen", "stopPropagation", "index", "isSender", "showAvatar", "title", "onSubmit", "rows", "onKeyDown", "key", "shift<PERSON>ey", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Projeler/kidgarden/burky_root_web/client/src/pages/expert/messages/MessagesPage.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '../../../hooks/useAuth';\nimport api from '../../../services/api';\nimport { toast } from 'react-hot-toast';\nimport io from 'socket.io-client';\nimport {\n  ChatBubbleLeftEllipsisIcon,\n  MagnifyingGlassIcon,\n  UserIcon,\n  PaperAirplaneIcon,\n  PaperClipIcon,\n  FaceSmileIcon,\n  UserCircleIcon,\n  EllipsisHorizontalIcon,\n  PhoneIcon,\n  VideoCameraIcon,\n  InformationCircleIcon,\n  ClockIcon,\n  CheckCircleIcon,\n  XMarkIcon,\n  TrashIcon,\n  ArchiveBoxIcon,\n  StarIcon\n} from '@heroicons/react/24/outline';\nimport { format } from 'date-fns';\nimport { tr } from 'date-fns/locale';\nimport { Link } from 'react-router-dom';\n\n/**\n * <PERSON><PERSON> mesajlaşma sayfası\n */\nconst MessagesPage = () => {\n  const { user } = useAuth();\n  const [isLoading, setIsLoading] = useState(true);\n  const [conversations, setConversations] = useState([]);\n  const [selectedConversation, setSelectedConversation] = useState(null);\n  const [messages, setMessages] = useState([]);\n  const [messageText, setMessageText] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filter, setFilter] = useState('all'); // all, unread, archived, starred\n  const [socket, setSocket] = useState(null);\n  const [onlineUsers, setOnlineUsers] = useState(new Set());\n  const [typingUsers, setTypingUsers] = useState(new Set());\n  const messagesEndRef = useRef(null);\n  const conversationsRef = useRef(null);\n  const messagesContainerRef = useRef(null);\n\n  // Socket.IO connection - ayrı useEffect\n  useEffect(() => {\n    const token = localStorage.getItem('accessToken');\n    if (token && user?.id) {\n      console.log('🔌 EXPERT: Socket.IO bağlantısı kuruluyor... User ID:', user.id);\n      const socketConnection = io(process.env.REACT_APP_API_URL || 'http://localhost:5000', {\n        auth: { token },\n        transports: ['websocket', 'polling'],\n        reconnection: true,\n        reconnectionDelay: 1000,\n        reconnectionAttempts: 5,\n        timeout: 20000\n      });\n\n      setSocket(socketConnection);\n\n      // Connection events\n      socketConnection.on('connect', () => {\n        console.log('✅ EXPERT: Socket.IO bağlantısı kuruldu:', socketConnection.id);\n        console.log('🔗 EXPERT: Socket connected:', socketConnection.connected);\n        console.log('🏠 EXPERT: Socket rooms:', socketConnection.rooms);\n        // Kullanıcının online olduğunu bildir\n        socketConnection.emit('user_online');\n      });\n\n      socketConnection.on('disconnect', (reason) => {\n        console.log('❌ EXPERT: Socket.IO bağlantısı kesildi:', reason);\n      });\n\n      socketConnection.on('connect_error', (error) => {\n        console.error('🔥 EXPERT: Socket.IO bağlantı hatası:', error);\n      });\n\n      socketConnection.on('reconnect', (attemptNumber) => {\n        console.log('🔄 EXPERT: Socket.IO yeniden bağlandı, deneme:', attemptNumber);\n      });\n\n      socketConnection.on('reconnect_error', (error) => {\n        console.error('🔥 EXPERT: Socket.IO yeniden bağlanma hatası:', error);\n      });\n\n      // Heartbeat mekanizması\n      const heartbeatInterval = setInterval(() => {\n        if (socketConnection.connected) {\n          socketConnection.emit('ping', (response) => {\n            if (response === 'pong') {\n              console.log('💓 EXPERT: Heartbeat OK');\n            }\n          });\n        }\n      }, 30000); // 30 saniyede bir ping gönder\n\n      return () => {\n        clearInterval(heartbeatInterval);\n        socketConnection.disconnect();\n      };\n    } else {\n      console.log('❌ EXPERT: Socket bağlantısı kurulamıyor - token veya user eksik:', {\n        hasToken: !!token,\n        hasUser: !!user?.id\n      });\n    }\n  }, [user?.id]);\n\n  // Socket event listeners - ayrı useEffect\n  useEffect(() => {\n    if (socket && user?.id) {\n      console.log('🎧 Socket event listeners kuruluyor...');\n\n      // Socket event listeners\n      const handleNewMessage = (message) => {\n        console.log('📨 EXPERT: Yeni mesaj alındı:', message);\n        console.log('👤 EXPERT: Current user ID:', user.id);\n        console.log('💬 EXPERT: Message sender ID:', message.senderId);\n        console.log('🏠 EXPERT: Message conversation ID:', message.conversationId);\n\n        // Conversation listesini ÖNCE güncelle (her zaman)\n        setConversations(prev => {\n          console.log('📋 EXPERT: Updating conversations list');\n          return prev.map(conv =>\n            conv.id === message.conversationId\n              ? { ...conv, lastMessage: message.content, timestamp: message.createdAt, unread: message.senderId !== user.id }\n              : conv\n          );\n        });\n\n        // Eğer şu anda bu conversation açıksa, mesajı da ekle\n        setSelectedConversation(currentSelected => {\n          console.log('🎯 EXPERT: Current selected conversation:', currentSelected?.id);\n          if (currentSelected && message.conversationId === currentSelected.id) {\n            console.log('✅ EXPERT: Adding message to current conversation');\n            setMessages(prev => {\n              // Mesajın zaten var olup olmadığını kontrol et\n              const messageExists = prev.some(msg => msg.id === message.id);\n              if (messageExists) {\n                console.log('⚠️ EXPERT: Message already exists, skipping:', message.id);\n                return prev;\n              }\n\n              return [...prev, {\n                id: message.id,\n                senderId: message.senderId,\n                senderName: message.sender.name,\n                senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n                text: message.content,\n                timestamp: message.createdAt,\n                read: message.isRead,\n                delivered: true // Socket'ten gelen mesajlar delivered olarak kabul edilir\n              }];\n            });\n\n            // Scroll to bottom\n            setTimeout(() => {\n              messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n            }, 100);\n          } else {\n            console.log('❌ EXPERT: Not adding message - conversation not selected or different conversation');\n          }\n          return currentSelected;\n        });\n      };\n\n      const handleMessageSent = (message) => {\n        console.log('✅ Mesaj gönderildi onayı:', message);\n      };\n\n      const handleUserStatusChange = (data) => {\n        setOnlineUsers(prev => {\n          const newSet = new Set(prev);\n          if (data.status === 'online') {\n            newSet.add(data.userId);\n          } else {\n            newSet.delete(data.userId);\n          }\n          return newSet;\n        });\n      };\n\n      const handleUserTyping = (data) => {\n        setSelectedConversation(currentSelected => {\n          if (currentSelected && data.conversationId === currentSelected.id) {\n            setTypingUsers(prev => new Set([...prev, data.userId]));\n          }\n          return currentSelected;\n        });\n      };\n\n      const handleUserStoppedTyping = (data) => {\n        setTypingUsers(prev => {\n          const newSet = new Set(prev);\n          newSet.delete(data.userId);\n          return newSet;\n        });\n      };\n\n      const handleMessagesRead = (data) => {\n        console.log('👁️ EXPERT: Messages read event:', data);\n        // Mesajları okundu olarak işaretle\n        setMessages(prev => prev.map(msg =>\n          data.messageIds.includes(msg.id) ? { ...msg, read: true } : msg\n        ));\n      };\n\n      // Event listener'ları ekle\n      socket.on('new_message', handleNewMessage);\n      socket.on('message_sent', handleMessageSent);\n      socket.on('user_status_change', handleUserStatusChange);\n      socket.on('user_typing', handleUserTyping);\n      socket.on('user_stopped_typing', handleUserStoppedTyping);\n      socket.on('messages_read', handleMessagesRead);\n\n      // Cleanup function\n      return () => {\n        socket.off('new_message', handleNewMessage);\n        socket.off('message_sent', handleMessageSent);\n        socket.off('user_status_change', handleUserStatusChange);\n        socket.off('user_typing', handleUserTyping);\n        socket.off('user_stopped_typing', handleUserStoppedTyping);\n        socket.off('messages_read', handleMessagesRead);\n      };\n    }\n  }, [socket, user.id]);\n\n  // Conversations yükleme - ayrı useEffect\n  useEffect(() => {\n    loadConversations();\n  }, []);\n\n  // Socket bağlandığında tüm conversation'lara join ol - sadece bir kez\n  const [joinedConversations, setJoinedConversations] = useState(new Set());\n\n  useEffect(() => {\n    if (socket && conversations.length > 0) {\n      console.log('🏠 EXPERT: Tüm conversation\\'lara katılıyor...', conversations.length, 'conversation');\n\n      conversations.forEach(conversation => {\n        if (!joinedConversations.has(conversation.id)) {\n          socket.emit('join_conversation', conversation.id);\n          console.log(`🏠 EXPERT: Conversation ${conversation.id} katıldı`);\n          setJoinedConversations(prev => new Set([...prev, conversation.id]));\n        }\n      });\n    }\n  }, [socket, conversations, joinedConversations]);\n\n  // Conversations yükleme\n  const loadConversations = async () => {\n    try {\n      setIsLoading(true);\n      const response = await api.get('/messages/conversations');\n\n      // API verisini frontend formatına çevir\n      const formattedConversations = response.data.conversations.map(conversation => ({\n        id: conversation.id,\n        clientId: conversation.otherUser.id,\n        clientName: conversation.otherUser.name,\n        lastMessage: conversation.lastMessage?.content || 'Henüz mesaj yok',\n        timestamp: conversation.lastMessage?.timestamp || conversation.createdAt,\n        unread: conversation.lastMessage ? !conversation.lastMessage.isRead && conversation.lastMessage.senderId !== user.id : false,\n        avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(conversation.otherUser.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        status: onlineUsers.has(conversation.otherUser.id) ? 'online' : 'offline',\n        starred: false,\n        archived: false\n      }));\n\n      setConversations(formattedConversations);\n    } catch (error) {\n      console.error('Konuşmalar yüklenirken hata:', error);\n      toast.error('Konuşmalar yüklenemedi');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Mesaj yükleme ve okundu işaretleme\n  useEffect(() => {\n    if (selectedConversation) {\n      loadMessages(selectedConversation.id);\n      markConversationAsRead(selectedConversation.id);\n    }\n  }, [selectedConversation]);\n\n  // Conversation'daki mesajları okundu olarak işaretle\n  const markConversationAsRead = async (conversationId) => {\n    try {\n      // Okunmamış mesajları bul\n      const unreadMessages = messages.filter(msg =>\n        msg.senderId !== user.id && !msg.read\n      );\n\n      if (unreadMessages.length > 0) {\n        // Server'a okundu bilgisi gönder\n        await api.put(`/messages/conversations/${conversationId}/read`);\n\n        // Local state'i güncelle\n        setMessages(prev => prev.map(msg =>\n          msg.senderId !== user.id ? { ...msg, read: true } : msg\n        ));\n\n        // Socket'e okundu bilgisi gönder\n        if (socket) {\n          socket.emit('mark_messages_read', {\n            conversationId,\n            messageIds: unreadMessages.map(msg => msg.id)\n          });\n        }\n      }\n    } catch (error) {\n      console.error('Mesajları okundu olarak işaretlerken hata:', error);\n    }\n  };\n\n  // Messages yükleme\n  const loadMessages = async (conversationId) => {\n    try {\n      const response = await api.get(`/messages/conversations/${conversationId}`);\n\n      // API verisini frontend formatına çevir\n      const formattedMessages = response.data.messages.map(message => ({\n        id: message.id,\n        senderId: message.senderId,\n        senderName: message.sender.name,\n        senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        text: message.content,\n        timestamp: message.createdAt,\n        read: message.isRead,\n        delivered: true, // Veritabanından gelen mesajlar delivered olarak kabul edilir\n        attachments: message.attachments\n      }));\n\n      setMessages(formattedMessages);\n\n      // Scroll to bottom\n      setTimeout(() => {\n        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n      }, 100);\n    } catch (error) {\n      console.error('Mesajlar yüklenirken hata:', error);\n      toast.error('Mesajlar yüklenemedi');\n    }\n  };\n  // Mesaj gönderme\n  const sendMessage = async () => {\n    if (!messageText.trim() || !selectedConversation) return;\n\n    console.log('📤 Mesaj gönderiliyor:', {\n      receiverId: selectedConversation.clientId,\n      content: messageText.trim()\n    });\n\n    try {\n      const response = await api.post('/messages/send', {\n        receiverId: selectedConversation.clientId,\n        content: messageText.trim()\n      });\n\n      console.log('✅ Mesaj gönderildi:', response.data);\n\n      // Sadece mesaj input'unu temizle - mesaj socket'ten gelecek\n      setMessageText('');\n\n    } catch (error) {\n      console.error('Mesaj gönderilirken hata:', error);\n      toast.error('Mesaj gönderilemedi');\n    }\n  };\n\n  useEffect(() => {\n    // Mesajları otomatik kaydır\n    if (messagesEndRef.current && messagesContainerRef.current) {\n      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;\n    }\n  }, [messages]);\n\n  // Mesaj gönderme\n  const handleSendMessage = (e) => {\n    e.preventDefault();\n    sendMessage();\n  };\n\n  // Konuşma seçme\n  const handleSelectConversation = (conversation) => {\n    setSelectedConversation(conversation);\n  };\n\n  // Tarih formatı\n  const formatMessageDate = (dateString) => {\n    const date = new Date(dateString);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    \n    if (date.toDateString() === today.toDateString()) {\n      return format(date, 'HH:mm', { locale: tr });\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Dün ' + format(date, 'HH:mm', { locale: tr });\n    } else {\n      return format(date, 'dd MMM HH:mm', { locale: tr });\n    }\n  };\n\n  // Konuşmaları filtrele\n  const filteredConversations = conversations.filter(conv => {\n    // Arama filtresi\n    const matchesSearch = conv.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         conv.lastMessage.toLowerCase().includes(searchTerm.toLowerCase());\n    \n    // Durum filtresi\n    const matchesFilter = filter === 'all' ||\n                         (filter === 'unread' && conv.unread) ||\n                         (filter === 'archived' && conv.archived) ||\n                         (filter === 'starred' && conv.starred);\n                        \n    return matchesSearch && matchesFilter;\n  });\n\n  // Konuşmayı yıldızla\n  const toggleStar = (id) => {\n    setConversations(prevConversations =>\n      prevConversations.map(conv =>\n        conv.id === id ? { ...conv, starred: !conv.starred } : conv\n      )\n    );\n    \n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({ ...prev, starred: !prev.starred }));\n    }\n  };\n\n  // Konuşmayı arşivle\n  const toggleArchive = (id) => {\n    setConversations(prevConversations =>\n      prevConversations.map(conv =>\n        conv.id === id ? { ...conv, archived: !conv.archived } : conv\n      )\n    );\n    \n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({ ...prev, archived: !prev.archived }));\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto px-4 py-6\">\n      {/* Sayfa Başlığı */}\n      <div className=\"bg-gradient-to-r from-teal-500 to-teal-700 shadow-lg rounded-lg p-6 mb-6 text-white\">\n        <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center\">\n          <div>\n            <h1 className=\"text-2xl font-bold\">Mesajlarım</h1>\n            <p className=\"mt-1 text-indigo-100\">\n              Danışanlarınızla olan tüm yazışmalarınızı buradan yönetebilirsiniz.\n            </p>\n          </div>\n          <div className=\"mt-3 sm:mt-0 flex space-x-2\">\n            <button className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-indigo-800 bg-white hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-300\">\n              <ChatBubbleLeftEllipsisIcon className=\"-ml-1 mr-2 h-5 w-5\" aria-hidden=\"true\" />\n              Yeni Mesaj\n            </button>\n          </div>\n        </div>\n      </div>\n      \n      {/* Mesajlaşma arayüzü */}\n      <div className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n        <div className=\"grid grid-cols-12 h-[75vh]\">\n          {/* Sol Kenar - Konuşma Listesi */}\n          <div className=\"col-span-12 md:col-span-4 border-r border-gray-200 flex flex-col\">\n            <div className=\"p-4 border-b border-gray-200 bg-white\">\n              <h1 className=\"text-xl font-semibold text-gray-800 flex items-center\">\n                <ChatBubbleLeftEllipsisIcon className=\"h-6 w-6 text-indigo-600 mr-2\" />\n                Mesajlar\n              </h1>\n              <div className=\"mt-3 relative\">\n                <input\n                  type=\"text\"\n                  placeholder=\"Konuşmalarda ara...\"\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                />\n                <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400 absolute left-3 top-2.5\" />\n              </div>\n              <div className=\"mt-3 flex space-x-2\">\n                <button\n                  className={`px-3 py-1 text-sm rounded-full ${filter === 'all' \n                    ? 'bg-indigo-100 text-indigo-800' \n                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                  onClick={() => setFilter('all')}\n                >\n                  Tümü\n                </button>\n                <button\n                  className={`px-3 py-1 text-sm rounded-full ${filter === 'unread' \n                    ? 'bg-indigo-100 text-indigo-800' \n                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                  onClick={() => setFilter('unread')}\n                >\n                  Okunmamış\n                </button>\n                <button\n                  className={`px-3 py-1 text-sm rounded-full ${filter === 'starred' \n                    ? 'bg-indigo-100 text-indigo-800' \n                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                  onClick={() => setFilter('starred')}\n                >\n                  Yıldızlı\n                </button>\n                <button\n                  className={`px-3 py-1 text-sm rounded-full ${filter === 'archived' \n                    ? 'bg-indigo-100 text-indigo-800' \n                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                  onClick={() => setFilter('archived')}\n                >\n                  Arşiv\n                </button>\n              </div>\n            </div>\n            <div \n              ref={conversationsRef}\n              style={{\n                height: 'calc(75vh - 145px)',\n                overflowY: 'auto',\n                scrollbarWidth: 'thin',\n                scrollbarColor: '#D1D5DB #F3F4F6'\n              }}\n            >\n              {filteredConversations.length === 0 ? (\n                <div className=\"p-4 text-center text-gray-500\">\n                  Hiç mesajınız yok\n                </div>\n              ) : (\n                filteredConversations.map(conversation => (\n                  <div\n                    key={conversation.id}\n                    className={`p-3 cursor-pointer hover:bg-gray-50 transition-colors duration-200 ${\n                      selectedConversation?.id === conversation.id ? 'bg-indigo-50' : ''\n                    } ${conversation.unread ? 'bg-indigo-50 hover:bg-indigo-100' : ''}`}\n                    onClick={() => handleSelectConversation(conversation)}\n                  >\n                    <div className=\"flex items-start space-x-3\">\n                      <div className=\"relative flex-shrink-0\">\n                        <img\n                          src={conversation.avatar}\n                          alt={conversation.clientName}\n                          className={`h-10 w-10 rounded-full ${\n                            selectedConversation?.id === conversation.id \n                              ? 'ring-2 ring-indigo-600' \n                              : ''\n                          }`}\n                        />\n                        {conversation.status === 'online' && (\n                          <span className=\"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"></span>\n                        )}\n                      </div>\n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex justify-between items-start\">\n                          <h3 className={`text-sm font-medium ${conversation.unread ? 'text-gray-900' : 'text-gray-700'}`}>\n                            {conversation.clientName}\n                          </h3>\n                          <div className=\"flex items-center space-x-1\">\n                            {conversation.starred && (\n                              <StarIcon className=\"h-4 w-4 text-yellow-400 fill-current\" />\n                            )}\n                            <span className=\"text-xs text-gray-500\">\n                              {formatMessageDate(conversation.timestamp)}\n                            </span>\n                          </div>\n                        </div>\n                        <p className={`text-sm truncate mt-1 ${\n                          conversation.unread ? 'text-gray-900 font-medium' : 'text-gray-500'\n                        }`}>\n                          {conversation.lastMessage}\n                        </p>\n                        <div className=\"flex justify-between items-center mt-1\">\n                          <span className=\"text-xs text-gray-500\">\n                            {conversation.status === 'online' \n                              ? 'Çevrimiçi' \n                              : conversation.lastSeen \n                                ? `Son görülme: ${conversation.lastSeen}` \n                                : ''}\n                          </span>\n                          <div className=\"flex space-x-1\">\n                            <button \n                              onClick={(e) => {\n                                e.stopPropagation();\n                                toggleStar(conversation.id);\n                              }}\n                              className=\"text-gray-400 hover:text-yellow-400\"\n                            >\n                              <StarIcon className={`h-4 w-4 ${conversation.starred ? 'text-yellow-400 fill-current' : ''}`} />\n                            </button>\n                            <button \n                              onClick={(e) => {\n                                e.stopPropagation();\n                                toggleArchive(conversation.id);\n                              }}\n                              className=\"text-gray-400 hover:text-gray-600\"\n                            >\n                              <ArchiveBoxIcon className=\"h-4 w-4\" />\n                            </button>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {conversation.unread && (\n                      <span className=\"inline-flex items-center ml-12 mt-1 px-2 py-0.5 rounded-full text-xs font-medium bg-indigo-600 text-white\">\n                        Yeni\n                      </span>\n                    )}\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n\n          {/* Sağ Taraf - Mesaj Alanı */}\n          <div className=\"col-span-12 md:col-span-8 flex flex-col\">\n            {selectedConversation ? (\n              <>\n                {/* Mesajlaşma Başlığı */}\n                <div className=\"p-4 border-b border-gray-200 bg-white flex justify-between items-center\">\n                  <div className=\"flex items-center\">\n                    <div className=\"relative mr-3\">\n                      <img\n                        src={selectedConversation.avatar}\n                        alt={selectedConversation.clientName}\n                        className=\"h-10 w-10 rounded-full\"\n                      />\n                      {selectedConversation.status === 'online' && (\n                        <span className=\"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"></span>\n                      )}\n                    </div>\n                    <div>\n                      <h2 className=\"text-lg font-medium text-gray-800\">\n                        {selectedConversation.clientName}\n                      </h2>\n                      <p className=\"text-xs text-gray-500\">\n                        {selectedConversation.status === 'online' \n                          ? 'Çevrimiçi' \n                          : selectedConversation.lastSeen \n                            ? `Son görülme: ${selectedConversation.lastSeen}` \n                            : ''}\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <button className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\">\n                      <PhoneIcon className=\"h-5 w-5\" />\n                    </button>\n                    <button className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\">\n                      <VideoCameraIcon className=\"h-5 w-5\" />\n                    </button>\n                    <button className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\">\n                      <InformationCircleIcon className=\"h-5 w-5\" />\n                    </button>\n                    <button className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\">\n                      <EllipsisHorizontalIcon className=\"h-5 w-5\" />\n                    </button>\n                  </div>\n                </div>\n\n                {/* Mesaj Alanı */}\n                <div \n                  ref={messagesContainerRef}\n                  className=\"p-4 bg-gray-50\"\n                  style={{\n                    height: 'calc(75vh - 195px)',\n                    overflowY: 'auto',\n                    scrollbarWidth: 'thin',\n                    scrollbarColor: '#D1D5DB #F3F4F6'\n                  }}\n                >\n                  {messages.map((message, index) => {\n                    const isSender = message.senderId === user.id;\n                    const showAvatar = index === 0 || messages[index - 1].senderId !== message.senderId;\n                    \n                    return (\n                      <div key={message.id} className={`flex ${isSender ? 'justify-end' : 'justify-start'} mb-4`}>\n                        {!isSender && showAvatar && (\n                          <img \n                            src={message.senderAvatar} \n                            alt={message.senderName}\n                            className=\"h-8 w-8 rounded-full mr-2 mt-1\"\n                          />\n                        )}\n                        {!isSender && !showAvatar && <div className=\"w-8 mr-2\"></div>}\n                        <div \n                          className={`max-w-xs md:max-w-md lg:max-w-lg rounded-lg px-4 py-2 ${\n                            isSender \n                              ? 'bg-indigo-600 text-white rounded-br-none' \n                              : 'bg-white text-gray-800 rounded-bl-none border border-gray-200'\n                          }`}\n                        >\n                          <p className=\"text-sm\">{message.text}</p>\n                          <div className={`text-xs mt-1 ${isSender ? 'text-indigo-200' : 'text-gray-500'} flex items-center justify-end`}>\n                            {formatMessageDate(message.timestamp)}\n                            {isSender && (\n                              <div className=\"flex items-center ml-1\">\n                                {/* Tek tik - İletildi */}\n                                <CheckCircleIcon\n                                  className={`h-3 w-3 ${message.read ? 'text-blue-400' : 'text-gray-400'}`}\n                                  title={message.delivered ? 'İletildi' : 'Gönderiliyor'}\n                                />\n                                {/* Çift tik - Okundu */}\n                                {message.read && (\n                                  <CheckCircleIcon\n                                    className=\"h-3 w-3 -ml-1 text-blue-400\"\n                                    title=\"Okundu\"\n                                  />\n                                )}\n                              </div>\n                            )}\n                          </div>\n                        </div>\n                        {isSender && showAvatar && (\n                          <img \n                            src={message.senderAvatar}\n                            alt={message.senderName}\n                            className=\"h-8 w-8 rounded-full ml-2 mt-1\"\n                          />\n                        )}\n                        {isSender && !showAvatar && <div className=\"w-8 ml-2\"></div>}\n                      </div>\n                    );\n                  })}\n                  <div ref={messagesEndRef} />\n                </div>\n\n                {/* Mesaj Giriş Alanı */}\n                <div className=\"p-3 border-t border-gray-200 bg-white\">\n                  <form onSubmit={handleSendMessage} className=\"flex items-end\">\n                    <button\n                      type=\"button\"\n                      className=\"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\"\n                    >\n                      <PaperClipIcon className=\"h-5 w-5\" />\n                    </button>\n                    <div className=\"flex-1 mx-2\">\n                      <textarea\n                        className=\"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none\"\n                        placeholder=\"Mesajınızı yazın...\"\n                        rows=\"2\"\n                        value={messageText}\n                        onChange={(e) => setMessageText(e.target.value)}\n                        onKeyDown={(e) => {\n                          if (e.key === 'Enter' && !e.shiftKey) {\n                            e.preventDefault();\n                            sendMessage();\n                          }\n                        }}\n                      ></textarea>\n                    </div>\n                    <button\n                      type=\"button\"\n                      className=\"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\"\n                    >\n                      <FaceSmileIcon className=\"h-5 w-5\" />\n                    </button>\n                    <button\n                      type=\"submit\"\n                      disabled={!messageText.trim()}\n                      className={`ml-2 p-2 rounded-full ${\n                        messageText.trim() \n                          ? 'bg-indigo-600 text-white hover:bg-indigo-700' \n                          : 'bg-gray-200 text-gray-400 cursor-not-allowed'\n                      } focus:outline-none`}\n                    >\n                      <PaperAirplaneIcon className=\"h-5 w-5\" />\n                    </button>\n                  </form>\n                </div>\n              </>\n            ) : (\n              // Mesaj seçilmediğinde\n              <div className=\"flex-1 flex flex-col items-center justify-center p-6 bg-gray-50\">\n                <div className=\"w-full max-w-md text-center\">\n                  <ChatBubbleLeftEllipsisIcon className=\"h-16 w-16 text-gray-300 mb-4 mx-auto\" />\n                  <h3 className=\"text-xl font-medium text-gray-800 mb-2\">Mesajlarınız</h3>\n                  <p className=\"text-gray-500 mx-auto\">\n                    Mesajlaşmaya başlamak için sol taraftan bir konuşma seçin veya yeni bir mesaj başlatın.\n                  </p>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default MessagesPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,GAAG,MAAM,uBAAuB;AACvC,SAASC,KAAK,QAAQ,iBAAiB;AACvC,OAAOC,EAAE,MAAM,kBAAkB;AACjC,SACEC,0BAA0B,EAC1BC,mBAAmB,EACnBC,QAAQ,EACRC,iBAAiB,EACjBC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,sBAAsB,EACtBC,SAAS,EACTC,eAAe,EACfC,qBAAqB,EACrBC,SAAS,EACTC,eAAe,EACfC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,QAAQ,QACH,6BAA6B;AACpC,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,EAAE,QAAQ,iBAAiB;AACpC,SAASC,IAAI,QAAQ,kBAAkB;;AAEvC;AACA;AACA;AAFA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGA,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC;EAAK,CAAC,GAAG9B,OAAO,CAAC,CAAC;EAC1B,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACoC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACsC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAACwC,QAAQ,EAAEC,WAAW,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC0C,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC4C,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC8C,MAAM,EAAEC,SAAS,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACgD,MAAM,EAAEC,SAAS,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAC,IAAIoD,GAAG,CAAC,CAAC,CAAC;EACzD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtD,QAAQ,CAAC,IAAIoD,GAAG,CAAC,CAAC,CAAC;EACzD,MAAMG,cAAc,GAAGrD,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMsD,gBAAgB,GAAGtD,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMuD,oBAAoB,GAAGvD,MAAM,CAAC,IAAI,CAAC;;EAEzC;EACAD,SAAS,CAAC,MAAM;IACd,MAAMyD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACjD,IAAIF,KAAK,IAAIzB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE4B,EAAE,EAAE;MACrBC,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAE9B,IAAI,CAAC4B,EAAE,CAAC;MAC7E,MAAMG,gBAAgB,GAAG1D,EAAE,CAAC2D,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB,EAAE;QACpFC,IAAI,EAAE;UAAEV;QAAM,CAAC;QACfW,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;QACpCC,YAAY,EAAE,IAAI;QAClBC,iBAAiB,EAAE,IAAI;QACvBC,oBAAoB,EAAE,CAAC;QACvBC,OAAO,EAAE;MACX,CAAC,CAAC;MAEFxB,SAAS,CAACe,gBAAgB,CAAC;;MAE3B;MACAA,gBAAgB,CAACU,EAAE,CAAC,SAAS,EAAE,MAAM;QACnCZ,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEC,gBAAgB,CAACH,EAAE,CAAC;QAC3EC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEC,gBAAgB,CAACW,SAAS,CAAC;QACvEb,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEC,gBAAgB,CAACY,KAAK,CAAC;QAC/D;QACAZ,gBAAgB,CAACa,IAAI,CAAC,aAAa,CAAC;MACtC,CAAC,CAAC;MAEFb,gBAAgB,CAACU,EAAE,CAAC,YAAY,EAAGI,MAAM,IAAK;QAC5ChB,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEe,MAAM,CAAC;MAChE,CAAC,CAAC;MAEFd,gBAAgB,CAACU,EAAE,CAAC,eAAe,EAAGK,KAAK,IAAK;QAC9CjB,OAAO,CAACiB,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC/D,CAAC,CAAC;MAEFf,gBAAgB,CAACU,EAAE,CAAC,WAAW,EAAGM,aAAa,IAAK;QAClDlB,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEiB,aAAa,CAAC;MAC9E,CAAC,CAAC;MAEFhB,gBAAgB,CAACU,EAAE,CAAC,iBAAiB,EAAGK,KAAK,IAAK;QAChDjB,OAAO,CAACiB,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACvE,CAAC,CAAC;;MAEF;MACA,MAAME,iBAAiB,GAAGC,WAAW,CAAC,MAAM;QAC1C,IAAIlB,gBAAgB,CAACW,SAAS,EAAE;UAC9BX,gBAAgB,CAACa,IAAI,CAAC,MAAM,EAAGM,QAAQ,IAAK;YAC1C,IAAIA,QAAQ,KAAK,MAAM,EAAE;cACvBrB,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;YACxC;UACF,CAAC,CAAC;QACJ;MACF,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;MAEX,OAAO,MAAM;QACXqB,aAAa,CAACH,iBAAiB,CAAC;QAChCjB,gBAAgB,CAACqB,UAAU,CAAC,CAAC;MAC/B,CAAC;IACH,CAAC,MAAM;MACLvB,OAAO,CAACC,GAAG,CAAC,kEAAkE,EAAE;QAC9EuB,QAAQ,EAAE,CAAC,CAAC5B,KAAK;QACjB6B,OAAO,EAAE,CAAC,EAACtD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE4B,EAAE;MACrB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC5B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4B,EAAE,CAAC,CAAC;;EAEd;EACA5D,SAAS,CAAC,MAAM;IACd,IAAI+C,MAAM,IAAIf,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE4B,EAAE,EAAE;MACtBC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;MAErD;MACA,MAAMyB,gBAAgB,GAAIC,OAAO,IAAK;QACpC3B,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE0B,OAAO,CAAC;QACrD3B,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE9B,IAAI,CAAC4B,EAAE,CAAC;QACnDC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE0B,OAAO,CAACC,QAAQ,CAAC;QAC9D5B,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE0B,OAAO,CAACE,cAAc,CAAC;;QAE1E;QACAtD,gBAAgB,CAACuD,IAAI,IAAI;UACvB9B,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;UACrD,OAAO6B,IAAI,CAACC,GAAG,CAACC,IAAI,IAClBA,IAAI,CAACjC,EAAE,KAAK4B,OAAO,CAACE,cAAc,GAC9B;YAAE,GAAGG,IAAI;YAAEC,WAAW,EAAEN,OAAO,CAACO,OAAO;YAAEC,SAAS,EAAER,OAAO,CAACS,SAAS;YAAEC,MAAM,EAAEV,OAAO,CAACC,QAAQ,KAAKzD,IAAI,CAAC4B;UAAG,CAAC,GAC7GiC,IACN,CAAC;QACH,CAAC,CAAC;;QAEF;QACAvD,uBAAuB,CAAC6D,eAAe,IAAI;UACzCtC,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEqC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEvC,EAAE,CAAC;UAC7E,IAAIuC,eAAe,IAAIX,OAAO,CAACE,cAAc,KAAKS,eAAe,CAACvC,EAAE,EAAE;YACpEC,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;YAC/DtB,WAAW,CAACmD,IAAI,IAAI;cAClB;cACA,MAAMS,aAAa,GAAGT,IAAI,CAACU,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC1C,EAAE,KAAK4B,OAAO,CAAC5B,EAAE,CAAC;cAC7D,IAAIwC,aAAa,EAAE;gBACjBvC,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE0B,OAAO,CAAC5B,EAAE,CAAC;gBACvE,OAAO+B,IAAI;cACb;cAEA,OAAO,CAAC,GAAGA,IAAI,EAAE;gBACf/B,EAAE,EAAE4B,OAAO,CAAC5B,EAAE;gBACd6B,QAAQ,EAAED,OAAO,CAACC,QAAQ;gBAC1Bc,UAAU,EAAEf,OAAO,CAACgB,MAAM,CAACC,IAAI;gBAC/BC,YAAY,EAAE,oCAAoCC,kBAAkB,CAACnB,OAAO,CAACgB,MAAM,CAACC,IAAI,CAAC,qDAAqD;gBAC9IG,IAAI,EAAEpB,OAAO,CAACO,OAAO;gBACrBC,SAAS,EAAER,OAAO,CAACS,SAAS;gBAC5BY,IAAI,EAAErB,OAAO,CAACsB,MAAM;gBACpBC,SAAS,EAAE,IAAI,CAAC;cAClB,CAAC,CAAC;YACJ,CAAC,CAAC;;YAEF;YACAC,UAAU,CAAC,MAAM;cAAA,IAAAC,qBAAA;cACf,CAAAA,qBAAA,GAAA3D,cAAc,CAAC4D,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;gBAAEC,QAAQ,EAAE;cAAS,CAAC,CAAC;YAChE,CAAC,EAAE,GAAG,CAAC;UACT,CAAC,MAAM;YACLvD,OAAO,CAACC,GAAG,CAAC,oFAAoF,CAAC;UACnG;UACA,OAAOqC,eAAe;QACxB,CAAC,CAAC;MACJ,CAAC;MAED,MAAMkB,iBAAiB,GAAI7B,OAAO,IAAK;QACrC3B,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE0B,OAAO,CAAC;MACnD,CAAC;MAED,MAAM8B,sBAAsB,GAAIC,IAAI,IAAK;QACvCrE,cAAc,CAACyC,IAAI,IAAI;UACrB,MAAM6B,MAAM,GAAG,IAAIrE,GAAG,CAACwC,IAAI,CAAC;UAC5B,IAAI4B,IAAI,CAACE,MAAM,KAAK,QAAQ,EAAE;YAC5BD,MAAM,CAACE,GAAG,CAACH,IAAI,CAACI,MAAM,CAAC;UACzB,CAAC,MAAM;YACLH,MAAM,CAACI,MAAM,CAACL,IAAI,CAACI,MAAM,CAAC;UAC5B;UACA,OAAOH,MAAM;QACf,CAAC,CAAC;MACJ,CAAC;MAED,MAAMK,gBAAgB,GAAIN,IAAI,IAAK;QACjCjF,uBAAuB,CAAC6D,eAAe,IAAI;UACzC,IAAIA,eAAe,IAAIoB,IAAI,CAAC7B,cAAc,KAAKS,eAAe,CAACvC,EAAE,EAAE;YACjEP,cAAc,CAACsC,IAAI,IAAI,IAAIxC,GAAG,CAAC,CAAC,GAAGwC,IAAI,EAAE4B,IAAI,CAACI,MAAM,CAAC,CAAC,CAAC;UACzD;UACA,OAAOxB,eAAe;QACxB,CAAC,CAAC;MACJ,CAAC;MAED,MAAM2B,uBAAuB,GAAIP,IAAI,IAAK;QACxClE,cAAc,CAACsC,IAAI,IAAI;UACrB,MAAM6B,MAAM,GAAG,IAAIrE,GAAG,CAACwC,IAAI,CAAC;UAC5B6B,MAAM,CAACI,MAAM,CAACL,IAAI,CAACI,MAAM,CAAC;UAC1B,OAAOH,MAAM;QACf,CAAC,CAAC;MACJ,CAAC;MAED,MAAMO,kBAAkB,GAAIR,IAAI,IAAK;QACnC1D,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEyD,IAAI,CAAC;QACrD;QACA/E,WAAW,CAACmD,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACU,GAAG,IAC9BiB,IAAI,CAACS,UAAU,CAACC,QAAQ,CAAC3B,GAAG,CAAC1C,EAAE,CAAC,GAAG;UAAE,GAAG0C,GAAG;UAAEO,IAAI,EAAE;QAAK,CAAC,GAAGP,GAC9D,CAAC,CAAC;MACJ,CAAC;;MAED;MACAvD,MAAM,CAAC0B,EAAE,CAAC,aAAa,EAAEc,gBAAgB,CAAC;MAC1CxC,MAAM,CAAC0B,EAAE,CAAC,cAAc,EAAE4C,iBAAiB,CAAC;MAC5CtE,MAAM,CAAC0B,EAAE,CAAC,oBAAoB,EAAE6C,sBAAsB,CAAC;MACvDvE,MAAM,CAAC0B,EAAE,CAAC,aAAa,EAAEoD,gBAAgB,CAAC;MAC1C9E,MAAM,CAAC0B,EAAE,CAAC,qBAAqB,EAAEqD,uBAAuB,CAAC;MACzD/E,MAAM,CAAC0B,EAAE,CAAC,eAAe,EAAEsD,kBAAkB,CAAC;;MAE9C;MACA,OAAO,MAAM;QACXhF,MAAM,CAACmF,GAAG,CAAC,aAAa,EAAE3C,gBAAgB,CAAC;QAC3CxC,MAAM,CAACmF,GAAG,CAAC,cAAc,EAAEb,iBAAiB,CAAC;QAC7CtE,MAAM,CAACmF,GAAG,CAAC,oBAAoB,EAAEZ,sBAAsB,CAAC;QACxDvE,MAAM,CAACmF,GAAG,CAAC,aAAa,EAAEL,gBAAgB,CAAC;QAC3C9E,MAAM,CAACmF,GAAG,CAAC,qBAAqB,EAAEJ,uBAAuB,CAAC;QAC1D/E,MAAM,CAACmF,GAAG,CAAC,eAAe,EAAEH,kBAAkB,CAAC;MACjD,CAAC;IACH;EACF,CAAC,EAAE,CAAChF,MAAM,EAAEf,IAAI,CAAC4B,EAAE,CAAC,CAAC;;EAErB;EACA5D,SAAS,CAAC,MAAM;IACdmI,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGtI,QAAQ,CAAC,IAAIoD,GAAG,CAAC,CAAC,CAAC;EAEzEnD,SAAS,CAAC,MAAM;IACd,IAAI+C,MAAM,IAAIZ,aAAa,CAACmG,MAAM,GAAG,CAAC,EAAE;MACtCzE,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE3B,aAAa,CAACmG,MAAM,EAAE,cAAc,CAAC;MAEnGnG,aAAa,CAACoG,OAAO,CAACC,YAAY,IAAI;QACpC,IAAI,CAACJ,mBAAmB,CAACK,GAAG,CAACD,YAAY,CAAC5E,EAAE,CAAC,EAAE;UAC7Cb,MAAM,CAAC6B,IAAI,CAAC,mBAAmB,EAAE4D,YAAY,CAAC5E,EAAE,CAAC;UACjDC,OAAO,CAACC,GAAG,CAAC,2BAA2B0E,YAAY,CAAC5E,EAAE,UAAU,CAAC;UACjEyE,sBAAsB,CAAC1C,IAAI,IAAI,IAAIxC,GAAG,CAAC,CAAC,GAAGwC,IAAI,EAAE6C,YAAY,CAAC5E,EAAE,CAAC,CAAC,CAAC;QACrE;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACb,MAAM,EAAEZ,aAAa,EAAEiG,mBAAmB,CAAC,CAAC;;EAEhD;EACA,MAAMD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFjG,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMgD,QAAQ,GAAG,MAAM/E,GAAG,CAACuI,GAAG,CAAC,yBAAyB,CAAC;;MAEzD;MACA,MAAMC,sBAAsB,GAAGzD,QAAQ,CAACqC,IAAI,CAACpF,aAAa,CAACyD,GAAG,CAAC4C,YAAY;QAAA,IAAAI,qBAAA,EAAAC,sBAAA;QAAA,OAAK;UAC9EjF,EAAE,EAAE4E,YAAY,CAAC5E,EAAE;UACnBkF,QAAQ,EAAEN,YAAY,CAACO,SAAS,CAACnF,EAAE;UACnCoF,UAAU,EAAER,YAAY,CAACO,SAAS,CAACtC,IAAI;UACvCX,WAAW,EAAE,EAAA8C,qBAAA,GAAAJ,YAAY,CAAC1C,WAAW,cAAA8C,qBAAA,uBAAxBA,qBAAA,CAA0B7C,OAAO,KAAI,iBAAiB;UACnEC,SAAS,EAAE,EAAA6C,sBAAA,GAAAL,YAAY,CAAC1C,WAAW,cAAA+C,sBAAA,uBAAxBA,sBAAA,CAA0B7C,SAAS,KAAIwC,YAAY,CAACvC,SAAS;UACxEC,MAAM,EAAEsC,YAAY,CAAC1C,WAAW,GAAG,CAAC0C,YAAY,CAAC1C,WAAW,CAACgB,MAAM,IAAI0B,YAAY,CAAC1C,WAAW,CAACL,QAAQ,KAAKzD,IAAI,CAAC4B,EAAE,GAAG,KAAK;UAC5HqF,MAAM,EAAE,oCAAoCtC,kBAAkB,CAAC6B,YAAY,CAACO,SAAS,CAACtC,IAAI,CAAC,qDAAqD;UAChJgB,MAAM,EAAExE,WAAW,CAACwF,GAAG,CAACD,YAAY,CAACO,SAAS,CAACnF,EAAE,CAAC,GAAG,QAAQ,GAAG,SAAS;UACzEsF,OAAO,EAAE,KAAK;UACdC,QAAQ,EAAE;QACZ,CAAC;MAAA,CAAC,CAAC;MAEH/G,gBAAgB,CAACuG,sBAAsB,CAAC;IAC1C,CAAC,CAAC,OAAO7D,KAAK,EAAE;MACdjB,OAAO,CAACiB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD1E,KAAK,CAAC0E,KAAK,CAAC,wBAAwB,CAAC;IACvC,CAAC,SAAS;MACR5C,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACAlC,SAAS,CAAC,MAAM;IACd,IAAIqC,oBAAoB,EAAE;MACxB+G,YAAY,CAAC/G,oBAAoB,CAACuB,EAAE,CAAC;MACrCyF,sBAAsB,CAAChH,oBAAoB,CAACuB,EAAE,CAAC;IACjD;EACF,CAAC,EAAE,CAACvB,oBAAoB,CAAC,CAAC;;EAE1B;EACA,MAAMgH,sBAAsB,GAAG,MAAO3D,cAAc,IAAK;IACvD,IAAI;MACF;MACA,MAAM4D,cAAc,GAAG/G,QAAQ,CAACM,MAAM,CAACyD,GAAG,IACxCA,GAAG,CAACb,QAAQ,KAAKzD,IAAI,CAAC4B,EAAE,IAAI,CAAC0C,GAAG,CAACO,IACnC,CAAC;MAED,IAAIyC,cAAc,CAAChB,MAAM,GAAG,CAAC,EAAE;QAC7B;QACA,MAAMnI,GAAG,CAACoJ,GAAG,CAAC,2BAA2B7D,cAAc,OAAO,CAAC;;QAE/D;QACAlD,WAAW,CAACmD,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACU,GAAG,IAC9BA,GAAG,CAACb,QAAQ,KAAKzD,IAAI,CAAC4B,EAAE,GAAG;UAAE,GAAG0C,GAAG;UAAEO,IAAI,EAAE;QAAK,CAAC,GAAGP,GACtD,CAAC,CAAC;;QAEF;QACA,IAAIvD,MAAM,EAAE;UACVA,MAAM,CAAC6B,IAAI,CAAC,oBAAoB,EAAE;YAChCc,cAAc;YACdsC,UAAU,EAAEsB,cAAc,CAAC1D,GAAG,CAACU,GAAG,IAAIA,GAAG,CAAC1C,EAAE;UAC9C,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdjB,OAAO,CAACiB,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;IACpE;EACF,CAAC;;EAED;EACA,MAAMsE,YAAY,GAAG,MAAO1D,cAAc,IAAK;IAC7C,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAM/E,GAAG,CAACuI,GAAG,CAAC,2BAA2BhD,cAAc,EAAE,CAAC;;MAE3E;MACA,MAAM8D,iBAAiB,GAAGtE,QAAQ,CAACqC,IAAI,CAAChF,QAAQ,CAACqD,GAAG,CAACJ,OAAO,KAAK;QAC/D5B,EAAE,EAAE4B,OAAO,CAAC5B,EAAE;QACd6B,QAAQ,EAAED,OAAO,CAACC,QAAQ;QAC1Bc,UAAU,EAAEf,OAAO,CAACgB,MAAM,CAACC,IAAI;QAC/BC,YAAY,EAAE,oCAAoCC,kBAAkB,CAACnB,OAAO,CAACgB,MAAM,CAACC,IAAI,CAAC,qDAAqD;QAC9IG,IAAI,EAAEpB,OAAO,CAACO,OAAO;QACrBC,SAAS,EAAER,OAAO,CAACS,SAAS;QAC5BY,IAAI,EAAErB,OAAO,CAACsB,MAAM;QACpBC,SAAS,EAAE,IAAI;QAAE;QACjB0C,WAAW,EAAEjE,OAAO,CAACiE;MACvB,CAAC,CAAC,CAAC;MAEHjH,WAAW,CAACgH,iBAAiB,CAAC;;MAE9B;MACAxC,UAAU,CAAC,MAAM;QAAA,IAAA0C,sBAAA;QACf,CAAAA,sBAAA,GAAApG,cAAc,CAAC4D,OAAO,cAAAwC,sBAAA,uBAAtBA,sBAAA,CAAwBvC,cAAc,CAAC;UAAEC,QAAQ,EAAE;QAAS,CAAC,CAAC;MAChE,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACdjB,OAAO,CAACiB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD1E,KAAK,CAAC0E,KAAK,CAAC,sBAAsB,CAAC;IACrC;EACF,CAAC;EACD;EACA,MAAM6E,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAAClH,WAAW,CAACmH,IAAI,CAAC,CAAC,IAAI,CAACvH,oBAAoB,EAAE;IAElDwB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;MACpC+F,UAAU,EAAExH,oBAAoB,CAACyG,QAAQ;MACzC/C,OAAO,EAAEtD,WAAW,CAACmH,IAAI,CAAC;IAC5B,CAAC,CAAC;IAEF,IAAI;MACF,MAAM1E,QAAQ,GAAG,MAAM/E,GAAG,CAAC2J,IAAI,CAAC,gBAAgB,EAAE;QAChDD,UAAU,EAAExH,oBAAoB,CAACyG,QAAQ;QACzC/C,OAAO,EAAEtD,WAAW,CAACmH,IAAI,CAAC;MAC5B,CAAC,CAAC;MAEF/F,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEoB,QAAQ,CAACqC,IAAI,CAAC;;MAEjD;MACA7E,cAAc,CAAC,EAAE,CAAC;IAEpB,CAAC,CAAC,OAAOoC,KAAK,EAAE;MACdjB,OAAO,CAACiB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD1E,KAAK,CAAC0E,KAAK,CAAC,qBAAqB,CAAC;IACpC;EACF,CAAC;EAED9E,SAAS,CAAC,MAAM;IACd;IACA,IAAIsD,cAAc,CAAC4D,OAAO,IAAI1D,oBAAoB,CAAC0D,OAAO,EAAE;MAC1D1D,oBAAoB,CAAC0D,OAAO,CAAC6C,SAAS,GAAGvG,oBAAoB,CAAC0D,OAAO,CAAC8C,YAAY;IACpF;EACF,CAAC,EAAE,CAACzH,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAM0H,iBAAiB,GAAIC,CAAC,IAAK;IAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBR,WAAW,CAAC,CAAC;EACf,CAAC;;EAED;EACA,MAAMS,wBAAwB,GAAI5B,YAAY,IAAK;IACjDlG,uBAAuB,CAACkG,YAAY,CAAC;EACvC,CAAC;;EAED;EACA,MAAM6B,iBAAiB,GAAIC,UAAU,IAAK;IACxC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,KAAK,GAAG,IAAID,IAAI,CAAC,CAAC;IACxB,MAAME,SAAS,GAAG,IAAIF,IAAI,CAACC,KAAK,CAAC;IACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IAE1C,IAAIL,IAAI,CAACM,YAAY,CAAC,CAAC,KAAKJ,KAAK,CAACI,YAAY,CAAC,CAAC,EAAE;MAChD,OAAOtJ,MAAM,CAACgJ,IAAI,EAAE,OAAO,EAAE;QAAEO,MAAM,EAAEtJ;MAAG,CAAC,CAAC;IAC9C,CAAC,MAAM,IAAI+I,IAAI,CAACM,YAAY,CAAC,CAAC,KAAKH,SAAS,CAACG,YAAY,CAAC,CAAC,EAAE;MAC3D,OAAO,MAAM,GAAGtJ,MAAM,CAACgJ,IAAI,EAAE,OAAO,EAAE;QAAEO,MAAM,EAAEtJ;MAAG,CAAC,CAAC;IACvD,CAAC,MAAM;MACL,OAAOD,MAAM,CAACgJ,IAAI,EAAE,cAAc,EAAE;QAAEO,MAAM,EAAEtJ;MAAG,CAAC,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAMuJ,qBAAqB,GAAG5I,aAAa,CAACU,MAAM,CAACgD,IAAI,IAAI;IACzD;IACA,MAAMmF,aAAa,GAAGnF,IAAI,CAACmD,UAAU,CAACiC,WAAW,CAAC,CAAC,CAAChD,QAAQ,CAACtF,UAAU,CAACsI,WAAW,CAAC,CAAC,CAAC,IACjEpF,IAAI,CAACC,WAAW,CAACmF,WAAW,CAAC,CAAC,CAAChD,QAAQ,CAACtF,UAAU,CAACsI,WAAW,CAAC,CAAC,CAAC;;IAEtF;IACA,MAAMC,aAAa,GAAGrI,MAAM,KAAK,KAAK,IAChBA,MAAM,KAAK,QAAQ,IAAIgD,IAAI,CAACK,MAAO,IACnCrD,MAAM,KAAK,UAAU,IAAIgD,IAAI,CAACsD,QAAS,IACvCtG,MAAM,KAAK,SAAS,IAAIgD,IAAI,CAACqD,OAAQ;IAE3D,OAAO8B,aAAa,IAAIE,aAAa;EACvC,CAAC,CAAC;;EAEF;EACA,MAAMC,UAAU,GAAIvH,EAAE,IAAK;IACzBxB,gBAAgB,CAACgJ,iBAAiB,IAChCA,iBAAiB,CAACxF,GAAG,CAACC,IAAI,IACxBA,IAAI,CAACjC,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGiC,IAAI;MAAEqD,OAAO,EAAE,CAACrD,IAAI,CAACqD;IAAQ,CAAC,GAAGrD,IACzD,CACF,CAAC;IAED,IAAIxD,oBAAoB,IAAIA,oBAAoB,CAACuB,EAAE,KAAKA,EAAE,EAAE;MAC1DtB,uBAAuB,CAACqD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEuD,OAAO,EAAE,CAACvD,IAAI,CAACuD;MAAQ,CAAC,CAAC,CAAC;IACxE;EACF,CAAC;;EAED;EACA,MAAMmC,aAAa,GAAIzH,EAAE,IAAK;IAC5BxB,gBAAgB,CAACgJ,iBAAiB,IAChCA,iBAAiB,CAACxF,GAAG,CAACC,IAAI,IACxBA,IAAI,CAACjC,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGiC,IAAI;MAAEsD,QAAQ,EAAE,CAACtD,IAAI,CAACsD;IAAS,CAAC,GAAGtD,IAC3D,CACF,CAAC;IAED,IAAIxD,oBAAoB,IAAIA,oBAAoB,CAACuB,EAAE,KAAKA,EAAE,EAAE;MAC1DtB,uBAAuB,CAACqD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEwD,QAAQ,EAAE,CAACxD,IAAI,CAACwD;MAAS,CAAC,CAAC,CAAC;IAC1E;EACF,CAAC;EAED,IAAIlH,SAAS,EAAE;IACb,oBACEN,OAAA;MAAK2J,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5D5J,OAAA;QAAK2J,SAAS,EAAC;MAA6E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChG,CAAC;EAEV;EAEA,oBACEhK,OAAA;IAAK2J,SAAS,EAAC,6BAA6B;IAAAC,QAAA,gBAE1C5J,OAAA;MAAK2J,SAAS,EAAC,qFAAqF;MAAAC,QAAA,eAClG5J,OAAA;QAAK2J,SAAS,EAAC,uEAAuE;QAAAC,QAAA,gBACpF5J,OAAA;UAAA4J,QAAA,gBACE5J,OAAA;YAAI2J,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClDhK,OAAA;YAAG2J,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAC;UAEpC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNhK,OAAA;UAAK2J,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAC1C5J,OAAA;YAAQ2J,SAAS,EAAC,6NAA6N;YAAAC,QAAA,gBAC7O5J,OAAA,CAACrB,0BAA0B;cAACgL,SAAS,EAAC,oBAAoB;cAAC,eAAY;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cAElF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhK,OAAA;MAAK2J,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5D5J,OAAA;QAAK2J,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBAEzC5J,OAAA;UAAK2J,SAAS,EAAC,kEAAkE;UAAAC,QAAA,gBAC/E5J,OAAA;YAAK2J,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD5J,OAAA;cAAI2J,SAAS,EAAC,uDAAuD;cAAAC,QAAA,gBACnE5J,OAAA,CAACrB,0BAA0B;gBAACgL,SAAS,EAAC;cAA8B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAEzE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLhK,OAAA;cAAK2J,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B5J,OAAA;gBACEiK,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,0BAAqB;gBACjCP,SAAS,EAAC,qHAAqH;gBAC/HQ,KAAK,EAAEnJ,UAAW;gBAClBoJ,QAAQ,EAAG7B,CAAC,IAAKtH,aAAa,CAACsH,CAAC,CAAC8B,MAAM,CAACF,KAAK;cAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACFhK,OAAA,CAACpB,mBAAmB;gBAAC+K,SAAS,EAAC;cAA+C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC,eACNhK,OAAA;cAAK2J,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAClC5J,OAAA;gBACE2J,SAAS,EAAE,kCAAkCzI,MAAM,KAAK,KAAK,GACzD,+BAA+B,GAC/B,6CAA6C,EAAG;gBACpDoJ,OAAO,EAAEA,CAAA,KAAMnJ,SAAS,CAAC,KAAK,CAAE;gBAAAyI,QAAA,EACjC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACThK,OAAA;gBACE2J,SAAS,EAAE,kCAAkCzI,MAAM,KAAK,QAAQ,GAC5D,+BAA+B,GAC/B,6CAA6C,EAAG;gBACpDoJ,OAAO,EAAEA,CAAA,KAAMnJ,SAAS,CAAC,QAAQ,CAAE;gBAAAyI,QAAA,EACpC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACThK,OAAA;gBACE2J,SAAS,EAAE,kCAAkCzI,MAAM,KAAK,SAAS,GAC7D,+BAA+B,GAC/B,6CAA6C,EAAG;gBACpDoJ,OAAO,EAAEA,CAAA,KAAMnJ,SAAS,CAAC,SAAS,CAAE;gBAAAyI,QAAA,EACrC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACThK,OAAA;gBACE2J,SAAS,EAAE,kCAAkCzI,MAAM,KAAK,UAAU,GAC9D,+BAA+B,GAC/B,6CAA6C,EAAG;gBACpDoJ,OAAO,EAAEA,CAAA,KAAMnJ,SAAS,CAAC,UAAU,CAAE;gBAAAyI,QAAA,EACtC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhK,OAAA;YACEuK,GAAG,EAAE3I,gBAAiB;YACtB4I,KAAK,EAAE;cACLC,MAAM,EAAE,oBAAoB;cAC5BC,SAAS,EAAE,MAAM;cACjBC,cAAc,EAAE,MAAM;cACtBC,cAAc,EAAE;YAClB,CAAE;YAAAhB,QAAA,EAEDR,qBAAqB,CAACzC,MAAM,KAAK,CAAC,gBACjC3G,OAAA;cAAK2J,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAE/C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GAENZ,qBAAqB,CAACnF,GAAG,CAAC4C,YAAY,iBACpC7G,OAAA;cAEE2J,SAAS,EAAE,sEACT,CAAAjJ,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEuB,EAAE,MAAK4E,YAAY,CAAC5E,EAAE,GAAG,cAAc,GAAG,EAAE,IAChE4E,YAAY,CAACtC,MAAM,GAAG,kCAAkC,GAAG,EAAE,EAAG;cACpE+F,OAAO,EAAEA,CAAA,KAAM7B,wBAAwB,CAAC5B,YAAY,CAAE;cAAA+C,QAAA,gBAEtD5J,OAAA;gBAAK2J,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzC5J,OAAA;kBAAK2J,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrC5J,OAAA;oBACE6K,GAAG,EAAEhE,YAAY,CAACS,MAAO;oBACzBwD,GAAG,EAAEjE,YAAY,CAACQ,UAAW;oBAC7BsC,SAAS,EAAE,0BACT,CAAAjJ,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEuB,EAAE,MAAK4E,YAAY,CAAC5E,EAAE,GACxC,wBAAwB,GACxB,EAAE;kBACL;oBAAA4H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,EACDnD,YAAY,CAACf,MAAM,KAAK,QAAQ,iBAC/B9F,OAAA;oBAAM2J,SAAS,EAAC;kBAAyF;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CACjH;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACNhK,OAAA;kBAAK2J,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7B5J,OAAA;oBAAK2J,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,gBAC/C5J,OAAA;sBAAI2J,SAAS,EAAE,uBAAuB9C,YAAY,CAACtC,MAAM,GAAG,eAAe,GAAG,eAAe,EAAG;sBAAAqF,QAAA,EAC7F/C,YAAY,CAACQ;oBAAU;sBAAAwC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB,CAAC,eACLhK,OAAA;sBAAK2J,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,GACzC/C,YAAY,CAACU,OAAO,iBACnBvH,OAAA,CAACL,QAAQ;wBAACgK,SAAS,EAAC;sBAAsC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAC7D,eACDhK,OAAA;wBAAM2J,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EACpClB,iBAAiB,CAAC7B,YAAY,CAACxC,SAAS;sBAAC;wBAAAwF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNhK,OAAA;oBAAG2J,SAAS,EAAE,yBACZ9C,YAAY,CAACtC,MAAM,GAAG,2BAA2B,GAAG,eAAe,EAClE;oBAAAqF,QAAA,EACA/C,YAAY,CAAC1C;kBAAW;oBAAA0F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,eACJhK,OAAA;oBAAK2J,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrD5J,OAAA;sBAAM2J,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EACpC/C,YAAY,CAACf,MAAM,KAAK,QAAQ,GAC7B,WAAW,GACXe,YAAY,CAACkE,QAAQ,GACnB,gBAAgBlE,YAAY,CAACkE,QAAQ,EAAE,GACvC;oBAAE;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACPhK,OAAA;sBAAK2J,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,gBAC7B5J,OAAA;wBACEsK,OAAO,EAAG/B,CAAC,IAAK;0BACdA,CAAC,CAACyC,eAAe,CAAC,CAAC;0BACnBxB,UAAU,CAAC3C,YAAY,CAAC5E,EAAE,CAAC;wBAC7B,CAAE;wBACF0H,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,eAE/C5J,OAAA,CAACL,QAAQ;0BAACgK,SAAS,EAAE,WAAW9C,YAAY,CAACU,OAAO,GAAG,8BAA8B,GAAG,EAAE;wBAAG;0BAAAsC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1F,CAAC,eACThK,OAAA;wBACEsK,OAAO,EAAG/B,CAAC,IAAK;0BACdA,CAAC,CAACyC,eAAe,CAAC,CAAC;0BACnBtB,aAAa,CAAC7C,YAAY,CAAC5E,EAAE,CAAC;wBAChC,CAAE;wBACF0H,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,eAE7C5J,OAAA,CAACN,cAAc;0BAACiK,SAAS,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACLnD,YAAY,CAACtC,MAAM,iBAClBvE,OAAA;gBAAM2J,SAAS,EAAC,2GAA2G;gBAAAC,QAAA,EAAC;cAE5H;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP;YAAA,GA3EInD,YAAY,CAAC5E,EAAE;cAAA4H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA4EjB,CACN;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhK,OAAA;UAAK2J,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EACrDlJ,oBAAoB,gBACnBV,OAAA,CAAAE,SAAA;YAAA0J,QAAA,gBAEE5J,OAAA;cAAK2J,SAAS,EAAC,yEAAyE;cAAAC,QAAA,gBACtF5J,OAAA;gBAAK2J,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC5J,OAAA;kBAAK2J,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5B5J,OAAA;oBACE6K,GAAG,EAAEnK,oBAAoB,CAAC4G,MAAO;oBACjCwD,GAAG,EAAEpK,oBAAoB,CAAC2G,UAAW;oBACrCsC,SAAS,EAAC;kBAAwB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC,EACDtJ,oBAAoB,CAACoF,MAAM,KAAK,QAAQ,iBACvC9F,OAAA;oBAAM2J,SAAS,EAAC;kBAAyF;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CACjH;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACNhK,OAAA;kBAAA4J,QAAA,gBACE5J,OAAA;oBAAI2J,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAC9ClJ,oBAAoB,CAAC2G;kBAAU;oBAAAwC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACLhK,OAAA;oBAAG2J,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EACjClJ,oBAAoB,CAACoF,MAAM,KAAK,QAAQ,GACrC,WAAW,GACXpF,oBAAoB,CAACqK,QAAQ,GAC3B,gBAAgBrK,oBAAoB,CAACqK,QAAQ,EAAE,GAC/C;kBAAE;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNhK,OAAA;gBAAK2J,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1C5J,OAAA;kBAAQ2J,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,eAClE5J,OAAA,CAACb,SAAS;oBAACwK,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACThK,OAAA;kBAAQ2J,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,eAClE5J,OAAA,CAACZ,eAAe;oBAACuK,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACThK,OAAA;kBAAQ2J,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,eAClE5J,OAAA,CAACX,qBAAqB;oBAACsK,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACThK,OAAA;kBAAQ2J,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,eAClE5J,OAAA,CAACd,sBAAsB;oBAACyK,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNhK,OAAA;cACEuK,GAAG,EAAE1I,oBAAqB;cAC1B8H,SAAS,EAAC,gBAAgB;cAC1Ba,KAAK,EAAE;gBACLC,MAAM,EAAE,oBAAoB;gBAC5BC,SAAS,EAAE,MAAM;gBACjBC,cAAc,EAAE,MAAM;gBACtBC,cAAc,EAAE;cAClB,CAAE;cAAAhB,QAAA,GAEDhJ,QAAQ,CAACqD,GAAG,CAAC,CAACJ,OAAO,EAAEoH,KAAK,KAAK;gBAChC,MAAMC,QAAQ,GAAGrH,OAAO,CAACC,QAAQ,KAAKzD,IAAI,CAAC4B,EAAE;gBAC7C,MAAMkJ,UAAU,GAAGF,KAAK,KAAK,CAAC,IAAIrK,QAAQ,CAACqK,KAAK,GAAG,CAAC,CAAC,CAACnH,QAAQ,KAAKD,OAAO,CAACC,QAAQ;gBAEnF,oBACE9D,OAAA;kBAAsB2J,SAAS,EAAE,QAAQuB,QAAQ,GAAG,aAAa,GAAG,eAAe,OAAQ;kBAAAtB,QAAA,GACxF,CAACsB,QAAQ,IAAIC,UAAU,iBACtBnL,OAAA;oBACE6K,GAAG,EAAEhH,OAAO,CAACkB,YAAa;oBAC1B+F,GAAG,EAAEjH,OAAO,CAACe,UAAW;oBACxB+E,SAAS,EAAC;kBAAgC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CACF,EACA,CAACkB,QAAQ,IAAI,CAACC,UAAU,iBAAInL,OAAA;oBAAK2J,SAAS,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7DhK,OAAA;oBACE2J,SAAS,EAAE,yDACTuB,QAAQ,GACJ,0CAA0C,GAC1C,+DAA+D,EAClE;oBAAAtB,QAAA,gBAEH5J,OAAA;sBAAG2J,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAE/F,OAAO,CAACoB;oBAAI;sBAAA4E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzChK,OAAA;sBAAK2J,SAAS,EAAE,gBAAgBuB,QAAQ,GAAG,iBAAiB,GAAG,eAAe,gCAAiC;sBAAAtB,QAAA,GAC5GlB,iBAAiB,CAAC7E,OAAO,CAACQ,SAAS,CAAC,EACpC6G,QAAQ,iBACPlL,OAAA;wBAAK2J,SAAS,EAAC,wBAAwB;wBAAAC,QAAA,gBAErC5J,OAAA,CAACT,eAAe;0BACdoK,SAAS,EAAE,WAAW9F,OAAO,CAACqB,IAAI,GAAG,eAAe,GAAG,eAAe,EAAG;0BACzEkG,KAAK,EAAEvH,OAAO,CAACuB,SAAS,GAAG,UAAU,GAAG;wBAAe;0BAAAyE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxD,CAAC,EAEDnG,OAAO,CAACqB,IAAI,iBACXlF,OAAA,CAACT,eAAe;0BACdoK,SAAS,EAAC,6BAA6B;0BACvCyB,KAAK,EAAC;wBAAQ;0BAAAvB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACf,CACF;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EACLkB,QAAQ,IAAIC,UAAU,iBACrBnL,OAAA;oBACE6K,GAAG,EAAEhH,OAAO,CAACkB,YAAa;oBAC1B+F,GAAG,EAAEjH,OAAO,CAACe,UAAW;oBACxB+E,SAAS,EAAC;kBAAgC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CACF,EACAkB,QAAQ,IAAI,CAACC,UAAU,iBAAInL,OAAA;oBAAK2J,SAAS,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,GA5CpDnG,OAAO,CAAC5B,EAAE;kBAAA4H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA6Cf,CAAC;cAEV,CAAC,CAAC,eACFhK,OAAA;gBAAKuK,GAAG,EAAE5I;cAAe;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eAGNhK,OAAA;cAAK2J,SAAS,EAAC,uCAAuC;cAAAC,QAAA,eACpD5J,OAAA;gBAAMqL,QAAQ,EAAE/C,iBAAkB;gBAACqB,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC3D5J,OAAA;kBACEiK,IAAI,EAAC,QAAQ;kBACbN,SAAS,EAAC,uEAAuE;kBAAAC,QAAA,eAEjF5J,OAAA,CAACjB,aAAa;oBAAC4K,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACThK,OAAA;kBAAK2J,SAAS,EAAC,aAAa;kBAAAC,QAAA,eAC1B5J,OAAA;oBACE2J,SAAS,EAAC,qHAAqH;oBAC/HO,WAAW,EAAC,yCAAqB;oBACjCoB,IAAI,EAAC,GAAG;oBACRnB,KAAK,EAAErJ,WAAY;oBACnBsJ,QAAQ,EAAG7B,CAAC,IAAKxH,cAAc,CAACwH,CAAC,CAAC8B,MAAM,CAACF,KAAK,CAAE;oBAChDoB,SAAS,EAAGhD,CAAC,IAAK;sBAChB,IAAIA,CAAC,CAACiD,GAAG,KAAK,OAAO,IAAI,CAACjD,CAAC,CAACkD,QAAQ,EAAE;wBACpClD,CAAC,CAACC,cAAc,CAAC,CAAC;wBAClBR,WAAW,CAAC,CAAC;sBACf;oBACF;kBAAE;oBAAA6B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACNhK,OAAA;kBACEiK,IAAI,EAAC,QAAQ;kBACbN,SAAS,EAAC,uEAAuE;kBAAAC,QAAA,eAEjF5J,OAAA,CAAChB,aAAa;oBAAC2K,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACThK,OAAA;kBACEiK,IAAI,EAAC,QAAQ;kBACbyB,QAAQ,EAAE,CAAC5K,WAAW,CAACmH,IAAI,CAAC,CAAE;kBAC9B0B,SAAS,EAAE,yBACT7I,WAAW,CAACmH,IAAI,CAAC,CAAC,GACd,8CAA8C,GAC9C,8CAA8C,qBAC9B;kBAAA2B,QAAA,eAEtB5J,OAAA,CAAClB,iBAAiB;oBAAC6K,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,eACN,CAAC;UAAA;UAEH;UACAhK,OAAA;YAAK2J,SAAS,EAAC,iEAAiE;YAAAC,QAAA,eAC9E5J,OAAA;cAAK2J,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1C5J,OAAA,CAACrB,0BAA0B;gBAACgL,SAAS,EAAC;cAAsC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/EhK,OAAA;gBAAI2J,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxEhK,OAAA;gBAAG2J,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAErC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5J,EAAA,CArwBID,YAAY;EAAA,QACC5B,OAAO;AAAA;AAAAoN,EAAA,GADpBxL,YAAY;AAuwBlB,eAAeA,YAAY;AAAC,IAAAwL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}