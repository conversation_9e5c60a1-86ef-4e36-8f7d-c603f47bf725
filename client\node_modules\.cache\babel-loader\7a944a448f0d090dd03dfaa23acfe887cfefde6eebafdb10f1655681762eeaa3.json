{"ast": null, "code": "var _jsxFileName = \"C:\\\\Projeler\\\\kidgarden\\\\burky_root_web\\\\client\\\\src\\\\pages\\\\expert\\\\messages\\\\MessagesPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '../../../hooks/useAuth';\nimport api from '../../../services/api';\nimport { toast } from 'react-hot-toast';\nimport io from 'socket.io-client';\nimport { ChatBubbleLeftEllipsisIcon, MagnifyingGlassIcon, UserIcon, PaperAirplaneIcon, PaperClipIcon, FaceSmileIcon, UserCircleIcon, EllipsisHorizontalIcon, PhoneIcon, VideoCameraIcon, InformationCircleIcon, ClockIcon, CheckCircleIcon, XMarkIcon, TrashIcon, ArchiveBoxIcon, StarIcon } from '@heroicons/react/24/outline';\nimport { format } from 'date-fns';\nimport { tr } from 'date-fns/locale';\nimport { Link } from 'react-router-dom';\n\n/**\n * <PERSON><PERSON> mesaj<PERSON><PERSON> sayfası\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MessagesPage = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [isLoading, setIsLoading] = useState(true);\n  const [conversations, setConversations] = useState([]);\n  const [selectedConversation, setSelectedConversation] = useState(null);\n  const [messages, setMessages] = useState([]);\n  const [messageText, setMessageText] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filter, setFilter] = useState('all'); // all, unread, archived, starred\n  const [socket, setSocket] = useState(null);\n  const [onlineUsers, setOnlineUsers] = useState(new Set());\n  const [typingUsers, setTypingUsers] = useState(new Set());\n  const messagesEndRef = useRef(null);\n  const conversationsRef = useRef(null);\n  const messagesContainerRef = useRef(null);\n\n  // Socket.IO connection - ayrı useEffect\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      console.log('🔌 Socket.IO bağlantısı kuruluyor...');\n      const socketConnection = io(process.env.REACT_APP_API_URL || 'http://localhost:5000', {\n        auth: {\n          token\n        },\n        transports: ['websocket', 'polling'],\n        reconnection: true,\n        reconnectionDelay: 1000,\n        reconnectionAttempts: 5,\n        timeout: 20000\n      });\n      setSocket(socketConnection);\n\n      // Connection events\n      socketConnection.on('connect', () => {\n        console.log('✅ Socket.IO bağlantısı kuruldu:', socketConnection.id);\n        // Kullanıcının online olduğunu bildir\n        socketConnection.emit('user_online');\n      });\n      socketConnection.on('disconnect', reason => {\n        console.log('❌ Socket.IO bağlantısı kesildi:', reason);\n      });\n      socketConnection.on('connect_error', error => {\n        console.error('🔥 Socket.IO bağlantı hatası:', error);\n      });\n      socketConnection.on('reconnect', attemptNumber => {\n        console.log('🔄 Socket.IO yeniden bağlandı, deneme:', attemptNumber);\n      });\n      socketConnection.on('reconnect_error', error => {\n        console.error('🔥 Socket.IO yeniden bağlanma hatası:', error);\n      });\n\n      // Heartbeat mekanizması\n      const heartbeatInterval = setInterval(() => {\n        if (socketConnection.connected) {\n          socketConnection.emit('ping', response => {\n            if (response === 'pong') {\n              console.log('💓 Heartbeat OK');\n            }\n          });\n        }\n      }, 30000); // 30 saniyede bir ping gönder\n\n      return () => {\n        clearInterval(heartbeatInterval);\n        socketConnection.disconnect();\n      };\n    }\n  }, []);\n\n  // Socket event listeners - ayrı useEffect\n  useEffect(() => {\n    if (socket && user !== null && user !== void 0 && user.id) {\n      console.log('🎧 Socket event listeners kuruluyor...');\n\n      // Socket event listeners\n      const handleNewMessage = message => {\n        console.log('📨 EXPERT: Yeni mesaj alındı:', message);\n        console.log('👤 EXPERT: Current user ID:', user.id);\n        console.log('💬 EXPERT: Message sender ID:', message.senderId);\n        console.log('🏠 EXPERT: Message conversation ID:', message.conversationId);\n\n        // Conversation listesini ÖNCE güncelle (her zaman)\n        setConversations(prev => {\n          console.log('📋 EXPERT: Updating conversations list');\n          return prev.map(conv => conv.id === message.conversationId ? {\n            ...conv,\n            lastMessage: message.content,\n            timestamp: message.createdAt,\n            unread: message.senderId !== user.id\n          } : conv);\n        });\n\n        // Eğer şu anda bu conversation açıksa, mesajı da ekle\n        setSelectedConversation(currentSelected => {\n          console.log('🎯 EXPERT: Current selected conversation:', currentSelected === null || currentSelected === void 0 ? void 0 : currentSelected.id);\n          if (currentSelected && message.conversationId === currentSelected.id) {\n            console.log('✅ EXPERT: Adding message to current conversation');\n            setMessages(prev => [...prev, {\n              id: message.id,\n              senderId: message.senderId,\n              senderName: message.sender.name,\n              senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n              text: message.content,\n              timestamp: message.createdAt,\n              read: message.isRead\n            }]);\n\n            // Scroll to bottom\n            setTimeout(() => {\n              var _messagesEndRef$curre;\n              (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n                behavior: 'smooth'\n              });\n            }, 100);\n          } else {\n            console.log('❌ EXPERT: Not adding message - conversation not selected or different conversation');\n          }\n          return currentSelected;\n        });\n      };\n      const handleMessageSent = message => {\n        console.log('✅ Mesaj gönderildi onayı:', message);\n      };\n      const handleUserStatusChange = data => {\n        setOnlineUsers(prev => {\n          const newSet = new Set(prev);\n          if (data.status === 'online') {\n            newSet.add(data.userId);\n          } else {\n            newSet.delete(data.userId);\n          }\n          return newSet;\n        });\n      };\n      const handleUserTyping = data => {\n        setSelectedConversation(currentSelected => {\n          if (currentSelected && data.conversationId === currentSelected.id) {\n            setTypingUsers(prev => new Set([...prev, data.userId]));\n          }\n          return currentSelected;\n        });\n      };\n      const handleUserStoppedTyping = data => {\n        setTypingUsers(prev => {\n          const newSet = new Set(prev);\n          newSet.delete(data.userId);\n          return newSet;\n        });\n      };\n\n      // Event listener'ları ekle\n      socket.on('new_message', handleNewMessage);\n      socket.on('message_sent', handleMessageSent);\n      socket.on('user_status_change', handleUserStatusChange);\n      socket.on('user_typing', handleUserTyping);\n      socket.on('user_stopped_typing', handleUserStoppedTyping);\n\n      // Cleanup function\n      return () => {\n        socket.off('new_message', handleNewMessage);\n        socket.off('message_sent', handleMessageSent);\n        socket.off('user_status_change', handleUserStatusChange);\n        socket.off('user_typing', handleUserTyping);\n        socket.off('user_stopped_typing', handleUserStoppedTyping);\n      };\n    }\n  }, [socket, user.id]);\n\n  // Conversations yükleme - ayrı useEffect\n  useEffect(() => {\n    loadConversations();\n  }, []);\n\n  // Socket bağlandığında tüm conversation'lara join ol\n  useEffect(() => {\n    if (socket && conversations.length > 0) {\n      console.log('🏠 Tüm conversation\\'lara katılıyor...');\n      conversations.forEach(conversation => {\n        socket.emit('join_conversation', conversation.id);\n        console.log(`🏠 Conversation ${conversation.id} katıldı`);\n      });\n    }\n  }, [socket, conversations]);\n\n  // Conversations yükleme\n  const loadConversations = async () => {\n    try {\n      setIsLoading(true);\n      const response = await api.get('/messages/conversations');\n\n      // API verisini frontend formatına çevir\n      const formattedConversations = response.data.conversations.map(conversation => {\n        var _conversation$lastMes, _conversation$lastMes2;\n        return {\n          id: conversation.id,\n          clientId: conversation.otherUser.id,\n          clientName: conversation.otherUser.name,\n          lastMessage: ((_conversation$lastMes = conversation.lastMessage) === null || _conversation$lastMes === void 0 ? void 0 : _conversation$lastMes.content) || 'Henüz mesaj yok',\n          timestamp: ((_conversation$lastMes2 = conversation.lastMessage) === null || _conversation$lastMes2 === void 0 ? void 0 : _conversation$lastMes2.timestamp) || conversation.createdAt,\n          unread: conversation.lastMessage ? !conversation.lastMessage.isRead && conversation.lastMessage.senderId !== user.id : false,\n          avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(conversation.otherUser.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n          status: onlineUsers.has(conversation.otherUser.id) ? 'online' : 'offline',\n          starred: false,\n          archived: false\n        };\n      });\n      setConversations(formattedConversations);\n    } catch (error) {\n      console.error('Konuşmalar yüklenirken hata:', error);\n      toast.error('Konuşmalar yüklenemedi');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Mesaj yükleme\n  useEffect(() => {\n    if (selectedConversation) {\n      loadMessages(selectedConversation.id);\n    }\n  }, [selectedConversation]);\n\n  // Messages yükleme\n  const loadMessages = async conversationId => {\n    try {\n      const response = await api.get(`/messages/conversations/${conversationId}`);\n\n      // API verisini frontend formatına çevir\n      const formattedMessages = response.data.messages.map(message => ({\n        id: message.id,\n        senderId: message.senderId,\n        senderName: message.sender.name,\n        senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        text: message.content,\n        timestamp: message.createdAt,\n        read: message.isRead,\n        attachments: message.attachments\n      }));\n      setMessages(formattedMessages);\n\n      // Scroll to bottom\n      setTimeout(() => {\n        var _messagesEndRef$curre2;\n        (_messagesEndRef$curre2 = messagesEndRef.current) === null || _messagesEndRef$curre2 === void 0 ? void 0 : _messagesEndRef$curre2.scrollIntoView({\n          behavior: 'smooth'\n        });\n      }, 100);\n    } catch (error) {\n      console.error('Mesajlar yüklenirken hata:', error);\n      toast.error('Mesajlar yüklenemedi');\n    }\n  };\n  // Mesaj gönderme\n  const sendMessage = async () => {\n    if (!messageText.trim() || !selectedConversation) return;\n    console.log('📤 Mesaj gönderiliyor:', {\n      receiverId: selectedConversation.clientId,\n      content: messageText.trim()\n    });\n    try {\n      const response = await api.post('/messages/send', {\n        receiverId: selectedConversation.clientId,\n        content: messageText.trim()\n      });\n      console.log('✅ Mesaj gönderildi:', response.data);\n\n      // Mesajı local state'e ekle\n      const newMessage = {\n        id: response.data.message.id,\n        senderId: user.id,\n        senderName: `${user.firstName} ${user.lastName}`,\n        senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(user.firstName)}+${encodeURIComponent(user.lastName)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        text: messageText.trim(),\n        timestamp: response.data.message.createdAt,\n        read: false\n      };\n      setMessages(prev => [...prev, newMessage]);\n      setMessageText('');\n\n      // Conversation listesini güncelle\n      setConversations(prev => prev.map(conv => conv.id === selectedConversation.id ? {\n        ...conv,\n        lastMessage: messageText.trim(),\n        timestamp: newMessage.timestamp\n      } : conv));\n\n      // Scroll to bottom\n      setTimeout(() => {\n        var _messagesEndRef$curre3;\n        (_messagesEndRef$curre3 = messagesEndRef.current) === null || _messagesEndRef$curre3 === void 0 ? void 0 : _messagesEndRef$curre3.scrollIntoView({\n          behavior: 'smooth'\n        });\n      }, 100);\n    } catch (error) {\n      console.error('Mesaj gönderilirken hata:', error);\n      toast.error('Mesaj gönderilemedi');\n    }\n  };\n  useEffect(() => {\n    // Mesajları otomatik kaydır\n    if (messagesEndRef.current && messagesContainerRef.current) {\n      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;\n    }\n  }, [messages]);\n\n  // Mesaj gönderme\n  const handleSendMessage = e => {\n    e.preventDefault();\n    sendMessage();\n  };\n\n  // Konuşma seçme\n  const handleSelectConversation = conversation => {\n    setSelectedConversation(conversation);\n  };\n\n  // Tarih formatı\n  const formatMessageDate = dateString => {\n    const date = new Date(dateString);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    if (date.toDateString() === today.toDateString()) {\n      return format(date, 'HH:mm', {\n        locale: tr\n      });\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Dün ' + format(date, 'HH:mm', {\n        locale: tr\n      });\n    } else {\n      return format(date, 'dd MMM HH:mm', {\n        locale: tr\n      });\n    }\n  };\n\n  // Konuşmaları filtrele\n  const filteredConversations = conversations.filter(conv => {\n    // Arama filtresi\n    const matchesSearch = conv.clientName.toLowerCase().includes(searchTerm.toLowerCase()) || conv.lastMessage.toLowerCase().includes(searchTerm.toLowerCase());\n\n    // Durum filtresi\n    const matchesFilter = filter === 'all' || filter === 'unread' && conv.unread || filter === 'archived' && conv.archived || filter === 'starred' && conv.starred;\n    return matchesSearch && matchesFilter;\n  });\n\n  // Konuşmayı yıldızla\n  const toggleStar = id => {\n    setConversations(prevConversations => prevConversations.map(conv => conv.id === id ? {\n      ...conv,\n      starred: !conv.starred\n    } : conv));\n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({\n        ...prev,\n        starred: !prev.starred\n      }));\n    }\n  };\n\n  // Konuşmayı arşivle\n  const toggleArchive = id => {\n    setConversations(prevConversations => prevConversations.map(conv => conv.id === id ? {\n      ...conv,\n      archived: !conv.archived\n    } : conv));\n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({\n        ...prev,\n        archived: !prev.archived\n      }));\n    }\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 411,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 410,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mx-auto px-4 py-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-teal-500 to-teal-700 shadow-lg rounded-lg p-6 mb-6 text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold\",\n            children: \"Mesajlar\\u0131m\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-1 text-indigo-100\",\n            children: \"Dan\\u0131\\u015Fanlar\\u0131n\\u0131zla olan t\\xFCm yaz\\u0131\\u015Fmalar\\u0131n\\u0131z\\u0131 buradan y\\xF6netebilirsiniz.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-3 sm:mt-0 flex space-x-2\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-indigo-800 bg-white hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-300\",\n            children: [/*#__PURE__*/_jsxDEV(ChatBubbleLeftEllipsisIcon, {\n              className: \"-ml-1 mr-2 h-5 w-5\",\n              \"aria-hidden\": \"true\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 15\n            }, this), \"Yeni Mesaj\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 419,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-md overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-12 h-[75vh]\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-span-12 md:col-span-4 border-r border-gray-200 flex flex-col\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 border-b border-gray-200 bg-white\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-semibold text-gray-800 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(ChatBubbleLeftEllipsisIcon, {\n                className: \"h-6 w-6 text-indigo-600 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 17\n              }, this), \"Mesajlar\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3 relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Konu\\u015Fmalarda ara...\",\n                className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                className: \"h-5 w-5 text-gray-400 absolute left-3 top-2.5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3 flex space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: `px-3 py-1 text-sm rounded-full ${filter === 'all' ? 'bg-indigo-100 text-indigo-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                onClick: () => setFilter('all'),\n                children: \"T\\xFCm\\xFC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `px-3 py-1 text-sm rounded-full ${filter === 'unread' ? 'bg-indigo-100 text-indigo-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                onClick: () => setFilter('unread'),\n                children: \"Okunmam\\u0131\\u015F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `px-3 py-1 text-sm rounded-full ${filter === 'starred' ? 'bg-indigo-100 text-indigo-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                onClick: () => setFilter('starred'),\n                children: \"Y\\u0131ld\\u0131zl\\u0131\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 473,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `px-3 py-1 text-sm rounded-full ${filter === 'archived' ? 'bg-indigo-100 text-indigo-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                onClick: () => setFilter('archived'),\n                children: \"Ar\\u015Fiv\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: conversationsRef,\n            style: {\n              height: 'calc(75vh - 145px)',\n              overflowY: 'auto',\n              scrollbarWidth: 'thin',\n              scrollbarColor: '#D1D5DB #F3F4F6'\n            },\n            children: filteredConversations.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 text-center text-gray-500\",\n              children: \"Hi\\xE7 mesaj\\u0131n\\u0131z yok\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 17\n            }, this) : filteredConversations.map(conversation => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `p-3 cursor-pointer hover:bg-gray-50 transition-colors duration-200 ${(selectedConversation === null || selectedConversation === void 0 ? void 0 : selectedConversation.id) === conversation.id ? 'bg-indigo-50' : ''} ${conversation.unread ? 'bg-indigo-50 hover:bg-indigo-100' : ''}`,\n              onClick: () => handleSelectConversation(conversation),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative flex-shrink-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: conversation.avatar,\n                    alt: conversation.clientName,\n                    className: `h-10 w-10 rounded-full ${(selectedConversation === null || selectedConversation === void 0 ? void 0 : selectedConversation.id) === conversation.id ? 'ring-2 ring-indigo-600' : ''}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 515,\n                    columnNumber: 25\n                  }, this), conversation.status === 'online' && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 525,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 514,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: `text-sm font-medium ${conversation.unread ? 'text-gray-900' : 'text-gray-700'}`,\n                      children: conversation.clientName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 530,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-1\",\n                      children: [conversation.starred && /*#__PURE__*/_jsxDEV(StarIcon, {\n                        className: \"h-4 w-4 text-yellow-400 fill-current\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 535,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-gray-500\",\n                        children: formatMessageDate(conversation.timestamp)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 537,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 533,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 529,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: `text-sm truncate mt-1 ${conversation.unread ? 'text-gray-900 font-medium' : 'text-gray-500'}`,\n                    children: conversation.lastMessage\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 542,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center mt-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-gray-500\",\n                      children: conversation.status === 'online' ? 'Çevrimiçi' : conversation.lastSeen ? `Son görülme: ${conversation.lastSeen}` : ''\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 548,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex space-x-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: e => {\n                          e.stopPropagation();\n                          toggleStar(conversation.id);\n                        },\n                        className: \"text-gray-400 hover:text-yellow-400\",\n                        children: /*#__PURE__*/_jsxDEV(StarIcon, {\n                          className: `h-4 w-4 ${conversation.starred ? 'text-yellow-400 fill-current' : ''}`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 563,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 556,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: e => {\n                          e.stopPropagation();\n                          toggleArchive(conversation.id);\n                        },\n                        className: \"text-gray-400 hover:text-gray-600\",\n                        children: /*#__PURE__*/_jsxDEV(ArchiveBoxIcon, {\n                          className: \"h-4 w-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 572,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 565,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 555,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 547,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 528,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 21\n              }, this), conversation.unread && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"inline-flex items-center ml-12 mt-1 px-2 py-0.5 rounded-full text-xs font-medium bg-indigo-600 text-white\",\n                children: \"Yeni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 23\n              }, this)]\n            }, conversation.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-span-12 md:col-span-8 flex flex-col\",\n          children: selectedConversation ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 border-b border-gray-200 bg-white flex justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative mr-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: selectedConversation.avatar,\n                    alt: selectedConversation.clientName,\n                    className: \"h-10 w-10 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 597,\n                    columnNumber: 23\n                  }, this), selectedConversation.status === 'online' && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 603,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 596,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-lg font-medium text-gray-800\",\n                    children: selectedConversation.clientName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 607,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500\",\n                    children: selectedConversation.status === 'online' ? 'Çevrimiçi' : selectedConversation.lastSeen ? `Son görülme: ${selectedConversation.lastSeen}` : ''\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 610,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 606,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 595,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                  children: /*#__PURE__*/_jsxDEV(PhoneIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 621,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 620,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                  children: /*#__PURE__*/_jsxDEV(VideoCameraIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 624,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 623,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                  children: /*#__PURE__*/_jsxDEV(InformationCircleIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 627,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 626,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                  children: /*#__PURE__*/_jsxDEV(EllipsisHorizontalIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 630,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 629,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 619,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 594,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: messagesContainerRef,\n              className: \"p-4 bg-gray-50\",\n              style: {\n                height: 'calc(75vh - 195px)',\n                overflowY: 'auto',\n                scrollbarWidth: 'thin',\n                scrollbarColor: '#D1D5DB #F3F4F6'\n              },\n              children: [messages.map((message, index) => {\n                const isSender = message.senderId === user.id;\n                const showAvatar = index === 0 || messages[index - 1].senderId !== message.senderId;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `flex ${isSender ? 'justify-end' : 'justify-start'} mb-4`,\n                  children: [!isSender && showAvatar && /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: message.senderAvatar,\n                    alt: message.senderName,\n                    className: \"h-8 w-8 rounded-full mr-2 mt-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 653,\n                    columnNumber: 27\n                  }, this), !isSender && !showAvatar && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 659,\n                    columnNumber: 54\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `max-w-xs md:max-w-md lg:max-w-lg rounded-lg px-4 py-2 ${isSender ? 'bg-indigo-600 text-white rounded-br-none' : 'bg-white text-gray-800 rounded-bl-none border border-gray-200'}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm\",\n                      children: message.text\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 667,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `text-xs mt-1 ${isSender ? 'text-indigo-200' : 'text-gray-500'} flex items-center justify-end`,\n                      children: [formatMessageDate(message.timestamp), isSender && /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                        className: \"h-3 w-3 ml-1\",\n                        title: message.read ? 'Okundu' : 'İletildi'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 671,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 668,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 660,\n                    columnNumber: 25\n                  }, this), isSender && showAvatar && /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: message.senderAvatar,\n                    alt: message.senderName,\n                    className: \"h-8 w-8 rounded-full ml-2 mt-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 676,\n                    columnNumber: 27\n                  }, this), isSender && !showAvatar && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 ml-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 682,\n                    columnNumber: 53\n                  }, this)]\n                }, message.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 651,\n                  columnNumber: 23\n                }, this);\n              }), /*#__PURE__*/_jsxDEV(\"div\", {\n                ref: messagesEndRef\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 636,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 border-t border-gray-200 bg-white\",\n              children: /*#__PURE__*/_jsxDEV(\"form\", {\n                onSubmit: handleSendMessage,\n                className: \"flex items-end\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\",\n                  children: /*#__PURE__*/_jsxDEV(PaperClipIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 696,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 692,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    className: \"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none\",\n                    placeholder: \"Mesaj\\u0131n\\u0131z\\u0131 yaz\\u0131n...\",\n                    rows: \"2\",\n                    value: messageText,\n                    onChange: e => setMessageText(e.target.value)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 699,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 698,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\",\n                  children: /*#__PURE__*/_jsxDEV(FaceSmileIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 711,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 707,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"submit\",\n                  disabled: !messageText.trim(),\n                  className: `ml-2 p-2 rounded-full ${messageText.trim() ? 'bg-indigo-600 text-white hover:bg-indigo-700' : 'bg-gray-200 text-gray-400 cursor-not-allowed'} focus:outline-none`,\n                  children: /*#__PURE__*/_jsxDEV(PaperAirplaneIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 722,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 713,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 691,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 690,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) :\n          /*#__PURE__*/\n          // Mesaj seçilmediğinde\n          _jsxDEV(\"div\", {\n            className: \"flex-1 flex flex-col items-center justify-center p-6 bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full max-w-md text-center\",\n              children: [/*#__PURE__*/_jsxDEV(ChatBubbleLeftEllipsisIcon, {\n                className: \"h-16 w-16 text-gray-300 mb-4 mx-auto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 731,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-medium text-gray-800 mb-2\",\n                children: \"Mesajlar\\u0131n\\u0131z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 732,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-500 mx-auto\",\n                children: \"Mesajla\\u015Fmaya ba\\u015Flamak i\\xE7in sol taraftan bir konu\\u015Fma se\\xE7in veya yeni bir mesaj ba\\u015Flat\\u0131n.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 733,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 730,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 729,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 590,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 438,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 437,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 417,\n    columnNumber: 5\n  }, this);\n};\n_s(MessagesPage, \"ixQyLnGgomNfV/LhGB8XZQDCuYU=\", false, function () {\n  return [useAuth];\n});\n_c = MessagesPage;\nexport default MessagesPage;\nvar _c;\n$RefreshReg$(_c, \"MessagesPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useAuth", "api", "toast", "io", "ChatBubbleLeftEllipsisIcon", "MagnifyingGlassIcon", "UserIcon", "PaperAirplaneIcon", "PaperClipIcon", "FaceSmileIcon", "UserCircleIcon", "EllipsisHorizontalIcon", "PhoneIcon", "VideoCameraIcon", "InformationCircleIcon", "ClockIcon", "CheckCircleIcon", "XMarkIcon", "TrashIcon", "ArchiveBoxIcon", "StarIcon", "format", "tr", "Link", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MessagesPage", "_s", "user", "isLoading", "setIsLoading", "conversations", "setConversations", "selectedConversation", "setSelectedConversation", "messages", "setMessages", "messageText", "setMessageText", "searchTerm", "setSearchTerm", "filter", "setFilter", "socket", "setSocket", "onlineUsers", "setOnlineUsers", "Set", "typingUsers", "setTypingUsers", "messagesEndRef", "conversationsRef", "messagesContainerRef", "token", "localStorage", "getItem", "console", "log", "socketConnection", "process", "env", "REACT_APP_API_URL", "auth", "transports", "reconnection", "reconnectionDelay", "reconnectionAttempts", "timeout", "on", "id", "emit", "reason", "error", "attemptNumber", "heartbeatInterval", "setInterval", "connected", "response", "clearInterval", "disconnect", "handleNewMessage", "message", "senderId", "conversationId", "prev", "map", "conv", "lastMessage", "content", "timestamp", "createdAt", "unread", "currentSelected", "sender<PERSON>ame", "sender", "name", "senderAvatar", "encodeURIComponent", "text", "read", "isRead", "setTimeout", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleMessageSent", "handleUserStatusChange", "data", "newSet", "status", "add", "userId", "delete", "handleUserTyping", "handleUserStoppedTyping", "off", "loadConversations", "length", "for<PERSON>ach", "conversation", "get", "formattedConversations", "_conversation$lastMes", "_conversation$lastMes2", "clientId", "otherUser", "clientName", "avatar", "has", "starred", "archived", "loadMessages", "formattedMessages", "attachments", "_messagesEndRef$curre2", "sendMessage", "trim", "receiverId", "post", "newMessage", "firstName", "lastName", "_messagesEndRef$curre3", "scrollTop", "scrollHeight", "handleSendMessage", "e", "preventDefault", "handleSelectConversation", "formatMessageDate", "dateString", "date", "Date", "today", "yesterday", "setDate", "getDate", "toDateString", "locale", "filteredConversations", "matchesSearch", "toLowerCase", "includes", "matchesFilter", "toggleStar", "prevConversations", "toggleArchive", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "value", "onChange", "target", "onClick", "ref", "style", "height", "overflowY", "scrollbarWidth", "scrollbarColor", "src", "alt", "lastSeen", "stopPropagation", "index", "isSender", "showAvatar", "title", "onSubmit", "rows", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Projeler/kidgarden/burky_root_web/client/src/pages/expert/messages/MessagesPage.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '../../../hooks/useAuth';\nimport api from '../../../services/api';\nimport { toast } from 'react-hot-toast';\nimport io from 'socket.io-client';\nimport {\n  ChatBubbleLeftEllipsisIcon,\n  MagnifyingGlassIcon,\n  UserIcon,\n  PaperAirplaneIcon,\n  PaperClipIcon,\n  FaceSmileIcon,\n  UserCircleIcon,\n  EllipsisHorizontalIcon,\n  PhoneIcon,\n  VideoCameraIcon,\n  InformationCircleIcon,\n  ClockIcon,\n  CheckCircleIcon,\n  XMarkIcon,\n  TrashIcon,\n  ArchiveBoxIcon,\n  StarIcon\n} from '@heroicons/react/24/outline';\nimport { format } from 'date-fns';\nimport { tr } from 'date-fns/locale';\nimport { Link } from 'react-router-dom';\n\n/**\n * <PERSON><PERSON> mesajlaşma sayfası\n */\nconst MessagesPage = () => {\n  const { user } = useAuth();\n  const [isLoading, setIsLoading] = useState(true);\n  const [conversations, setConversations] = useState([]);\n  const [selectedConversation, setSelectedConversation] = useState(null);\n  const [messages, setMessages] = useState([]);\n  const [messageText, setMessageText] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filter, setFilter] = useState('all'); // all, unread, archived, starred\n  const [socket, setSocket] = useState(null);\n  const [onlineUsers, setOnlineUsers] = useState(new Set());\n  const [typingUsers, setTypingUsers] = useState(new Set());\n  const messagesEndRef = useRef(null);\n  const conversationsRef = useRef(null);\n  const messagesContainerRef = useRef(null);\n\n  // Socket.IO connection - ayrı useEffect\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      console.log('🔌 Socket.IO bağlantısı kuruluyor...');\n      const socketConnection = io(process.env.REACT_APP_API_URL || 'http://localhost:5000', {\n        auth: { token },\n        transports: ['websocket', 'polling'],\n        reconnection: true,\n        reconnectionDelay: 1000,\n        reconnectionAttempts: 5,\n        timeout: 20000\n      });\n\n      setSocket(socketConnection);\n\n      // Connection events\n      socketConnection.on('connect', () => {\n        console.log('✅ Socket.IO bağlantısı kuruldu:', socketConnection.id);\n        // Kullanıcının online olduğunu bildir\n        socketConnection.emit('user_online');\n      });\n\n      socketConnection.on('disconnect', (reason) => {\n        console.log('❌ Socket.IO bağlantısı kesildi:', reason);\n      });\n\n      socketConnection.on('connect_error', (error) => {\n        console.error('🔥 Socket.IO bağlantı hatası:', error);\n      });\n\n      socketConnection.on('reconnect', (attemptNumber) => {\n        console.log('🔄 Socket.IO yeniden bağlandı, deneme:', attemptNumber);\n      });\n\n      socketConnection.on('reconnect_error', (error) => {\n        console.error('🔥 Socket.IO yeniden bağlanma hatası:', error);\n      });\n\n      // Heartbeat mekanizması\n      const heartbeatInterval = setInterval(() => {\n        if (socketConnection.connected) {\n          socketConnection.emit('ping', (response) => {\n            if (response === 'pong') {\n              console.log('💓 Heartbeat OK');\n            }\n          });\n        }\n      }, 30000); // 30 saniyede bir ping gönder\n\n      return () => {\n        clearInterval(heartbeatInterval);\n        socketConnection.disconnect();\n      };\n    }\n  }, []);\n\n  // Socket event listeners - ayrı useEffect\n  useEffect(() => {\n    if (socket && user?.id) {\n      console.log('🎧 Socket event listeners kuruluyor...');\n\n      // Socket event listeners\n      const handleNewMessage = (message) => {\n        console.log('📨 EXPERT: Yeni mesaj alındı:', message);\n        console.log('👤 EXPERT: Current user ID:', user.id);\n        console.log('💬 EXPERT: Message sender ID:', message.senderId);\n        console.log('🏠 EXPERT: Message conversation ID:', message.conversationId);\n\n        // Conversation listesini ÖNCE güncelle (her zaman)\n        setConversations(prev => {\n          console.log('📋 EXPERT: Updating conversations list');\n          return prev.map(conv =>\n            conv.id === message.conversationId\n              ? { ...conv, lastMessage: message.content, timestamp: message.createdAt, unread: message.senderId !== user.id }\n              : conv\n          );\n        });\n\n        // Eğer şu anda bu conversation açıksa, mesajı da ekle\n        setSelectedConversation(currentSelected => {\n          console.log('🎯 EXPERT: Current selected conversation:', currentSelected?.id);\n          if (currentSelected && message.conversationId === currentSelected.id) {\n            console.log('✅ EXPERT: Adding message to current conversation');\n            setMessages(prev => [...prev, {\n              id: message.id,\n              senderId: message.senderId,\n              senderName: message.sender.name,\n              senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n              text: message.content,\n              timestamp: message.createdAt,\n              read: message.isRead\n            }]);\n\n            // Scroll to bottom\n            setTimeout(() => {\n              messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n            }, 100);\n          } else {\n            console.log('❌ EXPERT: Not adding message - conversation not selected or different conversation');\n          }\n          return currentSelected;\n        });\n      };\n\n      const handleMessageSent = (message) => {\n        console.log('✅ Mesaj gönderildi onayı:', message);\n      };\n\n      const handleUserStatusChange = (data) => {\n        setOnlineUsers(prev => {\n          const newSet = new Set(prev);\n          if (data.status === 'online') {\n            newSet.add(data.userId);\n          } else {\n            newSet.delete(data.userId);\n          }\n          return newSet;\n        });\n      };\n\n      const handleUserTyping = (data) => {\n        setSelectedConversation(currentSelected => {\n          if (currentSelected && data.conversationId === currentSelected.id) {\n            setTypingUsers(prev => new Set([...prev, data.userId]));\n          }\n          return currentSelected;\n        });\n      };\n\n      const handleUserStoppedTyping = (data) => {\n        setTypingUsers(prev => {\n          const newSet = new Set(prev);\n          newSet.delete(data.userId);\n          return newSet;\n        });\n      };\n\n      // Event listener'ları ekle\n      socket.on('new_message', handleNewMessage);\n      socket.on('message_sent', handleMessageSent);\n      socket.on('user_status_change', handleUserStatusChange);\n      socket.on('user_typing', handleUserTyping);\n      socket.on('user_stopped_typing', handleUserStoppedTyping);\n\n      // Cleanup function\n      return () => {\n        socket.off('new_message', handleNewMessage);\n        socket.off('message_sent', handleMessageSent);\n        socket.off('user_status_change', handleUserStatusChange);\n        socket.off('user_typing', handleUserTyping);\n        socket.off('user_stopped_typing', handleUserStoppedTyping);\n      };\n    }\n  }, [socket, user.id]);\n\n  // Conversations yükleme - ayrı useEffect\n  useEffect(() => {\n    loadConversations();\n  }, []);\n\n  // Socket bağlandığında tüm conversation'lara join ol\n  useEffect(() => {\n    if (socket && conversations.length > 0) {\n      console.log('🏠 Tüm conversation\\'lara katılıyor...');\n      conversations.forEach(conversation => {\n        socket.emit('join_conversation', conversation.id);\n        console.log(`🏠 Conversation ${conversation.id} katıldı`);\n      });\n    }\n  }, [socket, conversations]);\n\n  // Conversations yükleme\n  const loadConversations = async () => {\n    try {\n      setIsLoading(true);\n      const response = await api.get('/messages/conversations');\n\n      // API verisini frontend formatına çevir\n      const formattedConversations = response.data.conversations.map(conversation => ({\n        id: conversation.id,\n        clientId: conversation.otherUser.id,\n        clientName: conversation.otherUser.name,\n        lastMessage: conversation.lastMessage?.content || 'Henüz mesaj yok',\n        timestamp: conversation.lastMessage?.timestamp || conversation.createdAt,\n        unread: conversation.lastMessage ? !conversation.lastMessage.isRead && conversation.lastMessage.senderId !== user.id : false,\n        avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(conversation.otherUser.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        status: onlineUsers.has(conversation.otherUser.id) ? 'online' : 'offline',\n        starred: false,\n        archived: false\n      }));\n\n      setConversations(formattedConversations);\n    } catch (error) {\n      console.error('Konuşmalar yüklenirken hata:', error);\n      toast.error('Konuşmalar yüklenemedi');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Mesaj yükleme\n  useEffect(() => {\n    if (selectedConversation) {\n      loadMessages(selectedConversation.id);\n    }\n  }, [selectedConversation]);\n\n  // Messages yükleme\n  const loadMessages = async (conversationId) => {\n    try {\n      const response = await api.get(`/messages/conversations/${conversationId}`);\n\n      // API verisini frontend formatına çevir\n      const formattedMessages = response.data.messages.map(message => ({\n        id: message.id,\n        senderId: message.senderId,\n        senderName: message.sender.name,\n        senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        text: message.content,\n        timestamp: message.createdAt,\n        read: message.isRead,\n        attachments: message.attachments\n      }));\n\n      setMessages(formattedMessages);\n\n      // Scroll to bottom\n      setTimeout(() => {\n        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n      }, 100);\n    } catch (error) {\n      console.error('Mesajlar yüklenirken hata:', error);\n      toast.error('Mesajlar yüklenemedi');\n    }\n  };\n  // Mesaj gönderme\n  const sendMessage = async () => {\n    if (!messageText.trim() || !selectedConversation) return;\n\n    console.log('📤 Mesaj gönderiliyor:', {\n      receiverId: selectedConversation.clientId,\n      content: messageText.trim()\n    });\n\n    try {\n      const response = await api.post('/messages/send', {\n        receiverId: selectedConversation.clientId,\n        content: messageText.trim()\n      });\n\n      console.log('✅ Mesaj gönderildi:', response.data);\n\n      // Mesajı local state'e ekle\n      const newMessage = {\n        id: response.data.message.id,\n        senderId: user.id,\n        senderName: `${user.firstName} ${user.lastName}`,\n        senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(user.firstName)}+${encodeURIComponent(user.lastName)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        text: messageText.trim(),\n        timestamp: response.data.message.createdAt,\n        read: false\n      };\n\n      setMessages(prev => [...prev, newMessage]);\n      setMessageText('');\n\n      // Conversation listesini güncelle\n      setConversations(prev => prev.map(conv =>\n        conv.id === selectedConversation.id\n          ? { ...conv, lastMessage: messageText.trim(), timestamp: newMessage.timestamp }\n          : conv\n      ));\n\n      // Scroll to bottom\n      setTimeout(() => {\n        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n      }, 100);\n\n    } catch (error) {\n      console.error('Mesaj gönderilirken hata:', error);\n      toast.error('Mesaj gönderilemedi');\n    }\n  };\n\n  useEffect(() => {\n    // Mesajları otomatik kaydır\n    if (messagesEndRef.current && messagesContainerRef.current) {\n      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;\n    }\n  }, [messages]);\n\n  // Mesaj gönderme\n  const handleSendMessage = (e) => {\n    e.preventDefault();\n    sendMessage();\n  };\n\n  // Konuşma seçme\n  const handleSelectConversation = (conversation) => {\n    setSelectedConversation(conversation);\n  };\n\n  // Tarih formatı\n  const formatMessageDate = (dateString) => {\n    const date = new Date(dateString);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    \n    if (date.toDateString() === today.toDateString()) {\n      return format(date, 'HH:mm', { locale: tr });\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Dün ' + format(date, 'HH:mm', { locale: tr });\n    } else {\n      return format(date, 'dd MMM HH:mm', { locale: tr });\n    }\n  };\n\n  // Konuşmaları filtrele\n  const filteredConversations = conversations.filter(conv => {\n    // Arama filtresi\n    const matchesSearch = conv.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         conv.lastMessage.toLowerCase().includes(searchTerm.toLowerCase());\n    \n    // Durum filtresi\n    const matchesFilter = filter === 'all' ||\n                         (filter === 'unread' && conv.unread) ||\n                         (filter === 'archived' && conv.archived) ||\n                         (filter === 'starred' && conv.starred);\n                        \n    return matchesSearch && matchesFilter;\n  });\n\n  // Konuşmayı yıldızla\n  const toggleStar = (id) => {\n    setConversations(prevConversations =>\n      prevConversations.map(conv =>\n        conv.id === id ? { ...conv, starred: !conv.starred } : conv\n      )\n    );\n    \n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({ ...prev, starred: !prev.starred }));\n    }\n  };\n\n  // Konuşmayı arşivle\n  const toggleArchive = (id) => {\n    setConversations(prevConversations =>\n      prevConversations.map(conv =>\n        conv.id === id ? { ...conv, archived: !conv.archived } : conv\n      )\n    );\n    \n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({ ...prev, archived: !prev.archived }));\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto px-4 py-6\">\n      {/* Sayfa Başlığı */}\n      <div className=\"bg-gradient-to-r from-teal-500 to-teal-700 shadow-lg rounded-lg p-6 mb-6 text-white\">\n        <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center\">\n          <div>\n            <h1 className=\"text-2xl font-bold\">Mesajlarım</h1>\n            <p className=\"mt-1 text-indigo-100\">\n              Danışanlarınızla olan tüm yazışmalarınızı buradan yönetebilirsiniz.\n            </p>\n          </div>\n          <div className=\"mt-3 sm:mt-0 flex space-x-2\">\n            <button className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-indigo-800 bg-white hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-300\">\n              <ChatBubbleLeftEllipsisIcon className=\"-ml-1 mr-2 h-5 w-5\" aria-hidden=\"true\" />\n              Yeni Mesaj\n            </button>\n          </div>\n        </div>\n      </div>\n      \n      {/* Mesajlaşma arayüzü */}\n      <div className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n        <div className=\"grid grid-cols-12 h-[75vh]\">\n          {/* Sol Kenar - Konuşma Listesi */}\n          <div className=\"col-span-12 md:col-span-4 border-r border-gray-200 flex flex-col\">\n            <div className=\"p-4 border-b border-gray-200 bg-white\">\n              <h1 className=\"text-xl font-semibold text-gray-800 flex items-center\">\n                <ChatBubbleLeftEllipsisIcon className=\"h-6 w-6 text-indigo-600 mr-2\" />\n                Mesajlar\n              </h1>\n              <div className=\"mt-3 relative\">\n                <input\n                  type=\"text\"\n                  placeholder=\"Konuşmalarda ara...\"\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                />\n                <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400 absolute left-3 top-2.5\" />\n              </div>\n              <div className=\"mt-3 flex space-x-2\">\n                <button\n                  className={`px-3 py-1 text-sm rounded-full ${filter === 'all' \n                    ? 'bg-indigo-100 text-indigo-800' \n                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                  onClick={() => setFilter('all')}\n                >\n                  Tümü\n                </button>\n                <button\n                  className={`px-3 py-1 text-sm rounded-full ${filter === 'unread' \n                    ? 'bg-indigo-100 text-indigo-800' \n                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                  onClick={() => setFilter('unread')}\n                >\n                  Okunmamış\n                </button>\n                <button\n                  className={`px-3 py-1 text-sm rounded-full ${filter === 'starred' \n                    ? 'bg-indigo-100 text-indigo-800' \n                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                  onClick={() => setFilter('starred')}\n                >\n                  Yıldızlı\n                </button>\n                <button\n                  className={`px-3 py-1 text-sm rounded-full ${filter === 'archived' \n                    ? 'bg-indigo-100 text-indigo-800' \n                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                  onClick={() => setFilter('archived')}\n                >\n                  Arşiv\n                </button>\n              </div>\n            </div>\n            <div \n              ref={conversationsRef}\n              style={{\n                height: 'calc(75vh - 145px)',\n                overflowY: 'auto',\n                scrollbarWidth: 'thin',\n                scrollbarColor: '#D1D5DB #F3F4F6'\n              }}\n            >\n              {filteredConversations.length === 0 ? (\n                <div className=\"p-4 text-center text-gray-500\">\n                  Hiç mesajınız yok\n                </div>\n              ) : (\n                filteredConversations.map(conversation => (\n                  <div\n                    key={conversation.id}\n                    className={`p-3 cursor-pointer hover:bg-gray-50 transition-colors duration-200 ${\n                      selectedConversation?.id === conversation.id ? 'bg-indigo-50' : ''\n                    } ${conversation.unread ? 'bg-indigo-50 hover:bg-indigo-100' : ''}`}\n                    onClick={() => handleSelectConversation(conversation)}\n                  >\n                    <div className=\"flex items-start space-x-3\">\n                      <div className=\"relative flex-shrink-0\">\n                        <img\n                          src={conversation.avatar}\n                          alt={conversation.clientName}\n                          className={`h-10 w-10 rounded-full ${\n                            selectedConversation?.id === conversation.id \n                              ? 'ring-2 ring-indigo-600' \n                              : ''\n                          }`}\n                        />\n                        {conversation.status === 'online' && (\n                          <span className=\"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"></span>\n                        )}\n                      </div>\n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex justify-between items-start\">\n                          <h3 className={`text-sm font-medium ${conversation.unread ? 'text-gray-900' : 'text-gray-700'}`}>\n                            {conversation.clientName}\n                          </h3>\n                          <div className=\"flex items-center space-x-1\">\n                            {conversation.starred && (\n                              <StarIcon className=\"h-4 w-4 text-yellow-400 fill-current\" />\n                            )}\n                            <span className=\"text-xs text-gray-500\">\n                              {formatMessageDate(conversation.timestamp)}\n                            </span>\n                          </div>\n                        </div>\n                        <p className={`text-sm truncate mt-1 ${\n                          conversation.unread ? 'text-gray-900 font-medium' : 'text-gray-500'\n                        }`}>\n                          {conversation.lastMessage}\n                        </p>\n                        <div className=\"flex justify-between items-center mt-1\">\n                          <span className=\"text-xs text-gray-500\">\n                            {conversation.status === 'online' \n                              ? 'Çevrimiçi' \n                              : conversation.lastSeen \n                                ? `Son görülme: ${conversation.lastSeen}` \n                                : ''}\n                          </span>\n                          <div className=\"flex space-x-1\">\n                            <button \n                              onClick={(e) => {\n                                e.stopPropagation();\n                                toggleStar(conversation.id);\n                              }}\n                              className=\"text-gray-400 hover:text-yellow-400\"\n                            >\n                              <StarIcon className={`h-4 w-4 ${conversation.starred ? 'text-yellow-400 fill-current' : ''}`} />\n                            </button>\n                            <button \n                              onClick={(e) => {\n                                e.stopPropagation();\n                                toggleArchive(conversation.id);\n                              }}\n                              className=\"text-gray-400 hover:text-gray-600\"\n                            >\n                              <ArchiveBoxIcon className=\"h-4 w-4\" />\n                            </button>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {conversation.unread && (\n                      <span className=\"inline-flex items-center ml-12 mt-1 px-2 py-0.5 rounded-full text-xs font-medium bg-indigo-600 text-white\">\n                        Yeni\n                      </span>\n                    )}\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n\n          {/* Sağ Taraf - Mesaj Alanı */}\n          <div className=\"col-span-12 md:col-span-8 flex flex-col\">\n            {selectedConversation ? (\n              <>\n                {/* Mesajlaşma Başlığı */}\n                <div className=\"p-4 border-b border-gray-200 bg-white flex justify-between items-center\">\n                  <div className=\"flex items-center\">\n                    <div className=\"relative mr-3\">\n                      <img\n                        src={selectedConversation.avatar}\n                        alt={selectedConversation.clientName}\n                        className=\"h-10 w-10 rounded-full\"\n                      />\n                      {selectedConversation.status === 'online' && (\n                        <span className=\"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"></span>\n                      )}\n                    </div>\n                    <div>\n                      <h2 className=\"text-lg font-medium text-gray-800\">\n                        {selectedConversation.clientName}\n                      </h2>\n                      <p className=\"text-xs text-gray-500\">\n                        {selectedConversation.status === 'online' \n                          ? 'Çevrimiçi' \n                          : selectedConversation.lastSeen \n                            ? `Son görülme: ${selectedConversation.lastSeen}` \n                            : ''}\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <button className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\">\n                      <PhoneIcon className=\"h-5 w-5\" />\n                    </button>\n                    <button className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\">\n                      <VideoCameraIcon className=\"h-5 w-5\" />\n                    </button>\n                    <button className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\">\n                      <InformationCircleIcon className=\"h-5 w-5\" />\n                    </button>\n                    <button className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\">\n                      <EllipsisHorizontalIcon className=\"h-5 w-5\" />\n                    </button>\n                  </div>\n                </div>\n\n                {/* Mesaj Alanı */}\n                <div \n                  ref={messagesContainerRef}\n                  className=\"p-4 bg-gray-50\"\n                  style={{\n                    height: 'calc(75vh - 195px)',\n                    overflowY: 'auto',\n                    scrollbarWidth: 'thin',\n                    scrollbarColor: '#D1D5DB #F3F4F6'\n                  }}\n                >\n                  {messages.map((message, index) => {\n                    const isSender = message.senderId === user.id;\n                    const showAvatar = index === 0 || messages[index - 1].senderId !== message.senderId;\n                    \n                    return (\n                      <div key={message.id} className={`flex ${isSender ? 'justify-end' : 'justify-start'} mb-4`}>\n                        {!isSender && showAvatar && (\n                          <img \n                            src={message.senderAvatar} \n                            alt={message.senderName}\n                            className=\"h-8 w-8 rounded-full mr-2 mt-1\"\n                          />\n                        )}\n                        {!isSender && !showAvatar && <div className=\"w-8 mr-2\"></div>}\n                        <div \n                          className={`max-w-xs md:max-w-md lg:max-w-lg rounded-lg px-4 py-2 ${\n                            isSender \n                              ? 'bg-indigo-600 text-white rounded-br-none' \n                              : 'bg-white text-gray-800 rounded-bl-none border border-gray-200'\n                          }`}\n                        >\n                          <p className=\"text-sm\">{message.text}</p>\n                          <div className={`text-xs mt-1 ${isSender ? 'text-indigo-200' : 'text-gray-500'} flex items-center justify-end`}>\n                            {formatMessageDate(message.timestamp)}\n                            {isSender && (\n                              <CheckCircleIcon className=\"h-3 w-3 ml-1\" title={message.read ? 'Okundu' : 'İletildi'} />\n                            )}\n                          </div>\n                        </div>\n                        {isSender && showAvatar && (\n                          <img \n                            src={message.senderAvatar}\n                            alt={message.senderName}\n                            className=\"h-8 w-8 rounded-full ml-2 mt-1\"\n                          />\n                        )}\n                        {isSender && !showAvatar && <div className=\"w-8 ml-2\"></div>}\n                      </div>\n                    );\n                  })}\n                  <div ref={messagesEndRef} />\n                </div>\n\n                {/* Mesaj Giriş Alanı */}\n                <div className=\"p-3 border-t border-gray-200 bg-white\">\n                  <form onSubmit={handleSendMessage} className=\"flex items-end\">\n                    <button\n                      type=\"button\"\n                      className=\"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\"\n                    >\n                      <PaperClipIcon className=\"h-5 w-5\" />\n                    </button>\n                    <div className=\"flex-1 mx-2\">\n                      <textarea\n                        className=\"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none\"\n                        placeholder=\"Mesajınızı yazın...\"\n                        rows=\"2\"\n                        value={messageText}\n                        onChange={(e) => setMessageText(e.target.value)}\n                      ></textarea>\n                    </div>\n                    <button\n                      type=\"button\"\n                      className=\"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\"\n                    >\n                      <FaceSmileIcon className=\"h-5 w-5\" />\n                    </button>\n                    <button\n                      type=\"submit\"\n                      disabled={!messageText.trim()}\n                      className={`ml-2 p-2 rounded-full ${\n                        messageText.trim() \n                          ? 'bg-indigo-600 text-white hover:bg-indigo-700' \n                          : 'bg-gray-200 text-gray-400 cursor-not-allowed'\n                      } focus:outline-none`}\n                    >\n                      <PaperAirplaneIcon className=\"h-5 w-5\" />\n                    </button>\n                  </form>\n                </div>\n              </>\n            ) : (\n              // Mesaj seçilmediğinde\n              <div className=\"flex-1 flex flex-col items-center justify-center p-6 bg-gray-50\">\n                <div className=\"w-full max-w-md text-center\">\n                  <ChatBubbleLeftEllipsisIcon className=\"h-16 w-16 text-gray-300 mb-4 mx-auto\" />\n                  <h3 className=\"text-xl font-medium text-gray-800 mb-2\">Mesajlarınız</h3>\n                  <p className=\"text-gray-500 mx-auto\">\n                    Mesajlaşmaya başlamak için sol taraftan bir konuşma seçin veya yeni bir mesaj başlatın.\n                  </p>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default MessagesPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,GAAG,MAAM,uBAAuB;AACvC,SAASC,KAAK,QAAQ,iBAAiB;AACvC,OAAOC,EAAE,MAAM,kBAAkB;AACjC,SACEC,0BAA0B,EAC1BC,mBAAmB,EACnBC,QAAQ,EACRC,iBAAiB,EACjBC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,sBAAsB,EACtBC,SAAS,EACTC,eAAe,EACfC,qBAAqB,EACrBC,SAAS,EACTC,eAAe,EACfC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,QAAQ,QACH,6BAA6B;AACpC,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,EAAE,QAAQ,iBAAiB;AACpC,SAASC,IAAI,QAAQ,kBAAkB;;AAEvC;AACA;AACA;AAFA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGA,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC;EAAK,CAAC,GAAG9B,OAAO,CAAC,CAAC;EAC1B,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACoC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACsC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAACwC,QAAQ,EAAEC,WAAW,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC0C,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC4C,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC8C,MAAM,EAAEC,SAAS,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACgD,MAAM,EAAEC,SAAS,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAC,IAAIoD,GAAG,CAAC,CAAC,CAAC;EACzD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtD,QAAQ,CAAC,IAAIoD,GAAG,CAAC,CAAC,CAAC;EACzD,MAAMG,cAAc,GAAGrD,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMsD,gBAAgB,GAAGtD,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMuD,oBAAoB,GAAGvD,MAAM,CAAC,IAAI,CAAC;;EAEzC;EACAD,SAAS,CAAC,MAAM;IACd,MAAMyD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACTG,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnD,MAAMC,gBAAgB,GAAGzD,EAAE,CAAC0D,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB,EAAE;QACpFC,IAAI,EAAE;UAAET;QAAM,CAAC;QACfU,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;QACpCC,YAAY,EAAE,IAAI;QAClBC,iBAAiB,EAAE,IAAI;QACvBC,oBAAoB,EAAE,CAAC;QACvBC,OAAO,EAAE;MACX,CAAC,CAAC;MAEFvB,SAAS,CAACc,gBAAgB,CAAC;;MAE3B;MACAA,gBAAgB,CAACU,EAAE,CAAC,SAAS,EAAE,MAAM;QACnCZ,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEC,gBAAgB,CAACW,EAAE,CAAC;QACnE;QACAX,gBAAgB,CAACY,IAAI,CAAC,aAAa,CAAC;MACtC,CAAC,CAAC;MAEFZ,gBAAgB,CAACU,EAAE,CAAC,YAAY,EAAGG,MAAM,IAAK;QAC5Cf,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEc,MAAM,CAAC;MACxD,CAAC,CAAC;MAEFb,gBAAgB,CAACU,EAAE,CAAC,eAAe,EAAGI,KAAK,IAAK;QAC9ChB,OAAO,CAACgB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD,CAAC,CAAC;MAEFd,gBAAgB,CAACU,EAAE,CAAC,WAAW,EAAGK,aAAa,IAAK;QAClDjB,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEgB,aAAa,CAAC;MACtE,CAAC,CAAC;MAEFf,gBAAgB,CAACU,EAAE,CAAC,iBAAiB,EAAGI,KAAK,IAAK;QAChDhB,OAAO,CAACgB,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC/D,CAAC,CAAC;;MAEF;MACA,MAAME,iBAAiB,GAAGC,WAAW,CAAC,MAAM;QAC1C,IAAIjB,gBAAgB,CAACkB,SAAS,EAAE;UAC9BlB,gBAAgB,CAACY,IAAI,CAAC,MAAM,EAAGO,QAAQ,IAAK;YAC1C,IAAIA,QAAQ,KAAK,MAAM,EAAE;cACvBrB,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;YAChC;UACF,CAAC,CAAC;QACJ;MACF,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;MAEX,OAAO,MAAM;QACXqB,aAAa,CAACJ,iBAAiB,CAAC;QAChChB,gBAAgB,CAACqB,UAAU,CAAC,CAAC;MAC/B,CAAC;IACH;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnF,SAAS,CAAC,MAAM;IACd,IAAI+C,MAAM,IAAIf,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEyC,EAAE,EAAE;MACtBb,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;MAErD;MACA,MAAMuB,gBAAgB,GAAIC,OAAO,IAAK;QACpCzB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEwB,OAAO,CAAC;QACrDzB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE7B,IAAI,CAACyC,EAAE,CAAC;QACnDb,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEwB,OAAO,CAACC,QAAQ,CAAC;QAC9D1B,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEwB,OAAO,CAACE,cAAc,CAAC;;QAE1E;QACAnD,gBAAgB,CAACoD,IAAI,IAAI;UACvB5B,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;UACrD,OAAO2B,IAAI,CAACC,GAAG,CAACC,IAAI,IAClBA,IAAI,CAACjB,EAAE,KAAKY,OAAO,CAACE,cAAc,GAC9B;YAAE,GAAGG,IAAI;YAAEC,WAAW,EAAEN,OAAO,CAACO,OAAO;YAAEC,SAAS,EAAER,OAAO,CAACS,SAAS;YAAEC,MAAM,EAAEV,OAAO,CAACC,QAAQ,KAAKtD,IAAI,CAACyC;UAAG,CAAC,GAC7GiB,IACN,CAAC;QACH,CAAC,CAAC;;QAEF;QACApD,uBAAuB,CAAC0D,eAAe,IAAI;UACzCpC,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEmC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEvB,EAAE,CAAC;UAC7E,IAAIuB,eAAe,IAAIX,OAAO,CAACE,cAAc,KAAKS,eAAe,CAACvB,EAAE,EAAE;YACpEb,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;YAC/DrB,WAAW,CAACgD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;cAC5Bf,EAAE,EAAEY,OAAO,CAACZ,EAAE;cACda,QAAQ,EAAED,OAAO,CAACC,QAAQ;cAC1BW,UAAU,EAAEZ,OAAO,CAACa,MAAM,CAACC,IAAI;cAC/BC,YAAY,EAAE,oCAAoCC,kBAAkB,CAAChB,OAAO,CAACa,MAAM,CAACC,IAAI,CAAC,qDAAqD;cAC9IG,IAAI,EAAEjB,OAAO,CAACO,OAAO;cACrBC,SAAS,EAAER,OAAO,CAACS,SAAS;cAC5BS,IAAI,EAAElB,OAAO,CAACmB;YAChB,CAAC,CAAC,CAAC;;YAEH;YACAC,UAAU,CAAC,MAAM;cAAA,IAAAC,qBAAA;cACf,CAAAA,qBAAA,GAAApD,cAAc,CAACqD,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;gBAAEC,QAAQ,EAAE;cAAS,CAAC,CAAC;YAChE,CAAC,EAAE,GAAG,CAAC;UACT,CAAC,MAAM;YACLjD,OAAO,CAACC,GAAG,CAAC,oFAAoF,CAAC;UACnG;UACA,OAAOmC,eAAe;QACxB,CAAC,CAAC;MACJ,CAAC;MAED,MAAMc,iBAAiB,GAAIzB,OAAO,IAAK;QACrCzB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEwB,OAAO,CAAC;MACnD,CAAC;MAED,MAAM0B,sBAAsB,GAAIC,IAAI,IAAK;QACvC9D,cAAc,CAACsC,IAAI,IAAI;UACrB,MAAMyB,MAAM,GAAG,IAAI9D,GAAG,CAACqC,IAAI,CAAC;UAC5B,IAAIwB,IAAI,CAACE,MAAM,KAAK,QAAQ,EAAE;YAC5BD,MAAM,CAACE,GAAG,CAACH,IAAI,CAACI,MAAM,CAAC;UACzB,CAAC,MAAM;YACLH,MAAM,CAACI,MAAM,CAACL,IAAI,CAACI,MAAM,CAAC;UAC5B;UACA,OAAOH,MAAM;QACf,CAAC,CAAC;MACJ,CAAC;MAED,MAAMK,gBAAgB,GAAIN,IAAI,IAAK;QACjC1E,uBAAuB,CAAC0D,eAAe,IAAI;UACzC,IAAIA,eAAe,IAAIgB,IAAI,CAACzB,cAAc,KAAKS,eAAe,CAACvB,EAAE,EAAE;YACjEpB,cAAc,CAACmC,IAAI,IAAI,IAAIrC,GAAG,CAAC,CAAC,GAAGqC,IAAI,EAAEwB,IAAI,CAACI,MAAM,CAAC,CAAC,CAAC;UACzD;UACA,OAAOpB,eAAe;QACxB,CAAC,CAAC;MACJ,CAAC;MAED,MAAMuB,uBAAuB,GAAIP,IAAI,IAAK;QACxC3D,cAAc,CAACmC,IAAI,IAAI;UACrB,MAAMyB,MAAM,GAAG,IAAI9D,GAAG,CAACqC,IAAI,CAAC;UAC5ByB,MAAM,CAACI,MAAM,CAACL,IAAI,CAACI,MAAM,CAAC;UAC1B,OAAOH,MAAM;QACf,CAAC,CAAC;MACJ,CAAC;;MAED;MACAlE,MAAM,CAACyB,EAAE,CAAC,aAAa,EAAEY,gBAAgB,CAAC;MAC1CrC,MAAM,CAACyB,EAAE,CAAC,cAAc,EAAEsC,iBAAiB,CAAC;MAC5C/D,MAAM,CAACyB,EAAE,CAAC,oBAAoB,EAAEuC,sBAAsB,CAAC;MACvDhE,MAAM,CAACyB,EAAE,CAAC,aAAa,EAAE8C,gBAAgB,CAAC;MAC1CvE,MAAM,CAACyB,EAAE,CAAC,qBAAqB,EAAE+C,uBAAuB,CAAC;;MAEzD;MACA,OAAO,MAAM;QACXxE,MAAM,CAACyE,GAAG,CAAC,aAAa,EAAEpC,gBAAgB,CAAC;QAC3CrC,MAAM,CAACyE,GAAG,CAAC,cAAc,EAAEV,iBAAiB,CAAC;QAC7C/D,MAAM,CAACyE,GAAG,CAAC,oBAAoB,EAAET,sBAAsB,CAAC;QACxDhE,MAAM,CAACyE,GAAG,CAAC,aAAa,EAAEF,gBAAgB,CAAC;QAC3CvE,MAAM,CAACyE,GAAG,CAAC,qBAAqB,EAAED,uBAAuB,CAAC;MAC5D,CAAC;IACH;EACF,CAAC,EAAE,CAACxE,MAAM,EAAEf,IAAI,CAACyC,EAAE,CAAC,CAAC;;EAErB;EACAzE,SAAS,CAAC,MAAM;IACdyH,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAzH,SAAS,CAAC,MAAM;IACd,IAAI+C,MAAM,IAAIZ,aAAa,CAACuF,MAAM,GAAG,CAAC,EAAE;MACtC9D,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrD1B,aAAa,CAACwF,OAAO,CAACC,YAAY,IAAI;QACpC7E,MAAM,CAAC2B,IAAI,CAAC,mBAAmB,EAAEkD,YAAY,CAACnD,EAAE,CAAC;QACjDb,OAAO,CAACC,GAAG,CAAC,mBAAmB+D,YAAY,CAACnD,EAAE,UAAU,CAAC;MAC3D,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC1B,MAAM,EAAEZ,aAAa,CAAC,CAAC;;EAE3B;EACA,MAAMsF,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFvF,YAAY,CAAC,IAAI,CAAC;MAClB,MAAM+C,QAAQ,GAAG,MAAM9E,GAAG,CAAC0H,GAAG,CAAC,yBAAyB,CAAC;;MAEzD;MACA,MAAMC,sBAAsB,GAAG7C,QAAQ,CAAC+B,IAAI,CAAC7E,aAAa,CAACsD,GAAG,CAACmC,YAAY;QAAA,IAAAG,qBAAA,EAAAC,sBAAA;QAAA,OAAK;UAC9EvD,EAAE,EAAEmD,YAAY,CAACnD,EAAE;UACnBwD,QAAQ,EAAEL,YAAY,CAACM,SAAS,CAACzD,EAAE;UACnC0D,UAAU,EAAEP,YAAY,CAACM,SAAS,CAAC/B,IAAI;UACvCR,WAAW,EAAE,EAAAoC,qBAAA,GAAAH,YAAY,CAACjC,WAAW,cAAAoC,qBAAA,uBAAxBA,qBAAA,CAA0BnC,OAAO,KAAI,iBAAiB;UACnEC,SAAS,EAAE,EAAAmC,sBAAA,GAAAJ,YAAY,CAACjC,WAAW,cAAAqC,sBAAA,uBAAxBA,sBAAA,CAA0BnC,SAAS,KAAI+B,YAAY,CAAC9B,SAAS;UACxEC,MAAM,EAAE6B,YAAY,CAACjC,WAAW,GAAG,CAACiC,YAAY,CAACjC,WAAW,CAACa,MAAM,IAAIoB,YAAY,CAACjC,WAAW,CAACL,QAAQ,KAAKtD,IAAI,CAACyC,EAAE,GAAG,KAAK;UAC5H2D,MAAM,EAAE,oCAAoC/B,kBAAkB,CAACuB,YAAY,CAACM,SAAS,CAAC/B,IAAI,CAAC,qDAAqD;UAChJe,MAAM,EAAEjE,WAAW,CAACoF,GAAG,CAACT,YAAY,CAACM,SAAS,CAACzD,EAAE,CAAC,GAAG,QAAQ,GAAG,SAAS;UACzE6D,OAAO,EAAE,KAAK;UACdC,QAAQ,EAAE;QACZ,CAAC;MAAA,CAAC,CAAC;MAEHnG,gBAAgB,CAAC0F,sBAAsB,CAAC;IAC1C,CAAC,CAAC,OAAOlD,KAAK,EAAE;MACdhB,OAAO,CAACgB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDxE,KAAK,CAACwE,KAAK,CAAC,wBAAwB,CAAC;IACvC,CAAC,SAAS;MACR1C,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACAlC,SAAS,CAAC,MAAM;IACd,IAAIqC,oBAAoB,EAAE;MACxBmG,YAAY,CAACnG,oBAAoB,CAACoC,EAAE,CAAC;IACvC;EACF,CAAC,EAAE,CAACpC,oBAAoB,CAAC,CAAC;;EAE1B;EACA,MAAMmG,YAAY,GAAG,MAAOjD,cAAc,IAAK;IAC7C,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAM9E,GAAG,CAAC0H,GAAG,CAAC,2BAA2BtC,cAAc,EAAE,CAAC;;MAE3E;MACA,MAAMkD,iBAAiB,GAAGxD,QAAQ,CAAC+B,IAAI,CAACzE,QAAQ,CAACkD,GAAG,CAACJ,OAAO,KAAK;QAC/DZ,EAAE,EAAEY,OAAO,CAACZ,EAAE;QACda,QAAQ,EAAED,OAAO,CAACC,QAAQ;QAC1BW,UAAU,EAAEZ,OAAO,CAACa,MAAM,CAACC,IAAI;QAC/BC,YAAY,EAAE,oCAAoCC,kBAAkB,CAAChB,OAAO,CAACa,MAAM,CAACC,IAAI,CAAC,qDAAqD;QAC9IG,IAAI,EAAEjB,OAAO,CAACO,OAAO;QACrBC,SAAS,EAAER,OAAO,CAACS,SAAS;QAC5BS,IAAI,EAAElB,OAAO,CAACmB,MAAM;QACpBkC,WAAW,EAAErD,OAAO,CAACqD;MACvB,CAAC,CAAC,CAAC;MAEHlG,WAAW,CAACiG,iBAAiB,CAAC;;MAE9B;MACAhC,UAAU,CAAC,MAAM;QAAA,IAAAkC,sBAAA;QACf,CAAAA,sBAAA,GAAArF,cAAc,CAACqD,OAAO,cAAAgC,sBAAA,uBAAtBA,sBAAA,CAAwB/B,cAAc,CAAC;UAAEC,QAAQ,EAAE;QAAS,CAAC,CAAC;MAChE,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACdhB,OAAO,CAACgB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDxE,KAAK,CAACwE,KAAK,CAAC,sBAAsB,CAAC;IACrC;EACF,CAAC;EACD;EACA,MAAMgE,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAACnG,WAAW,CAACoG,IAAI,CAAC,CAAC,IAAI,CAACxG,oBAAoB,EAAE;IAElDuB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;MACpCiF,UAAU,EAAEzG,oBAAoB,CAAC4F,QAAQ;MACzCrC,OAAO,EAAEnD,WAAW,CAACoG,IAAI,CAAC;IAC5B,CAAC,CAAC;IAEF,IAAI;MACF,MAAM5D,QAAQ,GAAG,MAAM9E,GAAG,CAAC4I,IAAI,CAAC,gBAAgB,EAAE;QAChDD,UAAU,EAAEzG,oBAAoB,CAAC4F,QAAQ;QACzCrC,OAAO,EAAEnD,WAAW,CAACoG,IAAI,CAAC;MAC5B,CAAC,CAAC;MAEFjF,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEoB,QAAQ,CAAC+B,IAAI,CAAC;;MAEjD;MACA,MAAMgC,UAAU,GAAG;QACjBvE,EAAE,EAAEQ,QAAQ,CAAC+B,IAAI,CAAC3B,OAAO,CAACZ,EAAE;QAC5Ba,QAAQ,EAAEtD,IAAI,CAACyC,EAAE;QACjBwB,UAAU,EAAE,GAAGjE,IAAI,CAACiH,SAAS,IAAIjH,IAAI,CAACkH,QAAQ,EAAE;QAChD9C,YAAY,EAAE,oCAAoCC,kBAAkB,CAACrE,IAAI,CAACiH,SAAS,CAAC,IAAI5C,kBAAkB,CAACrE,IAAI,CAACkH,QAAQ,CAAC,qDAAqD;QAC9K5C,IAAI,EAAE7D,WAAW,CAACoG,IAAI,CAAC,CAAC;QACxBhD,SAAS,EAAEZ,QAAQ,CAAC+B,IAAI,CAAC3B,OAAO,CAACS,SAAS;QAC1CS,IAAI,EAAE;MACR,CAAC;MAED/D,WAAW,CAACgD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEwD,UAAU,CAAC,CAAC;MAC1CtG,cAAc,CAAC,EAAE,CAAC;;MAElB;MACAN,gBAAgB,CAACoD,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IACpCA,IAAI,CAACjB,EAAE,KAAKpC,oBAAoB,CAACoC,EAAE,GAC/B;QAAE,GAAGiB,IAAI;QAAEC,WAAW,EAAElD,WAAW,CAACoG,IAAI,CAAC,CAAC;QAAEhD,SAAS,EAAEmD,UAAU,CAACnD;MAAU,CAAC,GAC7EH,IACN,CAAC,CAAC;;MAEF;MACAe,UAAU,CAAC,MAAM;QAAA,IAAA0C,sBAAA;QACf,CAAAA,sBAAA,GAAA7F,cAAc,CAACqD,OAAO,cAAAwC,sBAAA,uBAAtBA,sBAAA,CAAwBvC,cAAc,CAAC;UAAEC,QAAQ,EAAE;QAAS,CAAC,CAAC;MAChE,CAAC,EAAE,GAAG,CAAC;IAET,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACdhB,OAAO,CAACgB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDxE,KAAK,CAACwE,KAAK,CAAC,qBAAqB,CAAC;IACpC;EACF,CAAC;EAED5E,SAAS,CAAC,MAAM;IACd;IACA,IAAIsD,cAAc,CAACqD,OAAO,IAAInD,oBAAoB,CAACmD,OAAO,EAAE;MAC1DnD,oBAAoB,CAACmD,OAAO,CAACyC,SAAS,GAAG5F,oBAAoB,CAACmD,OAAO,CAAC0C,YAAY;IACpF;EACF,CAAC,EAAE,CAAC9G,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAM+G,iBAAiB,GAAIC,CAAC,IAAK;IAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBZ,WAAW,CAAC,CAAC;EACf,CAAC;;EAED;EACA,MAAMa,wBAAwB,GAAI7B,YAAY,IAAK;IACjDtF,uBAAuB,CAACsF,YAAY,CAAC;EACvC,CAAC;;EAED;EACA,MAAM8B,iBAAiB,GAAIC,UAAU,IAAK;IACxC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,KAAK,GAAG,IAAID,IAAI,CAAC,CAAC;IACxB,MAAME,SAAS,GAAG,IAAIF,IAAI,CAACC,KAAK,CAAC;IACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IAE1C,IAAIL,IAAI,CAACM,YAAY,CAAC,CAAC,KAAKJ,KAAK,CAACI,YAAY,CAAC,CAAC,EAAE;MAChD,OAAO3I,MAAM,CAACqI,IAAI,EAAE,OAAO,EAAE;QAAEO,MAAM,EAAE3I;MAAG,CAAC,CAAC;IAC9C,CAAC,MAAM,IAAIoI,IAAI,CAACM,YAAY,CAAC,CAAC,KAAKH,SAAS,CAACG,YAAY,CAAC,CAAC,EAAE;MAC3D,OAAO,MAAM,GAAG3I,MAAM,CAACqI,IAAI,EAAE,OAAO,EAAE;QAAEO,MAAM,EAAE3I;MAAG,CAAC,CAAC;IACvD,CAAC,MAAM;MACL,OAAOD,MAAM,CAACqI,IAAI,EAAE,cAAc,EAAE;QAAEO,MAAM,EAAE3I;MAAG,CAAC,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAM4I,qBAAqB,GAAGjI,aAAa,CAACU,MAAM,CAAC6C,IAAI,IAAI;IACzD;IACA,MAAM2E,aAAa,GAAG3E,IAAI,CAACyC,UAAU,CAACmC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC5H,UAAU,CAAC2H,WAAW,CAAC,CAAC,CAAC,IACjE5E,IAAI,CAACC,WAAW,CAAC2E,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC5H,UAAU,CAAC2H,WAAW,CAAC,CAAC,CAAC;;IAEtF;IACA,MAAME,aAAa,GAAG3H,MAAM,KAAK,KAAK,IAChBA,MAAM,KAAK,QAAQ,IAAI6C,IAAI,CAACK,MAAO,IACnClD,MAAM,KAAK,UAAU,IAAI6C,IAAI,CAAC6C,QAAS,IACvC1F,MAAM,KAAK,SAAS,IAAI6C,IAAI,CAAC4C,OAAQ;IAE3D,OAAO+B,aAAa,IAAIG,aAAa;EACvC,CAAC,CAAC;;EAEF;EACA,MAAMC,UAAU,GAAIhG,EAAE,IAAK;IACzBrC,gBAAgB,CAACsI,iBAAiB,IAChCA,iBAAiB,CAACjF,GAAG,CAACC,IAAI,IACxBA,IAAI,CAACjB,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGiB,IAAI;MAAE4C,OAAO,EAAE,CAAC5C,IAAI,CAAC4C;IAAQ,CAAC,GAAG5C,IACzD,CACF,CAAC;IAED,IAAIrD,oBAAoB,IAAIA,oBAAoB,CAACoC,EAAE,KAAKA,EAAE,EAAE;MAC1DnC,uBAAuB,CAACkD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE8C,OAAO,EAAE,CAAC9C,IAAI,CAAC8C;MAAQ,CAAC,CAAC,CAAC;IACxE;EACF,CAAC;;EAED;EACA,MAAMqC,aAAa,GAAIlG,EAAE,IAAK;IAC5BrC,gBAAgB,CAACsI,iBAAiB,IAChCA,iBAAiB,CAACjF,GAAG,CAACC,IAAI,IACxBA,IAAI,CAACjB,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGiB,IAAI;MAAE6C,QAAQ,EAAE,CAAC7C,IAAI,CAAC6C;IAAS,CAAC,GAAG7C,IAC3D,CACF,CAAC;IAED,IAAIrD,oBAAoB,IAAIA,oBAAoB,CAACoC,EAAE,KAAKA,EAAE,EAAE;MAC1DnC,uBAAuB,CAACkD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE+C,QAAQ,EAAE,CAAC/C,IAAI,CAAC+C;MAAS,CAAC,CAAC,CAAC;IAC1E;EACF,CAAC;EAED,IAAItG,SAAS,EAAE;IACb,oBACEN,OAAA;MAAKiJ,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5DlJ,OAAA;QAAKiJ,SAAS,EAAC;MAA6E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChG,CAAC;EAEV;EAEA,oBACEtJ,OAAA;IAAKiJ,SAAS,EAAC,6BAA6B;IAAAC,QAAA,gBAE1ClJ,OAAA;MAAKiJ,SAAS,EAAC,qFAAqF;MAAAC,QAAA,eAClGlJ,OAAA;QAAKiJ,SAAS,EAAC,uEAAuE;QAAAC,QAAA,gBACpFlJ,OAAA;UAAAkJ,QAAA,gBACElJ,OAAA;YAAIiJ,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClDtJ,OAAA;YAAGiJ,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAC;UAEpC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNtJ,OAAA;UAAKiJ,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAC1ClJ,OAAA;YAAQiJ,SAAS,EAAC,6NAA6N;YAAAC,QAAA,gBAC7OlJ,OAAA,CAACrB,0BAA0B;cAACsK,SAAS,EAAC,oBAAoB;cAAC,eAAY;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cAElF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtJ,OAAA;MAAKiJ,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5DlJ,OAAA;QAAKiJ,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBAEzClJ,OAAA;UAAKiJ,SAAS,EAAC,kEAAkE;UAAAC,QAAA,gBAC/ElJ,OAAA;YAAKiJ,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDlJ,OAAA;cAAIiJ,SAAS,EAAC,uDAAuD;cAAAC,QAAA,gBACnElJ,OAAA,CAACrB,0BAA0B;gBAACsK,SAAS,EAAC;cAA8B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAEzE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLtJ,OAAA;cAAKiJ,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BlJ,OAAA;gBACEuJ,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,0BAAqB;gBACjCP,SAAS,EAAC,qHAAqH;gBAC/HQ,KAAK,EAAEzI,UAAW;gBAClB0I,QAAQ,EAAG9B,CAAC,IAAK3G,aAAa,CAAC2G,CAAC,CAAC+B,MAAM,CAACF,KAAK;cAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACFtJ,OAAA,CAACpB,mBAAmB;gBAACqK,SAAS,EAAC;cAA+C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC,eACNtJ,OAAA;cAAKiJ,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAClClJ,OAAA;gBACEiJ,SAAS,EAAE,kCAAkC/H,MAAM,KAAK,KAAK,GACzD,+BAA+B,GAC/B,6CAA6C,EAAG;gBACpD0I,OAAO,EAAEA,CAAA,KAAMzI,SAAS,CAAC,KAAK,CAAE;gBAAA+H,QAAA,EACjC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTtJ,OAAA;gBACEiJ,SAAS,EAAE,kCAAkC/H,MAAM,KAAK,QAAQ,GAC5D,+BAA+B,GAC/B,6CAA6C,EAAG;gBACpD0I,OAAO,EAAEA,CAAA,KAAMzI,SAAS,CAAC,QAAQ,CAAE;gBAAA+H,QAAA,EACpC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTtJ,OAAA;gBACEiJ,SAAS,EAAE,kCAAkC/H,MAAM,KAAK,SAAS,GAC7D,+BAA+B,GAC/B,6CAA6C,EAAG;gBACpD0I,OAAO,EAAEA,CAAA,KAAMzI,SAAS,CAAC,SAAS,CAAE;gBAAA+H,QAAA,EACrC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTtJ,OAAA;gBACEiJ,SAAS,EAAE,kCAAkC/H,MAAM,KAAK,UAAU,GAC9D,+BAA+B,GAC/B,6CAA6C,EAAG;gBACpD0I,OAAO,EAAEA,CAAA,KAAMzI,SAAS,CAAC,UAAU,CAAE;gBAAA+H,QAAA,EACtC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNtJ,OAAA;YACE6J,GAAG,EAAEjI,gBAAiB;YACtBkI,KAAK,EAAE;cACLC,MAAM,EAAE,oBAAoB;cAC5BC,SAAS,EAAE,MAAM;cACjBC,cAAc,EAAE,MAAM;cACtBC,cAAc,EAAE;YAClB,CAAE;YAAAhB,QAAA,EAEDT,qBAAqB,CAAC1C,MAAM,KAAK,CAAC,gBACjC/F,OAAA;cAAKiJ,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAE/C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GAENb,qBAAqB,CAAC3E,GAAG,CAACmC,YAAY,iBACpCjG,OAAA;cAEEiJ,SAAS,EAAE,sEACT,CAAAvI,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEoC,EAAE,MAAKmD,YAAY,CAACnD,EAAE,GAAG,cAAc,GAAG,EAAE,IAChEmD,YAAY,CAAC7B,MAAM,GAAG,kCAAkC,GAAG,EAAE,EAAG;cACpEwF,OAAO,EAAEA,CAAA,KAAM9B,wBAAwB,CAAC7B,YAAY,CAAE;cAAAiD,QAAA,gBAEtDlJ,OAAA;gBAAKiJ,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzClJ,OAAA;kBAAKiJ,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrClJ,OAAA;oBACEmK,GAAG,EAAElE,YAAY,CAACQ,MAAO;oBACzB2D,GAAG,EAAEnE,YAAY,CAACO,UAAW;oBAC7ByC,SAAS,EAAE,0BACT,CAAAvI,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEoC,EAAE,MAAKmD,YAAY,CAACnD,EAAE,GACxC,wBAAwB,GACxB,EAAE;kBACL;oBAAAqG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,EACDrD,YAAY,CAACV,MAAM,KAAK,QAAQ,iBAC/BvF,OAAA;oBAAMiJ,SAAS,EAAC;kBAAyF;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CACjH;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACNtJ,OAAA;kBAAKiJ,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BlJ,OAAA;oBAAKiJ,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,gBAC/ClJ,OAAA;sBAAIiJ,SAAS,EAAE,uBAAuBhD,YAAY,CAAC7B,MAAM,GAAG,eAAe,GAAG,eAAe,EAAG;sBAAA8E,QAAA,EAC7FjD,YAAY,CAACO;oBAAU;sBAAA2C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB,CAAC,eACLtJ,OAAA;sBAAKiJ,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,GACzCjD,YAAY,CAACU,OAAO,iBACnB3G,OAAA,CAACL,QAAQ;wBAACsJ,SAAS,EAAC;sBAAsC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAC7D,eACDtJ,OAAA;wBAAMiJ,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EACpCnB,iBAAiB,CAAC9B,YAAY,CAAC/B,SAAS;sBAAC;wBAAAiF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNtJ,OAAA;oBAAGiJ,SAAS,EAAE,yBACZhD,YAAY,CAAC7B,MAAM,GAAG,2BAA2B,GAAG,eAAe,EAClE;oBAAA8E,QAAA,EACAjD,YAAY,CAACjC;kBAAW;oBAAAmF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,eACJtJ,OAAA;oBAAKiJ,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrDlJ,OAAA;sBAAMiJ,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EACpCjD,YAAY,CAACV,MAAM,KAAK,QAAQ,GAC7B,WAAW,GACXU,YAAY,CAACoE,QAAQ,GACnB,gBAAgBpE,YAAY,CAACoE,QAAQ,EAAE,GACvC;oBAAE;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACPtJ,OAAA;sBAAKiJ,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,gBAC7BlJ,OAAA;wBACE4J,OAAO,EAAGhC,CAAC,IAAK;0BACdA,CAAC,CAAC0C,eAAe,CAAC,CAAC;0BACnBxB,UAAU,CAAC7C,YAAY,CAACnD,EAAE,CAAC;wBAC7B,CAAE;wBACFmG,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,eAE/ClJ,OAAA,CAACL,QAAQ;0BAACsJ,SAAS,EAAE,WAAWhD,YAAY,CAACU,OAAO,GAAG,8BAA8B,GAAG,EAAE;wBAAG;0BAAAwC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1F,CAAC,eACTtJ,OAAA;wBACE4J,OAAO,EAAGhC,CAAC,IAAK;0BACdA,CAAC,CAAC0C,eAAe,CAAC,CAAC;0BACnBtB,aAAa,CAAC/C,YAAY,CAACnD,EAAE,CAAC;wBAChC,CAAE;wBACFmG,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,eAE7ClJ,OAAA,CAACN,cAAc;0BAACuJ,SAAS,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACLrD,YAAY,CAAC7B,MAAM,iBAClBpE,OAAA;gBAAMiJ,SAAS,EAAC,2GAA2G;gBAAAC,QAAA,EAAC;cAE5H;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP;YAAA,GA3EIrD,YAAY,CAACnD,EAAE;cAAAqG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA4EjB,CACN;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtJ,OAAA;UAAKiJ,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EACrDxI,oBAAoB,gBACnBV,OAAA,CAAAE,SAAA;YAAAgJ,QAAA,gBAEElJ,OAAA;cAAKiJ,SAAS,EAAC,yEAAyE;cAAAC,QAAA,gBACtFlJ,OAAA;gBAAKiJ,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChClJ,OAAA;kBAAKiJ,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BlJ,OAAA;oBACEmK,GAAG,EAAEzJ,oBAAoB,CAAC+F,MAAO;oBACjC2D,GAAG,EAAE1J,oBAAoB,CAAC8F,UAAW;oBACrCyC,SAAS,EAAC;kBAAwB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC,EACD5I,oBAAoB,CAAC6E,MAAM,KAAK,QAAQ,iBACvCvF,OAAA;oBAAMiJ,SAAS,EAAC;kBAAyF;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CACjH;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACNtJ,OAAA;kBAAAkJ,QAAA,gBACElJ,OAAA;oBAAIiJ,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAC9CxI,oBAAoB,CAAC8F;kBAAU;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACLtJ,OAAA;oBAAGiJ,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EACjCxI,oBAAoB,CAAC6E,MAAM,KAAK,QAAQ,GACrC,WAAW,GACX7E,oBAAoB,CAAC2J,QAAQ,GAC3B,gBAAgB3J,oBAAoB,CAAC2J,QAAQ,EAAE,GAC/C;kBAAE;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNtJ,OAAA;gBAAKiJ,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1ClJ,OAAA;kBAAQiJ,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,eAClElJ,OAAA,CAACb,SAAS;oBAAC8J,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACTtJ,OAAA;kBAAQiJ,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,eAClElJ,OAAA,CAACZ,eAAe;oBAAC6J,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACTtJ,OAAA;kBAAQiJ,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,eAClElJ,OAAA,CAACX,qBAAqB;oBAAC4J,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACTtJ,OAAA;kBAAQiJ,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,eAClElJ,OAAA,CAACd,sBAAsB;oBAAC+J,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNtJ,OAAA;cACE6J,GAAG,EAAEhI,oBAAqB;cAC1BoH,SAAS,EAAC,gBAAgB;cAC1Ba,KAAK,EAAE;gBACLC,MAAM,EAAE,oBAAoB;gBAC5BC,SAAS,EAAE,MAAM;gBACjBC,cAAc,EAAE,MAAM;gBACtBC,cAAc,EAAE;cAClB,CAAE;cAAAhB,QAAA,GAEDtI,QAAQ,CAACkD,GAAG,CAAC,CAACJ,OAAO,EAAE6G,KAAK,KAAK;gBAChC,MAAMC,QAAQ,GAAG9G,OAAO,CAACC,QAAQ,KAAKtD,IAAI,CAACyC,EAAE;gBAC7C,MAAM2H,UAAU,GAAGF,KAAK,KAAK,CAAC,IAAI3J,QAAQ,CAAC2J,KAAK,GAAG,CAAC,CAAC,CAAC5G,QAAQ,KAAKD,OAAO,CAACC,QAAQ;gBAEnF,oBACE3D,OAAA;kBAAsBiJ,SAAS,EAAE,QAAQuB,QAAQ,GAAG,aAAa,GAAG,eAAe,OAAQ;kBAAAtB,QAAA,GACxF,CAACsB,QAAQ,IAAIC,UAAU,iBACtBzK,OAAA;oBACEmK,GAAG,EAAEzG,OAAO,CAACe,YAAa;oBAC1B2F,GAAG,EAAE1G,OAAO,CAACY,UAAW;oBACxB2E,SAAS,EAAC;kBAAgC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CACF,EACA,CAACkB,QAAQ,IAAI,CAACC,UAAU,iBAAIzK,OAAA;oBAAKiJ,SAAS,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7DtJ,OAAA;oBACEiJ,SAAS,EAAE,yDACTuB,QAAQ,GACJ,0CAA0C,GAC1C,+DAA+D,EAClE;oBAAAtB,QAAA,gBAEHlJ,OAAA;sBAAGiJ,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAExF,OAAO,CAACiB;oBAAI;sBAAAwE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzCtJ,OAAA;sBAAKiJ,SAAS,EAAE,gBAAgBuB,QAAQ,GAAG,iBAAiB,GAAG,eAAe,gCAAiC;sBAAAtB,QAAA,GAC5GnB,iBAAiB,CAACrE,OAAO,CAACQ,SAAS,CAAC,EACpCsG,QAAQ,iBACPxK,OAAA,CAACT,eAAe;wBAAC0J,SAAS,EAAC,cAAc;wBAACyB,KAAK,EAAEhH,OAAO,CAACkB,IAAI,GAAG,QAAQ,GAAG;sBAAW;wBAAAuE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CACzF;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EACLkB,QAAQ,IAAIC,UAAU,iBACrBzK,OAAA;oBACEmK,GAAG,EAAEzG,OAAO,CAACe,YAAa;oBAC1B2F,GAAG,EAAE1G,OAAO,CAACY,UAAW;oBACxB2E,SAAS,EAAC;kBAAgC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CACF,EACAkB,QAAQ,IAAI,CAACC,UAAU,iBAAIzK,OAAA;oBAAKiJ,SAAS,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,GA/BpD5F,OAAO,CAACZ,EAAE;kBAAAqG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAgCf,CAAC;cAEV,CAAC,CAAC,eACFtJ,OAAA;gBAAK6J,GAAG,EAAElI;cAAe;gBAAAwH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eAGNtJ,OAAA;cAAKiJ,SAAS,EAAC,uCAAuC;cAAAC,QAAA,eACpDlJ,OAAA;gBAAM2K,QAAQ,EAAEhD,iBAAkB;gBAACsB,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC3DlJ,OAAA;kBACEuJ,IAAI,EAAC,QAAQ;kBACbN,SAAS,EAAC,uEAAuE;kBAAAC,QAAA,eAEjFlJ,OAAA,CAACjB,aAAa;oBAACkK,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACTtJ,OAAA;kBAAKiJ,SAAS,EAAC,aAAa;kBAAAC,QAAA,eAC1BlJ,OAAA;oBACEiJ,SAAS,EAAC,qHAAqH;oBAC/HO,WAAW,EAAC,yCAAqB;oBACjCoB,IAAI,EAAC,GAAG;oBACRnB,KAAK,EAAE3I,WAAY;oBACnB4I,QAAQ,EAAG9B,CAAC,IAAK7G,cAAc,CAAC6G,CAAC,CAAC+B,MAAM,CAACF,KAAK;kBAAE;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACNtJ,OAAA;kBACEuJ,IAAI,EAAC,QAAQ;kBACbN,SAAS,EAAC,uEAAuE;kBAAAC,QAAA,eAEjFlJ,OAAA,CAAChB,aAAa;oBAACiK,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACTtJ,OAAA;kBACEuJ,IAAI,EAAC,QAAQ;kBACbsB,QAAQ,EAAE,CAAC/J,WAAW,CAACoG,IAAI,CAAC,CAAE;kBAC9B+B,SAAS,EAAE,yBACTnI,WAAW,CAACoG,IAAI,CAAC,CAAC,GACd,8CAA8C,GAC9C,8CAA8C,qBAC9B;kBAAAgC,QAAA,eAEtBlJ,OAAA,CAAClB,iBAAiB;oBAACmK,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,eACN,CAAC;UAAA;UAEH;UACAtJ,OAAA;YAAKiJ,SAAS,EAAC,iEAAiE;YAAAC,QAAA,eAC9ElJ,OAAA;cAAKiJ,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1ClJ,OAAA,CAACrB,0BAA0B;gBAACsK,SAAS,EAAC;cAAsC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/EtJ,OAAA;gBAAIiJ,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxEtJ,OAAA;gBAAGiJ,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAErC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClJ,EAAA,CAxsBID,YAAY;EAAA,QACC5B,OAAO;AAAA;AAAAuM,EAAA,GADpB3K,YAAY;AA0sBlB,eAAeA,YAAY;AAAC,IAAA2K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}