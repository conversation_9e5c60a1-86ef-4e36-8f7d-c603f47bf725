{"ast": null, "code": "var _jsxFileName = \"C:\\\\burky root\\\\burky_root_web\\\\client\\\\src\\\\components\\\\layout\\\\MainLayout.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, Fragment } from 'react';\nimport { Link, Outlet, useLocation, useNavigate } from 'react-router-dom';\nimport { Dialog, Menu, Transition } from '@headlessui/react';\nimport { Bars3Icon, XMarkIcon, UserCircleIcon, ArrowRightOnRectangleIcon, Cog6ToothIcon, ChevronDownIcon, HomeIcon, UsersIcon, UserGroupIcon, ShieldCheckIcon, ClipboardDocumentListIcon, UserIcon, CalendarIcon, ChatBubbleLeftRightIcon, CreditCardIcon, DocumentTextIcon, ChartBarIcon, ClockIcon, AcademicCapIcon } from '@heroicons/react/24/outline';\nimport { useAuth } from '../../hooks/useAuth';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MainLayout = ({\n  children\n}) => {\n  _s();\n  var _user$role, _user$role2, _user$role3, _user$role4, _user$role5;\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const {\n    user,\n    hasPermission,\n    logout\n  } = useAuth();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const handleLogout = async () => {\n    try {\n      await logout();\n      navigate('/auth/login');\n    } catch (error) {\n      console.error('Logout failed:', error);\n    }\n  };\n\n  // Ana navigasyon öğeleri - admin\n  const adminNavigation = [{\n    name: 'Dashboard',\n    href: '/dashboard',\n    icon: HomeIcon,\n    permission: 'READ_DASHBOARD',\n    current: location.pathname === '/dashboard'\n  }, {\n    name: 'Kullanıcılar',\n    href: '/admin/users',\n    icon: UsersIcon,\n    permission: 'READ_ADMIN_USERS',\n    current: location.pathname.startsWith('/admin/users')\n  }, {\n    name: 'Roller',\n    href: '/admin/roles',\n    icon: ShieldCheckIcon,\n    permission: 'READ_ADMIN_ROLES',\n    current: location.pathname.startsWith('/admin/roles')\n  }, {\n    name: 'Ayarlar',\n    href: '/admin/settings',\n    icon: Cog6ToothIcon,\n    permission: 'READ_ADMIN_SETTINGS',\n    current: location.pathname.startsWith('/admin/settings')\n  }, {\n    name: 'Denetim Kayıtları',\n    href: '/admin/audit-logs',\n    icon: ClipboardDocumentListIcon,\n    permission: 'READ_ADMIN_AUDIT_LOGS',\n    current: location.pathname.startsWith('/admin/audit-logs')\n  }];\n\n  // Uzman navigasyonu\n  const expertNavigation = [{\n    name: 'Uzman Paneli',\n    href: '/expert/dashboard',\n    icon: ChartBarIcon,\n    permission: 'READ_EXPERT_DASHBOARD',\n    current: location.pathname === '/expert/dashboard'\n  }, {\n    name: 'Uygunluklar',\n    href: '/expert/availabilities',\n    icon: ClockIcon,\n    permission: 'READ_EXPERT_AVAILABILITIES',\n    current: location.pathname.startsWith('/expert/availabilities')\n  }, {\n    name: 'Randevular',\n    href: '/expert/appointments',\n    icon: CalendarIcon,\n    permission: 'READ_EXPERT_APPOINTMENTS',\n    current: location.pathname.startsWith('/expert/appointments')\n  }, {\n    name: 'Görüşmeler',\n    href: '/expert/sessions',\n    icon: AcademicCapIcon,\n    permission: 'READ_EXPERT_SESSIONS',\n    current: location.pathname.startsWith('/expert/sessions')\n  }, {\n    name: 'Mesajlar',\n    href: '/expert/messages',\n    icon: ChatBubbleLeftRightIcon,\n    permission: 'READ_EXPERT_MESSAGES',\n    current: location.pathname.startsWith('/expert/messages')\n  }, {\n    name: 'Danışanlar',\n    href: '/expert/clients',\n    icon: UserGroupIcon,\n    permission: 'READ_EXPERT_CLIENTS',\n    current: location.pathname.startsWith('/expert/clients')\n  }, {\n    name: 'Raporlar',\n    href: '/expert/reports',\n    icon: DocumentTextIcon,\n    permission: 'READ_EXPERT_REPORTS',\n    current: location.pathname.startsWith('/expert/reports')\n  }];\n\n  // Danışan navigasyonu\n  const clientNavigation = [{\n    name: 'Danışan Paneli',\n    href: '/client/dashboard',\n    icon: ChartBarIcon,\n    permission: 'READ_CLIENT_DASHBOARD',\n    current: location.pathname === '/client/dashboard'\n  }, {\n    name: 'Uzmanlar',\n    href: '/client/experts',\n    icon: UserIcon,\n    permission: 'READ_CLIENT_EXPERTS',\n    current: location.pathname.startsWith('/client/experts')\n  }, {\n    name: 'Randevular',\n    href: '/client/appointments',\n    icon: CalendarIcon,\n    permission: 'READ_CLIENT_APPOINTMENTS',\n    current: location.pathname.startsWith('/client/appointments')\n  }, {\n    name: 'Görüşmeler',\n    href: '/client/sessions',\n    icon: AcademicCapIcon,\n    permission: 'READ_CLIENT_SESSIONS',\n    current: location.pathname.startsWith('/client/sessions')\n  }, {\n    name: 'Mesajlar',\n    href: '/client/messages',\n    icon: ChatBubbleLeftRightIcon,\n    permission: 'READ_CLIENT_MESSAGES',\n    current: location.pathname.startsWith('/client/messages')\n  }, {\n    name: 'Paketler',\n    href: '/client/packages',\n    icon: ClipboardDocumentListIcon,\n    permission: 'READ_CLIENT_PACKAGES',\n    current: location.pathname.startsWith('/client/packages')\n  }, {\n    name: 'Ödemeler',\n    href: '/client/payments',\n    icon: CreditCardIcon,\n    permission: 'READ_CLIENT_PAYMENTS',\n    current: location.pathname.startsWith('/client/payments')\n  }];\n\n  // Rol tabanlı menü bölümlerini gösterme/gizleme\n  const isAdmin = (user === null || user === void 0 ? void 0 : (_user$role = user.role) === null || _user$role === void 0 ? void 0 : _user$role.name) === 'Admin';\n\n  // Rol bilgisini kontrol ediyoruz - özel roller için \"burak rol1\" gibi\n  const isCustomRole = user && ((_user$role2 = user.role) === null || _user$role2 === void 0 ? void 0 : _user$role2.name) && !['Admin', 'Expert', 'Client'].includes(user.role.name);\n\n  // Uzman ve danışan kontrolleri\n  const isExpert = user && (isAdmin || ((_user$role3 = user.role) === null || _user$role3 === void 0 ? void 0 : _user$role3.name) === 'Expert' || user.userType === 'expert' || user.isExpert === true);\n  const isClient = user && (isAdmin || ((_user$role4 = user.role) === null || _user$role4 === void 0 ? void 0 : _user$role4.name) === 'Client' || user.userType === 'client' || user.isClient === true);\n  console.log('Kullanıcı rol kontrolü (detaylı):', {\n    isAdmin,\n    isExpert,\n    isClient,\n    isCustomRole,\n    \"user.role\": user === null || user === void 0 ? void 0 : user.role,\n    \"user.role?.name\": user === null || user === void 0 ? void 0 : (_user$role5 = user.role) === null || _user$role5 === void 0 ? void 0 : _user$role5.name,\n    \"user.userType\": user === null || user === void 0 ? void 0 : user.userType\n  });\n\n  // Filtre fonksiyonları - izinleri daha esnek kontrol ediyoruz\n  const filterAdminItems = item => {\n    // Admin her zaman tüm menüleri görür\n    if (isAdmin) return true;\n\n    // Özel rol için READ izni varsa item.permission ile başlayan herşeyi göster \n    if (isCustomRole) {\n      const hasPermissionResult = hasPermission(item.permission);\n      console.log(`Özel rol admin menü kontrolü: ${item.name} (${item.permission}) - Sonuç: ${hasPermissionResult}`);\n      return hasPermissionResult;\n    }\n    return hasPermission(item.permission);\n  };\n  const filterExpertItems = item => {\n    // Admin her zaman tüm menüleri görür\n    if (isAdmin) return true;\n\n    // Uzman rolü varsa uzman menülerini görür\n    if (isExpert) return true;\n\n    // Özel rol için READ izni varsa item.permission ile başlayan herşeyi göster\n    if (isCustomRole) {\n      const hasPermissionResult = hasPermission(item.permission);\n      console.log(`Özel rol uzman menü kontrolü: ${item.name} (${item.permission}) - Sonuç: ${hasPermissionResult}`);\n      return hasPermissionResult;\n    }\n    return hasPermission(item.permission);\n  };\n  const filterClientItems = item => {\n    // Admin her zaman tüm menüleri görür\n    if (isAdmin) return true;\n\n    // Danışan rolü varsa danışan menülerini görür\n    if (isClient) return true;\n\n    // Özel rol için READ izni varsa item.permission ile başlayan herşeyi göster\n    if (isCustomRole) {\n      const hasPermissionResult = hasPermission(item.permission);\n      console.log(`Özel rol danışan menü kontrolü: ${item.name} (${item.permission}) - Sonuç: ${hasPermissionResult}`);\n      return hasPermissionResult;\n    }\n    return hasPermission(item.permission);\n  };\n\n  // Filtrelenmiş navigasyon öğeleri - sadece izni olanlar\n  const filteredAdminNavigation = adminNavigation.filter(filterAdminItems);\n  const filteredExpertNavigation = expertNavigation.filter(filterExpertItems);\n  const filteredClientNavigation = clientNavigation.filter(filterClientItems);\n  console.log('Filtrelenmiş menü öğeleri:', {\n    \"Admin menü sayısı\": filteredAdminNavigation.length,\n    \"Admin menüler\": filteredAdminNavigation.map(item => item.name),\n    \"Uzman menü sayısı\": filteredExpertNavigation.length,\n    \"Uzman menüler\": filteredExpertNavigation.map(item => item.name),\n    \"Danışan menü sayısı\": filteredClientNavigation.length,\n    \"Danışan menüler\": filteredClientNavigation.map(item => item.name)\n  });\n\n  // Ana navigasyon kategorilere ayrılmış\n  // Özel rol için: Herhangi bir bölümde filtrelenmiş öğe varsa o bölümü göster\n  const hasAdminSection = isAdmin || isCustomRole && filteredAdminNavigation.length > 0 || !isCustomRole && filteredAdminNavigation.length > 0;\n  const hasExpertSection = isExpert || isCustomRole && filteredExpertNavigation.length > 0 || !isCustomRole && filteredExpertNavigation.length > 0;\n  const hasClientSection = isClient || isCustomRole && filteredClientNavigation.length > 0 || !isCustomRole && filteredClientNavigation.length > 0;\n  console.log('Menü bölümleri görünürlüğü:', {\n    hasAdminSection,\n    hasExpertSection,\n    hasClientSection,\n    \"filteredAdmin.length\": filteredAdminNavigation.length,\n    \"filteredExpert.length\": filteredExpertNavigation.length,\n    \"filteredClient.length\": filteredClientNavigation.length\n  });\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Transition.Root, {\n        show: sidebarOpen,\n        as: Fragment,\n        children: /*#__PURE__*/_jsxDEV(Dialog, {\n          as: \"div\",\n          className: \"relative z-40 md:hidden\",\n          onClose: setSidebarOpen,\n          children: [/*#__PURE__*/_jsxDEV(Transition.Child, {\n            as: Fragment,\n            enter: \"transition-opacity ease-linear duration-300\",\n            enterFrom: \"opacity-0\",\n            enterTo: \"opacity-100\",\n            leave: \"transition-opacity ease-linear duration-300\",\n            leaveFrom: \"opacity-100\",\n            leaveTo: \"opacity-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fixed inset-0 bg-gray-600 bg-opacity-75\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fixed inset-0 z-40 flex\",\n            children: [/*#__PURE__*/_jsxDEV(Transition.Child, {\n              as: Fragment,\n              enter: \"transition ease-in-out duration-300 transform\",\n              enterFrom: \"-translate-x-full\",\n              enterTo: \"translate-x-0\",\n              leave: \"transition ease-in-out duration-300 transform\",\n              leaveFrom: \"translate-x-0\",\n              leaveTo: \"-translate-x-full\",\n              children: /*#__PURE__*/_jsxDEV(Dialog.Panel, {\n                className: \"relative flex w-full max-w-xs flex-1 flex-col bg-white\",\n                children: [/*#__PURE__*/_jsxDEV(Transition.Child, {\n                  as: Fragment,\n                  enter: \"ease-in-out duration-300\",\n                  enterFrom: \"opacity-0\",\n                  enterTo: \"opacity-100\",\n                  leave: \"ease-in-out duration-300\",\n                  leaveFrom: \"opacity-100\",\n                  leaveTo: \"opacity-0\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute top-0 right-0 -mr-12 pt-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      className: \"ml-1 flex h-10 w-10 items-center justify-center rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\",\n                      onClick: () => setSidebarOpen(false),\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"sr-only\",\n                        children: \"Close sidebar\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 344,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(XMarkIcon, {\n                        className: \"h-6 w-6 text-white\",\n                        \"aria-hidden\": \"true\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 345,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 339,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 338,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-0 flex-1 overflow-y-auto pt-5 pb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-shrink-0 items-center px-4\",\n                    children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                      className: \"text-xl font-bold text-primary-600\",\n                      children: \"Burky Root\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 351,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 350,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n                    className: \"mt-5 space-y-1 px-2\",\n                    children: [hasAdminSection && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"pt-2 pb-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"px-3 py-1 font-semibold text-xs uppercase tracking-wider text-gray-500\",\n                        children: \"Y\\xF6netici\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 357,\n                        columnNumber: 27\n                      }, this), filteredAdminNavigation.map(item => /*#__PURE__*/_jsxDEV(Link, {\n                        to: item.href,\n                        className: `${item.current ? 'bg-gray-100 text-primary-600' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'} group flex items-center px-2 py-2 text-sm font-medium rounded-md`,\n                        onClick: () => setSidebarOpen(false),\n                        children: [/*#__PURE__*/_jsxDEV(item.icon, {\n                          className: `${item.current ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-500'} mr-3 flex-shrink-0 h-6 w-6`,\n                          \"aria-hidden\": \"true\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 371,\n                          columnNumber: 31\n                        }, this), item.name]\n                      }, item.name, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 361,\n                        columnNumber: 29\n                      }, this))]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 356,\n                      columnNumber: 25\n                    }, this), hasExpertSection && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"pt-2 pb-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"px-3 py-1 font-semibold text-xs uppercase tracking-wider text-gray-500\",\n                        children: \"Uzman\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 386,\n                        columnNumber: 27\n                      }, this), filteredExpertNavigation.map(item => /*#__PURE__*/_jsxDEV(Link, {\n                        to: item.href,\n                        className: `${item.current ? 'bg-gray-100 text-primary-600' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'} group flex items-center px-2 py-2 text-sm font-medium rounded-md`,\n                        onClick: () => setSidebarOpen(false),\n                        children: [/*#__PURE__*/_jsxDEV(item.icon, {\n                          className: `${item.current ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-500'} mr-3 flex-shrink-0 h-6 w-6`,\n                          \"aria-hidden\": \"true\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 400,\n                          columnNumber: 31\n                        }, this), item.name]\n                      }, item.name, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 390,\n                        columnNumber: 29\n                      }, this))]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 385,\n                      columnNumber: 25\n                    }, this), hasClientSection && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"pt-2 pb-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"px-3 py-1 font-semibold text-xs uppercase tracking-wider text-gray-500\",\n                        children: \"Dan\\u0131\\u015Fan\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 415,\n                        columnNumber: 27\n                      }, this), filteredClientNavigation.map(item => /*#__PURE__*/_jsxDEV(Link, {\n                        to: item.href,\n                        className: `${item.current ? 'bg-gray-100 text-primary-600' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'} group flex items-center px-2 py-2 text-sm font-medium rounded-md`,\n                        onClick: () => setSidebarOpen(false),\n                        children: [/*#__PURE__*/_jsxDEV(item.icon, {\n                          className: `${item.current ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-500'} mr-3 flex-shrink-0 h-6 w-6`,\n                          \"aria-hidden\": \"true\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 429,\n                          columnNumber: 31\n                        }, this), item.name]\n                      }, item.name, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 419,\n                        columnNumber: 29\n                      }, this))]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 414,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 353,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-shrink-0 border-t border-gray-200 p-4\",\n                  children: /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/profile\",\n                    className: \"group block flex-shrink-0\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: /*#__PURE__*/_jsxDEV(UserCircleIcon, {\n                          className: \"inline-block h-10 w-10 rounded-full text-gray-500\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 446,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 445,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"ml-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-base font-medium text-gray-700 group-hover:text-gray-900\",\n                          children: (user === null || user === void 0 ? void 0 : user.name) || 'Kullanıcı'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 449,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-sm font-medium text-gray-500 group-hover:text-gray-700\",\n                          children: \"Profil\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 452,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 448,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 444,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 443,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-14 flex-shrink-0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hidden md:fixed md:inset-y-0 md:flex md:w-64 md:flex-col\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex min-h-0 flex-1 flex-col bg-white border-r border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-1 flex-col overflow-y-auto pt-5 pb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-shrink-0 items-center px-4\",\n              children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-xl font-bold text-primary-600\",\n                children: \"Burky Root\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n              className: \"mt-5 flex-1 space-y-2 px-2\",\n              children: [hasAdminSection && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pt-2 pb-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"px-3 py-1 font-semibold text-xs uppercase tracking-wider text-gray-500\",\n                  children: \"Y\\xF6netici\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 477,\n                  columnNumber: 21\n                }, this), filteredAdminNavigation.map(item => /*#__PURE__*/_jsxDEV(Link, {\n                  to: item.href,\n                  className: `${item.current ? 'bg-gray-100 text-primary-600' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'} group flex items-center px-2 py-2 text-sm font-medium rounded-md`,\n                  children: [/*#__PURE__*/_jsxDEV(item.icon, {\n                    className: `${item.current ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-500'} mr-3 flex-shrink-0 h-6 w-6`,\n                    \"aria-hidden\": \"true\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 490,\n                    columnNumber: 25\n                  }, this), item.name]\n                }, item.name, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 19\n              }, this), hasExpertSection && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pt-2 pb-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"px-3 py-1 font-semibold text-xs uppercase tracking-wider text-gray-500\",\n                  children: \"Uzman\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 505,\n                  columnNumber: 21\n                }, this), filteredExpertNavigation.map(item => /*#__PURE__*/_jsxDEV(Link, {\n                  to: item.href,\n                  className: `${item.current ? 'bg-gray-100 text-primary-600' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'} group flex items-center px-2 py-2 text-sm font-medium rounded-md`,\n                  children: [/*#__PURE__*/_jsxDEV(item.icon, {\n                    className: `${item.current ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-500'} mr-3 flex-shrink-0 h-6 w-6`,\n                    \"aria-hidden\": \"true\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 518,\n                    columnNumber: 25\n                  }, this), item.name]\n                }, item.name, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 19\n              }, this), hasClientSection && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pt-2 pb-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"px-3 py-1 font-semibold text-xs uppercase tracking-wider text-gray-500\",\n                  children: \"Dan\\u0131\\u015Fan\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 533,\n                  columnNumber: 21\n                }, this), filteredClientNavigation.map(item => /*#__PURE__*/_jsxDEV(Link, {\n                  to: item.href,\n                  className: `${item.current ? 'bg-gray-100 text-primary-600' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'} group flex items-center px-2 py-2 text-sm font-medium rounded-md`,\n                  children: [/*#__PURE__*/_jsxDEV(item.icon, {\n                    className: `${item.current ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-500'} mr-3 flex-shrink-0 h-6 w-6`,\n                    \"aria-hidden\": \"true\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 546,\n                    columnNumber: 25\n                  }, this), item.name]\n                }, item.name, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 537,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-shrink-0 border-t border-gray-200 p-4\",\n            children: /*#__PURE__*/_jsxDEV(Menu, {\n              as: \"div\",\n              className: \"relative inline-block text-left w-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(Menu.Button, {\n                  className: \"group w-full rounded-md text-sm text-left font-medium text-gray-700 hover:text-gray-900 focus:outline-none\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex w-full items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(UserCircleIcon, {\n                        className: \"inline-block h-10 w-10 rounded-full text-gray-500\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 565,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 564,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"ml-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium text-gray-700 group-hover:text-gray-900\",\n                        children: (user === null || user === void 0 ? void 0 : user.name) || 'Kullanıcı'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 568,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs font-medium text-gray-500 group-hover:text-gray-700\",\n                        children: (user === null || user === void 0 ? void 0 : user.email) || 'Kullanıcı e-postası'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 571,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 567,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(ChevronDownIcon, {\n                      className: \"ml-auto h-5 w-5 flex-shrink-0 text-gray-400 group-hover:text-gray-500\",\n                      \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 575,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 563,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Transition, {\n                as: Fragment,\n                enter: \"transition ease-out duration-100\",\n                enterFrom: \"transform opacity-0 scale-95\",\n                enterTo: \"transform opacity-100 scale-100\",\n                leave: \"transition ease-in duration-75\",\n                leaveFrom: \"transform opacity-100 scale-100\",\n                leaveTo: \"transform opacity-0 scale-95\",\n                children: /*#__PURE__*/_jsxDEV(Menu.Items, {\n                  className: \"absolute bottom-full left-0 z-10 mb-2 w-56 origin-bottom-left rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"py-1\",\n                    children: [/*#__PURE__*/_jsxDEV(Menu.Item, {\n                      children: ({\n                        active\n                      }) => /*#__PURE__*/_jsxDEV(Link, {\n                        to: \"/profile\",\n                        className: `${active ? 'bg-gray-100 text-gray-900' : 'text-gray-700'} flex px-4 py-2 text-sm`,\n                        children: [/*#__PURE__*/_jsxDEV(UserCircleIcon, {\n                          className: \"mr-3 h-5 w-5 text-gray-400\",\n                          \"aria-hidden\": \"true\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 601,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: \"Profilim\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 602,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 595,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 593,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Menu.Item, {\n                      children: ({\n                        active\n                      }) => /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: handleLogout,\n                        className: `${active ? 'bg-gray-100 text-gray-900' : 'text-gray-700'} flex w-full px-4 py-2 text-left text-sm`,\n                        children: [/*#__PURE__*/_jsxDEV(ArrowRightOnRectangleIcon, {\n                          className: \"mr-3 h-5 w-5 text-gray-400\",\n                          \"aria-hidden\": \"true\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 614,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: \"\\xC7\\u0131k\\u0131\\u015F Yap\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 615,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 608,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 606,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 592,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 591,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 582,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 559,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-1 flex-col md:pl-64\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sticky top-0 z-10 bg-white pl-1 pt-1 sm:pl-3 sm:pt-3 md:hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"-ml-0.5 -mt-0.5 inline-flex h-12 w-12 items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500\",\n            onClick: () => setSidebarOpen(true),\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"sr-only\",\n              children: \"Open sidebar\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Bars3Icon, {\n              className: \"h-6 w-6\",\n              \"aria-hidden\": \"true\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 628,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 627,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          className: \"flex-1\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"py-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mx-auto max-w-7xl px-4 sm:px-6 md:px-8\",\n              children: /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 640,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 639,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 638,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 637,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 626,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n};\n_s(MainLayout, \"JVVi55t2rEaEF+33K6DL4UDUC3A=\", false, function () {\n  return [useAuth, useLocation, useNavigate];\n});\n_c = MainLayout;\nexport default MainLayout;\nvar _c;\n$RefreshReg$(_c, \"MainLayout\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Fragment", "Link", "Outlet", "useLocation", "useNavigate", "Dialog", "<PERSON><PERSON>", "Transition", "Bars3Icon", "XMarkIcon", "UserCircleIcon", "ArrowRightOnRectangleIcon", "Cog6ToothIcon", "ChevronDownIcon", "HomeIcon", "UsersIcon", "UserGroupIcon", "ShieldCheckIcon", "ClipboardDocumentListIcon", "UserIcon", "CalendarIcon", "ChatBubbleLeftRightIcon", "CreditCardIcon", "DocumentTextIcon", "ChartBarIcon", "ClockIcon", "AcademicCapIcon", "useAuth", "jsxDEV", "_jsxDEV", "_Fragment", "MainLayout", "children", "_s", "_user$role", "_user$role2", "_user$role3", "_user$role4", "_user$role5", "sidebarOpen", "setSidebarOpen", "user", "hasPermission", "logout", "location", "navigate", "handleLogout", "error", "console", "adminNavigation", "name", "href", "icon", "permission", "current", "pathname", "startsWith", "expertNavigation", "clientNavigation", "isAdmin", "role", "isCustomRole", "includes", "isExpert", "userType", "isClient", "log", "filterAdminItems", "item", "hasPermissionResult", "filterExpertItems", "filterClientItems", "filteredAdminNavigation", "filter", "filteredExpertNavigation", "filteredClientNavigation", "length", "map", "hasAdminSection", "hasExpertSection", "hasClientSection", "Root", "show", "as", "className", "onClose", "Child", "enter", "enterFrom", "enterTo", "leave", "leaveFrom", "leaveTo", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Panel", "type", "onClick", "to", "<PERSON><PERSON>", "email", "Items", "<PERSON><PERSON>", "active", "_c", "$RefreshReg$"], "sources": ["C:/burky root/burky_root_web/client/src/components/layout/MainLayout.jsx"], "sourcesContent": ["import React, { useState, useEffect, Fragment } from 'react';\nimport { Link, Outlet, useLocation, useNavigate } from 'react-router-dom';\nimport { Dialog, Menu, Transition } from '@headlessui/react';\nimport {\n  Bars3Icon,\n  XMarkIcon,\n  UserCircleIcon,\n  ArrowRightOnRectangleIcon,\n  Cog6ToothIcon,\n  ChevronDownIcon,\n  HomeIcon,\n  UsersIcon,\n  UserGroupIcon,\n  ShieldCheckIcon,\n  ClipboardDocumentListIcon,\n  UserIcon,\n  CalendarIcon,\n  ChatBubbleLeftRightIcon,\n  CreditCardIcon,\n  DocumentTextIcon,\n  ChartBarIcon,\n  ClockIcon,\n  AcademicCapIcon,\n} from '@heroicons/react/24/outline';\nimport { useAuth } from '../../hooks/useAuth';\n\nconst MainLayout = ({ children }) => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const { user, hasPermission, logout } = useAuth();\n  const location = useLocation();\n  const navigate = useNavigate();\n\n  const handleLogout = async () => {\n    try {\n      await logout();\n      navigate('/auth/login');\n    } catch (error) {\n      console.error('Logout failed:', error);\n    }\n  };\n\n  // Ana navigasyon öğeleri - admin\n  const adminNavigation = [\n    { \n      name: 'Dashboard', \n      href: '/dashboard', \n      icon: HomeIcon, \n      permission: 'READ_DASHBOARD',\n      current: location.pathname === '/dashboard' \n    },\n    { \n      name: 'Kullanıcılar', \n      href: '/admin/users', \n      icon: UsersIcon, \n      permission: 'READ_ADMIN_USERS',\n      current: location.pathname.startsWith('/admin/users') \n    },\n    { \n      name: 'Roller', \n      href: '/admin/roles', \n      icon: ShieldCheckIcon, \n      permission: 'READ_ADMIN_ROLES',\n      current: location.pathname.startsWith('/admin/roles') \n    },\n    { \n      name: 'Ayarlar', \n      href: '/admin/settings', \n      icon: Cog6ToothIcon, \n      permission: 'READ_ADMIN_SETTINGS',\n      current: location.pathname.startsWith('/admin/settings')\n    },\n    { \n      name: 'Denetim Kayıtları', \n      href: '/admin/audit-logs', \n      icon: ClipboardDocumentListIcon, \n      permission: 'READ_ADMIN_AUDIT_LOGS',\n      current: location.pathname.startsWith('/admin/audit-logs')\n    },\n  ];\n\n  // Uzman navigasyonu\n  const expertNavigation = [\n    { \n      name: 'Uzman Paneli', \n      href: '/expert/dashboard', \n      icon: ChartBarIcon, \n      permission: 'READ_EXPERT_DASHBOARD',\n      current: location.pathname === '/expert/dashboard' \n    },\n    { \n      name: 'Uygunluklar', \n      href: '/expert/availabilities', \n      icon: ClockIcon, \n      permission: 'READ_EXPERT_AVAILABILITIES',\n      current: location.pathname.startsWith('/expert/availabilities') \n    },\n    { \n      name: 'Randevular', \n      href: '/expert/appointments', \n      icon: CalendarIcon, \n      permission: 'READ_EXPERT_APPOINTMENTS',\n      current: location.pathname.startsWith('/expert/appointments') \n    },\n    { \n      name: 'Görüşmeler', \n      href: '/expert/sessions', \n      icon: AcademicCapIcon, \n      permission: 'READ_EXPERT_SESSIONS',\n      current: location.pathname.startsWith('/expert/sessions') \n    },\n    { \n      name: 'Mesajlar', \n      href: '/expert/messages', \n      icon: ChatBubbleLeftRightIcon, \n      permission: 'READ_EXPERT_MESSAGES',\n      current: location.pathname.startsWith('/expert/messages') \n    },\n    { \n      name: 'Danışanlar', \n      href: '/expert/clients', \n      icon: UserGroupIcon, \n      permission: 'READ_EXPERT_CLIENTS',\n      current: location.pathname.startsWith('/expert/clients') \n    },\n    { \n      name: 'Raporlar', \n      href: '/expert/reports', \n      icon: DocumentTextIcon, \n      permission: 'READ_EXPERT_REPORTS',\n      current: location.pathname.startsWith('/expert/reports') \n    },\n  ];\n\n  // Danışan navigasyonu\n  const clientNavigation = [\n    { \n      name: 'Danışan Paneli', \n      href: '/client/dashboard', \n      icon: ChartBarIcon, \n      permission: 'READ_CLIENT_DASHBOARD',\n      current: location.pathname === '/client/dashboard' \n    },\n    { \n      name: 'Uzmanlar', \n      href: '/client/experts', \n      icon: UserIcon, \n      permission: 'READ_CLIENT_EXPERTS',\n      current: location.pathname.startsWith('/client/experts') \n    },\n    { \n      name: 'Randevular', \n      href: '/client/appointments', \n      icon: CalendarIcon, \n      permission: 'READ_CLIENT_APPOINTMENTS',\n      current: location.pathname.startsWith('/client/appointments') \n    },\n    { \n      name: 'Görüşmeler', \n      href: '/client/sessions', \n      icon: AcademicCapIcon, \n      permission: 'READ_CLIENT_SESSIONS',\n      current: location.pathname.startsWith('/client/sessions') \n    },\n    { \n      name: 'Mesajlar', \n      href: '/client/messages', \n      icon: ChatBubbleLeftRightIcon, \n      permission: 'READ_CLIENT_MESSAGES',\n      current: location.pathname.startsWith('/client/messages') \n    },\n    { \n      name: 'Paketler', \n      href: '/client/packages', \n      icon: ClipboardDocumentListIcon, \n      permission: 'READ_CLIENT_PACKAGES',\n      current: location.pathname.startsWith('/client/packages') \n    },\n    { \n      name: 'Ödemeler', \n      href: '/client/payments', \n      icon: CreditCardIcon, \n      permission: 'READ_CLIENT_PAYMENTS',\n      current: location.pathname.startsWith('/client/payments') \n    },\n  ];\n\n  // Rol tabanlı menü bölümlerini gösterme/gizleme\n  const isAdmin = user?.role?.name === 'Admin';\n  \n  // Rol bilgisini kontrol ediyoruz - özel roller için \"burak rol1\" gibi\n  const isCustomRole = user && user.role?.name && !['Admin', 'Expert', 'Client'].includes(user.role.name);\n  \n  // Uzman ve danışan kontrolleri\n  const isExpert = user && (\n    isAdmin || \n    user.role?.name === 'Expert' || \n    user.userType === 'expert' || \n    user.isExpert === true\n  );\n  \n  const isClient = user && (\n    isAdmin || \n    user.role?.name === 'Client' || \n    user.userType === 'client' || \n    user.isClient === true\n  );\n  \n  console.log('Kullanıcı rol kontrolü (detaylı):', { \n    isAdmin, \n    isExpert, \n    isClient,\n    isCustomRole,\n    \"user.role\": user?.role,\n    \"user.role?.name\": user?.role?.name,\n    \"user.userType\": user?.userType\n  });\n  \n  // Filtre fonksiyonları - izinleri daha esnek kontrol ediyoruz\n  const filterAdminItems = (item) => {\n    // Admin her zaman tüm menüleri görür\n    if (isAdmin) return true; \n    \n    // Özel rol için READ izni varsa item.permission ile başlayan herşeyi göster \n    if (isCustomRole) {\n      const hasPermissionResult = hasPermission(item.permission);\n      console.log(`Özel rol admin menü kontrolü: ${item.name} (${item.permission}) - Sonuç: ${hasPermissionResult}`);\n      return hasPermissionResult;\n    }\n    \n    return hasPermission(item.permission);\n  };\n  \n  const filterExpertItems = (item) => {\n    // Admin her zaman tüm menüleri görür\n    if (isAdmin) return true; \n    \n    // Uzman rolü varsa uzman menülerini görür\n    if (isExpert) return true; \n    \n    // Özel rol için READ izni varsa item.permission ile başlayan herşeyi göster\n    if (isCustomRole) {\n      const hasPermissionResult = hasPermission(item.permission);\n      console.log(`Özel rol uzman menü kontrolü: ${item.name} (${item.permission}) - Sonuç: ${hasPermissionResult}`);\n      return hasPermissionResult;\n    }\n    \n    return hasPermission(item.permission);\n  };\n  \n  const filterClientItems = (item) => {\n    // Admin her zaman tüm menüleri görür\n    if (isAdmin) return true; \n    \n    // Danışan rolü varsa danışan menülerini görür\n    if (isClient) return true; \n    \n    // Özel rol için READ izni varsa item.permission ile başlayan herşeyi göster\n    if (isCustomRole) {\n      const hasPermissionResult = hasPermission(item.permission);\n      console.log(`Özel rol danışan menü kontrolü: ${item.name} (${item.permission}) - Sonuç: ${hasPermissionResult}`);\n      return hasPermissionResult;\n    }\n    \n    return hasPermission(item.permission);\n  };\n  \n  // Filtrelenmiş navigasyon öğeleri - sadece izni olanlar\n  const filteredAdminNavigation = adminNavigation.filter(filterAdminItems);\n  const filteredExpertNavigation = expertNavigation.filter(filterExpertItems);\n  const filteredClientNavigation = clientNavigation.filter(filterClientItems);\n  \n  console.log('Filtrelenmiş menü öğeleri:', { \n    \"Admin menü sayısı\": filteredAdminNavigation.length,\n    \"Admin menüler\": filteredAdminNavigation.map(item => item.name),\n    \"Uzman menü sayısı\": filteredExpertNavigation.length,\n    \"Uzman menüler\": filteredExpertNavigation.map(item => item.name),\n    \"Danışan menü sayısı\": filteredClientNavigation.length,\n    \"Danışan menüler\": filteredClientNavigation.map(item => item.name)\n  });\n  \n  // Ana navigasyon kategorilere ayrılmış\n  // Özel rol için: Herhangi bir bölümde filtrelenmiş öğe varsa o bölümü göster\n  const hasAdminSection = isAdmin || (isCustomRole && filteredAdminNavigation.length > 0) || \n                         (!isCustomRole && filteredAdminNavigation.length > 0);\n  \n  const hasExpertSection = isExpert || (isCustomRole && filteredExpertNavigation.length > 0) || \n                          (!isCustomRole && filteredExpertNavigation.length > 0);\n  \n  const hasClientSection = isClient || (isCustomRole && filteredClientNavigation.length > 0) || \n                          (!isCustomRole && filteredClientNavigation.length > 0);\n  \n  console.log('Menü bölümleri görünürlüğü:', { \n    hasAdminSection, \n    hasExpertSection, \n    hasClientSection,\n    \"filteredAdmin.length\": filteredAdminNavigation.length,\n    \"filteredExpert.length\": filteredExpertNavigation.length,\n    \"filteredClient.length\": filteredClientNavigation.length\n  });\n\n  return (\n    <>\n      <div>\n        <Transition.Root show={sidebarOpen} as={Fragment}>\n          <Dialog as=\"div\" className=\"relative z-40 md:hidden\" onClose={setSidebarOpen}>\n            <Transition.Child\n              as={Fragment}\n              enter=\"transition-opacity ease-linear duration-300\"\n              enterFrom=\"opacity-0\"\n              enterTo=\"opacity-100\"\n              leave=\"transition-opacity ease-linear duration-300\"\n              leaveFrom=\"opacity-100\"\n              leaveTo=\"opacity-0\"\n            >\n              <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" />\n            </Transition.Child>\n\n            <div className=\"fixed inset-0 z-40 flex\">\n              <Transition.Child\n                as={Fragment}\n                enter=\"transition ease-in-out duration-300 transform\"\n                enterFrom=\"-translate-x-full\"\n                enterTo=\"translate-x-0\"\n                leave=\"transition ease-in-out duration-300 transform\"\n                leaveFrom=\"translate-x-0\"\n                leaveTo=\"-translate-x-full\"\n              >\n                <Dialog.Panel className=\"relative flex w-full max-w-xs flex-1 flex-col bg-white\">\n                  <Transition.Child\n                    as={Fragment}\n                    enter=\"ease-in-out duration-300\"\n                    enterFrom=\"opacity-0\"\n                    enterTo=\"opacity-100\"\n                    leave=\"ease-in-out duration-300\"\n                    leaveFrom=\"opacity-100\"\n                    leaveTo=\"opacity-0\"\n                  >\n                    <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n                      <button\n                        type=\"button\"\n                        className=\"ml-1 flex h-10 w-10 items-center justify-center rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\"\n                        onClick={() => setSidebarOpen(false)}\n                      >\n                        <span className=\"sr-only\">Close sidebar</span>\n                        <XMarkIcon className=\"h-6 w-6 text-white\" aria-hidden=\"true\" />\n                      </button>\n                    </div>\n                  </Transition.Child>\n                  <div className=\"h-0 flex-1 overflow-y-auto pt-5 pb-4\">\n                    <div className=\"flex flex-shrink-0 items-center px-4\">\n                      <h1 className=\"text-xl font-bold text-primary-600\">Burky Root</h1>\n                    </div>\n                    <nav className=\"mt-5 space-y-1 px-2\">\n                      {/* Admin Section */}\n                      {hasAdminSection && (\n                        <div className=\"pt-2 pb-1\">\n                          <p className=\"px-3 py-1 font-semibold text-xs uppercase tracking-wider text-gray-500\">\n                            Yönetici\n                          </p>\n                          {filteredAdminNavigation.map((item) => (\n                            <Link\n                              key={item.name}\n                              to={item.href}\n                              className={`${\n                                item.current\n                                  ? 'bg-gray-100 text-primary-600'\n                                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                              } group flex items-center px-2 py-2 text-sm font-medium rounded-md`}\n                              onClick={() => setSidebarOpen(false)}\n                            >\n                              <item.icon\n                                className={`${\n                                  item.current ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-500'\n                                } mr-3 flex-shrink-0 h-6 w-6`}\n                                aria-hidden=\"true\"\n                              />\n                              {item.name}\n                            </Link>\n                          ))}\n                        </div>\n                      )}\n\n                      {/* Expert Section */}\n                      {hasExpertSection && (\n                        <div className=\"pt-2 pb-1\">\n                          <p className=\"px-3 py-1 font-semibold text-xs uppercase tracking-wider text-gray-500\">\n                            Uzman\n                          </p>\n                          {filteredExpertNavigation.map((item) => (\n                            <Link\n                              key={item.name}\n                              to={item.href}\n                              className={`${\n                                item.current\n                                  ? 'bg-gray-100 text-primary-600'\n                                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                              } group flex items-center px-2 py-2 text-sm font-medium rounded-md`}\n                              onClick={() => setSidebarOpen(false)}\n                            >\n                              <item.icon\n                                className={`${\n                                  item.current ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-500'\n                                } mr-3 flex-shrink-0 h-6 w-6`}\n                                aria-hidden=\"true\"\n                              />\n                              {item.name}\n                            </Link>\n                          ))}\n                        </div>\n                      )}\n\n                      {/* Client Section */}\n                      {hasClientSection && (\n                        <div className=\"pt-2 pb-1\">\n                          <p className=\"px-3 py-1 font-semibold text-xs uppercase tracking-wider text-gray-500\">\n                            Danışan\n                          </p>\n                          {filteredClientNavigation.map((item) => (\n                            <Link\n                              key={item.name}\n                              to={item.href}\n                              className={`${\n                                item.current\n                                  ? 'bg-gray-100 text-primary-600'\n                                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                              } group flex items-center px-2 py-2 text-sm font-medium rounded-md`}\n                              onClick={() => setSidebarOpen(false)}\n                            >\n                              <item.icon\n                                className={`${\n                                  item.current ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-500'\n                                } mr-3 flex-shrink-0 h-6 w-6`}\n                                aria-hidden=\"true\"\n                              />\n                              {item.name}\n                            </Link>\n                          ))}\n                        </div>\n                      )}\n                    </nav>\n                  </div>\n                  <div className=\"flex flex-shrink-0 border-t border-gray-200 p-4\">\n                    <Link to=\"/profile\" className=\"group block flex-shrink-0\">\n                      <div className=\"flex items-center\">\n                        <div>\n                          <UserCircleIcon className=\"inline-block h-10 w-10 rounded-full text-gray-500\" />\n                        </div>\n                        <div className=\"ml-3\">\n                          <p className=\"text-base font-medium text-gray-700 group-hover:text-gray-900\">\n                            {user?.name || 'Kullanıcı'}\n                          </p>\n                          <p className=\"text-sm font-medium text-gray-500 group-hover:text-gray-700\">\n                            Profil\n                          </p>\n                        </div>\n                      </div>\n                    </Link>\n                  </div>\n                </Dialog.Panel>\n              </Transition.Child>\n              <div className=\"w-14 flex-shrink-0\">{/* Force sidebar to shrink to fit close icon */}</div>\n            </div>\n          </Dialog>\n        </Transition.Root>\n\n        {/* Static sidebar for desktop */}\n        <div className=\"hidden md:fixed md:inset-y-0 md:flex md:w-64 md:flex-col\">\n          <div className=\"flex min-h-0 flex-1 flex-col bg-white border-r border-gray-200\">\n            <div className=\"flex flex-1 flex-col overflow-y-auto pt-5 pb-4\">\n              <div className=\"flex flex-shrink-0 items-center px-4\">\n                <h1 className=\"text-xl font-bold text-primary-600\">Burky Root</h1>\n              </div>\n              <nav className=\"mt-5 flex-1 space-y-2 px-2\">\n                {/* Admin Section */}\n                {hasAdminSection && (\n                  <div className=\"pt-2 pb-1\">\n                    <p className=\"px-3 py-1 font-semibold text-xs uppercase tracking-wider text-gray-500\">\n                      Yönetici\n                    </p>\n                    {filteredAdminNavigation.map((item) => (\n                      <Link\n                        key={item.name}\n                        to={item.href}\n                        className={`${\n                          item.current\n                            ? 'bg-gray-100 text-primary-600'\n                            : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                        } group flex items-center px-2 py-2 text-sm font-medium rounded-md`}\n                      >\n                        <item.icon\n                          className={`${\n                            item.current ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-500'\n                          } mr-3 flex-shrink-0 h-6 w-6`}\n                          aria-hidden=\"true\"\n                        />\n                        {item.name}\n                      </Link>\n                    ))}\n                  </div>\n                )}\n\n                {/* Expert Section */}\n                {hasExpertSection && (\n                  <div className=\"pt-2 pb-1\">\n                    <p className=\"px-3 py-1 font-semibold text-xs uppercase tracking-wider text-gray-500\">\n                      Uzman\n                    </p>\n                    {filteredExpertNavigation.map((item) => (\n                      <Link\n                        key={item.name}\n                        to={item.href}\n                        className={`${\n                          item.current\n                            ? 'bg-gray-100 text-primary-600'\n                            : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                        } group flex items-center px-2 py-2 text-sm font-medium rounded-md`}\n                      >\n                        <item.icon\n                          className={`${\n                            item.current ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-500'\n                          } mr-3 flex-shrink-0 h-6 w-6`}\n                          aria-hidden=\"true\"\n                        />\n                        {item.name}\n                      </Link>\n                    ))}\n                  </div>\n                )}\n\n                {/* Client Section */}\n                {hasClientSection && (\n                  <div className=\"pt-2 pb-1\">\n                    <p className=\"px-3 py-1 font-semibold text-xs uppercase tracking-wider text-gray-500\">\n                      Danışan\n                    </p>\n                    {filteredClientNavigation.map((item) => (\n                      <Link\n                        key={item.name}\n                        to={item.href}\n                        className={`${\n                          item.current\n                            ? 'bg-gray-100 text-primary-600'\n                            : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                        } group flex items-center px-2 py-2 text-sm font-medium rounded-md`}\n                      >\n                        <item.icon\n                          className={`${\n                            item.current ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-500'\n                          } mr-3 flex-shrink-0 h-6 w-6`}\n                          aria-hidden=\"true\"\n                        />\n                        {item.name}\n                      </Link>\n                    ))}\n                  </div>\n                )}\n              </nav>\n            </div>\n            <div className=\"flex flex-shrink-0 border-t border-gray-200 p-4\">\n              <Menu as=\"div\" className=\"relative inline-block text-left w-full\">\n                <div>\n                  <Menu.Button className=\"group w-full rounded-md text-sm text-left font-medium text-gray-700 hover:text-gray-900 focus:outline-none\">\n                    <div className=\"flex w-full items-center\">\n                      <div>\n                        <UserCircleIcon className=\"inline-block h-10 w-10 rounded-full text-gray-500\" />\n                      </div>\n                      <div className=\"ml-3\">\n                        <p className=\"text-sm font-medium text-gray-700 group-hover:text-gray-900\">\n                          {user?.name || 'Kullanıcı'}\n                        </p>\n                        <p className=\"text-xs font-medium text-gray-500 group-hover:text-gray-700\">\n                          {user?.email || 'Kullanıcı e-postası'}\n                        </p>\n                      </div>\n                      <ChevronDownIcon\n                        className=\"ml-auto h-5 w-5 flex-shrink-0 text-gray-400 group-hover:text-gray-500\"\n                        aria-hidden=\"true\"\n                      />\n                    </div>\n                  </Menu.Button>\n                </div>\n                <Transition\n                  as={Fragment}\n                  enter=\"transition ease-out duration-100\"\n                  enterFrom=\"transform opacity-0 scale-95\"\n                  enterTo=\"transform opacity-100 scale-100\"\n                  leave=\"transition ease-in duration-75\"\n                  leaveFrom=\"transform opacity-100 scale-100\"\n                  leaveTo=\"transform opacity-0 scale-95\"\n                >\n                  <Menu.Items className=\"absolute bottom-full left-0 z-10 mb-2 w-56 origin-bottom-left rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\">\n                    <div className=\"py-1\">\n                      <Menu.Item>\n                        {({ active }) => (\n                          <Link\n                            to=\"/profile\"\n                            className={`${\n                              active ? 'bg-gray-100 text-gray-900' : 'text-gray-700'\n                            } flex px-4 py-2 text-sm`}\n                          >\n                            <UserCircleIcon className=\"mr-3 h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\n                            <span>Profilim</span>\n                          </Link>\n                        )}\n                      </Menu.Item>\n                      <Menu.Item>\n                        {({ active }) => (\n                          <button\n                            onClick={handleLogout}\n                            className={`${\n                              active ? 'bg-gray-100 text-gray-900' : 'text-gray-700'\n                            } flex w-full px-4 py-2 text-left text-sm`}\n                          >\n                            <ArrowRightOnRectangleIcon className=\"mr-3 h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\n                            <span>Çıkış Yap</span>\n                          </button>\n                        )}\n                      </Menu.Item>\n                    </div>\n                  </Menu.Items>\n                </Transition>\n              </Menu>\n            </div>\n          </div>\n        </div>\n        <div className=\"flex flex-1 flex-col md:pl-64\">\n          <div className=\"sticky top-0 z-10 bg-white pl-1 pt-1 sm:pl-3 sm:pt-3 md:hidden\">\n            <button\n              type=\"button\"\n              className=\"-ml-0.5 -mt-0.5 inline-flex h-12 w-12 items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500\"\n              onClick={() => setSidebarOpen(true)}\n            >\n              <span className=\"sr-only\">Open sidebar</span>\n              <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n            </button>\n          </div>\n          <main className=\"flex-1\">\n            <div className=\"py-6\">\n              <div className=\"mx-auto max-w-7xl px-4 sm:px-6 md:px-8\">\n                <Outlet />\n              </div>\n            </div>\n          </main>\n        </div>\n      </div>\n    </>\n  );\n};\n\nexport default MainLayout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC5D,SAASC,IAAI,EAAEC,MAAM,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACzE,SAASC,MAAM,EAAEC,IAAI,EAAEC,UAAU,QAAQ,mBAAmB;AAC5D,SACEC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,yBAAyB,EACzBC,aAAa,EACbC,eAAe,EACfC,QAAQ,EACRC,SAAS,EACTC,aAAa,EACbC,eAAe,EACfC,yBAAyB,EACzBC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,cAAc,EACdC,gBAAgB,EAChBC,YAAY,EACZC,SAAS,EACTC,eAAe,QACV,6BAA6B;AACpC,SAASC,OAAO,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAA7B,QAAA,IAAA8B,SAAA;AAE9C,MAAMC,UAAU,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,UAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA;EACnC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM;IAAE2C,IAAI;IAAEC,aAAa;IAAEC;EAAO,CAAC,GAAGhB,OAAO,CAAC,CAAC;EACjD,MAAMiB,QAAQ,GAAGzC,WAAW,CAAC,CAAC;EAC9B,MAAM0C,QAAQ,GAAGzC,WAAW,CAAC,CAAC;EAE9B,MAAM0C,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMH,MAAM,CAAC,CAAC;MACdE,QAAQ,CAAC,aAAa,CAAC;IACzB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAME,eAAe,GAAG,CACtB;IACEC,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAEtC,QAAQ;IACduC,UAAU,EAAE,gBAAgB;IAC5BC,OAAO,EAAEV,QAAQ,CAACW,QAAQ,KAAK;EACjC,CAAC,EACD;IACEL,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAErC,SAAS;IACfsC,UAAU,EAAE,kBAAkB;IAC9BC,OAAO,EAAEV,QAAQ,CAACW,QAAQ,CAACC,UAAU,CAAC,cAAc;EACtD,CAAC,EACD;IACEN,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAEnC,eAAe;IACrBoC,UAAU,EAAE,kBAAkB;IAC9BC,OAAO,EAAEV,QAAQ,CAACW,QAAQ,CAACC,UAAU,CAAC,cAAc;EACtD,CAAC,EACD;IACEN,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAExC,aAAa;IACnByC,UAAU,EAAE,qBAAqB;IACjCC,OAAO,EAAEV,QAAQ,CAACW,QAAQ,CAACC,UAAU,CAAC,iBAAiB;EACzD,CAAC,EACD;IACEN,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAElC,yBAAyB;IAC/BmC,UAAU,EAAE,uBAAuB;IACnCC,OAAO,EAAEV,QAAQ,CAACW,QAAQ,CAACC,UAAU,CAAC,mBAAmB;EAC3D,CAAC,CACF;;EAED;EACA,MAAMC,gBAAgB,GAAG,CACvB;IACEP,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE5B,YAAY;IAClB6B,UAAU,EAAE,uBAAuB;IACnCC,OAAO,EAAEV,QAAQ,CAACW,QAAQ,KAAK;EACjC,CAAC,EACD;IACEL,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,wBAAwB;IAC9BC,IAAI,EAAE3B,SAAS;IACf4B,UAAU,EAAE,4BAA4B;IACxCC,OAAO,EAAEV,QAAQ,CAACW,QAAQ,CAACC,UAAU,CAAC,wBAAwB;EAChE,CAAC,EACD;IACEN,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,sBAAsB;IAC5BC,IAAI,EAAEhC,YAAY;IAClBiC,UAAU,EAAE,0BAA0B;IACtCC,OAAO,EAAEV,QAAQ,CAACW,QAAQ,CAACC,UAAU,CAAC,sBAAsB;EAC9D,CAAC,EACD;IACEN,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE1B,eAAe;IACrB2B,UAAU,EAAE,sBAAsB;IAClCC,OAAO,EAAEV,QAAQ,CAACW,QAAQ,CAACC,UAAU,CAAC,kBAAkB;EAC1D,CAAC,EACD;IACEN,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE/B,uBAAuB;IAC7BgC,UAAU,EAAE,sBAAsB;IAClCC,OAAO,EAAEV,QAAQ,CAACW,QAAQ,CAACC,UAAU,CAAC,kBAAkB;EAC1D,CAAC,EACD;IACEN,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAEpC,aAAa;IACnBqC,UAAU,EAAE,qBAAqB;IACjCC,OAAO,EAAEV,QAAQ,CAACW,QAAQ,CAACC,UAAU,CAAC,iBAAiB;EACzD,CAAC,EACD;IACEN,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE7B,gBAAgB;IACtB8B,UAAU,EAAE,qBAAqB;IACjCC,OAAO,EAAEV,QAAQ,CAACW,QAAQ,CAACC,UAAU,CAAC,iBAAiB;EACzD,CAAC,CACF;;EAED;EACA,MAAME,gBAAgB,GAAG,CACvB;IACER,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE5B,YAAY;IAClB6B,UAAU,EAAE,uBAAuB;IACnCC,OAAO,EAAEV,QAAQ,CAACW,QAAQ,KAAK;EACjC,CAAC,EACD;IACEL,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAEjC,QAAQ;IACdkC,UAAU,EAAE,qBAAqB;IACjCC,OAAO,EAAEV,QAAQ,CAACW,QAAQ,CAACC,UAAU,CAAC,iBAAiB;EACzD,CAAC,EACD;IACEN,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,sBAAsB;IAC5BC,IAAI,EAAEhC,YAAY;IAClBiC,UAAU,EAAE,0BAA0B;IACtCC,OAAO,EAAEV,QAAQ,CAACW,QAAQ,CAACC,UAAU,CAAC,sBAAsB;EAC9D,CAAC,EACD;IACEN,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE1B,eAAe;IACrB2B,UAAU,EAAE,sBAAsB;IAClCC,OAAO,EAAEV,QAAQ,CAACW,QAAQ,CAACC,UAAU,CAAC,kBAAkB;EAC1D,CAAC,EACD;IACEN,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE/B,uBAAuB;IAC7BgC,UAAU,EAAE,sBAAsB;IAClCC,OAAO,EAAEV,QAAQ,CAACW,QAAQ,CAACC,UAAU,CAAC,kBAAkB;EAC1D,CAAC,EACD;IACEN,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAElC,yBAAyB;IAC/BmC,UAAU,EAAE,sBAAsB;IAClCC,OAAO,EAAEV,QAAQ,CAACW,QAAQ,CAACC,UAAU,CAAC,kBAAkB;EAC1D,CAAC,EACD;IACEN,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE9B,cAAc;IACpB+B,UAAU,EAAE,sBAAsB;IAClCC,OAAO,EAAEV,QAAQ,CAACW,QAAQ,CAACC,UAAU,CAAC,kBAAkB;EAC1D,CAAC,CACF;;EAED;EACA,MAAMG,OAAO,GAAG,CAAAlB,IAAI,aAAJA,IAAI,wBAAAP,UAAA,GAAJO,IAAI,CAAEmB,IAAI,cAAA1B,UAAA,uBAAVA,UAAA,CAAYgB,IAAI,MAAK,OAAO;;EAE5C;EACA,MAAMW,YAAY,GAAGpB,IAAI,MAAAN,WAAA,GAAIM,IAAI,CAACmB,IAAI,cAAAzB,WAAA,uBAATA,WAAA,CAAWe,IAAI,KAAI,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAACY,QAAQ,CAACrB,IAAI,CAACmB,IAAI,CAACV,IAAI,CAAC;;EAEvG;EACA,MAAMa,QAAQ,GAAGtB,IAAI,KACnBkB,OAAO,IACP,EAAAvB,WAAA,GAAAK,IAAI,CAACmB,IAAI,cAAAxB,WAAA,uBAATA,WAAA,CAAWc,IAAI,MAAK,QAAQ,IAC5BT,IAAI,CAACuB,QAAQ,KAAK,QAAQ,IAC1BvB,IAAI,CAACsB,QAAQ,KAAK,IAAI,CACvB;EAED,MAAME,QAAQ,GAAGxB,IAAI,KACnBkB,OAAO,IACP,EAAAtB,WAAA,GAAAI,IAAI,CAACmB,IAAI,cAAAvB,WAAA,uBAATA,WAAA,CAAWa,IAAI,MAAK,QAAQ,IAC5BT,IAAI,CAACuB,QAAQ,KAAK,QAAQ,IAC1BvB,IAAI,CAACwB,QAAQ,KAAK,IAAI,CACvB;EAEDjB,OAAO,CAACkB,GAAG,CAAC,mCAAmC,EAAE;IAC/CP,OAAO;IACPI,QAAQ;IACRE,QAAQ;IACRJ,YAAY;IACZ,WAAW,EAAEpB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,IAAI;IACvB,iBAAiB,EAAEnB,IAAI,aAAJA,IAAI,wBAAAH,WAAA,GAAJG,IAAI,CAAEmB,IAAI,cAAAtB,WAAA,uBAAVA,WAAA,CAAYY,IAAI;IACnC,eAAe,EAAET,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuB;EACzB,CAAC,CAAC;;EAEF;EACA,MAAMG,gBAAgB,GAAIC,IAAI,IAAK;IACjC;IACA,IAAIT,OAAO,EAAE,OAAO,IAAI;;IAExB;IACA,IAAIE,YAAY,EAAE;MAChB,MAAMQ,mBAAmB,GAAG3B,aAAa,CAAC0B,IAAI,CAACf,UAAU,CAAC;MAC1DL,OAAO,CAACkB,GAAG,CAAC,iCAAiCE,IAAI,CAAClB,IAAI,KAAKkB,IAAI,CAACf,UAAU,cAAcgB,mBAAmB,EAAE,CAAC;MAC9G,OAAOA,mBAAmB;IAC5B;IAEA,OAAO3B,aAAa,CAAC0B,IAAI,CAACf,UAAU,CAAC;EACvC,CAAC;EAED,MAAMiB,iBAAiB,GAAIF,IAAI,IAAK;IAClC;IACA,IAAIT,OAAO,EAAE,OAAO,IAAI;;IAExB;IACA,IAAII,QAAQ,EAAE,OAAO,IAAI;;IAEzB;IACA,IAAIF,YAAY,EAAE;MAChB,MAAMQ,mBAAmB,GAAG3B,aAAa,CAAC0B,IAAI,CAACf,UAAU,CAAC;MAC1DL,OAAO,CAACkB,GAAG,CAAC,iCAAiCE,IAAI,CAAClB,IAAI,KAAKkB,IAAI,CAACf,UAAU,cAAcgB,mBAAmB,EAAE,CAAC;MAC9G,OAAOA,mBAAmB;IAC5B;IAEA,OAAO3B,aAAa,CAAC0B,IAAI,CAACf,UAAU,CAAC;EACvC,CAAC;EAED,MAAMkB,iBAAiB,GAAIH,IAAI,IAAK;IAClC;IACA,IAAIT,OAAO,EAAE,OAAO,IAAI;;IAExB;IACA,IAAIM,QAAQ,EAAE,OAAO,IAAI;;IAEzB;IACA,IAAIJ,YAAY,EAAE;MAChB,MAAMQ,mBAAmB,GAAG3B,aAAa,CAAC0B,IAAI,CAACf,UAAU,CAAC;MAC1DL,OAAO,CAACkB,GAAG,CAAC,mCAAmCE,IAAI,CAAClB,IAAI,KAAKkB,IAAI,CAACf,UAAU,cAAcgB,mBAAmB,EAAE,CAAC;MAChH,OAAOA,mBAAmB;IAC5B;IAEA,OAAO3B,aAAa,CAAC0B,IAAI,CAACf,UAAU,CAAC;EACvC,CAAC;;EAED;EACA,MAAMmB,uBAAuB,GAAGvB,eAAe,CAACwB,MAAM,CAACN,gBAAgB,CAAC;EACxE,MAAMO,wBAAwB,GAAGjB,gBAAgB,CAACgB,MAAM,CAACH,iBAAiB,CAAC;EAC3E,MAAMK,wBAAwB,GAAGjB,gBAAgB,CAACe,MAAM,CAACF,iBAAiB,CAAC;EAE3EvB,OAAO,CAACkB,GAAG,CAAC,4BAA4B,EAAE;IACxC,mBAAmB,EAAEM,uBAAuB,CAACI,MAAM;IACnD,eAAe,EAAEJ,uBAAuB,CAACK,GAAG,CAACT,IAAI,IAAIA,IAAI,CAAClB,IAAI,CAAC;IAC/D,mBAAmB,EAAEwB,wBAAwB,CAACE,MAAM;IACpD,eAAe,EAAEF,wBAAwB,CAACG,GAAG,CAACT,IAAI,IAAIA,IAAI,CAAClB,IAAI,CAAC;IAChE,qBAAqB,EAAEyB,wBAAwB,CAACC,MAAM;IACtD,iBAAiB,EAAED,wBAAwB,CAACE,GAAG,CAACT,IAAI,IAAIA,IAAI,CAAClB,IAAI;EACnE,CAAC,CAAC;;EAEF;EACA;EACA,MAAM4B,eAAe,GAAGnB,OAAO,IAAKE,YAAY,IAAIW,uBAAuB,CAACI,MAAM,GAAG,CAAE,IAC/D,CAACf,YAAY,IAAIW,uBAAuB,CAACI,MAAM,GAAG,CAAE;EAE5E,MAAMG,gBAAgB,GAAGhB,QAAQ,IAAKF,YAAY,IAAIa,wBAAwB,CAACE,MAAM,GAAG,CAAE,IACjE,CAACf,YAAY,IAAIa,wBAAwB,CAACE,MAAM,GAAG,CAAE;EAE9E,MAAMI,gBAAgB,GAAGf,QAAQ,IAAKJ,YAAY,IAAIc,wBAAwB,CAACC,MAAM,GAAG,CAAE,IACjE,CAACf,YAAY,IAAIc,wBAAwB,CAACC,MAAM,GAAG,CAAE;EAE9E5B,OAAO,CAACkB,GAAG,CAAC,6BAA6B,EAAE;IACzCY,eAAe;IACfC,gBAAgB;IAChBC,gBAAgB;IAChB,sBAAsB,EAAER,uBAAuB,CAACI,MAAM;IACtD,uBAAuB,EAAEF,wBAAwB,CAACE,MAAM;IACxD,uBAAuB,EAAED,wBAAwB,CAACC;EACpD,CAAC,CAAC;EAEF,oBACE/C,OAAA,CAAAC,SAAA;IAAAE,QAAA,eACEH,OAAA;MAAAG,QAAA,gBACEH,OAAA,CAACtB,UAAU,CAAC0E,IAAI;QAACC,IAAI,EAAE3C,WAAY;QAAC4C,EAAE,EAAEnF,QAAS;QAAAgC,QAAA,eAC/CH,OAAA,CAACxB,MAAM;UAAC8E,EAAE,EAAC,KAAK;UAACC,SAAS,EAAC,yBAAyB;UAACC,OAAO,EAAE7C,cAAe;UAAAR,QAAA,gBAC3EH,OAAA,CAACtB,UAAU,CAAC+E,KAAK;YACfH,EAAE,EAAEnF,QAAS;YACbuF,KAAK,EAAC,6CAA6C;YACnDC,SAAS,EAAC,WAAW;YACrBC,OAAO,EAAC,aAAa;YACrBC,KAAK,EAAC,6CAA6C;YACnDC,SAAS,EAAC,aAAa;YACvBC,OAAO,EAAC,WAAW;YAAA5D,QAAA,eAEnBH,OAAA;cAAKuD,SAAS,EAAC;YAAyC;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eAEnBnE,OAAA;YAAKuD,SAAS,EAAC,yBAAyB;YAAApD,QAAA,gBACtCH,OAAA,CAACtB,UAAU,CAAC+E,KAAK;cACfH,EAAE,EAAEnF,QAAS;cACbuF,KAAK,EAAC,+CAA+C;cACrDC,SAAS,EAAC,mBAAmB;cAC7BC,OAAO,EAAC,eAAe;cACvBC,KAAK,EAAC,+CAA+C;cACrDC,SAAS,EAAC,eAAe;cACzBC,OAAO,EAAC,mBAAmB;cAAA5D,QAAA,eAE3BH,OAAA,CAACxB,MAAM,CAAC4F,KAAK;gBAACb,SAAS,EAAC,wDAAwD;gBAAApD,QAAA,gBAC9EH,OAAA,CAACtB,UAAU,CAAC+E,KAAK;kBACfH,EAAE,EAAEnF,QAAS;kBACbuF,KAAK,EAAC,0BAA0B;kBAChCC,SAAS,EAAC,WAAW;kBACrBC,OAAO,EAAC,aAAa;kBACrBC,KAAK,EAAC,0BAA0B;kBAChCC,SAAS,EAAC,aAAa;kBACvBC,OAAO,EAAC,WAAW;kBAAA5D,QAAA,eAEnBH,OAAA;oBAAKuD,SAAS,EAAC,oCAAoC;oBAAApD,QAAA,eACjDH,OAAA;sBACEqE,IAAI,EAAC,QAAQ;sBACbd,SAAS,EAAC,gIAAgI;sBAC1Ie,OAAO,EAAEA,CAAA,KAAM3D,cAAc,CAAC,KAAK,CAAE;sBAAAR,QAAA,gBAErCH,OAAA;wBAAMuD,SAAS,EAAC,SAAS;wBAAApD,QAAA,EAAC;sBAAa;wBAAA6D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC9CnE,OAAA,CAACpB,SAAS;wBAAC2E,SAAS,EAAC,oBAAoB;wBAAC,eAAY;sBAAM;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACU,CAAC,eACnBnE,OAAA;kBAAKuD,SAAS,EAAC,sCAAsC;kBAAApD,QAAA,gBACnDH,OAAA;oBAAKuD,SAAS,EAAC,sCAAsC;oBAAApD,QAAA,eACnDH,OAAA;sBAAIuD,SAAS,EAAC,oCAAoC;sBAAApD,QAAA,EAAC;oBAAU;sBAAA6D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC,eACNnE,OAAA;oBAAKuD,SAAS,EAAC,qBAAqB;oBAAApD,QAAA,GAEjC8C,eAAe,iBACdjD,OAAA;sBAAKuD,SAAS,EAAC,WAAW;sBAAApD,QAAA,gBACxBH,OAAA;wBAAGuD,SAAS,EAAC,wEAAwE;wBAAApD,QAAA,EAAC;sBAEtF;wBAAA6D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,EACHxB,uBAAuB,CAACK,GAAG,CAAET,IAAI,iBAChCvC,OAAA,CAAC5B,IAAI;wBAEHmG,EAAE,EAAEhC,IAAI,CAACjB,IAAK;wBACdiC,SAAS,EAAE,GACThB,IAAI,CAACd,OAAO,GACR,8BAA8B,GAC9B,oDAAoD,mEACU;wBACpE6C,OAAO,EAAEA,CAAA,KAAM3D,cAAc,CAAC,KAAK,CAAE;wBAAAR,QAAA,gBAErCH,OAAA,CAACuC,IAAI,CAAChB,IAAI;0BACRgC,SAAS,EAAE,GACThB,IAAI,CAACd,OAAO,GAAG,kBAAkB,GAAG,yCAAyC,6BACjD;0BAC9B,eAAY;wBAAM;0BAAAuC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnB,CAAC,EACD5B,IAAI,CAAClB,IAAI;sBAAA,GAfLkB,IAAI,CAAClB,IAAI;wBAAA2C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAgBV,CACP,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CACN,EAGAjB,gBAAgB,iBACflD,OAAA;sBAAKuD,SAAS,EAAC,WAAW;sBAAApD,QAAA,gBACxBH,OAAA;wBAAGuD,SAAS,EAAC,wEAAwE;wBAAApD,QAAA,EAAC;sBAEtF;wBAAA6D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,EACHtB,wBAAwB,CAACG,GAAG,CAAET,IAAI,iBACjCvC,OAAA,CAAC5B,IAAI;wBAEHmG,EAAE,EAAEhC,IAAI,CAACjB,IAAK;wBACdiC,SAAS,EAAE,GACThB,IAAI,CAACd,OAAO,GACR,8BAA8B,GAC9B,oDAAoD,mEACU;wBACpE6C,OAAO,EAAEA,CAAA,KAAM3D,cAAc,CAAC,KAAK,CAAE;wBAAAR,QAAA,gBAErCH,OAAA,CAACuC,IAAI,CAAChB,IAAI;0BACRgC,SAAS,EAAE,GACThB,IAAI,CAACd,OAAO,GAAG,kBAAkB,GAAG,yCAAyC,6BACjD;0BAC9B,eAAY;wBAAM;0BAAAuC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnB,CAAC,EACD5B,IAAI,CAAClB,IAAI;sBAAA,GAfLkB,IAAI,CAAClB,IAAI;wBAAA2C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAgBV,CACP,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CACN,EAGAhB,gBAAgB,iBACfnD,OAAA;sBAAKuD,SAAS,EAAC,WAAW;sBAAApD,QAAA,gBACxBH,OAAA;wBAAGuD,SAAS,EAAC,wEAAwE;wBAAApD,QAAA,EAAC;sBAEtF;wBAAA6D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,EACHrB,wBAAwB,CAACE,GAAG,CAAET,IAAI,iBACjCvC,OAAA,CAAC5B,IAAI;wBAEHmG,EAAE,EAAEhC,IAAI,CAACjB,IAAK;wBACdiC,SAAS,EAAE,GACThB,IAAI,CAACd,OAAO,GACR,8BAA8B,GAC9B,oDAAoD,mEACU;wBACpE6C,OAAO,EAAEA,CAAA,KAAM3D,cAAc,CAAC,KAAK,CAAE;wBAAAR,QAAA,gBAErCH,OAAA,CAACuC,IAAI,CAAChB,IAAI;0BACRgC,SAAS,EAAE,GACThB,IAAI,CAACd,OAAO,GAAG,kBAAkB,GAAG,yCAAyC,6BACjD;0BAC9B,eAAY;wBAAM;0BAAAuC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnB,CAAC,EACD5B,IAAI,CAAClB,IAAI;sBAAA,GAfLkB,IAAI,CAAClB,IAAI;wBAAA2C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAgBV,CACP,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNnE,OAAA;kBAAKuD,SAAS,EAAC,iDAAiD;kBAAApD,QAAA,eAC9DH,OAAA,CAAC5B,IAAI;oBAACmG,EAAE,EAAC,UAAU;oBAAChB,SAAS,EAAC,2BAA2B;oBAAApD,QAAA,eACvDH,OAAA;sBAAKuD,SAAS,EAAC,mBAAmB;sBAAApD,QAAA,gBAChCH,OAAA;wBAAAG,QAAA,eACEH,OAAA,CAACnB,cAAc;0BAAC0E,SAAS,EAAC;wBAAmD;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7E,CAAC,eACNnE,OAAA;wBAAKuD,SAAS,EAAC,MAAM;wBAAApD,QAAA,gBACnBH,OAAA;0BAAGuD,SAAS,EAAC,+DAA+D;0BAAApD,QAAA,EACzE,CAAAS,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,IAAI,KAAI;wBAAW;0BAAA2C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzB,CAAC,eACJnE,OAAA;0BAAGuD,SAAS,EAAC,6DAA6D;0BAAApD,QAAA,EAAC;wBAE3E;0BAAA6D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACnBnE,OAAA;cAAKuD,SAAS,EAAC;YAAoB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAuD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGlBnE,OAAA;QAAKuD,SAAS,EAAC,0DAA0D;QAAApD,QAAA,eACvEH,OAAA;UAAKuD,SAAS,EAAC,gEAAgE;UAAApD,QAAA,gBAC7EH,OAAA;YAAKuD,SAAS,EAAC,gDAAgD;YAAApD,QAAA,gBAC7DH,OAAA;cAAKuD,SAAS,EAAC,sCAAsC;cAAApD,QAAA,eACnDH,OAAA;gBAAIuD,SAAS,EAAC,oCAAoC;gBAAApD,QAAA,EAAC;cAAU;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,eACNnE,OAAA;cAAKuD,SAAS,EAAC,4BAA4B;cAAApD,QAAA,GAExC8C,eAAe,iBACdjD,OAAA;gBAAKuD,SAAS,EAAC,WAAW;gBAAApD,QAAA,gBACxBH,OAAA;kBAAGuD,SAAS,EAAC,wEAAwE;kBAAApD,QAAA,EAAC;gBAEtF;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,EACHxB,uBAAuB,CAACK,GAAG,CAAET,IAAI,iBAChCvC,OAAA,CAAC5B,IAAI;kBAEHmG,EAAE,EAAEhC,IAAI,CAACjB,IAAK;kBACdiC,SAAS,EAAE,GACThB,IAAI,CAACd,OAAO,GACR,8BAA8B,GAC9B,oDAAoD,mEACU;kBAAAtB,QAAA,gBAEpEH,OAAA,CAACuC,IAAI,CAAChB,IAAI;oBACRgC,SAAS,EAAE,GACThB,IAAI,CAACd,OAAO,GAAG,kBAAkB,GAAG,yCAAyC,6BACjD;oBAC9B,eAAY;kBAAM;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC,EACD5B,IAAI,CAAClB,IAAI;gBAAA,GAdLkB,IAAI,CAAClB,IAAI;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAeV,CACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,EAGAjB,gBAAgB,iBACflD,OAAA;gBAAKuD,SAAS,EAAC,WAAW;gBAAApD,QAAA,gBACxBH,OAAA;kBAAGuD,SAAS,EAAC,wEAAwE;kBAAApD,QAAA,EAAC;gBAEtF;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,EACHtB,wBAAwB,CAACG,GAAG,CAAET,IAAI,iBACjCvC,OAAA,CAAC5B,IAAI;kBAEHmG,EAAE,EAAEhC,IAAI,CAACjB,IAAK;kBACdiC,SAAS,EAAE,GACThB,IAAI,CAACd,OAAO,GACR,8BAA8B,GAC9B,oDAAoD,mEACU;kBAAAtB,QAAA,gBAEpEH,OAAA,CAACuC,IAAI,CAAChB,IAAI;oBACRgC,SAAS,EAAE,GACThB,IAAI,CAACd,OAAO,GAAG,kBAAkB,GAAG,yCAAyC,6BACjD;oBAC9B,eAAY;kBAAM;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC,EACD5B,IAAI,CAAClB,IAAI;gBAAA,GAdLkB,IAAI,CAAClB,IAAI;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAeV,CACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,EAGAhB,gBAAgB,iBACfnD,OAAA;gBAAKuD,SAAS,EAAC,WAAW;gBAAApD,QAAA,gBACxBH,OAAA;kBAAGuD,SAAS,EAAC,wEAAwE;kBAAApD,QAAA,EAAC;gBAEtF;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,EACHrB,wBAAwB,CAACE,GAAG,CAAET,IAAI,iBACjCvC,OAAA,CAAC5B,IAAI;kBAEHmG,EAAE,EAAEhC,IAAI,CAACjB,IAAK;kBACdiC,SAAS,EAAE,GACThB,IAAI,CAACd,OAAO,GACR,8BAA8B,GAC9B,oDAAoD,mEACU;kBAAAtB,QAAA,gBAEpEH,OAAA,CAACuC,IAAI,CAAChB,IAAI;oBACRgC,SAAS,EAAE,GACThB,IAAI,CAACd,OAAO,GAAG,kBAAkB,GAAG,yCAAyC,6BACjD;oBAC9B,eAAY;kBAAM;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC,EACD5B,IAAI,CAAClB,IAAI;gBAAA,GAdLkB,IAAI,CAAClB,IAAI;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAeV,CACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNnE,OAAA;YAAKuD,SAAS,EAAC,iDAAiD;YAAApD,QAAA,eAC9DH,OAAA,CAACvB,IAAI;cAAC6E,EAAE,EAAC,KAAK;cAACC,SAAS,EAAC,wCAAwC;cAAApD,QAAA,gBAC/DH,OAAA;gBAAAG,QAAA,eACEH,OAAA,CAACvB,IAAI,CAAC+F,MAAM;kBAACjB,SAAS,EAAC,4GAA4G;kBAAApD,QAAA,eACjIH,OAAA;oBAAKuD,SAAS,EAAC,0BAA0B;oBAAApD,QAAA,gBACvCH,OAAA;sBAAAG,QAAA,eACEH,OAAA,CAACnB,cAAc;wBAAC0E,SAAS,EAAC;sBAAmD;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7E,CAAC,eACNnE,OAAA;sBAAKuD,SAAS,EAAC,MAAM;sBAAApD,QAAA,gBACnBH,OAAA;wBAAGuD,SAAS,EAAC,6DAA6D;wBAAApD,QAAA,EACvE,CAAAS,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,IAAI,KAAI;sBAAW;wBAAA2C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzB,CAAC,eACJnE,OAAA;wBAAGuD,SAAS,EAAC,6DAA6D;wBAAApD,QAAA,EACvE,CAAAS,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D,KAAK,KAAI;sBAAqB;wBAAAT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC,eACNnE,OAAA,CAAChB,eAAe;sBACduE,SAAS,EAAC,uEAAuE;sBACjF,eAAY;oBAAM;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACNnE,OAAA,CAACtB,UAAU;gBACT4E,EAAE,EAAEnF,QAAS;gBACbuF,KAAK,EAAC,kCAAkC;gBACxCC,SAAS,EAAC,8BAA8B;gBACxCC,OAAO,EAAC,iCAAiC;gBACzCC,KAAK,EAAC,gCAAgC;gBACtCC,SAAS,EAAC,iCAAiC;gBAC3CC,OAAO,EAAC,8BAA8B;gBAAA5D,QAAA,eAEtCH,OAAA,CAACvB,IAAI,CAACiG,KAAK;kBAACnB,SAAS,EAAC,iJAAiJ;kBAAApD,QAAA,eACrKH,OAAA;oBAAKuD,SAAS,EAAC,MAAM;oBAAApD,QAAA,gBACnBH,OAAA,CAACvB,IAAI,CAACkG,IAAI;sBAAAxE,QAAA,EACPA,CAAC;wBAAEyE;sBAAO,CAAC,kBACV5E,OAAA,CAAC5B,IAAI;wBACHmG,EAAE,EAAC,UAAU;wBACbhB,SAAS,EAAE,GACTqB,MAAM,GAAG,2BAA2B,GAAG,eAAe,yBAC9B;wBAAAzE,QAAA,gBAE1BH,OAAA,CAACnB,cAAc;0BAAC0E,SAAS,EAAC,4BAA4B;0BAAC,eAAY;wBAAM;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC5EnE,OAAA;0BAAAG,QAAA,EAAM;wBAAQ;0BAAA6D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjB;oBACP;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ,CAAC,eACZnE,OAAA,CAACvB,IAAI,CAACkG,IAAI;sBAAAxE,QAAA,EACPA,CAAC;wBAAEyE;sBAAO,CAAC,kBACV5E,OAAA;wBACEsE,OAAO,EAAErD,YAAa;wBACtBsC,SAAS,EAAE,GACTqB,MAAM,GAAG,2BAA2B,GAAG,eAAe,0CACb;wBAAAzE,QAAA,gBAE3CH,OAAA,CAAClB,yBAAyB;0BAACyE,SAAS,EAAC,4BAA4B;0BAAC,eAAY;wBAAM;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACvFnE,OAAA;0BAAAG,QAAA,EAAM;wBAAS;0BAAA6D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChB;oBACT;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnE,OAAA;QAAKuD,SAAS,EAAC,+BAA+B;QAAApD,QAAA,gBAC5CH,OAAA;UAAKuD,SAAS,EAAC,gEAAgE;UAAApD,QAAA,eAC7EH,OAAA;YACEqE,IAAI,EAAC,QAAQ;YACbd,SAAS,EAAC,wLAAwL;YAClMe,OAAO,EAAEA,CAAA,KAAM3D,cAAc,CAAC,IAAI,CAAE;YAAAR,QAAA,gBAEpCH,OAAA;cAAMuD,SAAS,EAAC,SAAS;cAAApD,QAAA,EAAC;YAAY;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7CnE,OAAA,CAACrB,SAAS;cAAC4E,SAAS,EAAC,SAAS;cAAC,eAAY;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNnE,OAAA;UAAMuD,SAAS,EAAC,QAAQ;UAAApD,QAAA,eACtBH,OAAA;YAAKuD,SAAS,EAAC,MAAM;YAAApD,QAAA,eACnBH,OAAA;cAAKuD,SAAS,EAAC,wCAAwC;cAAApD,QAAA,eACrDH,OAAA,CAAC3B,MAAM;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC,gBACN,CAAC;AAEP,CAAC;AAAC/D,EAAA,CA7mBIF,UAAU;EAAA,QAE0BJ,OAAO,EAC9BxB,WAAW,EACXC,WAAW;AAAA;AAAAsG,EAAA,GAJxB3E,UAAU;AA+mBhB,eAAeA,UAAU;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}