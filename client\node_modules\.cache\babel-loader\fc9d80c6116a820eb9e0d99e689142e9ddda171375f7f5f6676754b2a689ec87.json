{"ast": null, "code": "var _jsxFileName = \"C:\\\\claude\\\\burky_root_web\\\\client\\\\src\\\\components\\\\PagePermission.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../hooks/useAuth';\n\n/**\n * İzin gerektiren sayfalar için sarmalayıcı bileşen\n * Bu bileşen sayfa bazlı READ izinlerini kontrol eder\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PagePermission = ({\n  pagePath,\n  children\n}) => {\n  _s();\n  var _user$role;\n  const {\n    hasPermission,\n    user\n  } = useAuth();\n  const location = useLocation();\n  const hasReadPermission = hasPermission(pagePath, 'READ');\n\n  // Admin rolüne sahip kullanıcı tüm sayfalara eri<PERSON><PERSON><PERSON>r\n  const isAdmin = (user === null || user === void 0 ? void 0 : (_user$role = user.role) === null || _user$role === void 0 ? void 0 : _user$role.name) === 'Admin';\n\n  // İzin kontrolü sonucunu loglayalım\n  useEffect(() => {\n    var _user$role2;\n    console.log(`${pagePath} sayfası için izin kontrolü:`, {\n      hasReadPermission,\n      isAdmin,\n      user: user === null || user === void 0 ? void 0 : (_user$role2 = user.role) === null || _user$role2 === void 0 ? void 0 : _user$role2.name\n    });\n  }, [pagePath, hasReadPermission, isAdmin, user]);\n\n  // İzin yoksa dashboard'a yönlendir\n  if (!hasReadPermission && !isAdmin) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/dashboard\",\n      state: {\n        from: location,\n        message: `${pagePath} sayfasına erişim izniniz yok`\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this);\n  }\n\n  // İzin varsa sayfayı göster\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n_s(PagePermission, \"0peq1G2BVW6mNtZ5RukRXznyFzU=\", false, function () {\n  return [useAuth, useLocation];\n});\n_c = PagePermission;\nexport default PagePermission;\nvar _c;\n$RefreshReg$(_c, \"PagePermission\");", "map": {"version": 3, "names": ["React", "useEffect", "Navigate", "useLocation", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PagePermission", "pagePath", "children", "_s", "_user$role", "hasPermission", "user", "location", "hasReadPermission", "isAdmin", "role", "name", "_user$role2", "console", "log", "to", "state", "from", "message", "replace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/claude/burky_root_web/client/src/components/PagePermission.jsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../hooks/useAuth';\n\n/**\n * İzin gerektiren sayfalar için sarmalayıcı bileşen\n * Bu bileşen sayfa bazlı READ izinlerini kontrol eder\n */\nconst PagePermission = ({ pagePath, children }) => {\n  const { hasPermission, user } = useAuth();\n  const location = useLocation();\n\n  const hasReadPermission = hasPermission(pagePath, 'READ');\n  \n  // Admin rolüne sahip kullanıcı tüm sayfalara erişebilir\n  const isAdmin = user?.role?.name === 'Admin';\n  \n  // İzin kontrolü sonucunu loglayalım\n  useEffect(() => {\n    console.log(`${pagePath} sayfası için izin kontrolü:`, {\n      hasReadPermission,\n      isAdmin,\n      user: user?.role?.name\n    });\n  }, [pagePath, hasReadPermission, isAdmin, user]);\n\n  // İzin yoksa dashboard'a yönlendir\n  if (!hasReadPermission && !isAdmin) {\n    return (\n      <Navigate \n        to=\"/dashboard\" \n        state={{ from: location, message: `${pagePath} sayfasına erişim izniniz yok` }} \n        replace \n      />\n    );\n  }\n\n  // İzin varsa sayfayı göster\n  return <>{children}</>;\n};\n\nexport default PagePermission;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,SAASC,OAAO,QAAQ,kBAAkB;;AAE1C;AACA;AACA;AACA;AAHA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAIA,MAAMC,cAAc,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,UAAA;EACjD,MAAM;IAAEC,aAAa;IAAEC;EAAK,CAAC,GAAGX,OAAO,CAAC,CAAC;EACzC,MAAMY,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAE9B,MAAMc,iBAAiB,GAAGH,aAAa,CAACJ,QAAQ,EAAE,MAAM,CAAC;;EAEzD;EACA,MAAMQ,OAAO,GAAG,CAAAH,IAAI,aAAJA,IAAI,wBAAAF,UAAA,GAAJE,IAAI,CAAEI,IAAI,cAAAN,UAAA,uBAAVA,UAAA,CAAYO,IAAI,MAAK,OAAO;;EAE5C;EACAnB,SAAS,CAAC,MAAM;IAAA,IAAAoB,WAAA;IACdC,OAAO,CAACC,GAAG,CAAC,GAAGb,QAAQ,8BAA8B,EAAE;MACrDO,iBAAiB;MACjBC,OAAO;MACPH,IAAI,EAAEA,IAAI,aAAJA,IAAI,wBAAAM,WAAA,GAAJN,IAAI,CAAEI,IAAI,cAAAE,WAAA,uBAAVA,WAAA,CAAYD;IACpB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACV,QAAQ,EAAEO,iBAAiB,EAAEC,OAAO,EAAEH,IAAI,CAAC,CAAC;;EAEhD;EACA,IAAI,CAACE,iBAAiB,IAAI,CAACC,OAAO,EAAE;IAClC,oBACEZ,OAAA,CAACJ,QAAQ;MACPsB,EAAE,EAAC,YAAY;MACfC,KAAK,EAAE;QAAEC,IAAI,EAAEV,QAAQ;QAAEW,OAAO,EAAE,GAAGjB,QAAQ;MAAgC,CAAE;MAC/EkB,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAEN;;EAEA;EACA,oBAAO1B,OAAA,CAAAE,SAAA;IAAAG,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;AAACC,EAAA,CA/BIH,cAAc;EAAA,QACcL,OAAO,EACtBD,WAAW;AAAA;AAAA8B,EAAA,GAFxBxB,cAAc;AAiCpB,eAAeA,cAAc;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}