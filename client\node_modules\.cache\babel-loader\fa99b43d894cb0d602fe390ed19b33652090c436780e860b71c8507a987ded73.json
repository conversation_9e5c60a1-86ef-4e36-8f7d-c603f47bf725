{"ast": null, "code": "\"use client\";\n\n// src/ReactQueryDevtoolsPanel.tsx\nimport * as React from \"react\";\nimport { onlineManager, useQueryClient } from \"@tanstack/react-query\";\nimport { TanstackQueryDevtoolsPanel } from \"@tanstack/query-devtools\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction ReactQueryDevtoolsPanel(props) {\n  const queryClient = useQueryClient(props.client);\n  const ref = React.useRef(null);\n  const {\n    errorTypes,\n    styleNonce,\n    shadowDOMTarget\n  } = props;\n  const [devtools] = React.useState(new TanstackQueryDevtoolsPanel({\n    client: queryClient,\n    queryFlavor: \"React Query\",\n    version: \"5\",\n    onlineManager,\n    buttonPosition: \"bottom-left\",\n    position: \"bottom\",\n    initialIsOpen: true,\n    errorTypes,\n    styleNonce,\n    shadowDOMTarget,\n    onClose: props.onClose\n  }));\n  React.useEffect(() => {\n    devtools.setClient(queryClient);\n  }, [queryClient, devtools]);\n  React.useEffect(() => {\n    devtools.setOnClose(props.onClose ?? (() => {}));\n  }, [props.onClose, devtools]);\n  React.useEffect(() => {\n    devtools.setErrorTypes(errorTypes || []);\n  }, [errorTypes, devtools]);\n  React.useEffect(() => {\n    if (ref.current) {\n      devtools.mount(ref.current);\n    }\n    return () => {\n      devtools.unmount();\n    };\n  }, [devtools]);\n  return /* @__PURE__ */jsx(\"div\", {\n    style: {\n      height: \"500px\",\n      ...props.style\n    },\n    className: \"tsqd-parent-container\",\n    ref\n  });\n}\nexport { ReactQueryDevtoolsPanel };", "map": {"version": 3, "names": ["React", "onlineManager", "useQueryClient", "TanstackQueryDevtoolsPanel", "jsx", "ReactQueryDevtoolsPanel", "props", "queryClient", "client", "ref", "useRef", "errorTypes", "styleNonce", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "devtools", "useState", "queryFlavor", "version", "buttonPosition", "position", "initialIsOpen", "onClose", "useEffect", "setClient", "setOnClose", "setErrorTypes", "current", "mount", "unmount", "style", "height", "className"], "sources": ["C:\\burky root\\burky_root_web\\client\\node_modules\\@tanstack\\react-query-devtools\\src\\ReactQueryDevtoolsPanel.tsx"], "sourcesContent": ["'use client'\nimport * as React from 'react'\nimport { onlineManager, useQueryClient } from '@tanstack/react-query'\nimport { TanstackQueryDevtoolsPanel } from '@tanstack/query-devtools'\nimport type { DevtoolsErrorType } from '@tanstack/query-devtools'\nimport type { QueryClient } from '@tanstack/react-query'\n\nexport interface DevtoolsPanelOptions {\n  /**\n   * Custom instance of QueryClient\n   */\n  client?: QueryClient\n  /**\n   * Use this so you can define custom errors that can be shown in the devtools.\n   */\n  errorTypes?: Array<DevtoolsErrorType>\n  /**\n   * Use this to pass a nonce to the style tag that is added to the document head. This is useful if you are using a Content Security Policy (CSP) nonce to allow inline styles.\n   */\n  styleNonce?: string\n  /**\n   * Use this so you can attach the devtool's styles to specific element in the DOM.\n   */\n  shadowDOMTarget?: ShadowRoot\n\n  /**\n   * Custom styles for the devtools panel\n   * @default { height: '500px' }\n   * @example { height: '100%' }\n   * @example { height: '100%', width: '100%' }\n   */\n  style?: React.CSSProperties\n\n  /**\n   * Callback function that is called when the devtools panel is closed\n   */\n  onClose?: () => unknown\n}\n\nexport function ReactQueryDevtoolsPanel(\n  props: DevtoolsPanelOptions,\n): React.ReactElement | null {\n  const queryClient = useQueryClient(props.client)\n  const ref = React.useRef<HTMLDivElement>(null)\n  const { errorTypes, styleNonce, shadowDOMTarget } = props\n  const [devtools] = React.useState(\n    new TanstackQueryDevtoolsPanel({\n      client: queryClient,\n      queryFlavor: 'React Query',\n      version: '5',\n      onlineManager,\n      buttonPosition: 'bottom-left',\n      position: 'bottom',\n      initialIsOpen: true,\n      errorTypes,\n      styleNonce,\n      shadowDOMTarget,\n      onClose: props.onClose,\n    }),\n  )\n\n  React.useEffect(() => {\n    devtools.setClient(queryClient)\n  }, [queryClient, devtools])\n\n  React.useEffect(() => {\n    devtools.setOnClose(props.onClose ?? (() => {}))\n  }, [props.onClose, devtools])\n\n  React.useEffect(() => {\n    devtools.setErrorTypes(errorTypes || [])\n  }, [errorTypes, devtools])\n\n  React.useEffect(() => {\n    if (ref.current) {\n      devtools.mount(ref.current)\n    }\n\n    return () => {\n      devtools.unmount()\n    }\n  }, [devtools])\n\n  return (\n    <div\n      style={{ height: '500px', ...props.style }}\n      className=\"tsqd-parent-container\"\n      ref={ref}\n    ></div>\n  )\n}\n"], "mappings": ";;;AACA,YAAYA,KAAA,MAAW;AACvB,SAASC,aAAA,EAAeC,cAAA,QAAsB;AAC9C,SAASC,0BAAA,QAAkC;AAiFvC,SAAAC,GAAA;AA7CG,SAASC,wBACdC,KAAA,EAC2B;EAC3B,MAAMC,WAAA,GAAcL,cAAA,CAAeI,KAAA,CAAME,MAAM;EAC/C,MAAMC,GAAA,GAAYT,KAAA,CAAAU,MAAA,CAAuB,IAAI;EAC7C,MAAM;IAAEC,UAAA;IAAYC,UAAA;IAAYC;EAAgB,IAAIP,KAAA;EACpD,MAAM,CAACQ,QAAQ,IAAUd,KAAA,CAAAe,QAAA,CACvB,IAAIZ,0BAAA,CAA2B;IAC7BK,MAAA,EAAQD,WAAA;IACRS,WAAA,EAAa;IACbC,OAAA,EAAS;IACThB,aAAA;IACAiB,cAAA,EAAgB;IAChBC,QAAA,EAAU;IACVC,aAAA,EAAe;IACfT,UAAA;IACAC,UAAA;IACAC,eAAA;IACAQ,OAAA,EAASf,KAAA,CAAMe;EACjB,CAAC,CACH;EAEMrB,KAAA,CAAAsB,SAAA,CAAU,MAAM;IACpBR,QAAA,CAASS,SAAA,CAAUhB,WAAW;EAChC,GAAG,CAACA,WAAA,EAAaO,QAAQ,CAAC;EAEpBd,KAAA,CAAAsB,SAAA,CAAU,MAAM;IACpBR,QAAA,CAASU,UAAA,CAAWlB,KAAA,CAAMe,OAAA,KAAY,MAAM,CAAC,EAAE;EACjD,GAAG,CAACf,KAAA,CAAMe,OAAA,EAASP,QAAQ,CAAC;EAEtBd,KAAA,CAAAsB,SAAA,CAAU,MAAM;IACpBR,QAAA,CAASW,aAAA,CAAcd,UAAA,IAAc,EAAE;EACzC,GAAG,CAACA,UAAA,EAAYG,QAAQ,CAAC;EAEnBd,KAAA,CAAAsB,SAAA,CAAU,MAAM;IACpB,IAAIb,GAAA,CAAIiB,OAAA,EAAS;MACfZ,QAAA,CAASa,KAAA,CAAMlB,GAAA,CAAIiB,OAAO;IAC5B;IAEA,OAAO,MAAM;MACXZ,QAAA,CAASc,OAAA,CAAQ;IACnB;EACF,GAAG,CAACd,QAAQ,CAAC;EAEb,OACE,eAAAV,GAAA,CAAC;IACCyB,KAAA,EAAO;MAAEC,MAAA,EAAQ;MAAS,GAAGxB,KAAA,CAAMuB;IAAM;IACzCE,SAAA,EAAU;IACVtB;EAAA,CACD;AAEL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}