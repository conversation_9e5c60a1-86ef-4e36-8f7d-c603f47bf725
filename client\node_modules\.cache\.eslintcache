[{"C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\index.jsx": "1", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\App.jsx": "2", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\contexts\\AuthContext.jsx": "3", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\hooks\\useAuth.js": "4", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\PagePermission.jsx": "5", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ProtectedRoute.jsx": "6", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\PageTransition.jsx": "7", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\layout\\MainLayout.jsx": "8", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\layout\\AuthLayout.jsx": "9", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\services\\api.js": "10", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\auth\\LoginPage.jsx": "11", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\auth\\RegisterPage.jsx": "12", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\auth\\ForgotPasswordPage.jsx": "13", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\dashboard\\DashboardPage.jsx": "14", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\profile\\ProfilePage.jsx": "15", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\dashboard\\ExpertDashboardPage.jsx": "16", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\profile\\ExpertProfilePage.jsx": "17", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\users\\UsersPage.jsx": "18", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\profile\\ClientProfilePage.jsx": "19", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\users\\UserFormPage.jsx": "20", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\RolesPage.jsx": "21", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\RoleFormPage.jsx": "22", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\permissions\\PermissionsPage.jsx": "23", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\RolePermissionsPage.jsx": "24", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\audit-logs\\AuditLogsPage.jsx": "25", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\settings\\SystemSettingsPage.jsx": "26", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\profile\\AdminProfilePage.jsx": "27", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\availabilities\\index.js": "28", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\sessions\\index.js": "29", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\appointments\\index.js": "30", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\messages\\index.js": "31", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\reports\\index.js": "32", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\clients\\index.js": "33", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\dashboard\\index.js": "34", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\experts\\index.js": "35", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\appointments\\index.js": "36", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\messages\\index.js": "37", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\sessions\\index.js": "38", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\packages\\index.js": "39", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\payments\\index.js": "40", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\availabilities\\AvailabilityPage.jsx": "41", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\sessions\\SessionsPage.jsx": "42", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\appointments\\AppointmentsPage.jsx": "43", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\messages\\MessagesPage.jsx": "44", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\reports\\ReportsPage.jsx": "45", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\clients\\ClientsPage.jsx": "46", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\components\\PermissionMatrix.jsx": "47", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\appointments\\ClientAppointmentsPage.jsx": "48", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\dashboard\\ClientDashboardPage.jsx": "49", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\experts\\ClientExpertsPage.jsx": "50", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\messages\\ClientMessagesPage.jsx": "51", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\index.js": "52", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\packages\\ClientPackagesPage.jsx": "53", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\sessions\\ClientSessionsPage.jsx": "54", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\payments\\ClientPaymentsPage.jsx": "55", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormTextarea.jsx": "56", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormSelect.jsx": "57", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormRadio.jsx": "58", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormCheckbox.jsx": "59", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormInput.jsx": "60", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\common\\MultiSelect.jsx": "61", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\experts\\ClientExpertDetailPage.jsx": "62", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\appointments\\BookAppointmentPage.jsx": "63"}, {"size": 1078, "mtime": 1742731191824, "results": "64", "hashOfConfig": "65"}, {"size": 12732, "mtime": 1754673777859, "results": "66", "hashOfConfig": "65"}, {"size": 12010, "mtime": 1743023176067, "results": "67", "hashOfConfig": "65"}, {"size": 283, "mtime": 1742651186300, "results": "68", "hashOfConfig": "65"}, {"size": 1200, "mtime": 1742844616558, "results": "69", "hashOfConfig": "65"}, {"size": 808, "mtime": 1742651192925, "results": "70", "hashOfConfig": "65"}, {"size": 813, "mtime": 1743121345478, "results": "71", "hashOfConfig": "65"}, {"size": 26174, "mtime": 1743085672272, "results": "72", "hashOfConfig": "65"}, {"size": 840, "mtime": 1742651199783, "results": "73", "hashOfConfig": "65"}, {"size": 5307, "mtime": 1754904133339, "results": "74", "hashOfConfig": "65"}, {"size": 5161, "mtime": 1743023176067, "results": "75", "hashOfConfig": "65"}, {"size": 13594, "mtime": 1743023175878, "results": "76", "hashOfConfig": "65"}, {"size": 3680, "mtime": 1742839996729, "results": "77", "hashOfConfig": "65"}, {"size": 6149, "mtime": 1742652514170, "results": "78", "hashOfConfig": "65"}, {"size": 10004, "mtime": 1742687855831, "results": "79", "hashOfConfig": "65"}, {"size": 25808, "mtime": 1742995680884, "results": "80", "hashOfConfig": "65"}, {"size": 34266, "mtime": 1754517675551, "results": "81", "hashOfConfig": "65"}, {"size": 7579, "mtime": 1742681038274, "results": "82", "hashOfConfig": "65"}, {"size": 18240, "mtime": 1743121346919, "results": "83", "hashOfConfig": "65"}, {"size": 13581, "mtime": 1742729246617, "results": "84", "hashOfConfig": "65"}, {"size": 9374, "mtime": 1742685959799, "results": "85", "hashOfConfig": "65"}, {"size": 21513, "mtime": 1743085705486, "results": "86", "hashOfConfig": "65"}, {"size": 5307, "mtime": 1742686008746, "results": "87", "hashOfConfig": "65"}, {"size": 915, "mtime": 1742668681019, "results": "88", "hashOfConfig": "65"}, {"size": 10152, "mtime": 1742686053807, "results": "89", "hashOfConfig": "65"}, {"size": 16081, "mtime": 1742653511611, "results": "90", "hashOfConfig": "65"}, {"size": 183, "mtime": 1754672350696, "results": "91", "hashOfConfig": "65"}, {"size": 87, "mtime": 1742911290814, "results": "92", "hashOfConfig": "65"}, {"size": 75, "mtime": 1742926306611, "results": "93", "hashOfConfig": "65"}, {"size": 87, "mtime": 1742924113827, "results": "94", "hashOfConfig": "65"}, {"size": 75, "mtime": 1742928519256, "results": "95", "hashOfConfig": "65"}, {"size": 72, "mtime": 1742930957065, "results": "96", "hashOfConfig": "65"}, {"size": 72, "mtime": 1742930563398, "results": "97", "hashOfConfig": "65"}, {"size": 96, "mtime": 1742942293117, "results": "98", "hashOfConfig": "65"}, {"size": 189, "mtime": 1754672075729, "results": "99", "hashOfConfig": "65"}, {"size": 195, "mtime": 1754673752486, "results": "100", "hashOfConfig": "65"}, {"size": 93, "mtime": 1742952326351, "results": "101", "hashOfConfig": "65"}, {"size": 93, "mtime": 1742951288236, "results": "102", "hashOfConfig": "65"}, {"size": 93, "mtime": 1743107128763, "results": "103", "hashOfConfig": "65"}, {"size": 91, "mtime": 1743107140295, "results": "104", "hashOfConfig": "65"}, {"size": 33144, "mtime": 1754676655256, "results": "105", "hashOfConfig": "65"}, {"size": 21636, "mtime": 1754858963361, "results": "106", "hashOfConfig": "65"}, {"size": 22707, "mtime": 1754905183885, "results": "107", "hashOfConfig": "65"}, {"size": 35391, "mtime": 1754858963157, "results": "108", "hashOfConfig": "65"}, {"size": 33648, "mtime": 1742995680878, "results": "109", "hashOfConfig": "65"}, {"size": 22896, "mtime": 1742995680882, "results": "110", "hashOfConfig": "65"}, {"size": 8841, "mtime": 1742669558119, "results": "111", "hashOfConfig": "65"}, {"size": 22708, "mtime": 1754905306185, "results": "112", "hashOfConfig": "65"}, {"size": 25889, "mtime": 1742943667842, "results": "113", "hashOfConfig": "65"}, {"size": 29956, "mtime": 1754672945896, "results": "114", "hashOfConfig": "65"}, {"size": 37983, "mtime": 1754858963346, "results": "115", "hashOfConfig": "65"}, {"size": 295, "mtime": 1743101819804, "results": "116", "hashOfConfig": "65"}, {"size": 23208, "mtime": 1742991622871, "results": "117", "hashOfConfig": "65"}, {"size": 22944, "mtime": 1754905212875, "results": "118", "hashOfConfig": "65"}, {"size": 40654, "mtime": 1742991622874, "results": "119", "hashOfConfig": "65"}, {"size": 3969, "mtime": 1742671375144, "results": "120", "hashOfConfig": "65"}, {"size": 4340, "mtime": 1742671396113, "results": "121", "hashOfConfig": "65"}, {"size": 2654, "mtime": 1742671423203, "results": "122", "hashOfConfig": "65"}, {"size": 2614, "mtime": 1742671410454, "results": "123", "hashOfConfig": "65"}, {"size": 4148, "mtime": 1742671355716, "results": "124", "hashOfConfig": "65"}, {"size": 4754, "mtime": 1754517017782, "results": "125", "hashOfConfig": "65"}, {"size": 18012, "mtime": 1754673063946, "results": "126", "hashOfConfig": "65"}, {"size": 16509, "mtime": 1754678848469, "results": "127", "hashOfConfig": "65"}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "t9pu2d", {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\index.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\App.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\contexts\\AuthContext.jsx", ["317"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\hooks\\useAuth.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\PagePermission.jsx", ["318"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ProtectedRoute.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\PageTransition.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\layout\\MainLayout.jsx", ["319"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\layout\\AuthLayout.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\services\\api.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\auth\\LoginPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\auth\\RegisterPage.jsx", ["320"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\auth\\ForgotPasswordPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\dashboard\\DashboardPage.jsx", ["321", "322", "323"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\profile\\ProfilePage.jsx", ["324"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\dashboard\\ExpertDashboardPage.jsx", ["325", "326", "327", "328", "329"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\profile\\ExpertProfilePage.jsx", ["330"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\users\\UsersPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\profile\\ClientProfilePage.jsx", ["331", "332"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\users\\UserFormPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\RolesPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\RoleFormPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\permissions\\PermissionsPage.jsx", ["333", "334"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\RolePermissionsPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\audit-logs\\AuditLogsPage.jsx", ["335"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\settings\\SystemSettingsPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\profile\\AdminProfilePage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\availabilities\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\sessions\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\appointments\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\messages\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\reports\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\clients\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\dashboard\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\experts\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\appointments\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\messages\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\sessions\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\packages\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\payments\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\availabilities\\AvailabilityPage.jsx", ["336", "337", "338", "339", "340", "341"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\sessions\\SessionsPage.jsx", ["342", "343", "344", "345", "346", "347"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\appointments\\AppointmentsPage.jsx", ["348", "349", "350", "351", "352", "353", "354"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\messages\\MessagesPage.jsx", ["355", "356", "357", "358", "359", "360", "361", "362", "363", "364"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\reports\\ReportsPage.jsx", ["365", "366", "367"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\clients\\ClientsPage.jsx", ["368", "369", "370", "371", "372"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\components\\PermissionMatrix.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\appointments\\ClientAppointmentsPage.jsx", ["373", "374", "375", "376", "377", "378", "379", "380", "381", "382"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\dashboard\\ClientDashboardPage.jsx", ["383", "384", "385", "386"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\experts\\ClientExpertsPage.jsx", ["387", "388", "389", "390", "391"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\messages\\ClientMessagesPage.jsx", ["392", "393", "394", "395", "396", "397", "398", "399", "400"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\packages\\ClientPackagesPage.jsx", ["401", "402"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\sessions\\ClientSessionsPage.jsx", ["403"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\payments\\ClientPaymentsPage.jsx", ["404", "405", "406"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormTextarea.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormSelect.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormRadio.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormCheckbox.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormInput.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\common\\MultiSelect.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\experts\\ClientExpertDetailPage.jsx", ["407", "408", "409", "410", "411"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\appointments\\BookAppointmentPage.jsx", ["412"], [], {"ruleId": "413", "severity": 1, "message": "414", "line": 75, "column": 6, "nodeType": "415", "endLine": 75, "endColumn": 8, "suggestions": "416"}, {"ruleId": "417", "severity": 1, "message": "418", "line": 10, "column": 11, "nodeType": "419", "messageId": "420", "endLine": 10, "endColumn": 15}, {"ruleId": "417", "severity": 1, "message": "421", "line": 1, "column": 27, "nodeType": "419", "messageId": "420", "endLine": 1, "endColumn": 36}, {"ruleId": "417", "severity": 1, "message": "422", "line": 11, "column": 21, "nodeType": "419", "messageId": "420", "endLine": 11, "endColumn": 33}, {"ruleId": "423", "severity": 1, "message": "424", "line": 30, "column": 15, "nodeType": "425", "endLine": 30, "endColumn": 91}, {"ruleId": "423", "severity": 1, "message": "424", "line": 57, "column": 15, "nodeType": "425", "endLine": 57, "endColumn": 91}, {"ruleId": "423", "severity": 1, "message": "424", "line": 84, "column": 15, "nodeType": "425", "endLine": 84, "endColumn": 91}, {"ruleId": "417", "severity": 1, "message": "426", "line": 6, "column": 21, "nodeType": "419", "messageId": "420", "endLine": 6, "endColumn": 33}, {"ruleId": "417", "severity": 1, "message": "427", "line": 12, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 12, "endColumn": 19}, {"ruleId": "417", "severity": 1, "message": "428", "line": 14, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 14, "endColumn": 16}, {"ruleId": "417", "severity": 1, "message": "429", "line": 29, "column": 34, "nodeType": "419", "messageId": "420", "endLine": 29, "endColumn": 59}, {"ruleId": "417", "severity": 1, "message": "430", "line": 130, "column": 54, "nodeType": "419", "messageId": "420", "endLine": 130, "endColumn": 61}, {"ruleId": "417", "severity": 1, "message": "431", "line": 133, "column": 9, "nodeType": "419", "messageId": "420", "endLine": 133, "endColumn": 23}, {"ruleId": "417", "severity": 1, "message": "432", "line": 29, "column": 10, "nodeType": "419", "messageId": "420", "endLine": 29, "endColumn": 20}, {"ruleId": "417", "severity": 1, "message": "426", "line": 6, "column": 47, "nodeType": "419", "messageId": "420", "endLine": 6, "endColumn": 59}, {"ruleId": "417", "severity": 1, "message": "433", "line": 24, "column": 10, "nodeType": "419", "messageId": "420", "endLine": 24, "endColumn": 20}, {"ruleId": "417", "severity": 1, "message": "434", "line": 9, "column": 11, "nodeType": "419", "messageId": "420", "endLine": 9, "endColumn": 24}, {"ruleId": "417", "severity": 1, "message": "435", "line": 12, "column": 10, "nodeType": "419", "messageId": "420", "endLine": 12, "endColumn": 15}, {"ruleId": "417", "severity": 1, "message": "434", "line": 9, "column": 11, "nodeType": "419", "messageId": "420", "endLine": 9, "endColumn": 24}, {"ruleId": "417", "severity": 1, "message": "436", "line": 16, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 16, "endColumn": 20}, {"ruleId": "417", "severity": 1, "message": "437", "line": 19, "column": 51, "nodeType": "419", "messageId": "420", "endLine": 19, "endColumn": 58}, {"ruleId": "417", "severity": 1, "message": "418", "line": 28, "column": 11, "nodeType": "419", "messageId": "420", "endLine": 28, "endColumn": 15}, {"ruleId": "417", "severity": 1, "message": "438", "line": 42, "column": 10, "nodeType": "419", "messageId": "420", "endLine": 42, "endColumn": 20}, {"ruleId": "413", "severity": 1, "message": "439", "line": 54, "column": 6, "nodeType": "415", "endLine": 54, "endColumn": 8, "suggestions": "440"}, {"ruleId": "417", "severity": 1, "message": "441", "line": 397, "column": 9, "nodeType": "419", "messageId": "420", "endLine": 397, "endColumn": 22}, {"ruleId": "417", "severity": 1, "message": "442", "line": 4, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 4, "endColumn": 14}, {"ruleId": "417", "severity": 1, "message": "443", "line": 9, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 9, "endColumn": 14}, {"ruleId": "417", "severity": 1, "message": "444", "line": 11, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 11, "endColumn": 15}, {"ruleId": "417", "severity": 1, "message": "445", "line": 15, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 15, "endColumn": 11}, {"ruleId": "417", "severity": 1, "message": "446", "line": 17, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 17, "endColumn": 16}, {"ruleId": "417", "severity": 1, "message": "418", "line": 28, "column": 11, "nodeType": "419", "messageId": "420", "endLine": 28, "endColumn": 15}, {"ruleId": "417", "severity": 1, "message": "442", "line": 4, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 4, "endColumn": 14}, {"ruleId": "417", "severity": 1, "message": "447", "line": 7, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 7, "endColumn": 11}, {"ruleId": "417", "severity": 1, "message": "444", "line": 11, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 11, "endColumn": 15}, {"ruleId": "417", "severity": 1, "message": "448", "line": 15, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 15, "endColumn": 17}, {"ruleId": "417", "severity": 1, "message": "446", "line": 16, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 16, "endColumn": 16}, {"ruleId": "417", "severity": 1, "message": "449", "line": 17, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 17, "endColumn": 24}, {"ruleId": "417", "severity": 1, "message": "418", "line": 29, "column": 11, "nodeType": "419", "messageId": "420", "endLine": 29, "endColumn": 15}, {"ruleId": "417", "severity": 1, "message": "447", "line": 9, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 9, "endColumn": 11}, {"ruleId": "417", "severity": 1, "message": "450", "line": 13, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 13, "endColumn": 17}, {"ruleId": "417", "severity": 1, "message": "451", "line": 18, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 18, "endColumn": 12}, {"ruleId": "417", "severity": 1, "message": "452", "line": 20, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 20, "endColumn": 12}, {"ruleId": "417", "severity": 1, "message": "453", "line": 21, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 21, "endColumn": 12}, {"ruleId": "417", "severity": 1, "message": "454", "line": 27, "column": 10, "nodeType": "419", "messageId": "420", "endLine": 27, "endColumn": 14}, {"ruleId": "417", "severity": 1, "message": "455", "line": 43, "column": 10, "nodeType": "419", "messageId": "420", "endLine": 43, "endColumn": 21}, {"ruleId": "413", "severity": 1, "message": "456", "line": 276, "column": 6, "nodeType": "415", "endLine": 276, "endColumn": 23, "suggestions": "457"}, {"ruleId": "413", "severity": 1, "message": "458", "line": 281, "column": 6, "nodeType": "415", "endLine": 281, "endColumn": 8, "suggestions": "459"}, {"ruleId": "413", "severity": 1, "message": "456", "line": 342, "column": 6, "nodeType": "415", "endLine": 342, "endColumn": 28, "suggestions": "460"}, {"ruleId": "417", "severity": 1, "message": "447", "line": 13, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 13, "endColumn": 11}, {"ruleId": "417", "severity": 1, "message": "461", "line": 15, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 15, "endColumn": 28}, {"ruleId": "417", "severity": 1, "message": "418", "line": 256, "column": 11, "nodeType": "419", "messageId": "420", "endLine": 256, "endColumn": 15}, {"ruleId": "417", "severity": 1, "message": "436", "line": 9, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 9, "endColumn": 20}, {"ruleId": "417", "severity": 1, "message": "462", "line": 10, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 10, "endColumn": 17}, {"ruleId": "417", "severity": 1, "message": "447", "line": 11, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 11, "endColumn": 11}, {"ruleId": "417", "severity": 1, "message": "418", "line": 24, "column": 11, "nodeType": "419", "messageId": "420", "endLine": 24, "endColumn": 15}, {"ruleId": "417", "severity": 1, "message": "463", "line": 179, "column": 9, "nodeType": "419", "messageId": "420", "endLine": 179, "endColumn": 19}, {"ruleId": "417", "severity": 1, "message": "444", "line": 11, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 11, "endColumn": 15}, {"ruleId": "417", "severity": 1, "message": "464", "line": 12, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 12, "endColumn": 26}, {"ruleId": "417", "severity": 1, "message": "436", "line": 14, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 14, "endColumn": 20}, {"ruleId": "417", "severity": 1, "message": "448", "line": 15, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 15, "endColumn": 17}, {"ruleId": "417", "severity": 1, "message": "446", "line": 16, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 16, "endColumn": 16}, {"ruleId": "417", "severity": 1, "message": "449", "line": 17, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 17, "endColumn": 24}, {"ruleId": "417", "severity": 1, "message": "465", "line": 19, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 19, "endColumn": 28}, {"ruleId": "417", "severity": 1, "message": "418", "line": 31, "column": 11, "nodeType": "419", "messageId": "420", "endLine": 31, "endColumn": 15}, {"ruleId": "417", "severity": 1, "message": "466", "line": 163, "column": 9, "nodeType": "419", "messageId": "420", "endLine": 163, "endColumn": 24}, {"ruleId": "417", "severity": 1, "message": "463", "line": 183, "column": 9, "nodeType": "419", "messageId": "420", "endLine": 183, "endColumn": 19}, {"ruleId": "417", "severity": 1, "message": "444", "line": 5, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 5, "endColumn": 15}, {"ruleId": "417", "severity": 1, "message": "427", "line": 12, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 12, "endColumn": 19}, {"ruleId": "417", "severity": 1, "message": "428", "line": 14, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 14, "endColumn": 16}, {"ruleId": "417", "severity": 1, "message": "429", "line": 28, "column": 34, "nodeType": "419", "messageId": "420", "endLine": 28, "endColumn": 59}, {"ruleId": "417", "severity": 1, "message": "467", "line": 6, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 6, "endColumn": 18}, {"ruleId": "417", "severity": 1, "message": "468", "line": 10, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 10, "endColumn": 29}, {"ruleId": "417", "severity": 1, "message": "469", "line": 12, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 12, "endColumn": 18}, {"ruleId": "417", "severity": 1, "message": "470", "line": 13, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 13, "endColumn": 16}, {"ruleId": "417", "severity": 1, "message": "418", "line": 24, "column": 11, "nodeType": "419", "messageId": "420", "endLine": 24, "endColumn": 15}, {"ruleId": "417", "severity": 1, "message": "450", "line": 13, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 13, "endColumn": 17}, {"ruleId": "417", "severity": 1, "message": "471", "line": 15, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 15, "endColumn": 12}, {"ruleId": "417", "severity": 1, "message": "472", "line": 17, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 17, "endColumn": 24}, {"ruleId": "417", "severity": 1, "message": "452", "line": 20, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 20, "endColumn": 12}, {"ruleId": "417", "severity": 1, "message": "453", "line": 21, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 21, "endColumn": 12}, {"ruleId": "417", "severity": 1, "message": "455", "line": 45, "column": 10, "nodeType": "419", "messageId": "420", "endLine": 45, "endColumn": 21}, {"ruleId": "413", "severity": 1, "message": "456", "line": 280, "column": 6, "nodeType": "415", "endLine": 280, "endColumn": 23, "suggestions": "473"}, {"ruleId": "413", "severity": 1, "message": "458", "line": 285, "column": 6, "nodeType": "415", "endLine": 285, "endColumn": 8, "suggestions": "474"}, {"ruleId": "413", "severity": 1, "message": "456", "line": 347, "column": 6, "nodeType": "415", "endLine": 347, "endColumn": 28, "suggestions": "475"}, {"ruleId": "417", "severity": 1, "message": "418", "line": 20, "column": 11, "nodeType": "419", "messageId": "420", "endLine": 20, "endColumn": 15}, {"ruleId": "423", "severity": 1, "message": "424", "line": 487, "column": 19, "nodeType": "425", "endLine": 490, "endColumn": 20}, {"ruleId": "417", "severity": 1, "message": "476", "line": 2, "column": 10, "nodeType": "419", "messageId": "420", "endLine": 2, "endColumn": 17}, {"ruleId": "417", "severity": 1, "message": "477", "line": 7, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 7, "endColumn": 18}, {"ruleId": "417", "severity": 1, "message": "418", "line": 25, "column": 11, "nodeType": "419", "messageId": "420", "endLine": 25, "endColumn": 15}, {"ruleId": "417", "severity": 1, "message": "478", "line": 234, "column": 9, "nodeType": "419", "messageId": "420", "endLine": 234, "endColumn": 23}, {"ruleId": "417", "severity": 1, "message": "470", "line": 11, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 11, "endColumn": 16}, {"ruleId": "417", "severity": 1, "message": "479", "line": 15, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 15, "endColumn": 15}, {"ruleId": "417", "severity": 1, "message": "471", "line": 16, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 16, "endColumn": 12}, {"ruleId": "417", "severity": 1, "message": "480", "line": 17, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 17, "endColumn": 15}, {"ruleId": "417", "severity": 1, "message": "418", "line": 27, "column": 11, "nodeType": "419", "messageId": "420", "endLine": 27, "endColumn": 15}, {"ruleId": "413", "severity": 1, "message": "481", "line": 39, "column": 6, "nodeType": "415", "endLine": 39, "endColumn": 26, "suggestions": "482"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchUserPermissions'. Either include it or remove the dependency array.", "ArrayExpression", ["483"], "no-unused-vars", "'user' is assigned a value but never used.", "Identifier", "unusedVar", "'useEffect' is defined but never used.", "'registerUser' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'FormCheckbox' is defined but never used.", "'DocumentTextIcon' is defined but never used.", "'ArrowDownIcon' is defined but never used.", "'setHasUnreadNotifications' is assigned a value but never used.", "'clients' is assigned a value but never used.", "'getStatusColor' is assigned a value but never used.", "'expertData' is assigned a value but never used.", "'clientData' is assigned a value but never used.", "'hasPermission' is assigned a value but never used.", "'pages' is assigned a value but never used.", "'ArrowDownTrayIcon' is defined but never used.", "'addDays' is defined but never used.", "'hasChanges' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAvailabilities'. Either include it or remove the dependency array.", ["484"], "'formatDayName' is assigned a value but never used.", "'VideoCamera' is defined but never used.", "'XCircleIcon' is defined but never used.", "'ChartBarIcon' is defined but never used.", "'StarIcon' is defined but never used.", "'PaperClipIcon' is defined but never used.", "'UserIcon' is defined but never used.", "'PlayCircleIcon' is defined but never used.", "'DocumentArrowDownIcon' is defined but never used.", "'UserCircleIcon' is defined but never used.", "'ClockIcon' is defined but never used.", "'XMarkIcon' is defined but never used.", "'TrashIcon' is defined but never used.", "'Link' is defined but never used.", "'typingUsers' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'markConversationAsRead'. Either include it or remove the dependency array.", ["485"], "React Hook useEffect has a missing dependency: 'loadConversations'. Either include it or remove the dependency array.", ["486"], ["487"], "'PresentationChartLineIcon' is defined but never used.", "'CheckBadgeIcon' is defined but never used.", "'formatDate' is assigned a value but never used.", "'ChatBubbleLeftRightIcon' is defined but never used.", "'AdjustmentsHorizontalIcon' is defined but never used.", "'getStatusBorder' is assigned a value but never used.", "'ChevronDownIcon' is defined but never used.", "'ChatBubbleLeftEllipsisIcon' is defined but never used.", "'AcademicCapIcon' is defined but never used.", "'UserGroupIcon' is defined but never used.", "'PhoneIcon' is defined but never used.", "'InformationCircleIcon' is defined but never used.", ["488"], ["489"], ["490"], "'useAuth' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'formatDateTime' is assigned a value but never used.", "'GlobeAltIcon' is defined but never used.", "'EnvelopeIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadExpertAndAvailability'. Either include it or remove the dependency array.", ["491"], {"desc": "492", "fix": "493"}, {"desc": "494", "fix": "495"}, {"desc": "496", "fix": "497"}, {"desc": "498", "fix": "499"}, {"desc": "500", "fix": "501"}, {"desc": "496", "fix": "502"}, {"desc": "498", "fix": "503"}, {"desc": "500", "fix": "504"}, {"desc": "505", "fix": "506"}, "Update the dependencies array to be: [fetchUserPermissions]", {"range": "507", "text": "508"}, "Update the dependencies array to be: [fetchAvailabilities]", {"range": "509", "text": "510"}, "Update the dependencies array to be: [markConversationAsRead, socket, user.id]", {"range": "511", "text": "512"}, "Update the dependencies array to be: [loadConversations]", {"range": "513", "text": "514"}, "Update the dependencies array to be: [markConversationAsRead, selectedConversation]", {"range": "515", "text": "516"}, {"range": "517", "text": "512"}, {"range": "518", "text": "514"}, {"range": "519", "text": "516"}, "Update the dependencies array to be: [expertId, loadExpertAndAvailability, navigate]", {"range": "520", "text": "521"}, [2597, 2599], "[fetchUserPermissions]", [1761, 1763], "[fetchAvailabilities]", [10531, 10548], "[markConversationAsRead, socket, user.id]", [10646, 10648], "[loadConversations]", [13050, 13072], "[markConversationAsRead, selectedConversation]", [10699, 10716], [10814, 10816], [13321, 13343], [1208, 1228], "[expertId, loadExpertAndAvailability, navigate]"]