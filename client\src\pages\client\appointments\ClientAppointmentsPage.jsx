import React, { useState, useEffect, useMemo } from 'react';
import { useAuth } from '../../../hooks/useAuth';
import {
  VideoCameraIcon,
  ClockIcon,
  CalendarIcon,
  UserIcon,
  CheckCircleIcon,
  XCircleIcon,
  BellIcon,
  ChartBarIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  ArrowDownTrayIcon,
  PlayCircleIcon,
  PaperClipIcon,
  DocumentArrowDownIcon,
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon
} from '@heroicons/react/24/outline';
import { format, parseISO } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Link } from 'react-router-dom';
import api from '../../../services/api';
import toast from 'react-hot-toast';

/**
 * <PERSON><PERSON><PERSON><PERSON> randevular sayfası
 */
const ClientAppointmentsPage = () => {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [appointments, setAppointments] = useState([]);
  const [activeTab, setActiveTab] = useState('upcoming'); // upcoming, past, all
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  // Randevu durumları
  const appointmentStatuses = {
    pending: "Onay Bekliyor",
    confirmed: "Onaylandı",
    cancelled: "İptal Edildi",
    rejected: "Reddedildi",
    rescheduled: "Yeniden Planlandı",
    completed: "Tamamlandı"
  };

  const loadAppointments = async () => {
    try {
      setIsLoading(true);
      const response = await api.get('/clients/appointments');

      // API verisini frontend formatına çevir
      const formattedAppointments = response.data.appointments.map(appointment => ({
        id: appointment.id,
        expertId: appointment.expertId,
        expertName: `${appointment.expert.firstName} ${appointment.expert.lastName}`,
        expertTitle: appointment.expert.specialty || 'Uzman',
        date: appointment.startTime.split('T')[0],
        time: new Date(appointment.startTime).toTimeString().slice(0, 5),
        duration: 50,
        status: appointment.status.toLowerCase(),
        type: 'video',
        notes: appointment.notes || '',
        expertAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(appointment.expert.firstName)}+${encodeURIComponent(appointment.expert.lastName)}&background=4f46e5&color=fff&size=200&font-size=0.6`,
        createdAt: appointment.createdAt,
        endTime: appointment.endTime
      }));

      setAppointments(formattedAppointments);
    } catch (error) {
      console.error('Randevular yüklenirken hata:', error);
      toast.error('Randevular yüklenemedi');
      setAppointments([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadAppointments();
  }, []);

  // Filtreleme useMemo ile optimize edildi
  const filteredAppointments = useMemo(() => {
    const today = new Date();

    return appointments.filter(appointment => {
      const appointmentDate = parseISO(appointment.date);

      // Tab filtresi
      if (activeTab === 'upcoming' && appointmentDate >= today && (appointment.status === 'confirmed' || appointment.status === 'pending' || appointment.status === 'rescheduled')) {
        // Gelecek randevular
      } else if (activeTab === 'past' && (appointmentDate < today || appointment.status === 'completed' || appointment.status === 'cancelled' || appointment.status === 'rejected')) {
        // Geçmiş randevular
      } else if (activeTab === 'all') {
        // Tüm randevular
      } else if (activeTab !== 'all') {
        return false;
      }

      // Durum filtresi - rejected'ı cancelled olarak treat et
      if (filterStatus !== 'all') {
        if (filterStatus === 'cancelled' && (appointment.status === 'cancelled' || appointment.status === 'rejected')) {
          // Cancelled filter'ında hem cancelled hem rejected göster
        } else if (appointment.status !== filterStatus) {
          return false;
        }
      }

      // Arama filtresi
      if (searchTerm && !appointment.expertName.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false;
      }

      return true;
    });
  }, [appointments, activeTab, filterStatus, searchTerm]);

  // İstatistik hesaplamaları
  const stats = {
    total: appointments.length,
    pending: appointments.filter(a => a.status === 'pending').length,
    confirmed: appointments.filter(a => a.status === 'confirmed').length,
    cancelled: appointments.filter(a => a.status === 'cancelled' || a.status === 'rejected').length,
    rescheduled: appointments.filter(a => a.status === 'rescheduled').length,
    completed: appointments.filter(a => a.status === 'completed').length
  };

  // Tarihe göre sırala - useMemo ile optimize edildi
  const sortedAppointments = useMemo(() => {
    return [...filteredAppointments].sort((a, b) => {
      // Önce tarihleri karşılaştır
      const dateComparison = new Date(a.date) - new Date(b.date);
      if (dateComparison !== 0) return dateComparison;

      // Tarihler aynıysa başlama saatini karşılaştır
      return a.time.localeCompare(b.time);
    });
  }, [filteredAppointments]);

  // Durum badge renkleri
  const getStatusBadge = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'confirmed':
        return 'bg-teal-100 text-teal-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'rescheduled':
        return 'bg-orange-100 text-orange-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Border renkleri
  const getStatusBorder = (status) => {
    switch (status) {
      case 'pending':
        return 'border-yellow-500';
      case 'confirmed':
        return 'border-teal-500';
      case 'cancelled':
        return 'border-red-500';
      case 'rejected':
        return 'border-red-500';
      case 'rescheduled':
        return 'border-orange-500';
      case 'completed':
        return 'border-green-500';
      default:
        return 'border-gray-500';
    }
  };

  // Tarih formatı
  const formatDate = (dateStr) => {
    const date = parseISO(dateStr);
    return format(date, 'd MMMM yyyy, EEEE', { locale: tr });
  };

  // Yükleniyor durumu
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-600"></div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen pb-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-8">
        {/* Başlık ve Üst Kısım */}
        <div className="bg-gradient-to-r from-teal-500 to-teal-600 shadow-lg rounded-lg p-6 mb-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
            <div>
              <h1 className="text-2xl font-bold text-white">Randevularım</h1>
              <p className="mt-1 text-teal-100">
                Tüm randevularınızı bu sayfadan yönetebilirsiniz
              </p>
            </div>
            <div className="mt-3 sm:mt-0 flex space-x-2">
              <Link
                to="/client/experts"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-teal-600 bg-white hover:bg-teal-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-colors duration-150"
              >
                <UserIcon className="h-4 w-4 mr-2" />
                Yeni Randevu
              </Link>
              <Link
                to="/client/sessions"
                className="inline-flex items-center px-4 py-2 border border-white text-sm font-medium rounded-md shadow-sm text-white hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-colors duration-150"
              >
                <CheckCircleIcon className="h-4 w-4 mr-2" />
                Seanslarım
              </Link>
            </div>
          </div>
        </div>

        {/* Özet İstatistikler */}
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
          <div 
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-teal-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'all' && filterStatus === 'all' ? 'ring-2 ring-teal-500' : ''}`}
            onClick={() => {
              setActiveTab('all');
              setFilterStatus('all');
            }}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Toplam</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.total}</span>
          </div>
          
          <div
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-yellow-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'all' && filterStatus === 'pending' ? 'ring-2 ring-yellow-500' : ''}`}
            {...(stats.pending > 0 && {
              onClick: () => {
                setActiveTab('all');
                setFilterStatus('pending');
              }
            })}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Bekleyen</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.pending}</span>
          </div>
          
          <div
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-teal-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'all' && filterStatus === 'confirmed' ? 'ring-2 ring-teal-500' : ''}`}
            {...(stats.confirmed > 0 && {
              onClick: () => {
                setActiveTab('all');
                setFilterStatus('confirmed');
              }
            })}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Onaylanan</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.confirmed}</span>
          </div>

          <div
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-orange-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'all' && filterStatus === 'rescheduled' ? 'ring-2 ring-orange-500' : ''}`}
            {...(stats.rescheduled > 0 && {
              onClick: () => {
                setActiveTab('all');
                setFilterStatus('rescheduled');
              }
            })}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Ertelenen</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.rescheduled}</span>
          </div>

          <div
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-green-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'all' && filterStatus === 'completed' ? 'ring-2 ring-green-500' : ''}`}
            {...(stats.completed > 0 && {
              onClick: () => {
                setActiveTab('all');
                setFilterStatus('completed');
              }
            })}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Tamamlanan</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.completed}</span>
          </div>
          
          <div
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-red-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'all' && filterStatus === 'cancelled' ? 'ring-2 ring-red-500' : ''}`}
            {...(stats.cancelled > 0 && {
              onClick: () => {
                setActiveTab('all');
                setFilterStatus('cancelled');
              }
            })}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">İptal Edilen</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.cancelled}</span>
          </div>
        </div>

        {/* Ana Sekme Navigasyonu */}
        <div className="flex border-b border-gray-200 mb-6">
          <button
            onClick={() => {
              setActiveTab('upcoming');
              setFilterStatus('all');
            }}
            className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
              activeTab === 'upcoming'
                ? 'border-teal-500 text-teal-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center">
              <CalendarIcon className="h-5 w-5 mr-2" />
              <span>Yaklaşan Randevular</span>
            </div>
          </button>
          <button
            onClick={() => setActiveTab('past')}
            className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
              activeTab === 'past'
                ? 'border-teal-500 text-teal-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center">
              <CheckCircleIcon className="h-5 w-5 mr-2" />
              <span>Geçmiş Randevular</span>
            </div>
          </button>
          <button
            onClick={() => {
              setActiveTab('all');
              setFilterStatus('all');
            }}
            className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
              activeTab === 'all'
                ? 'border-teal-500 text-teal-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center">
              <DocumentTextIcon className="h-5 w-5 mr-2" />
              <span>Tüm Randevular</span>
            </div>
          </button>
        </div>

        {/* Arama */}
        <div className="bg-white shadow rounded-lg mb-6">
          <div className="px-4 py-5 sm:p-6">
            <div className="max-w-lg">
              <div className="relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Uzman adı ara..."
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Randevular Listesi */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">
              {activeTab === 'upcoming' ? 'Yaklaşan Randevular' :
               activeTab === 'past' ? 'Geçmiş Randevular' : 'Tüm Randevular'}
              {filterStatus !== 'all' && ` - ${appointmentStatuses[filterStatus]}`}
            </h2>
          </div>

          {sortedAppointments.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {sortedAppointments.map((appointment) => (
                <div key={appointment.id} className="p-6 hover:bg-gray-50 transition duration-150">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <img
                          className="h-10 w-10 rounded-full border border-gray-200"
                          src={appointment.expertAvatar}
                          alt={appointment.expertName}
                        />
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-900">{appointment.expertName}</h3>
                        <p className="text-xs text-gray-500">{appointment.expertTitle}</p>
                        <div className="flex space-x-2 text-xs text-gray-500 mt-1">
                          <span>{format(parseISO(appointment.date), 'EEEE', { locale: tr })}</span>
                          <span>•</span>
                          <span>{format(parseISO(appointment.date), 'd MMMM yyyy', { locale: tr })}</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadge(appointment.status)}`}>
                        {appointmentStatuses[appointment.status]}
                      </span>
                      <span className="text-xs text-gray-500">{appointment.packageName}</span>
                    </div>
                  </div>

                  <div className="mt-4 flex items-center justify-between">
                    <div className="flex space-x-6 text-sm text-gray-500">
                      <div className="flex items-center">
                        <ClockIcon className="h-4 w-4 text-gray-400 mr-1.5" />
                        <span>{appointment.time} ({appointment.duration} dk)</span>
                      </div>
                      <div className="flex items-center">
                        <VideoCameraIcon className="h-4 w-4 text-gray-400 mr-1.5" />
                        <span>Video Görüşme</span>
                      </div>
                    </div>

                    <div className="flex space-x-2">
                      {appointment.status === 'pending' && (
                        <button
                          className="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-orange-700 bg-orange-100 hover:bg-orange-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
                        >
                          <BellIcon className="-ml-0.5 mr-1 h-4 w-4" />
                          Hatırlat
                        </button>
                      )}
                      
                      {(appointment.status === 'pending' || appointment.status === 'confirmed') && (
                        <button
                          className="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 border-red-200"
                        >
                          <XCircleIcon className="-ml-0.5 mr-1 h-4 w-4 text-red-500" />
                          İptal Et
                        </button>
                      )}
                      
                      {(appointment.status === 'confirmed' && parseISO(appointment.date) >= new Date()) && (
                        <button
                          className="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-blue-700 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 border-blue-200"
                        >
                          <CalendarIcon className="-ml-0.5 mr-1 h-4 w-4 text-blue-500" />
                          Yeniden Planla
                        </button>
                      )}
                      
                      {appointment.status === 'completed' && (
                        <Link
                          to={`/client/sessions/${appointment.id}/notes`}
                          className="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-blue-700 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 border-blue-200"
                        >
                          <DocumentTextIcon className="-ml-0.5 mr-1 h-4 w-4 text-blue-500" />
                          Seans Notları
                        </Link>
                      )}
                      
                      <Link
                        to={`/client/experts/${appointment.expertId}`}
                        className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                      >
                        <UserIcon className="-ml-0.5 mr-1 h-4 w-4" />
                        Uzman Profili
                      </Link>
                    </div>
                  </div>

                  {appointment.notes && (
                    <div className="mt-2 text-xs text-gray-500 bg-gray-50 px-3 py-1.5 rounded-md">
                      <span className="font-medium">Not:</span> {appointment.notes}
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="py-12 text-center">
              <CalendarIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Randevu Bulunamadı</h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm || filterStatus !== 'all'
                  ? 'Arama kriterlerinize uygun randevu bulunamadı.'
                  : 'Henüz bir randevunuz bulunmuyor.'}
              </p>
              <div className="mt-6">
                <Link
                  to="/client/experts"
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-teal-600 hover:bg-teal-700 focus:outline-none"
                >
                  <UserIcon className="h-4 w-4 mr-2" />
                  Uzman Ara
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ClientAppointmentsPage; 