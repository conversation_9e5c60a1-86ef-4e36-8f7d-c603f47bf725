/**
 * API Service
 * Configures axios with interceptors for authentication and error handling
 */

import axios from 'axios';
import toast from 'react-hot-toast';

// Create API instance
const api = axios.create({
  baseURL: '/api', // Proxy ile çalışacak şekilde sadece path'i kullan 
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor
api.interceptors.request.use(
  (config) => {
    // Get token from localStorage
    const token = localStorage.getItem('accessToken');
    
    // Add token to headers if it exists
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Yanıt interceptor'ı - başarılı ve hatalı yanıtları loglama
api.interceptors.response.use(
  (response) => {
    // Başarılı yanıtları logla
    console.log(`API Yanıt - ${response.config.url}:`, {
      url: response.config.url,
      durum: response.status,
      durumMetni: response.statusText,
      veri: response.data,
    });
    return response;
  },
  async (error) => {
    // API yanıt hatalarını logla
    console.error(`API Hata - ${error.config?.url || 'bilinmeyen url'}:`, {
      hata: error.message,
      durum: error.response?.status,
      durumMetni: error.response?.statusText,
      veri: error.response?.data,
      url: error.config?.url,
    });

    // Token yenileme işlemi
    if (error.response?.status === 401) {
      const refreshToken = localStorage.getItem('refreshToken');
      
      if (refreshToken) {
        try {
          // Token yenileme isteği
          const refreshResponse = await axios.post(`/api/auth/refresh-token`, {
            refreshToken
          });
          
          if (refreshResponse.data.accessToken) {
            localStorage.setItem('accessToken', refreshResponse.data.accessToken);
            
            // Orijinal isteği yeni token ile tekrarla
            const originalRequest = error.config;
            originalRequest.headers['Authorization'] = `Bearer ${refreshResponse.data.accessToken}`;
            return axios(originalRequest);
          }
        } catch (refreshError) {
          console.error('Token yenileme hatası:', refreshError);
          // Token yenilenemedi, kullanıcıyı çıkış yap
          localStorage.removeItem('accessToken');
          localStorage.removeItem('refreshToken');
          
          toast.error('Oturumunuz sona erdi, lütfen tekrar giriş yapın.');
          window.location.href = '/login';
        }
      }
    }
    
    // 401 dışındaki hatalar için bildirim göster
    if (error.response?.data?.message) {
      toast.error(error.response.data.message);
    } else {
      toast.error('Bir hata oluştu.');
    }
    
    return Promise.reject(error);
  }
);

// Uzman müsaitlik endpoint'leri
export const expertAvailabilityApi = {
  // Uzmanın müsaitlik bilgilerini getir
  getAvailability: () => api.get('/experts/availability/me'),
  
  // Belirli bir uzmanın müsaitlik bilgilerini getir (public)
  getExpertAvailability: (expertId) => api.get(`/experts/${expertId}/availability`),
  
  // Yeni müsaitlik ekle
  addAvailability: (data) => api.post('/experts/availability', data),
  
  // Müsaitlik güncelle
  updateAvailability: (availabilityId, data) => api.put(`/experts/availability/${availabilityId}`, data),
  
  // Müsaitlik sil
  deleteAvailability: (availabilityId) => api.delete(`/experts/availability/${availabilityId}`),
  
  // Tekrarlı müsaitlik ayarı oluştur
  createRecurringAvailability: (data) => api.post('/experts/availability/recurring', data),
  
  // Belirli bir tarihe özel müsaitlik ayarı oluştur
  createSpecificDateAvailability: (data) => api.post('/experts/availability/specific-date', data),
  
  // Tarih aralığı için müsaitlik getir
  getAvailabilityByDateRange: (startDate, endDate) =>
    api.get(`/experts/availability/range?startDate=${startDate}&endDate=${endDate}`)
};

// Sessions API endpoints
export const sessionsApi = {
  // Client sessions
  getClientSessions: (filters = {}) => {
    const params = new URLSearchParams();
    if (filters.status && filters.status !== 'all') {
      params.append('status', filters.status);
    }
    if (filters.search) {
      params.append('search', filters.search);
    }
    const queryString = params.toString();
    return api.get(`/sessions/client${queryString ? `?${queryString}` : ''}`);
  },

  // Expert sessions
  getExpertSessions: (filters = {}) => {
    const params = new URLSearchParams();
    if (filters.status && filters.status !== 'all') {
      params.append('status', filters.status);
    }
    if (filters.search) {
      params.append('search', filters.search);
    }
    const queryString = params.toString();
    return api.get(`/sessions/expert${queryString ? `?${queryString}` : ''}`);
  },

  // Get session by ID
  getSessionById: (sessionId) => api.get(`/sessions/${sessionId}`),

  // Update session notes (Expert only)
  updateSessionNotes: (sessionId, notes) => api.put(`/sessions/${sessionId}/notes`, { notes }),

  // Update session status (Expert only)
  updateSessionStatus: (sessionId, status) => api.put(`/sessions/${sessionId}/status`, { status })
};

export default api;
