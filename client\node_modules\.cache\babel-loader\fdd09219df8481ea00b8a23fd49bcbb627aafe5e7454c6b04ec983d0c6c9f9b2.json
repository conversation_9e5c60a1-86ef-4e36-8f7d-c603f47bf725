{"ast": null, "code": "var translations = {\n  about: 'k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n  over: 'több mint',\n  almost: 'majdnem',\n  lessthan: 'k<PERSON>ebb mint'\n};\nvar withoutSuffixes = {\n  xseconds: ' másodperc',\n  halfaminute: 'fél perc',\n  xminutes: ' perc',\n  xhours: ' óra',\n  xdays: ' nap',\n  xweeks: ' hét',\n  xmonths: ' hónap',\n  xyears: ' év'\n};\nvar withSuffixes = {\n  xseconds: {\n    '-1': ' másodperccel ezelőtt',\n    '1': ' másodperc múlva',\n    '0': ' másodperce'\n  },\n  halfaminute: {\n    '-1': 'fél perccel ezelőtt',\n    '1': 'fél perc múlva',\n    '0': 'fél perce'\n  },\n  xminutes: {\n    '-1': ' perccel ezelőtt',\n    '1': ' perc múlva',\n    '0': ' perce'\n  },\n  xhours: {\n    '-1': ' ór<PERSON>val e<PERSON>',\n    '1': ' óra múlva',\n    '0': ' órá<PERSON>'\n  },\n  xdays: {\n    '-1': ' nappal ezelőtt',\n    '1': ' nap múlva',\n    '0': ' napja'\n  },\n  xweeks: {\n    '-1': ' héttel ezelőtt',\n    '1': ' hét múlva',\n    '0': ' hete'\n  },\n  xmonths: {\n    '-1': ' hónappal ezelőtt',\n    '1': ' hónap múlva',\n    '0': ' hónapja'\n  },\n  xyears: {\n    '-1': ' évvel ezelőtt',\n    '1': ' év múlva',\n    '0': ' éve'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var adverb = token.match(/about|over|almost|lessthan/i);\n  var unit = adverb ? token.replace(adverb[0], '') : token;\n  var addSuffix = (options === null || options === void 0 ? void 0 : options.addSuffix) === true;\n  var key = unit.toLowerCase();\n  var comparison = (options === null || options === void 0 ? void 0 : options.comparison) || 0;\n  var translated = addSuffix ? withSuffixes[key][comparison] : withoutSuffixes[key];\n  var result = key === 'halfaminute' ? translated : count + translated;\n  if (adverb) {\n    var adv = adverb[0].toLowerCase();\n    result = translations[adv] + ' ' + result;\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["translations", "about", "over", "almost", "lessthan", "withoutSuffixes", "xseconds", "halfaminute", "xminutes", "xhours", "xdays", "xweeks", "xmonths", "xyears", "withSuffixes", "formatDistance", "token", "count", "options", "adverb", "match", "unit", "replace", "addSuffix", "key", "toLowerCase", "comparison", "translated", "result", "adv"], "sources": ["C:/Projeler/kidgarden/burky_root_web/node_modules/date-fns/esm/locale/hu/_lib/formatDistance/index.js"], "sourcesContent": ["var translations = {\n  about: 'k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n  over: 'több mint',\n  almost: 'majdnem',\n  lessthan: 'k<PERSON>ebb mint'\n};\nvar withoutSuffixes = {\n  xseconds: ' másodperc',\n  halfaminute: 'fél perc',\n  xminutes: ' perc',\n  xhours: ' óra',\n  xdays: ' nap',\n  xweeks: ' hét',\n  xmonths: ' hónap',\n  xyears: ' év'\n};\nvar withSuffixes = {\n  xseconds: {\n    '-1': ' másodperccel ezelőtt',\n    '1': ' másodperc múlva',\n    '0': ' másodperce'\n  },\n  halfaminute: {\n    '-1': 'fél perccel ezelőtt',\n    '1': 'fél perc múlva',\n    '0': 'fél perce'\n  },\n  xminutes: {\n    '-1': ' perccel ezelőtt',\n    '1': ' perc múlva',\n    '0': ' perce'\n  },\n  xhours: {\n    '-1': ' ór<PERSON>val e<PERSON>',\n    '1': ' óra múlva',\n    '0': ' órá<PERSON>'\n  },\n  xdays: {\n    '-1': ' nappal ezelőtt',\n    '1': ' nap múlva',\n    '0': ' napja'\n  },\n  xweeks: {\n    '-1': ' héttel ezelőtt',\n    '1': ' hét múlva',\n    '0': ' hete'\n  },\n  xmonths: {\n    '-1': ' hónappal ezelőtt',\n    '1': ' hónap múlva',\n    '0': ' hónapja'\n  },\n  xyears: {\n    '-1': ' évvel ezelőtt',\n    '1': ' év múlva',\n    '0': ' éve'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var adverb = token.match(/about|over|almost|lessthan/i);\n  var unit = adverb ? token.replace(adverb[0], '') : token;\n  var addSuffix = (options === null || options === void 0 ? void 0 : options.addSuffix) === true;\n  var key = unit.toLowerCase();\n  var comparison = (options === null || options === void 0 ? void 0 : options.comparison) || 0;\n  var translated = addSuffix ? withSuffixes[key][comparison] : withoutSuffixes[key];\n  var result = key === 'halfaminute' ? translated : count + translated;\n  if (adverb) {\n    var adv = adverb[0].toLowerCase();\n    result = translations[adv] + ' ' + result;\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,YAAY,GAAG;EACjBC,KAAK,EAAE,YAAY;EACnBC,IAAI,EAAE,WAAW;EACjBC,MAAM,EAAE,SAAS;EACjBC,QAAQ,EAAE;AACZ,CAAC;AACD,IAAIC,eAAe,GAAG;EACpBC,QAAQ,EAAE,YAAY;EACtBC,WAAW,EAAE,UAAU;EACvBC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,MAAM;EACdC,KAAK,EAAE,MAAM;EACbC,MAAM,EAAE,MAAM;EACdC,OAAO,EAAE,QAAQ;EACjBC,MAAM,EAAE;AACV,CAAC;AACD,IAAIC,YAAY,GAAG;EACjBR,QAAQ,EAAE;IACR,IAAI,EAAE,uBAAuB;IAC7B,GAAG,EAAE,kBAAkB;IACvB,GAAG,EAAE;EACP,CAAC;EACDC,WAAW,EAAE;IACX,IAAI,EAAE,qBAAqB;IAC3B,GAAG,EAAE,gBAAgB;IACrB,GAAG,EAAE;EACP,CAAC;EACDC,QAAQ,EAAE;IACR,IAAI,EAAE,kBAAkB;IACxB,GAAG,EAAE,aAAa;IAClB,GAAG,EAAE;EACP,CAAC;EACDC,MAAM,EAAE;IACN,IAAI,EAAE,iBAAiB;IACvB,GAAG,EAAE,YAAY;IACjB,GAAG,EAAE;EACP,CAAC;EACDC,KAAK,EAAE;IACL,IAAI,EAAE,iBAAiB;IACvB,GAAG,EAAE,YAAY;IACjB,GAAG,EAAE;EACP,CAAC;EACDC,MAAM,EAAE;IACN,IAAI,EAAE,iBAAiB;IACvB,GAAG,EAAE,YAAY;IACjB,GAAG,EAAE;EACP,CAAC;EACDC,OAAO,EAAE;IACP,IAAI,EAAE,mBAAmB;IACzB,GAAG,EAAE,cAAc;IACnB,GAAG,EAAE;EACP,CAAC;EACDC,MAAM,EAAE;IACN,IAAI,EAAE,gBAAgB;IACtB,GAAG,EAAE,WAAW;IAChB,GAAG,EAAE;EACP;AACF,CAAC;AACD,IAAIE,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM,GAAGH,KAAK,CAACI,KAAK,CAAC,6BAA6B,CAAC;EACvD,IAAIC,IAAI,GAAGF,MAAM,GAAGH,KAAK,CAACM,OAAO,CAACH,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGH,KAAK;EACxD,IAAIO,SAAS,GAAG,CAACL,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACK,SAAS,MAAM,IAAI;EAC9F,IAAIC,GAAG,GAAGH,IAAI,CAACI,WAAW,CAAC,CAAC;EAC5B,IAAIC,UAAU,GAAG,CAACR,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACQ,UAAU,KAAK,CAAC;EAC5F,IAAIC,UAAU,GAAGJ,SAAS,GAAGT,YAAY,CAACU,GAAG,CAAC,CAACE,UAAU,CAAC,GAAGrB,eAAe,CAACmB,GAAG,CAAC;EACjF,IAAII,MAAM,GAAGJ,GAAG,KAAK,aAAa,GAAGG,UAAU,GAAGV,KAAK,GAAGU,UAAU;EACpE,IAAIR,MAAM,EAAE;IACV,IAAIU,GAAG,GAAGV,MAAM,CAAC,CAAC,CAAC,CAACM,WAAW,CAAC,CAAC;IACjCG,MAAM,GAAG5B,YAAY,CAAC6B,GAAG,CAAC,GAAG,GAAG,GAAGD,MAAM;EAC3C;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeb,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}