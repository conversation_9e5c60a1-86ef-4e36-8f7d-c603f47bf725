{"ast": null, "code": "// src/streamedQuery.ts\nfunction streamedQuery({\n  queryFn,\n  refetchMode\n}) {\n  return async context => {\n    if (refetchMode !== \"append\") {\n      const query = context.client.getQueryCache().find({\n        queryKey: context.queryKey,\n        exact: true\n      });\n      if (query && query.state.data !== void 0) {\n        query.setState({\n          status: \"pending\",\n          data: void 0,\n          error: null,\n          fetchStatus: \"fetching\"\n        });\n      }\n    }\n    const stream = await queryFn(context);\n    for await (const chunk of stream) {\n      if (context.signal.aborted) {\n        break;\n      }\n      context.client.setQueryData(context.queryKey, (prev = []) => {\n        return prev.concat(chunk);\n      });\n    }\n    return context.client.getQueryData(context.queryKey);\n  };\n}\nexport { streamedQuery };", "map": {"version": 3, "names": ["streamedQuery", "queryFn", "refetchMode", "context", "query", "client", "get<PERSON><PERSON><PERSON><PERSON>ache", "find", "query<PERSON><PERSON>", "exact", "state", "data", "setState", "status", "error", "fetchStatus", "stream", "chunk", "signal", "aborted", "setQueryData", "prev", "concat", "getQueryData"], "sources": ["C:\\burky root\\burky_root_web\\client\\node_modules\\@tanstack\\query-core\\src\\streamedQuery.ts"], "sourcesContent": ["import type { QueryFunction, QueryFunctionContext, QueryKey } from './types'\n\n/**\n * This is a helper function to create a query function that streams data from an AsyncIterable.\n * Data will be an Array of all the chunks received.\n * The query will be in a 'pending' state until the first chunk of data is received, but will go to 'success' after that.\n * The query will stay in fetchStatus 'fetching' until the stream ends.\n * @param queryFn - The function that returns an AsyncIterable to stream data from.\n * @param refetchMode - Defaults to 'reset', which replaces data when a refetch happens. Set to 'append' to append new data to the existing data.\n */\nexport function streamedQuery<\n  TQueryFnData = unknown,\n  TQueryKey extends QueryKey = QueryKey,\n>({\n  queryFn,\n  refetchMode,\n}: {\n  queryFn: (\n    context: QueryFunctionContext<TQueryKey>,\n  ) => AsyncIterable<TQueryFnData> | Promise<AsyncIterable<TQueryFnData>>\n  refetchMode?: 'append' | 'reset'\n}): QueryFunction<Array<TQueryFnData>, TQueryKey> {\n  return async (context) => {\n    if (refetchMode !== 'append') {\n      const query = context.client\n        .getQueryCache()\n        .find({ queryKey: context.queryKey, exact: true })\n      if (query && query.state.data !== undefined) {\n        query.setState({\n          status: 'pending',\n          data: undefined,\n          error: null,\n          fetchStatus: 'fetching',\n        })\n      }\n    }\n    const stream = await queryFn(context)\n    for await (const chunk of stream) {\n      if (context.signal.aborted) {\n        break\n      }\n      context.client.setQueryData<Array<TQueryFnData>>(\n        context.queryKey,\n        (prev = []) => {\n          return prev.concat(chunk)\n        },\n      )\n    }\n    return context.client.getQueryData(context.queryKey)!\n  }\n}\n"], "mappings": ";AAUO,SAASA,cAGd;EACAC,OAAA;EACAC;AACF,GAKkD;EAChD,OAAO,MAAOC,OAAA,IAAY;IACxB,IAAID,WAAA,KAAgB,UAAU;MAC5B,MAAME,KAAA,GAAQD,OAAA,CAAQE,MAAA,CACnBC,aAAA,CAAc,EACdC,IAAA,CAAK;QAAEC,QAAA,EAAUL,OAAA,CAAQK,QAAA;QAAUC,KAAA,EAAO;MAAK,CAAC;MACnD,IAAIL,KAAA,IAASA,KAAA,CAAMM,KAAA,CAAMC,IAAA,KAAS,QAAW;QAC3CP,KAAA,CAAMQ,QAAA,CAAS;UACbC,MAAA,EAAQ;UACRF,IAAA,EAAM;UACNG,KAAA,EAAO;UACPC,WAAA,EAAa;QACf,CAAC;MACH;IACF;IACA,MAAMC,MAAA,GAAS,MAAMf,OAAA,CAAQE,OAAO;IACpC,iBAAiBc,KAAA,IAASD,MAAA,EAAQ;MAChC,IAAIb,OAAA,CAAQe,MAAA,CAAOC,OAAA,EAAS;QAC1B;MACF;MACAhB,OAAA,CAAQE,MAAA,CAAOe,YAAA,CACbjB,OAAA,CAAQK,QAAA,EACR,CAACa,IAAA,GAAO,EAAC,KAAM;QACb,OAAOA,IAAA,CAAKC,MAAA,CAAOL,KAAK;MAC1B,CACF;IACF;IACA,OAAOd,OAAA,CAAQE,MAAA,CAAOkB,YAAA,CAAapB,OAAA,CAAQK,QAAQ;EACrD;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}