{"ast": null, "code": "var _jsxFileName = \"C:\\\\Projeler\\\\kidgarden\\\\burky_root_web\\\\client\\\\src\\\\pages\\\\expert\\\\messages\\\\MessagesPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '../../../hooks/useAuth';\nimport api from '../../../services/api';\nimport { toast } from 'react-hot-toast';\nimport io from 'socket.io-client';\nimport { ChatBubbleLeftEllipsisIcon, MagnifyingGlassIcon, UserIcon, PaperAirplaneIcon, PaperClipIcon, FaceSmileIcon, UserCircleIcon, EllipsisHorizontalIcon, PhoneIcon, VideoCameraIcon, InformationCircleIcon, ClockIcon, CheckCircleIcon, XMarkIcon, TrashIcon, ArchiveBoxIcon, StarIcon } from '@heroicons/react/24/outline';\nimport { format } from 'date-fns';\nimport { tr } from 'date-fns/locale';\nimport { Link } from 'react-router-dom';\n\n/**\n * <PERSON><PERSON> mesaj<PERSON><PERSON> sayfası\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MessagesPage = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [isLoading, setIsLoading] = useState(true);\n  const [conversations, setConversations] = useState([]);\n  const [selectedConversation, setSelectedConversation] = useState(null);\n  const [messages, setMessages] = useState([]);\n  const [messageText, setMessageText] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filter, setFilter] = useState('all'); // all, unread, archived, starred\n  const [socket, setSocket] = useState(null);\n  const [onlineUsers, setOnlineUsers] = useState(new Set());\n  const [typingUsers, setTypingUsers] = useState(new Set());\n  const messagesEndRef = useRef(null);\n  const conversationsRef = useRef(null);\n  const messagesContainerRef = useRef(null);\n\n  // Socket.IO connection - ayrı useEffect\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      console.log('🔌 Socket.IO bağlantısı kuruluyor...');\n      const socketConnection = io(process.env.REACT_APP_API_URL || 'http://localhost:5000', {\n        auth: {\n          token\n        },\n        transports: ['websocket', 'polling'],\n        reconnection: true,\n        reconnectionDelay: 1000,\n        reconnectionAttempts: 5,\n        timeout: 20000\n      });\n      setSocket(socketConnection);\n\n      // Connection events\n      socketConnection.on('connect', () => {\n        console.log('✅ Socket.IO bağlantısı kuruldu:', socketConnection.id);\n        // Kullanıcının online olduğunu bildir\n        socketConnection.emit('user_online');\n      });\n      socketConnection.on('disconnect', reason => {\n        console.log('❌ Socket.IO bağlantısı kesildi:', reason);\n      });\n      socketConnection.on('connect_error', error => {\n        console.error('🔥 Socket.IO bağlantı hatası:', error);\n      });\n      socketConnection.on('reconnect', attemptNumber => {\n        console.log('🔄 Socket.IO yeniden bağlandı, deneme:', attemptNumber);\n      });\n      socketConnection.on('reconnect_error', error => {\n        console.error('🔥 Socket.IO yeniden bağlanma hatası:', error);\n      });\n      return () => {\n        socketConnection.disconnect();\n      };\n    }\n  }, []);\n\n  // Socket event listeners - ayrı useEffect\n  useEffect(() => {\n    if (socket) {\n      console.log('🎧 Socket event listeners kuruluyor...');\n\n      // Socket event listeners\n      socket.on('new_message', message => {\n        console.log('📨 Yeni mesaj alındı:', message);\n\n        // Conversation listesini ÖNCE güncelle (her zaman)\n        setConversations(prev => prev.map(conv => conv.id === message.conversationId ? {\n          ...conv,\n          lastMessage: message.content,\n          timestamp: message.createdAt,\n          unread: message.senderId !== user.id\n        } : conv));\n\n        // Eğer şu anda bu conversation açıksa, mesajı da ekle\n        setSelectedConversation(currentSelected => {\n          if (currentSelected && message.conversationId === currentSelected.id) {\n            setMessages(prev => [...prev, {\n              id: message.id,\n              senderId: message.senderId,\n              senderName: message.sender.name,\n              senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n              text: message.content,\n              timestamp: message.createdAt,\n              read: message.isRead\n            }]);\n\n            // Scroll to bottom\n            setTimeout(() => {\n              var _messagesEndRef$curre;\n              (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n                behavior: 'smooth'\n              });\n            }, 100);\n          }\n          return currentSelected;\n        });\n      });\n      socket.on('message_sent', message => {\n        console.log('✅ Mesaj gönderildi onayı:', message);\n      });\n      socket.on('user_status_change', data => {\n        setOnlineUsers(prev => {\n          const newSet = new Set(prev);\n          if (data.status === 'online') {\n            newSet.add(data.userId);\n          } else {\n            newSet.delete(data.userId);\n          }\n          return newSet;\n        });\n      });\n      socket.on('user_typing', data => {\n        setSelectedConversation(currentSelected => {\n          if (currentSelected && data.conversationId === currentSelected.id) {\n            setTypingUsers(prev => new Set([...prev, data.userId]));\n          }\n          return currentSelected;\n        });\n      });\n      socket.on('user_stopped_typing', data => {\n        setTypingUsers(prev => {\n          const newSet = new Set(prev);\n          newSet.delete(data.userId);\n          return newSet;\n        });\n      });\n    }\n  }, [socket, user.id]);\n\n  // Conversations yükleme - ayrı useEffect\n  useEffect(() => {\n    loadConversations();\n  }, []);\n\n  // Conversations yükleme\n  const loadConversations = async () => {\n    try {\n      setIsLoading(true);\n      const response = await api.get('/messages/conversations');\n\n      // API verisini frontend formatına çevir\n      const formattedConversations = response.data.conversations.map(conversation => {\n        var _conversation$lastMes, _conversation$lastMes2;\n        return {\n          id: conversation.id,\n          clientId: conversation.otherUser.id,\n          clientName: conversation.otherUser.name,\n          lastMessage: ((_conversation$lastMes = conversation.lastMessage) === null || _conversation$lastMes === void 0 ? void 0 : _conversation$lastMes.content) || 'Henüz mesaj yok',\n          timestamp: ((_conversation$lastMes2 = conversation.lastMessage) === null || _conversation$lastMes2 === void 0 ? void 0 : _conversation$lastMes2.timestamp) || conversation.createdAt,\n          unread: conversation.lastMessage ? !conversation.lastMessage.isRead && conversation.lastMessage.senderId !== user.id : false,\n          avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(conversation.otherUser.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n          status: onlineUsers.has(conversation.otherUser.id) ? 'online' : 'offline',\n          starred: false,\n          archived: false\n        };\n      });\n      setConversations(formattedConversations);\n    } catch (error) {\n      console.error('Konuşmalar yüklenirken hata:', error);\n      toast.error('Konuşmalar yüklenemedi');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Mesaj yükleme\n  useEffect(() => {\n    if (selectedConversation) {\n      loadMessages(selectedConversation.id);\n\n      // Socket'e conversation'a katıl\n      if (socket) {\n        console.log('🏠 Konuşmaya katılıyor:', selectedConversation.id);\n        socket.emit('join_conversation', selectedConversation.id);\n      }\n    }\n  }, [selectedConversation, socket]);\n\n  // Messages yükleme\n  const loadMessages = async conversationId => {\n    try {\n      const response = await api.get(`/messages/conversations/${conversationId}`);\n\n      // API verisini frontend formatına çevir\n      const formattedMessages = response.data.messages.map(message => ({\n        id: message.id,\n        senderId: message.senderId,\n        senderName: message.sender.name,\n        senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        text: message.content,\n        timestamp: message.createdAt,\n        read: message.isRead,\n        attachments: message.attachments\n      }));\n      setMessages(formattedMessages);\n\n      // Scroll to bottom\n      setTimeout(() => {\n        var _messagesEndRef$curre2;\n        (_messagesEndRef$curre2 = messagesEndRef.current) === null || _messagesEndRef$curre2 === void 0 ? void 0 : _messagesEndRef$curre2.scrollIntoView({\n          behavior: 'smooth'\n        });\n      }, 100);\n    } catch (error) {\n      console.error('Mesajlar yüklenirken hata:', error);\n      toast.error('Mesajlar yüklenemedi');\n    }\n  };\n  // Mesaj gönderme\n  const sendMessage = async () => {\n    if (!messageText.trim() || !selectedConversation) return;\n    console.log('📤 Mesaj gönderiliyor:', {\n      receiverId: selectedConversation.clientId,\n      content: messageText.trim()\n    });\n    try {\n      const response = await api.post('/messages/send', {\n        receiverId: selectedConversation.clientId,\n        content: messageText.trim()\n      });\n      console.log('✅ Mesaj gönderildi:', response.data);\n\n      // Mesajı local state'e ekle\n      const newMessage = {\n        id: response.data.message.id,\n        senderId: user.id,\n        senderName: `${user.firstName} ${user.lastName}`,\n        senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(user.firstName)}+${encodeURIComponent(user.lastName)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        text: messageText.trim(),\n        timestamp: response.data.message.createdAt,\n        read: false\n      };\n      setMessages(prev => [...prev, newMessage]);\n      setMessageText('');\n\n      // Conversation listesini güncelle\n      setConversations(prev => prev.map(conv => conv.id === selectedConversation.id ? {\n        ...conv,\n        lastMessage: messageText.trim(),\n        timestamp: newMessage.timestamp\n      } : conv));\n\n      // Scroll to bottom\n      setTimeout(() => {\n        var _messagesEndRef$curre3;\n        (_messagesEndRef$curre3 = messagesEndRef.current) === null || _messagesEndRef$curre3 === void 0 ? void 0 : _messagesEndRef$curre3.scrollIntoView({\n          behavior: 'smooth'\n        });\n      }, 100);\n    } catch (error) {\n      console.error('Mesaj gönderilirken hata:', error);\n      toast.error('Mesaj gönderilemedi');\n    }\n  };\n  useEffect(() => {\n    // Mesajları otomatik kaydır\n    if (messagesEndRef.current && messagesContainerRef.current) {\n      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;\n    }\n  }, [messages]);\n\n  // Mesaj gönderme\n  const handleSendMessage = e => {\n    e.preventDefault();\n    sendMessage();\n  };\n\n  // Konuşma seçme\n  const handleSelectConversation = conversation => {\n    setSelectedConversation(conversation);\n  };\n\n  // Tarih formatı\n  const formatMessageDate = dateString => {\n    const date = new Date(dateString);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    if (date.toDateString() === today.toDateString()) {\n      return format(date, 'HH:mm', {\n        locale: tr\n      });\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Dün ' + format(date, 'HH:mm', {\n        locale: tr\n      });\n    } else {\n      return format(date, 'dd MMM HH:mm', {\n        locale: tr\n      });\n    }\n  };\n\n  // Konuşmaları filtrele\n  const filteredConversations = conversations.filter(conv => {\n    // Arama filtresi\n    const matchesSearch = conv.clientName.toLowerCase().includes(searchTerm.toLowerCase()) || conv.lastMessage.toLowerCase().includes(searchTerm.toLowerCase());\n\n    // Durum filtresi\n    const matchesFilter = filter === 'all' || filter === 'unread' && conv.unread || filter === 'archived' && conv.archived || filter === 'starred' && conv.starred;\n    return matchesSearch && matchesFilter;\n  });\n\n  // Konuşmayı yıldızla\n  const toggleStar = id => {\n    setConversations(prevConversations => prevConversations.map(conv => conv.id === id ? {\n      ...conv,\n      starred: !conv.starred\n    } : conv));\n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({\n        ...prev,\n        starred: !prev.starred\n      }));\n    }\n  };\n\n  // Konuşmayı arşivle\n  const toggleArchive = id => {\n    setConversations(prevConversations => prevConversations.map(conv => conv.id === id ? {\n      ...conv,\n      archived: !conv.archived\n    } : conv));\n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({\n        ...prev,\n        archived: !prev.archived\n      }));\n    }\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mx-auto px-4 py-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-teal-500 to-teal-700 shadow-lg rounded-lg p-6 mb-6 text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold\",\n            children: \"Mesajlar\\u0131m\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-1 text-indigo-100\",\n            children: \"Dan\\u0131\\u015Fanlar\\u0131n\\u0131zla olan t\\xFCm yaz\\u0131\\u015Fmalar\\u0131n\\u0131z\\u0131 buradan y\\xF6netebilirsiniz.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-3 sm:mt-0 flex space-x-2\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-indigo-800 bg-white hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-300\",\n            children: [/*#__PURE__*/_jsxDEV(ChatBubbleLeftEllipsisIcon, {\n              className: \"-ml-1 mr-2 h-5 w-5\",\n              \"aria-hidden\": \"true\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 15\n            }, this), \"Yeni Mesaj\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 376,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-md overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-12 h-[75vh]\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-span-12 md:col-span-4 border-r border-gray-200 flex flex-col\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 border-b border-gray-200 bg-white\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-semibold text-gray-800 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(ChatBubbleLeftEllipsisIcon, {\n                className: \"h-6 w-6 text-indigo-600 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 17\n              }, this), \"Mesajlar\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3 relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Konu\\u015Fmalarda ara...\",\n                className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                className: \"h-5 w-5 text-gray-400 absolute left-3 top-2.5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3 flex space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: `px-3 py-1 text-sm rounded-full ${filter === 'all' ? 'bg-indigo-100 text-indigo-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                onClick: () => setFilter('all'),\n                children: \"T\\xFCm\\xFC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `px-3 py-1 text-sm rounded-full ${filter === 'unread' ? 'bg-indigo-100 text-indigo-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                onClick: () => setFilter('unread'),\n                children: \"Okunmam\\u0131\\u015F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `px-3 py-1 text-sm rounded-full ${filter === 'starred' ? 'bg-indigo-100 text-indigo-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                onClick: () => setFilter('starred'),\n                children: \"Y\\u0131ld\\u0131zl\\u0131\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `px-3 py-1 text-sm rounded-full ${filter === 'archived' ? 'bg-indigo-100 text-indigo-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                onClick: () => setFilter('archived'),\n                children: \"Ar\\u015Fiv\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: conversationsRef,\n            style: {\n              height: 'calc(75vh - 145px)',\n              overflowY: 'auto',\n              scrollbarWidth: 'thin',\n              scrollbarColor: '#D1D5DB #F3F4F6'\n            },\n            children: filteredConversations.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 text-center text-gray-500\",\n              children: \"Hi\\xE7 mesaj\\u0131n\\u0131z yok\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 17\n            }, this) : filteredConversations.map(conversation => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `p-3 cursor-pointer hover:bg-gray-50 transition-colors duration-200 ${(selectedConversation === null || selectedConversation === void 0 ? void 0 : selectedConversation.id) === conversation.id ? 'bg-indigo-50' : ''} ${conversation.unread ? 'bg-indigo-50 hover:bg-indigo-100' : ''}`,\n              onClick: () => handleSelectConversation(conversation),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative flex-shrink-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: conversation.avatar,\n                    alt: conversation.clientName,\n                    className: `h-10 w-10 rounded-full ${(selectedConversation === null || selectedConversation === void 0 ? void 0 : selectedConversation.id) === conversation.id ? 'ring-2 ring-indigo-600' : ''}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 472,\n                    columnNumber: 25\n                  }, this), conversation.status === 'online' && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 482,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 471,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: `text-sm font-medium ${conversation.unread ? 'text-gray-900' : 'text-gray-700'}`,\n                      children: conversation.clientName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 487,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-1\",\n                      children: [conversation.starred && /*#__PURE__*/_jsxDEV(StarIcon, {\n                        className: \"h-4 w-4 text-yellow-400 fill-current\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 492,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-gray-500\",\n                        children: formatMessageDate(conversation.timestamp)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 494,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 490,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 486,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: `text-sm truncate mt-1 ${conversation.unread ? 'text-gray-900 font-medium' : 'text-gray-500'}`,\n                    children: conversation.lastMessage\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 499,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center mt-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-gray-500\",\n                      children: conversation.status === 'online' ? 'Çevrimiçi' : conversation.lastSeen ? `Son görülme: ${conversation.lastSeen}` : ''\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 505,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex space-x-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: e => {\n                          e.stopPropagation();\n                          toggleStar(conversation.id);\n                        },\n                        className: \"text-gray-400 hover:text-yellow-400\",\n                        children: /*#__PURE__*/_jsxDEV(StarIcon, {\n                          className: `h-4 w-4 ${conversation.starred ? 'text-yellow-400 fill-current' : ''}`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 520,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 513,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: e => {\n                          e.stopPropagation();\n                          toggleArchive(conversation.id);\n                        },\n                        className: \"text-gray-400 hover:text-gray-600\",\n                        children: /*#__PURE__*/_jsxDEV(ArchiveBoxIcon, {\n                          className: \"h-4 w-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 529,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 522,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 512,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 504,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 21\n              }, this), conversation.unread && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"inline-flex items-center ml-12 mt-1 px-2 py-0.5 rounded-full text-xs font-medium bg-indigo-600 text-white\",\n                children: \"Yeni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 23\n              }, this)]\n            }, conversation.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-span-12 md:col-span-8 flex flex-col\",\n          children: selectedConversation ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 border-b border-gray-200 bg-white flex justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative mr-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: selectedConversation.avatar,\n                    alt: selectedConversation.clientName,\n                    className: \"h-10 w-10 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 554,\n                    columnNumber: 23\n                  }, this), selectedConversation.status === 'online' && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 560,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-lg font-medium text-gray-800\",\n                    children: selectedConversation.clientName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 564,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500\",\n                    children: selectedConversation.status === 'online' ? 'Çevrimiçi' : selectedConversation.lastSeen ? `Son görülme: ${selectedConversation.lastSeen}` : ''\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 567,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 563,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                  children: /*#__PURE__*/_jsxDEV(PhoneIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 578,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 577,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                  children: /*#__PURE__*/_jsxDEV(VideoCameraIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 581,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                  children: /*#__PURE__*/_jsxDEV(InformationCircleIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 584,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 583,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                  children: /*#__PURE__*/_jsxDEV(EllipsisHorizontalIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 587,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 586,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: messagesContainerRef,\n              className: \"p-4 bg-gray-50\",\n              style: {\n                height: 'calc(75vh - 195px)',\n                overflowY: 'auto',\n                scrollbarWidth: 'thin',\n                scrollbarColor: '#D1D5DB #F3F4F6'\n              },\n              children: [messages.map((message, index) => {\n                const isSender = message.senderId === user.id;\n                const showAvatar = index === 0 || messages[index - 1].senderId !== message.senderId;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `flex ${isSender ? 'justify-end' : 'justify-start'} mb-4`,\n                  children: [!isSender && showAvatar && /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: message.senderAvatar,\n                    alt: message.senderName,\n                    className: \"h-8 w-8 rounded-full mr-2 mt-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 610,\n                    columnNumber: 27\n                  }, this), !isSender && !showAvatar && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 616,\n                    columnNumber: 54\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `max-w-xs md:max-w-md lg:max-w-lg rounded-lg px-4 py-2 ${isSender ? 'bg-indigo-600 text-white rounded-br-none' : 'bg-white text-gray-800 rounded-bl-none border border-gray-200'}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm\",\n                      children: message.text\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 624,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `text-xs mt-1 ${isSender ? 'text-indigo-200' : 'text-gray-500'} flex items-center justify-end`,\n                      children: [formatMessageDate(message.timestamp), isSender && /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                        className: \"h-3 w-3 ml-1\",\n                        title: message.read ? 'Okundu' : 'İletildi'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 628,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 625,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 617,\n                    columnNumber: 25\n                  }, this), isSender && showAvatar && /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: message.senderAvatar,\n                    alt: message.senderName,\n                    className: \"h-8 w-8 rounded-full ml-2 mt-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 633,\n                    columnNumber: 27\n                  }, this), isSender && !showAvatar && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 ml-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 639,\n                    columnNumber: 53\n                  }, this)]\n                }, message.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 608,\n                  columnNumber: 23\n                }, this);\n              }), /*#__PURE__*/_jsxDEV(\"div\", {\n                ref: messagesEndRef\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 643,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 593,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 border-t border-gray-200 bg-white\",\n              children: /*#__PURE__*/_jsxDEV(\"form\", {\n                onSubmit: handleSendMessage,\n                className: \"flex items-end\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\",\n                  children: /*#__PURE__*/_jsxDEV(PaperClipIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 653,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 649,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    className: \"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none\",\n                    placeholder: \"Mesaj\\u0131n\\u0131z\\u0131 yaz\\u0131n...\",\n                    rows: \"2\",\n                    value: messageText,\n                    onChange: e => setMessageText(e.target.value)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 656,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 655,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\",\n                  children: /*#__PURE__*/_jsxDEV(FaceSmileIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 668,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 664,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"submit\",\n                  disabled: !messageText.trim(),\n                  className: `ml-2 p-2 rounded-full ${messageText.trim() ? 'bg-indigo-600 text-white hover:bg-indigo-700' : 'bg-gray-200 text-gray-400 cursor-not-allowed'} focus:outline-none`,\n                  children: /*#__PURE__*/_jsxDEV(PaperAirplaneIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 679,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 670,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 648,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 647,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) :\n          /*#__PURE__*/\n          // Mesaj seçilmediğinde\n          _jsxDEV(\"div\", {\n            className: \"flex-1 flex flex-col items-center justify-center p-6 bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full max-w-md text-center\",\n              children: [/*#__PURE__*/_jsxDEV(ChatBubbleLeftEllipsisIcon, {\n                className: \"h-16 w-16 text-gray-300 mb-4 mx-auto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 688,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-medium text-gray-800 mb-2\",\n                children: \"Mesajlar\\u0131n\\u0131z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 689,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-500 mx-auto\",\n                children: \"Mesajla\\u015Fmaya ba\\u015Flamak i\\xE7in sol taraftan bir konu\\u015Fma se\\xE7in veya yeni bir mesaj ba\\u015Flat\\u0131n.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 690,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 687,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 686,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 547,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 394,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 374,\n    columnNumber: 5\n  }, this);\n};\n_s(MessagesPage, \"/HIBSL47wEHSC+087hW9D9bi+v8=\", false, function () {\n  return [useAuth];\n});\n_c = MessagesPage;\nexport default MessagesPage;\nvar _c;\n$RefreshReg$(_c, \"MessagesPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useAuth", "api", "toast", "io", "ChatBubbleLeftEllipsisIcon", "MagnifyingGlassIcon", "UserIcon", "PaperAirplaneIcon", "PaperClipIcon", "FaceSmileIcon", "UserCircleIcon", "EllipsisHorizontalIcon", "PhoneIcon", "VideoCameraIcon", "InformationCircleIcon", "ClockIcon", "CheckCircleIcon", "XMarkIcon", "TrashIcon", "ArchiveBoxIcon", "StarIcon", "format", "tr", "Link", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MessagesPage", "_s", "user", "isLoading", "setIsLoading", "conversations", "setConversations", "selectedConversation", "setSelectedConversation", "messages", "setMessages", "messageText", "setMessageText", "searchTerm", "setSearchTerm", "filter", "setFilter", "socket", "setSocket", "onlineUsers", "setOnlineUsers", "Set", "typingUsers", "setTypingUsers", "messagesEndRef", "conversationsRef", "messagesContainerRef", "token", "localStorage", "getItem", "console", "log", "socketConnection", "process", "env", "REACT_APP_API_URL", "auth", "transports", "reconnection", "reconnectionDelay", "reconnectionAttempts", "timeout", "on", "id", "emit", "reason", "error", "attemptNumber", "disconnect", "message", "prev", "map", "conv", "conversationId", "lastMessage", "content", "timestamp", "createdAt", "unread", "senderId", "currentSelected", "sender<PERSON>ame", "sender", "name", "senderAvatar", "encodeURIComponent", "text", "read", "isRead", "setTimeout", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "data", "newSet", "status", "add", "userId", "delete", "loadConversations", "response", "get", "formattedConversations", "conversation", "_conversation$lastMes", "_conversation$lastMes2", "clientId", "otherUser", "clientName", "avatar", "has", "starred", "archived", "loadMessages", "formattedMessages", "attachments", "_messagesEndRef$curre2", "sendMessage", "trim", "receiverId", "post", "newMessage", "firstName", "lastName", "_messagesEndRef$curre3", "scrollTop", "scrollHeight", "handleSendMessage", "e", "preventDefault", "handleSelectConversation", "formatMessageDate", "dateString", "date", "Date", "today", "yesterday", "setDate", "getDate", "toDateString", "locale", "filteredConversations", "matchesSearch", "toLowerCase", "includes", "matchesFilter", "toggleStar", "prevConversations", "toggleArchive", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "value", "onChange", "target", "onClick", "ref", "style", "height", "overflowY", "scrollbarWidth", "scrollbarColor", "length", "src", "alt", "lastSeen", "stopPropagation", "index", "isSender", "showAvatar", "title", "onSubmit", "rows", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Projeler/kidgarden/burky_root_web/client/src/pages/expert/messages/MessagesPage.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '../../../hooks/useAuth';\nimport api from '../../../services/api';\nimport { toast } from 'react-hot-toast';\nimport io from 'socket.io-client';\nimport {\n  ChatBubbleLeftEllipsisIcon,\n  MagnifyingGlassIcon,\n  UserIcon,\n  PaperAirplaneIcon,\n  PaperClipIcon,\n  FaceSmileIcon,\n  UserCircleIcon,\n  EllipsisHorizontalIcon,\n  PhoneIcon,\n  VideoCameraIcon,\n  InformationCircleIcon,\n  ClockIcon,\n  CheckCircleIcon,\n  XMarkIcon,\n  TrashIcon,\n  ArchiveBoxIcon,\n  StarIcon\n} from '@heroicons/react/24/outline';\nimport { format } from 'date-fns';\nimport { tr } from 'date-fns/locale';\nimport { Link } from 'react-router-dom';\n\n/**\n * <PERSON><PERSON> mesajlaşma sayfası\n */\nconst MessagesPage = () => {\n  const { user } = useAuth();\n  const [isLoading, setIsLoading] = useState(true);\n  const [conversations, setConversations] = useState([]);\n  const [selectedConversation, setSelectedConversation] = useState(null);\n  const [messages, setMessages] = useState([]);\n  const [messageText, setMessageText] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filter, setFilter] = useState('all'); // all, unread, archived, starred\n  const [socket, setSocket] = useState(null);\n  const [onlineUsers, setOnlineUsers] = useState(new Set());\n  const [typingUsers, setTypingUsers] = useState(new Set());\n  const messagesEndRef = useRef(null);\n  const conversationsRef = useRef(null);\n  const messagesContainerRef = useRef(null);\n\n  // Socket.IO connection - ayrı useEffect\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      console.log('🔌 Socket.IO bağlantısı kuruluyor...');\n      const socketConnection = io(process.env.REACT_APP_API_URL || 'http://localhost:5000', {\n        auth: { token },\n        transports: ['websocket', 'polling'],\n        reconnection: true,\n        reconnectionDelay: 1000,\n        reconnectionAttempts: 5,\n        timeout: 20000\n      });\n\n      setSocket(socketConnection);\n\n      // Connection events\n      socketConnection.on('connect', () => {\n        console.log('✅ Socket.IO bağlantısı kuruldu:', socketConnection.id);\n        // Kullanıcının online olduğunu bildir\n        socketConnection.emit('user_online');\n      });\n\n      socketConnection.on('disconnect', (reason) => {\n        console.log('❌ Socket.IO bağlantısı kesildi:', reason);\n      });\n\n      socketConnection.on('connect_error', (error) => {\n        console.error('🔥 Socket.IO bağlantı hatası:', error);\n      });\n\n      socketConnection.on('reconnect', (attemptNumber) => {\n        console.log('🔄 Socket.IO yeniden bağlandı, deneme:', attemptNumber);\n      });\n\n      socketConnection.on('reconnect_error', (error) => {\n        console.error('🔥 Socket.IO yeniden bağlanma hatası:', error);\n      });\n\n      return () => {\n        socketConnection.disconnect();\n      };\n    }\n  }, []);\n\n  // Socket event listeners - ayrı useEffect\n  useEffect(() => {\n    if (socket) {\n      console.log('🎧 Socket event listeners kuruluyor...');\n\n      // Socket event listeners\n      socket.on('new_message', (message) => {\n        console.log('📨 Yeni mesaj alındı:', message);\n\n        // Conversation listesini ÖNCE güncelle (her zaman)\n        setConversations(prev => prev.map(conv =>\n          conv.id === message.conversationId\n            ? { ...conv, lastMessage: message.content, timestamp: message.createdAt, unread: message.senderId !== user.id }\n            : conv\n        ));\n\n        // Eğer şu anda bu conversation açıksa, mesajı da ekle\n        setSelectedConversation(currentSelected => {\n          if (currentSelected && message.conversationId === currentSelected.id) {\n            setMessages(prev => [...prev, {\n              id: message.id,\n              senderId: message.senderId,\n              senderName: message.sender.name,\n              senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n              text: message.content,\n              timestamp: message.createdAt,\n              read: message.isRead\n            }]);\n\n            // Scroll to bottom\n            setTimeout(() => {\n              messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n            }, 100);\n          }\n          return currentSelected;\n        });\n      });\n\n      socket.on('message_sent', (message) => {\n        console.log('✅ Mesaj gönderildi onayı:', message);\n      });\n\n      socket.on('user_status_change', (data) => {\n        setOnlineUsers(prev => {\n          const newSet = new Set(prev);\n          if (data.status === 'online') {\n            newSet.add(data.userId);\n          } else {\n            newSet.delete(data.userId);\n          }\n          return newSet;\n        });\n      });\n\n      socket.on('user_typing', (data) => {\n        setSelectedConversation(currentSelected => {\n          if (currentSelected && data.conversationId === currentSelected.id) {\n            setTypingUsers(prev => new Set([...prev, data.userId]));\n          }\n          return currentSelected;\n        });\n      });\n\n      socket.on('user_stopped_typing', (data) => {\n        setTypingUsers(prev => {\n          const newSet = new Set(prev);\n          newSet.delete(data.userId);\n          return newSet;\n        });\n      });\n    }\n  }, [socket, user.id]);\n\n  // Conversations yükleme - ayrı useEffect\n  useEffect(() => {\n    loadConversations();\n  }, []);\n\n  // Conversations yükleme\n  const loadConversations = async () => {\n    try {\n      setIsLoading(true);\n      const response = await api.get('/messages/conversations');\n\n      // API verisini frontend formatına çevir\n      const formattedConversations = response.data.conversations.map(conversation => ({\n        id: conversation.id,\n        clientId: conversation.otherUser.id,\n        clientName: conversation.otherUser.name,\n        lastMessage: conversation.lastMessage?.content || 'Henüz mesaj yok',\n        timestamp: conversation.lastMessage?.timestamp || conversation.createdAt,\n        unread: conversation.lastMessage ? !conversation.lastMessage.isRead && conversation.lastMessage.senderId !== user.id : false,\n        avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(conversation.otherUser.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        status: onlineUsers.has(conversation.otherUser.id) ? 'online' : 'offline',\n        starred: false,\n        archived: false\n      }));\n\n      setConversations(formattedConversations);\n    } catch (error) {\n      console.error('Konuşmalar yüklenirken hata:', error);\n      toast.error('Konuşmalar yüklenemedi');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Mesaj yükleme\n  useEffect(() => {\n    if (selectedConversation) {\n      loadMessages(selectedConversation.id);\n\n      // Socket'e conversation'a katıl\n      if (socket) {\n        console.log('🏠 Konuşmaya katılıyor:', selectedConversation.id);\n        socket.emit('join_conversation', selectedConversation.id);\n      }\n    }\n  }, [selectedConversation, socket]);\n\n  // Messages yükleme\n  const loadMessages = async (conversationId) => {\n    try {\n      const response = await api.get(`/messages/conversations/${conversationId}`);\n\n      // API verisini frontend formatına çevir\n      const formattedMessages = response.data.messages.map(message => ({\n        id: message.id,\n        senderId: message.senderId,\n        senderName: message.sender.name,\n        senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        text: message.content,\n        timestamp: message.createdAt,\n        read: message.isRead,\n        attachments: message.attachments\n      }));\n\n      setMessages(formattedMessages);\n\n      // Scroll to bottom\n      setTimeout(() => {\n        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n      }, 100);\n    } catch (error) {\n      console.error('Mesajlar yüklenirken hata:', error);\n      toast.error('Mesajlar yüklenemedi');\n    }\n  };\n  // Mesaj gönderme\n  const sendMessage = async () => {\n    if (!messageText.trim() || !selectedConversation) return;\n\n    console.log('📤 Mesaj gönderiliyor:', {\n      receiverId: selectedConversation.clientId,\n      content: messageText.trim()\n    });\n\n    try {\n      const response = await api.post('/messages/send', {\n        receiverId: selectedConversation.clientId,\n        content: messageText.trim()\n      });\n\n      console.log('✅ Mesaj gönderildi:', response.data);\n\n      // Mesajı local state'e ekle\n      const newMessage = {\n        id: response.data.message.id,\n        senderId: user.id,\n        senderName: `${user.firstName} ${user.lastName}`,\n        senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(user.firstName)}+${encodeURIComponent(user.lastName)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        text: messageText.trim(),\n        timestamp: response.data.message.createdAt,\n        read: false\n      };\n\n      setMessages(prev => [...prev, newMessage]);\n      setMessageText('');\n\n      // Conversation listesini güncelle\n      setConversations(prev => prev.map(conv =>\n        conv.id === selectedConversation.id\n          ? { ...conv, lastMessage: messageText.trim(), timestamp: newMessage.timestamp }\n          : conv\n      ));\n\n      // Scroll to bottom\n      setTimeout(() => {\n        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n      }, 100);\n\n    } catch (error) {\n      console.error('Mesaj gönderilirken hata:', error);\n      toast.error('Mesaj gönderilemedi');\n    }\n  };\n\n  useEffect(() => {\n    // Mesajları otomatik kaydır\n    if (messagesEndRef.current && messagesContainerRef.current) {\n      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;\n    }\n  }, [messages]);\n\n  // Mesaj gönderme\n  const handleSendMessage = (e) => {\n    e.preventDefault();\n    sendMessage();\n  };\n\n  // Konuşma seçme\n  const handleSelectConversation = (conversation) => {\n    setSelectedConversation(conversation);\n  };\n\n  // Tarih formatı\n  const formatMessageDate = (dateString) => {\n    const date = new Date(dateString);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    \n    if (date.toDateString() === today.toDateString()) {\n      return format(date, 'HH:mm', { locale: tr });\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Dün ' + format(date, 'HH:mm', { locale: tr });\n    } else {\n      return format(date, 'dd MMM HH:mm', { locale: tr });\n    }\n  };\n\n  // Konuşmaları filtrele\n  const filteredConversations = conversations.filter(conv => {\n    // Arama filtresi\n    const matchesSearch = conv.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         conv.lastMessage.toLowerCase().includes(searchTerm.toLowerCase());\n    \n    // Durum filtresi\n    const matchesFilter = filter === 'all' ||\n                         (filter === 'unread' && conv.unread) ||\n                         (filter === 'archived' && conv.archived) ||\n                         (filter === 'starred' && conv.starred);\n                        \n    return matchesSearch && matchesFilter;\n  });\n\n  // Konuşmayı yıldızla\n  const toggleStar = (id) => {\n    setConversations(prevConversations =>\n      prevConversations.map(conv =>\n        conv.id === id ? { ...conv, starred: !conv.starred } : conv\n      )\n    );\n    \n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({ ...prev, starred: !prev.starred }));\n    }\n  };\n\n  // Konuşmayı arşivle\n  const toggleArchive = (id) => {\n    setConversations(prevConversations =>\n      prevConversations.map(conv =>\n        conv.id === id ? { ...conv, archived: !conv.archived } : conv\n      )\n    );\n    \n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({ ...prev, archived: !prev.archived }));\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto px-4 py-6\">\n      {/* Sayfa Başlığı */}\n      <div className=\"bg-gradient-to-r from-teal-500 to-teal-700 shadow-lg rounded-lg p-6 mb-6 text-white\">\n        <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center\">\n          <div>\n            <h1 className=\"text-2xl font-bold\">Mesajlarım</h1>\n            <p className=\"mt-1 text-indigo-100\">\n              Danışanlarınızla olan tüm yazışmalarınızı buradan yönetebilirsiniz.\n            </p>\n          </div>\n          <div className=\"mt-3 sm:mt-0 flex space-x-2\">\n            <button className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-indigo-800 bg-white hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-300\">\n              <ChatBubbleLeftEllipsisIcon className=\"-ml-1 mr-2 h-5 w-5\" aria-hidden=\"true\" />\n              Yeni Mesaj\n            </button>\n          </div>\n        </div>\n      </div>\n      \n      {/* Mesajlaşma arayüzü */}\n      <div className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n        <div className=\"grid grid-cols-12 h-[75vh]\">\n          {/* Sol Kenar - Konuşma Listesi */}\n          <div className=\"col-span-12 md:col-span-4 border-r border-gray-200 flex flex-col\">\n            <div className=\"p-4 border-b border-gray-200 bg-white\">\n              <h1 className=\"text-xl font-semibold text-gray-800 flex items-center\">\n                <ChatBubbleLeftEllipsisIcon className=\"h-6 w-6 text-indigo-600 mr-2\" />\n                Mesajlar\n              </h1>\n              <div className=\"mt-3 relative\">\n                <input\n                  type=\"text\"\n                  placeholder=\"Konuşmalarda ara...\"\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                />\n                <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400 absolute left-3 top-2.5\" />\n              </div>\n              <div className=\"mt-3 flex space-x-2\">\n                <button\n                  className={`px-3 py-1 text-sm rounded-full ${filter === 'all' \n                    ? 'bg-indigo-100 text-indigo-800' \n                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                  onClick={() => setFilter('all')}\n                >\n                  Tümü\n                </button>\n                <button\n                  className={`px-3 py-1 text-sm rounded-full ${filter === 'unread' \n                    ? 'bg-indigo-100 text-indigo-800' \n                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                  onClick={() => setFilter('unread')}\n                >\n                  Okunmamış\n                </button>\n                <button\n                  className={`px-3 py-1 text-sm rounded-full ${filter === 'starred' \n                    ? 'bg-indigo-100 text-indigo-800' \n                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                  onClick={() => setFilter('starred')}\n                >\n                  Yıldızlı\n                </button>\n                <button\n                  className={`px-3 py-1 text-sm rounded-full ${filter === 'archived' \n                    ? 'bg-indigo-100 text-indigo-800' \n                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                  onClick={() => setFilter('archived')}\n                >\n                  Arşiv\n                </button>\n              </div>\n            </div>\n            <div \n              ref={conversationsRef}\n              style={{\n                height: 'calc(75vh - 145px)',\n                overflowY: 'auto',\n                scrollbarWidth: 'thin',\n                scrollbarColor: '#D1D5DB #F3F4F6'\n              }}\n            >\n              {filteredConversations.length === 0 ? (\n                <div className=\"p-4 text-center text-gray-500\">\n                  Hiç mesajınız yok\n                </div>\n              ) : (\n                filteredConversations.map(conversation => (\n                  <div\n                    key={conversation.id}\n                    className={`p-3 cursor-pointer hover:bg-gray-50 transition-colors duration-200 ${\n                      selectedConversation?.id === conversation.id ? 'bg-indigo-50' : ''\n                    } ${conversation.unread ? 'bg-indigo-50 hover:bg-indigo-100' : ''}`}\n                    onClick={() => handleSelectConversation(conversation)}\n                  >\n                    <div className=\"flex items-start space-x-3\">\n                      <div className=\"relative flex-shrink-0\">\n                        <img\n                          src={conversation.avatar}\n                          alt={conversation.clientName}\n                          className={`h-10 w-10 rounded-full ${\n                            selectedConversation?.id === conversation.id \n                              ? 'ring-2 ring-indigo-600' \n                              : ''\n                          }`}\n                        />\n                        {conversation.status === 'online' && (\n                          <span className=\"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"></span>\n                        )}\n                      </div>\n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex justify-between items-start\">\n                          <h3 className={`text-sm font-medium ${conversation.unread ? 'text-gray-900' : 'text-gray-700'}`}>\n                            {conversation.clientName}\n                          </h3>\n                          <div className=\"flex items-center space-x-1\">\n                            {conversation.starred && (\n                              <StarIcon className=\"h-4 w-4 text-yellow-400 fill-current\" />\n                            )}\n                            <span className=\"text-xs text-gray-500\">\n                              {formatMessageDate(conversation.timestamp)}\n                            </span>\n                          </div>\n                        </div>\n                        <p className={`text-sm truncate mt-1 ${\n                          conversation.unread ? 'text-gray-900 font-medium' : 'text-gray-500'\n                        }`}>\n                          {conversation.lastMessage}\n                        </p>\n                        <div className=\"flex justify-between items-center mt-1\">\n                          <span className=\"text-xs text-gray-500\">\n                            {conversation.status === 'online' \n                              ? 'Çevrimiçi' \n                              : conversation.lastSeen \n                                ? `Son görülme: ${conversation.lastSeen}` \n                                : ''}\n                          </span>\n                          <div className=\"flex space-x-1\">\n                            <button \n                              onClick={(e) => {\n                                e.stopPropagation();\n                                toggleStar(conversation.id);\n                              }}\n                              className=\"text-gray-400 hover:text-yellow-400\"\n                            >\n                              <StarIcon className={`h-4 w-4 ${conversation.starred ? 'text-yellow-400 fill-current' : ''}`} />\n                            </button>\n                            <button \n                              onClick={(e) => {\n                                e.stopPropagation();\n                                toggleArchive(conversation.id);\n                              }}\n                              className=\"text-gray-400 hover:text-gray-600\"\n                            >\n                              <ArchiveBoxIcon className=\"h-4 w-4\" />\n                            </button>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {conversation.unread && (\n                      <span className=\"inline-flex items-center ml-12 mt-1 px-2 py-0.5 rounded-full text-xs font-medium bg-indigo-600 text-white\">\n                        Yeni\n                      </span>\n                    )}\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n\n          {/* Sağ Taraf - Mesaj Alanı */}\n          <div className=\"col-span-12 md:col-span-8 flex flex-col\">\n            {selectedConversation ? (\n              <>\n                {/* Mesajlaşma Başlığı */}\n                <div className=\"p-4 border-b border-gray-200 bg-white flex justify-between items-center\">\n                  <div className=\"flex items-center\">\n                    <div className=\"relative mr-3\">\n                      <img\n                        src={selectedConversation.avatar}\n                        alt={selectedConversation.clientName}\n                        className=\"h-10 w-10 rounded-full\"\n                      />\n                      {selectedConversation.status === 'online' && (\n                        <span className=\"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"></span>\n                      )}\n                    </div>\n                    <div>\n                      <h2 className=\"text-lg font-medium text-gray-800\">\n                        {selectedConversation.clientName}\n                      </h2>\n                      <p className=\"text-xs text-gray-500\">\n                        {selectedConversation.status === 'online' \n                          ? 'Çevrimiçi' \n                          : selectedConversation.lastSeen \n                            ? `Son görülme: ${selectedConversation.lastSeen}` \n                            : ''}\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <button className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\">\n                      <PhoneIcon className=\"h-5 w-5\" />\n                    </button>\n                    <button className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\">\n                      <VideoCameraIcon className=\"h-5 w-5\" />\n                    </button>\n                    <button className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\">\n                      <InformationCircleIcon className=\"h-5 w-5\" />\n                    </button>\n                    <button className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\">\n                      <EllipsisHorizontalIcon className=\"h-5 w-5\" />\n                    </button>\n                  </div>\n                </div>\n\n                {/* Mesaj Alanı */}\n                <div \n                  ref={messagesContainerRef}\n                  className=\"p-4 bg-gray-50\"\n                  style={{\n                    height: 'calc(75vh - 195px)',\n                    overflowY: 'auto',\n                    scrollbarWidth: 'thin',\n                    scrollbarColor: '#D1D5DB #F3F4F6'\n                  }}\n                >\n                  {messages.map((message, index) => {\n                    const isSender = message.senderId === user.id;\n                    const showAvatar = index === 0 || messages[index - 1].senderId !== message.senderId;\n                    \n                    return (\n                      <div key={message.id} className={`flex ${isSender ? 'justify-end' : 'justify-start'} mb-4`}>\n                        {!isSender && showAvatar && (\n                          <img \n                            src={message.senderAvatar} \n                            alt={message.senderName}\n                            className=\"h-8 w-8 rounded-full mr-2 mt-1\"\n                          />\n                        )}\n                        {!isSender && !showAvatar && <div className=\"w-8 mr-2\"></div>}\n                        <div \n                          className={`max-w-xs md:max-w-md lg:max-w-lg rounded-lg px-4 py-2 ${\n                            isSender \n                              ? 'bg-indigo-600 text-white rounded-br-none' \n                              : 'bg-white text-gray-800 rounded-bl-none border border-gray-200'\n                          }`}\n                        >\n                          <p className=\"text-sm\">{message.text}</p>\n                          <div className={`text-xs mt-1 ${isSender ? 'text-indigo-200' : 'text-gray-500'} flex items-center justify-end`}>\n                            {formatMessageDate(message.timestamp)}\n                            {isSender && (\n                              <CheckCircleIcon className=\"h-3 w-3 ml-1\" title={message.read ? 'Okundu' : 'İletildi'} />\n                            )}\n                          </div>\n                        </div>\n                        {isSender && showAvatar && (\n                          <img \n                            src={message.senderAvatar}\n                            alt={message.senderName}\n                            className=\"h-8 w-8 rounded-full ml-2 mt-1\"\n                          />\n                        )}\n                        {isSender && !showAvatar && <div className=\"w-8 ml-2\"></div>}\n                      </div>\n                    );\n                  })}\n                  <div ref={messagesEndRef} />\n                </div>\n\n                {/* Mesaj Giriş Alanı */}\n                <div className=\"p-3 border-t border-gray-200 bg-white\">\n                  <form onSubmit={handleSendMessage} className=\"flex items-end\">\n                    <button\n                      type=\"button\"\n                      className=\"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\"\n                    >\n                      <PaperClipIcon className=\"h-5 w-5\" />\n                    </button>\n                    <div className=\"flex-1 mx-2\">\n                      <textarea\n                        className=\"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none\"\n                        placeholder=\"Mesajınızı yazın...\"\n                        rows=\"2\"\n                        value={messageText}\n                        onChange={(e) => setMessageText(e.target.value)}\n                      ></textarea>\n                    </div>\n                    <button\n                      type=\"button\"\n                      className=\"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\"\n                    >\n                      <FaceSmileIcon className=\"h-5 w-5\" />\n                    </button>\n                    <button\n                      type=\"submit\"\n                      disabled={!messageText.trim()}\n                      className={`ml-2 p-2 rounded-full ${\n                        messageText.trim() \n                          ? 'bg-indigo-600 text-white hover:bg-indigo-700' \n                          : 'bg-gray-200 text-gray-400 cursor-not-allowed'\n                      } focus:outline-none`}\n                    >\n                      <PaperAirplaneIcon className=\"h-5 w-5\" />\n                    </button>\n                  </form>\n                </div>\n              </>\n            ) : (\n              // Mesaj seçilmediğinde\n              <div className=\"flex-1 flex flex-col items-center justify-center p-6 bg-gray-50\">\n                <div className=\"w-full max-w-md text-center\">\n                  <ChatBubbleLeftEllipsisIcon className=\"h-16 w-16 text-gray-300 mb-4 mx-auto\" />\n                  <h3 className=\"text-xl font-medium text-gray-800 mb-2\">Mesajlarınız</h3>\n                  <p className=\"text-gray-500 mx-auto\">\n                    Mesajlaşmaya başlamak için sol taraftan bir konuşma seçin veya yeni bir mesaj başlatın.\n                  </p>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default MessagesPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,GAAG,MAAM,uBAAuB;AACvC,SAASC,KAAK,QAAQ,iBAAiB;AACvC,OAAOC,EAAE,MAAM,kBAAkB;AACjC,SACEC,0BAA0B,EAC1BC,mBAAmB,EACnBC,QAAQ,EACRC,iBAAiB,EACjBC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,sBAAsB,EACtBC,SAAS,EACTC,eAAe,EACfC,qBAAqB,EACrBC,SAAS,EACTC,eAAe,EACfC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,QAAQ,QACH,6BAA6B;AACpC,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,EAAE,QAAQ,iBAAiB;AACpC,SAASC,IAAI,QAAQ,kBAAkB;;AAEvC;AACA;AACA;AAFA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGA,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC;EAAK,CAAC,GAAG9B,OAAO,CAAC,CAAC;EAC1B,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACoC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACsC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAACwC,QAAQ,EAAEC,WAAW,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC0C,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC4C,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC8C,MAAM,EAAEC,SAAS,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACgD,MAAM,EAAEC,SAAS,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAC,IAAIoD,GAAG,CAAC,CAAC,CAAC;EACzD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtD,QAAQ,CAAC,IAAIoD,GAAG,CAAC,CAAC,CAAC;EACzD,MAAMG,cAAc,GAAGrD,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMsD,gBAAgB,GAAGtD,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMuD,oBAAoB,GAAGvD,MAAM,CAAC,IAAI,CAAC;;EAEzC;EACAD,SAAS,CAAC,MAAM;IACd,MAAMyD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACTG,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnD,MAAMC,gBAAgB,GAAGzD,EAAE,CAAC0D,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB,EAAE;QACpFC,IAAI,EAAE;UAAET;QAAM,CAAC;QACfU,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;QACpCC,YAAY,EAAE,IAAI;QAClBC,iBAAiB,EAAE,IAAI;QACvBC,oBAAoB,EAAE,CAAC;QACvBC,OAAO,EAAE;MACX,CAAC,CAAC;MAEFvB,SAAS,CAACc,gBAAgB,CAAC;;MAE3B;MACAA,gBAAgB,CAACU,EAAE,CAAC,SAAS,EAAE,MAAM;QACnCZ,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEC,gBAAgB,CAACW,EAAE,CAAC;QACnE;QACAX,gBAAgB,CAACY,IAAI,CAAC,aAAa,CAAC;MACtC,CAAC,CAAC;MAEFZ,gBAAgB,CAACU,EAAE,CAAC,YAAY,EAAGG,MAAM,IAAK;QAC5Cf,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEc,MAAM,CAAC;MACxD,CAAC,CAAC;MAEFb,gBAAgB,CAACU,EAAE,CAAC,eAAe,EAAGI,KAAK,IAAK;QAC9ChB,OAAO,CAACgB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD,CAAC,CAAC;MAEFd,gBAAgB,CAACU,EAAE,CAAC,WAAW,EAAGK,aAAa,IAAK;QAClDjB,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEgB,aAAa,CAAC;MACtE,CAAC,CAAC;MAEFf,gBAAgB,CAACU,EAAE,CAAC,iBAAiB,EAAGI,KAAK,IAAK;QAChDhB,OAAO,CAACgB,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC/D,CAAC,CAAC;MAEF,OAAO,MAAM;QACXd,gBAAgB,CAACgB,UAAU,CAAC,CAAC;MAC/B,CAAC;IACH;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA9E,SAAS,CAAC,MAAM;IACd,IAAI+C,MAAM,EAAE;MACVa,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;MAErD;MACAd,MAAM,CAACyB,EAAE,CAAC,aAAa,EAAGO,OAAO,IAAK;QACpCnB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEkB,OAAO,CAAC;;QAE7C;QACA3C,gBAAgB,CAAC4C,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IACpCA,IAAI,CAACT,EAAE,KAAKM,OAAO,CAACI,cAAc,GAC9B;UAAE,GAAGD,IAAI;UAAEE,WAAW,EAAEL,OAAO,CAACM,OAAO;UAAEC,SAAS,EAAEP,OAAO,CAACQ,SAAS;UAAEC,MAAM,EAAET,OAAO,CAACU,QAAQ,KAAKzD,IAAI,CAACyC;QAAG,CAAC,GAC7GS,IACN,CAAC,CAAC;;QAEF;QACA5C,uBAAuB,CAACoD,eAAe,IAAI;UACzC,IAAIA,eAAe,IAAIX,OAAO,CAACI,cAAc,KAAKO,eAAe,CAACjB,EAAE,EAAE;YACpEjC,WAAW,CAACwC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;cAC5BP,EAAE,EAAEM,OAAO,CAACN,EAAE;cACdgB,QAAQ,EAAEV,OAAO,CAACU,QAAQ;cAC1BE,UAAU,EAAEZ,OAAO,CAACa,MAAM,CAACC,IAAI;cAC/BC,YAAY,EAAE,oCAAoCC,kBAAkB,CAAChB,OAAO,CAACa,MAAM,CAACC,IAAI,CAAC,qDAAqD;cAC9IG,IAAI,EAAEjB,OAAO,CAACM,OAAO;cACrBC,SAAS,EAAEP,OAAO,CAACQ,SAAS;cAC5BU,IAAI,EAAElB,OAAO,CAACmB;YAChB,CAAC,CAAC,CAAC;;YAEH;YACAC,UAAU,CAAC,MAAM;cAAA,IAAAC,qBAAA;cACf,CAAAA,qBAAA,GAAA9C,cAAc,CAAC+C,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;gBAAEC,QAAQ,EAAE;cAAS,CAAC,CAAC;YAChE,CAAC,EAAE,GAAG,CAAC;UACT;UACA,OAAOb,eAAe;QACxB,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF3C,MAAM,CAACyB,EAAE,CAAC,cAAc,EAAGO,OAAO,IAAK;QACrCnB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEkB,OAAO,CAAC;MACnD,CAAC,CAAC;MAEFhC,MAAM,CAACyB,EAAE,CAAC,oBAAoB,EAAGgC,IAAI,IAAK;QACxCtD,cAAc,CAAC8B,IAAI,IAAI;UACrB,MAAMyB,MAAM,GAAG,IAAItD,GAAG,CAAC6B,IAAI,CAAC;UAC5B,IAAIwB,IAAI,CAACE,MAAM,KAAK,QAAQ,EAAE;YAC5BD,MAAM,CAACE,GAAG,CAACH,IAAI,CAACI,MAAM,CAAC;UACzB,CAAC,MAAM;YACLH,MAAM,CAACI,MAAM,CAACL,IAAI,CAACI,MAAM,CAAC;UAC5B;UACA,OAAOH,MAAM;QACf,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF1D,MAAM,CAACyB,EAAE,CAAC,aAAa,EAAGgC,IAAI,IAAK;QACjClE,uBAAuB,CAACoD,eAAe,IAAI;UACzC,IAAIA,eAAe,IAAIc,IAAI,CAACrB,cAAc,KAAKO,eAAe,CAACjB,EAAE,EAAE;YACjEpB,cAAc,CAAC2B,IAAI,IAAI,IAAI7B,GAAG,CAAC,CAAC,GAAG6B,IAAI,EAAEwB,IAAI,CAACI,MAAM,CAAC,CAAC,CAAC;UACzD;UACA,OAAOlB,eAAe;QACxB,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF3C,MAAM,CAACyB,EAAE,CAAC,qBAAqB,EAAGgC,IAAI,IAAK;QACzCnD,cAAc,CAAC2B,IAAI,IAAI;UACrB,MAAMyB,MAAM,GAAG,IAAItD,GAAG,CAAC6B,IAAI,CAAC;UAC5ByB,MAAM,CAACI,MAAM,CAACL,IAAI,CAACI,MAAM,CAAC;UAC1B,OAAOH,MAAM;QACf,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC1D,MAAM,EAAEf,IAAI,CAACyC,EAAE,CAAC,CAAC;;EAErB;EACAzE,SAAS,CAAC,MAAM;IACd8G,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMA,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF5E,YAAY,CAAC,IAAI,CAAC;MAClB,MAAM6E,QAAQ,GAAG,MAAM5G,GAAG,CAAC6G,GAAG,CAAC,yBAAyB,CAAC;;MAEzD;MACA,MAAMC,sBAAsB,GAAGF,QAAQ,CAACP,IAAI,CAACrE,aAAa,CAAC8C,GAAG,CAACiC,YAAY;QAAA,IAAAC,qBAAA,EAAAC,sBAAA;QAAA,OAAK;UAC9E3C,EAAE,EAAEyC,YAAY,CAACzC,EAAE;UACnB4C,QAAQ,EAAEH,YAAY,CAACI,SAAS,CAAC7C,EAAE;UACnC8C,UAAU,EAAEL,YAAY,CAACI,SAAS,CAACzB,IAAI;UACvCT,WAAW,EAAE,EAAA+B,qBAAA,GAAAD,YAAY,CAAC9B,WAAW,cAAA+B,qBAAA,uBAAxBA,qBAAA,CAA0B9B,OAAO,KAAI,iBAAiB;UACnEC,SAAS,EAAE,EAAA8B,sBAAA,GAAAF,YAAY,CAAC9B,WAAW,cAAAgC,sBAAA,uBAAxBA,sBAAA,CAA0B9B,SAAS,KAAI4B,YAAY,CAAC3B,SAAS;UACxEC,MAAM,EAAE0B,YAAY,CAAC9B,WAAW,GAAG,CAAC8B,YAAY,CAAC9B,WAAW,CAACc,MAAM,IAAIgB,YAAY,CAAC9B,WAAW,CAACK,QAAQ,KAAKzD,IAAI,CAACyC,EAAE,GAAG,KAAK;UAC5H+C,MAAM,EAAE,oCAAoCzB,kBAAkB,CAACmB,YAAY,CAACI,SAAS,CAACzB,IAAI,CAAC,qDAAqD;UAChJa,MAAM,EAAEzD,WAAW,CAACwE,GAAG,CAACP,YAAY,CAACI,SAAS,CAAC7C,EAAE,CAAC,GAAG,QAAQ,GAAG,SAAS;UACzEiD,OAAO,EAAE,KAAK;UACdC,QAAQ,EAAE;QACZ,CAAC;MAAA,CAAC,CAAC;MAEHvF,gBAAgB,CAAC6E,sBAAsB,CAAC;IAC1C,CAAC,CAAC,OAAOrC,KAAK,EAAE;MACdhB,OAAO,CAACgB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDxE,KAAK,CAACwE,KAAK,CAAC,wBAAwB,CAAC;IACvC,CAAC,SAAS;MACR1C,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACAlC,SAAS,CAAC,MAAM;IACd,IAAIqC,oBAAoB,EAAE;MACxBuF,YAAY,CAACvF,oBAAoB,CAACoC,EAAE,CAAC;;MAErC;MACA,IAAI1B,MAAM,EAAE;QACVa,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAExB,oBAAoB,CAACoC,EAAE,CAAC;QAC/D1B,MAAM,CAAC2B,IAAI,CAAC,mBAAmB,EAAErC,oBAAoB,CAACoC,EAAE,CAAC;MAC3D;IACF;EACF,CAAC,EAAE,CAACpC,oBAAoB,EAAEU,MAAM,CAAC,CAAC;;EAElC;EACA,MAAM6E,YAAY,GAAG,MAAOzC,cAAc,IAAK;IAC7C,IAAI;MACF,MAAM4B,QAAQ,GAAG,MAAM5G,GAAG,CAAC6G,GAAG,CAAC,2BAA2B7B,cAAc,EAAE,CAAC;;MAE3E;MACA,MAAM0C,iBAAiB,GAAGd,QAAQ,CAACP,IAAI,CAACjE,QAAQ,CAAC0C,GAAG,CAACF,OAAO,KAAK;QAC/DN,EAAE,EAAEM,OAAO,CAACN,EAAE;QACdgB,QAAQ,EAAEV,OAAO,CAACU,QAAQ;QAC1BE,UAAU,EAAEZ,OAAO,CAACa,MAAM,CAACC,IAAI;QAC/BC,YAAY,EAAE,oCAAoCC,kBAAkB,CAAChB,OAAO,CAACa,MAAM,CAACC,IAAI,CAAC,qDAAqD;QAC9IG,IAAI,EAAEjB,OAAO,CAACM,OAAO;QACrBC,SAAS,EAAEP,OAAO,CAACQ,SAAS;QAC5BU,IAAI,EAAElB,OAAO,CAACmB,MAAM;QACpB4B,WAAW,EAAE/C,OAAO,CAAC+C;MACvB,CAAC,CAAC,CAAC;MAEHtF,WAAW,CAACqF,iBAAiB,CAAC;;MAE9B;MACA1B,UAAU,CAAC,MAAM;QAAA,IAAA4B,sBAAA;QACf,CAAAA,sBAAA,GAAAzE,cAAc,CAAC+C,OAAO,cAAA0B,sBAAA,uBAAtBA,sBAAA,CAAwBzB,cAAc,CAAC;UAAEC,QAAQ,EAAE;QAAS,CAAC,CAAC;MAChE,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACdhB,OAAO,CAACgB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDxE,KAAK,CAACwE,KAAK,CAAC,sBAAsB,CAAC;IACrC;EACF,CAAC;EACD;EACA,MAAMoD,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAACvF,WAAW,CAACwF,IAAI,CAAC,CAAC,IAAI,CAAC5F,oBAAoB,EAAE;IAElDuB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;MACpCqE,UAAU,EAAE7F,oBAAoB,CAACgF,QAAQ;MACzChC,OAAO,EAAE5C,WAAW,CAACwF,IAAI,CAAC;IAC5B,CAAC,CAAC;IAEF,IAAI;MACF,MAAMlB,QAAQ,GAAG,MAAM5G,GAAG,CAACgI,IAAI,CAAC,gBAAgB,EAAE;QAChDD,UAAU,EAAE7F,oBAAoB,CAACgF,QAAQ;QACzChC,OAAO,EAAE5C,WAAW,CAACwF,IAAI,CAAC;MAC5B,CAAC,CAAC;MAEFrE,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEkD,QAAQ,CAACP,IAAI,CAAC;;MAEjD;MACA,MAAM4B,UAAU,GAAG;QACjB3D,EAAE,EAAEsC,QAAQ,CAACP,IAAI,CAACzB,OAAO,CAACN,EAAE;QAC5BgB,QAAQ,EAAEzD,IAAI,CAACyC,EAAE;QACjBkB,UAAU,EAAE,GAAG3D,IAAI,CAACqG,SAAS,IAAIrG,IAAI,CAACsG,QAAQ,EAAE;QAChDxC,YAAY,EAAE,oCAAoCC,kBAAkB,CAAC/D,IAAI,CAACqG,SAAS,CAAC,IAAItC,kBAAkB,CAAC/D,IAAI,CAACsG,QAAQ,CAAC,qDAAqD;QAC9KtC,IAAI,EAAEvD,WAAW,CAACwF,IAAI,CAAC,CAAC;QACxB3C,SAAS,EAAEyB,QAAQ,CAACP,IAAI,CAACzB,OAAO,CAACQ,SAAS;QAC1CU,IAAI,EAAE;MACR,CAAC;MAEDzD,WAAW,CAACwC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEoD,UAAU,CAAC,CAAC;MAC1C1F,cAAc,CAAC,EAAE,CAAC;;MAElB;MACAN,gBAAgB,CAAC4C,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IACpCA,IAAI,CAACT,EAAE,KAAKpC,oBAAoB,CAACoC,EAAE,GAC/B;QAAE,GAAGS,IAAI;QAAEE,WAAW,EAAE3C,WAAW,CAACwF,IAAI,CAAC,CAAC;QAAE3C,SAAS,EAAE8C,UAAU,CAAC9C;MAAU,CAAC,GAC7EJ,IACN,CAAC,CAAC;;MAEF;MACAiB,UAAU,CAAC,MAAM;QAAA,IAAAoC,sBAAA;QACf,CAAAA,sBAAA,GAAAjF,cAAc,CAAC+C,OAAO,cAAAkC,sBAAA,uBAAtBA,sBAAA,CAAwBjC,cAAc,CAAC;UAAEC,QAAQ,EAAE;QAAS,CAAC,CAAC;MAChE,CAAC,EAAE,GAAG,CAAC;IAET,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACdhB,OAAO,CAACgB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDxE,KAAK,CAACwE,KAAK,CAAC,qBAAqB,CAAC;IACpC;EACF,CAAC;EAED5E,SAAS,CAAC,MAAM;IACd;IACA,IAAIsD,cAAc,CAAC+C,OAAO,IAAI7C,oBAAoB,CAAC6C,OAAO,EAAE;MAC1D7C,oBAAoB,CAAC6C,OAAO,CAACmC,SAAS,GAAGhF,oBAAoB,CAAC6C,OAAO,CAACoC,YAAY;IACpF;EACF,CAAC,EAAE,CAAClG,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMmG,iBAAiB,GAAIC,CAAC,IAAK;IAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBZ,WAAW,CAAC,CAAC;EACf,CAAC;;EAED;EACA,MAAMa,wBAAwB,GAAI3B,YAAY,IAAK;IACjD5E,uBAAuB,CAAC4E,YAAY,CAAC;EACvC,CAAC;;EAED;EACA,MAAM4B,iBAAiB,GAAIC,UAAU,IAAK;IACxC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,KAAK,GAAG,IAAID,IAAI,CAAC,CAAC;IACxB,MAAME,SAAS,GAAG,IAAIF,IAAI,CAACC,KAAK,CAAC;IACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IAE1C,IAAIL,IAAI,CAACM,YAAY,CAAC,CAAC,KAAKJ,KAAK,CAACI,YAAY,CAAC,CAAC,EAAE;MAChD,OAAO/H,MAAM,CAACyH,IAAI,EAAE,OAAO,EAAE;QAAEO,MAAM,EAAE/H;MAAG,CAAC,CAAC;IAC9C,CAAC,MAAM,IAAIwH,IAAI,CAACM,YAAY,CAAC,CAAC,KAAKH,SAAS,CAACG,YAAY,CAAC,CAAC,EAAE;MAC3D,OAAO,MAAM,GAAG/H,MAAM,CAACyH,IAAI,EAAE,OAAO,EAAE;QAAEO,MAAM,EAAE/H;MAAG,CAAC,CAAC;IACvD,CAAC,MAAM;MACL,OAAOD,MAAM,CAACyH,IAAI,EAAE,cAAc,EAAE;QAAEO,MAAM,EAAE/H;MAAG,CAAC,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAMgI,qBAAqB,GAAGrH,aAAa,CAACU,MAAM,CAACqC,IAAI,IAAI;IACzD;IACA,MAAMuE,aAAa,GAAGvE,IAAI,CAACqC,UAAU,CAACmC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChH,UAAU,CAAC+G,WAAW,CAAC,CAAC,CAAC,IACjExE,IAAI,CAACE,WAAW,CAACsE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChH,UAAU,CAAC+G,WAAW,CAAC,CAAC,CAAC;;IAEtF;IACA,MAAME,aAAa,GAAG/G,MAAM,KAAK,KAAK,IAChBA,MAAM,KAAK,QAAQ,IAAIqC,IAAI,CAACM,MAAO,IACnC3C,MAAM,KAAK,UAAU,IAAIqC,IAAI,CAACyC,QAAS,IACvC9E,MAAM,KAAK,SAAS,IAAIqC,IAAI,CAACwC,OAAQ;IAE3D,OAAO+B,aAAa,IAAIG,aAAa;EACvC,CAAC,CAAC;;EAEF;EACA,MAAMC,UAAU,GAAIpF,EAAE,IAAK;IACzBrC,gBAAgB,CAAC0H,iBAAiB,IAChCA,iBAAiB,CAAC7E,GAAG,CAACC,IAAI,IACxBA,IAAI,CAACT,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGS,IAAI;MAAEwC,OAAO,EAAE,CAACxC,IAAI,CAACwC;IAAQ,CAAC,GAAGxC,IACzD,CACF,CAAC;IAED,IAAI7C,oBAAoB,IAAIA,oBAAoB,CAACoC,EAAE,KAAKA,EAAE,EAAE;MAC1DnC,uBAAuB,CAAC0C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE0C,OAAO,EAAE,CAAC1C,IAAI,CAAC0C;MAAQ,CAAC,CAAC,CAAC;IACxE;EACF,CAAC;;EAED;EACA,MAAMqC,aAAa,GAAItF,EAAE,IAAK;IAC5BrC,gBAAgB,CAAC0H,iBAAiB,IAChCA,iBAAiB,CAAC7E,GAAG,CAACC,IAAI,IACxBA,IAAI,CAACT,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGS,IAAI;MAAEyC,QAAQ,EAAE,CAACzC,IAAI,CAACyC;IAAS,CAAC,GAAGzC,IAC3D,CACF,CAAC;IAED,IAAI7C,oBAAoB,IAAIA,oBAAoB,CAACoC,EAAE,KAAKA,EAAE,EAAE;MAC1DnC,uBAAuB,CAAC0C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE2C,QAAQ,EAAE,CAAC3C,IAAI,CAAC2C;MAAS,CAAC,CAAC,CAAC;IAC1E;EACF,CAAC;EAED,IAAI1F,SAAS,EAAE;IACb,oBACEN,OAAA;MAAKqI,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5DtI,OAAA;QAAKqI,SAAS,EAAC;MAA6E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChG,CAAC;EAEV;EAEA,oBACE1I,OAAA;IAAKqI,SAAS,EAAC,6BAA6B;IAAAC,QAAA,gBAE1CtI,OAAA;MAAKqI,SAAS,EAAC,qFAAqF;MAAAC,QAAA,eAClGtI,OAAA;QAAKqI,SAAS,EAAC,uEAAuE;QAAAC,QAAA,gBACpFtI,OAAA;UAAAsI,QAAA,gBACEtI,OAAA;YAAIqI,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClD1I,OAAA;YAAGqI,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAC;UAEpC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACN1I,OAAA;UAAKqI,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAC1CtI,OAAA;YAAQqI,SAAS,EAAC,6NAA6N;YAAAC,QAAA,gBAC7OtI,OAAA,CAACrB,0BAA0B;cAAC0J,SAAS,EAAC,oBAAoB;cAAC,eAAY;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cAElF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1I,OAAA;MAAKqI,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5DtI,OAAA;QAAKqI,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBAEzCtI,OAAA;UAAKqI,SAAS,EAAC,kEAAkE;UAAAC,QAAA,gBAC/EtI,OAAA;YAAKqI,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDtI,OAAA;cAAIqI,SAAS,EAAC,uDAAuD;cAAAC,QAAA,gBACnEtI,OAAA,CAACrB,0BAA0B;gBAAC0J,SAAS,EAAC;cAA8B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAEzE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL1I,OAAA;cAAKqI,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BtI,OAAA;gBACE2I,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,0BAAqB;gBACjCP,SAAS,EAAC,qHAAqH;gBAC/HQ,KAAK,EAAE7H,UAAW;gBAClB8H,QAAQ,EAAG9B,CAAC,IAAK/F,aAAa,CAAC+F,CAAC,CAAC+B,MAAM,CAACF,KAAK;cAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACF1I,OAAA,CAACpB,mBAAmB;gBAACyJ,SAAS,EAAC;cAA+C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC,eACN1I,OAAA;cAAKqI,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAClCtI,OAAA;gBACEqI,SAAS,EAAE,kCAAkCnH,MAAM,KAAK,KAAK,GACzD,+BAA+B,GAC/B,6CAA6C,EAAG;gBACpD8H,OAAO,EAAEA,CAAA,KAAM7H,SAAS,CAAC,KAAK,CAAE;gBAAAmH,QAAA,EACjC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT1I,OAAA;gBACEqI,SAAS,EAAE,kCAAkCnH,MAAM,KAAK,QAAQ,GAC5D,+BAA+B,GAC/B,6CAA6C,EAAG;gBACpD8H,OAAO,EAAEA,CAAA,KAAM7H,SAAS,CAAC,QAAQ,CAAE;gBAAAmH,QAAA,EACpC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT1I,OAAA;gBACEqI,SAAS,EAAE,kCAAkCnH,MAAM,KAAK,SAAS,GAC7D,+BAA+B,GAC/B,6CAA6C,EAAG;gBACpD8H,OAAO,EAAEA,CAAA,KAAM7H,SAAS,CAAC,SAAS,CAAE;gBAAAmH,QAAA,EACrC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT1I,OAAA;gBACEqI,SAAS,EAAE,kCAAkCnH,MAAM,KAAK,UAAU,GAC9D,+BAA+B,GAC/B,6CAA6C,EAAG;gBACpD8H,OAAO,EAAEA,CAAA,KAAM7H,SAAS,CAAC,UAAU,CAAE;gBAAAmH,QAAA,EACtC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN1I,OAAA;YACEiJ,GAAG,EAAErH,gBAAiB;YACtBsH,KAAK,EAAE;cACLC,MAAM,EAAE,oBAAoB;cAC5BC,SAAS,EAAE,MAAM;cACjBC,cAAc,EAAE,MAAM;cACtBC,cAAc,EAAE;YAClB,CAAE;YAAAhB,QAAA,EAEDT,qBAAqB,CAAC0B,MAAM,KAAK,CAAC,gBACjCvJ,OAAA;cAAKqI,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAE/C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GAENb,qBAAqB,CAACvE,GAAG,CAACiC,YAAY,iBACpCvF,OAAA;cAEEqI,SAAS,EAAE,sEACT,CAAA3H,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEoC,EAAE,MAAKyC,YAAY,CAACzC,EAAE,GAAG,cAAc,GAAG,EAAE,IAChEyC,YAAY,CAAC1B,MAAM,GAAG,kCAAkC,GAAG,EAAE,EAAG;cACpEmF,OAAO,EAAEA,CAAA,KAAM9B,wBAAwB,CAAC3B,YAAY,CAAE;cAAA+C,QAAA,gBAEtDtI,OAAA;gBAAKqI,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzCtI,OAAA;kBAAKqI,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCtI,OAAA;oBACEwJ,GAAG,EAAEjE,YAAY,CAACM,MAAO;oBACzB4D,GAAG,EAAElE,YAAY,CAACK,UAAW;oBAC7ByC,SAAS,EAAE,0BACT,CAAA3H,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEoC,EAAE,MAAKyC,YAAY,CAACzC,EAAE,GACxC,wBAAwB,GACxB,EAAE;kBACL;oBAAAyF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,EACDnD,YAAY,CAACR,MAAM,KAAK,QAAQ,iBAC/B/E,OAAA;oBAAMqI,SAAS,EAAC;kBAAyF;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CACjH;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACN1I,OAAA;kBAAKqI,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BtI,OAAA;oBAAKqI,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,gBAC/CtI,OAAA;sBAAIqI,SAAS,EAAE,uBAAuB9C,YAAY,CAAC1B,MAAM,GAAG,eAAe,GAAG,eAAe,EAAG;sBAAAyE,QAAA,EAC7F/C,YAAY,CAACK;oBAAU;sBAAA2C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB,CAAC,eACL1I,OAAA;sBAAKqI,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,GACzC/C,YAAY,CAACQ,OAAO,iBACnB/F,OAAA,CAACL,QAAQ;wBAAC0I,SAAS,EAAC;sBAAsC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAC7D,eACD1I,OAAA;wBAAMqI,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EACpCnB,iBAAiB,CAAC5B,YAAY,CAAC5B,SAAS;sBAAC;wBAAA4E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN1I,OAAA;oBAAGqI,SAAS,EAAE,yBACZ9C,YAAY,CAAC1B,MAAM,GAAG,2BAA2B,GAAG,eAAe,EAClE;oBAAAyE,QAAA,EACA/C,YAAY,CAAC9B;kBAAW;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,eACJ1I,OAAA;oBAAKqI,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrDtI,OAAA;sBAAMqI,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EACpC/C,YAAY,CAACR,MAAM,KAAK,QAAQ,GAC7B,WAAW,GACXQ,YAAY,CAACmE,QAAQ,GACnB,gBAAgBnE,YAAY,CAACmE,QAAQ,EAAE,GACvC;oBAAE;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACP1I,OAAA;sBAAKqI,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,gBAC7BtI,OAAA;wBACEgJ,OAAO,EAAGhC,CAAC,IAAK;0BACdA,CAAC,CAAC2C,eAAe,CAAC,CAAC;0BACnBzB,UAAU,CAAC3C,YAAY,CAACzC,EAAE,CAAC;wBAC7B,CAAE;wBACFuF,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,eAE/CtI,OAAA,CAACL,QAAQ;0BAAC0I,SAAS,EAAE,WAAW9C,YAAY,CAACQ,OAAO,GAAG,8BAA8B,GAAG,EAAE;wBAAG;0BAAAwC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1F,CAAC,eACT1I,OAAA;wBACEgJ,OAAO,EAAGhC,CAAC,IAAK;0BACdA,CAAC,CAAC2C,eAAe,CAAC,CAAC;0BACnBvB,aAAa,CAAC7C,YAAY,CAACzC,EAAE,CAAC;wBAChC,CAAE;wBACFuF,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,eAE7CtI,OAAA,CAACN,cAAc;0BAAC2I,SAAS,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACLnD,YAAY,CAAC1B,MAAM,iBAClB7D,OAAA;gBAAMqI,SAAS,EAAC,2GAA2G;gBAAAC,QAAA,EAAC;cAE5H;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP;YAAA,GA3EInD,YAAY,CAACzC,EAAE;cAAAyF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA4EjB,CACN;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN1I,OAAA;UAAKqI,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EACrD5H,oBAAoB,gBACnBV,OAAA,CAAAE,SAAA;YAAAoI,QAAA,gBAEEtI,OAAA;cAAKqI,SAAS,EAAC,yEAAyE;cAAAC,QAAA,gBACtFtI,OAAA;gBAAKqI,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCtI,OAAA;kBAAKqI,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BtI,OAAA;oBACEwJ,GAAG,EAAE9I,oBAAoB,CAACmF,MAAO;oBACjC4D,GAAG,EAAE/I,oBAAoB,CAACkF,UAAW;oBACrCyC,SAAS,EAAC;kBAAwB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC,EACDhI,oBAAoB,CAACqE,MAAM,KAAK,QAAQ,iBACvC/E,OAAA;oBAAMqI,SAAS,EAAC;kBAAyF;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CACjH;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACN1I,OAAA;kBAAAsI,QAAA,gBACEtI,OAAA;oBAAIqI,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAC9C5H,oBAAoB,CAACkF;kBAAU;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACL1I,OAAA;oBAAGqI,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EACjC5H,oBAAoB,CAACqE,MAAM,KAAK,QAAQ,GACrC,WAAW,GACXrE,oBAAoB,CAACgJ,QAAQ,GAC3B,gBAAgBhJ,oBAAoB,CAACgJ,QAAQ,EAAE,GAC/C;kBAAE;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN1I,OAAA;gBAAKqI,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CtI,OAAA;kBAAQqI,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,eAClEtI,OAAA,CAACb,SAAS;oBAACkJ,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACT1I,OAAA;kBAAQqI,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,eAClEtI,OAAA,CAACZ,eAAe;oBAACiJ,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACT1I,OAAA;kBAAQqI,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,eAClEtI,OAAA,CAACX,qBAAqB;oBAACgJ,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACT1I,OAAA;kBAAQqI,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,eAClEtI,OAAA,CAACd,sBAAsB;oBAACmJ,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN1I,OAAA;cACEiJ,GAAG,EAAEpH,oBAAqB;cAC1BwG,SAAS,EAAC,gBAAgB;cAC1Ba,KAAK,EAAE;gBACLC,MAAM,EAAE,oBAAoB;gBAC5BC,SAAS,EAAE,MAAM;gBACjBC,cAAc,EAAE,MAAM;gBACtBC,cAAc,EAAE;cAClB,CAAE;cAAAhB,QAAA,GAED1H,QAAQ,CAAC0C,GAAG,CAAC,CAACF,OAAO,EAAEwG,KAAK,KAAK;gBAChC,MAAMC,QAAQ,GAAGzG,OAAO,CAACU,QAAQ,KAAKzD,IAAI,CAACyC,EAAE;gBAC7C,MAAMgH,UAAU,GAAGF,KAAK,KAAK,CAAC,IAAIhJ,QAAQ,CAACgJ,KAAK,GAAG,CAAC,CAAC,CAAC9F,QAAQ,KAAKV,OAAO,CAACU,QAAQ;gBAEnF,oBACE9D,OAAA;kBAAsBqI,SAAS,EAAE,QAAQwB,QAAQ,GAAG,aAAa,GAAG,eAAe,OAAQ;kBAAAvB,QAAA,GACxF,CAACuB,QAAQ,IAAIC,UAAU,iBACtB9J,OAAA;oBACEwJ,GAAG,EAAEpG,OAAO,CAACe,YAAa;oBAC1BsF,GAAG,EAAErG,OAAO,CAACY,UAAW;oBACxBqE,SAAS,EAAC;kBAAgC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CACF,EACA,CAACmB,QAAQ,IAAI,CAACC,UAAU,iBAAI9J,OAAA;oBAAKqI,SAAS,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7D1I,OAAA;oBACEqI,SAAS,EAAE,yDACTwB,QAAQ,GACJ,0CAA0C,GAC1C,+DAA+D,EAClE;oBAAAvB,QAAA,gBAEHtI,OAAA;sBAAGqI,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAElF,OAAO,CAACiB;oBAAI;sBAAAkE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzC1I,OAAA;sBAAKqI,SAAS,EAAE,gBAAgBwB,QAAQ,GAAG,iBAAiB,GAAG,eAAe,gCAAiC;sBAAAvB,QAAA,GAC5GnB,iBAAiB,CAAC/D,OAAO,CAACO,SAAS,CAAC,EACpCkG,QAAQ,iBACP7J,OAAA,CAACT,eAAe;wBAAC8I,SAAS,EAAC,cAAc;wBAAC0B,KAAK,EAAE3G,OAAO,CAACkB,IAAI,GAAG,QAAQ,GAAG;sBAAW;wBAAAiE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CACzF;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EACLmB,QAAQ,IAAIC,UAAU,iBACrB9J,OAAA;oBACEwJ,GAAG,EAAEpG,OAAO,CAACe,YAAa;oBAC1BsF,GAAG,EAAErG,OAAO,CAACY,UAAW;oBACxBqE,SAAS,EAAC;kBAAgC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CACF,EACAmB,QAAQ,IAAI,CAACC,UAAU,iBAAI9J,OAAA;oBAAKqI,SAAS,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,GA/BpDtF,OAAO,CAACN,EAAE;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAgCf,CAAC;cAEV,CAAC,CAAC,eACF1I,OAAA;gBAAKiJ,GAAG,EAAEtH;cAAe;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eAGN1I,OAAA;cAAKqI,SAAS,EAAC,uCAAuC;cAAAC,QAAA,eACpDtI,OAAA;gBAAMgK,QAAQ,EAAEjD,iBAAkB;gBAACsB,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC3DtI,OAAA;kBACE2I,IAAI,EAAC,QAAQ;kBACbN,SAAS,EAAC,uEAAuE;kBAAAC,QAAA,eAEjFtI,OAAA,CAACjB,aAAa;oBAACsJ,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACT1I,OAAA;kBAAKqI,SAAS,EAAC,aAAa;kBAAAC,QAAA,eAC1BtI,OAAA;oBACEqI,SAAS,EAAC,qHAAqH;oBAC/HO,WAAW,EAAC,yCAAqB;oBACjCqB,IAAI,EAAC,GAAG;oBACRpB,KAAK,EAAE/H,WAAY;oBACnBgI,QAAQ,EAAG9B,CAAC,IAAKjG,cAAc,CAACiG,CAAC,CAAC+B,MAAM,CAACF,KAAK;kBAAE;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACN1I,OAAA;kBACE2I,IAAI,EAAC,QAAQ;kBACbN,SAAS,EAAC,uEAAuE;kBAAAC,QAAA,eAEjFtI,OAAA,CAAChB,aAAa;oBAACqJ,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACT1I,OAAA;kBACE2I,IAAI,EAAC,QAAQ;kBACbuB,QAAQ,EAAE,CAACpJ,WAAW,CAACwF,IAAI,CAAC,CAAE;kBAC9B+B,SAAS,EAAE,yBACTvH,WAAW,CAACwF,IAAI,CAAC,CAAC,GACd,8CAA8C,GAC9C,8CAA8C,qBAC9B;kBAAAgC,QAAA,eAEtBtI,OAAA,CAAClB,iBAAiB;oBAACuJ,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,eACN,CAAC;UAAA;UAEH;UACA1I,OAAA;YAAKqI,SAAS,EAAC,iEAAiE;YAAAC,QAAA,eAC9EtI,OAAA;cAAKqI,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CtI,OAAA,CAACrB,0BAA0B;gBAAC0J,SAAS,EAAC;cAAsC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/E1I,OAAA;gBAAIqI,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxE1I,OAAA;gBAAGqI,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAErC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtI,EAAA,CA7pBID,YAAY;EAAA,QACC5B,OAAO;AAAA;AAAA4L,EAAA,GADpBhK,YAAY;AA+pBlB,eAAeA,YAAY;AAAC,IAAAgK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}