{"ast": null, "code": "\"use client\";\n\n// src/errorBoundaryUtils.ts\nimport * as React from \"react\";\nimport { shouldThrowError } from \"./utils.js\";\nvar ensurePreventErrorBoundaryRetry = (options, errorResetBoundary) => {\n  if (options.suspense || options.throwOnError || options.experimental_prefetchInRender) {\n    if (!errorResetBoundary.isReset()) {\n      options.retryOnMount = false;\n    }\n  }\n};\nvar useClearResetErrorBoundary = errorResetBoundary => {\n  React.useEffect(() => {\n    errorResetBoundary.clearReset();\n  }, [errorResetBoundary]);\n};\nvar getHasError = ({\n  result,\n  errorResetBoundary,\n  throwOnError,\n  query,\n  suspense\n}) => {\n  return result.isError && !errorResetBoundary.isReset() && !result.isFetching && query && (suspense && result.data === void 0 || shouldThrowError(throwOnError, [result.error, query]));\n};\nexport { ensurePreventErrorBoundaryRetry, getHasError, useClearResetErrorBoundary };", "map": {"version": 3, "names": ["React", "shouldThrowError", "ensurePreventErrorBoundaryRetry", "options", "errorResetBoundary", "suspense", "throwOnError", "experimental_prefetchInRender", "isReset", "retryOnMount", "useClearResetErrorBoundary", "useEffect", "clear<PERSON><PERSON>t", "getHasError", "result", "query", "isError", "isFetching", "data", "error"], "sources": ["C:\\burky root\\burky_root_web\\client\\node_modules\\@tanstack\\react-query\\src\\errorBoundaryUtils.ts"], "sourcesContent": ["'use client'\nimport * as React from 'react'\nimport { shouldThrowError } from './utils'\nimport type {\n  DefaultedQueryObserverOptions,\n  Query,\n  QueryKey,\n  QueryObserverResult,\n  ThrowOnError,\n} from '@tanstack/query-core'\nimport type { QueryErrorResetBoundaryValue } from './QueryErrorResetBoundary'\n\nexport const ensurePreventErrorBoundaryRetry = <\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>(\n  options: DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) => {\n  if (\n    options.suspense ||\n    options.throwOnError ||\n    options.experimental_prefetchInRender\n  ) {\n    // Prevent retrying failed query if the error boundary has not been reset yet\n    if (!errorResetBoundary.isReset()) {\n      options.retryOnMount = false\n    }\n  }\n}\n\nexport const useClearResetErrorBoundary = (\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) => {\n  React.useEffect(() => {\n    errorResetBoundary.clearReset()\n  }, [errorResetBoundary])\n}\n\nexport const getHasError = <\n  TData,\n  TError,\n  TQueryFnData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>({\n  result,\n  errorResetBoundary,\n  throwOnError,\n  query,\n  suspense,\n}: {\n  result: QueryObserverResult<TData, TError>\n  errorResetBoundary: QueryErrorResetBoundaryValue\n  throwOnError: ThrowOnError<TQueryFnData, TError, TQueryData, TQueryKey>\n  query: Query<TQueryFnData, TError, TQueryData, TQueryKey> | undefined\n  suspense: boolean | undefined\n}) => {\n  return (\n    result.isError &&\n    !errorResetBoundary.isReset() &&\n    !result.isFetching &&\n    query &&\n    ((suspense && result.data === undefined) ||\n      shouldThrowError(throwOnError, [result.error, query]))\n  )\n}\n"], "mappings": ";;;AACA,YAAYA,KAAA,MAAW;AACvB,SAASC,gBAAA,QAAwB;AAU1B,IAAMC,+BAAA,GAAkCA,CAO7CC,OAAA,EAOAC,kBAAA,KACG;EACH,IACED,OAAA,CAAQE,QAAA,IACRF,OAAA,CAAQG,YAAA,IACRH,OAAA,CAAQI,6BAAA,EACR;IAEA,IAAI,CAACH,kBAAA,CAAmBI,OAAA,CAAQ,GAAG;MACjCL,OAAA,CAAQM,YAAA,GAAe;IACzB;EACF;AACF;AAEO,IAAMC,0BAAA,GACXN,kBAAA,IACG;EACGJ,KAAA,CAAAW,SAAA,CAAU,MAAM;IACpBP,kBAAA,CAAmBQ,UAAA,CAAW;EAChC,GAAG,CAACR,kBAAkB,CAAC;AACzB;AAEO,IAAMS,WAAA,GAAcA,CAMzB;EACAC,MAAA;EACAV,kBAAA;EACAE,YAAA;EACAS,KAAA;EACAV;AACF,MAMM;EACJ,OACES,MAAA,CAAOE,OAAA,IACP,CAACZ,kBAAA,CAAmBI,OAAA,CAAQ,KAC5B,CAACM,MAAA,CAAOG,UAAA,IACRF,KAAA,KACEV,QAAA,IAAYS,MAAA,CAAOI,IAAA,KAAS,UAC5BjB,gBAAA,CAAiBK,YAAA,EAAc,CAACQ,MAAA,CAAOK,KAAA,EAAOJ,KAAK,CAAC;AAE1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}