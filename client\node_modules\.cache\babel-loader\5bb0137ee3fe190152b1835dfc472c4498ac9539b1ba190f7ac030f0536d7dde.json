{"ast": null, "code": "import api from './api';\n\n/**\n * Sessions API service\n */\nconst sessionsApi = {\n  /**\n   * Get sessions for current user\n   */\n  getUserSessions: async () => {\n    try {\n      const response = await api.get('/sessions');\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching user sessions:', error);\n      throw error;\n    }\n  },\n  /**\n   * Get session by ID\n   */\n  getSessionById: async sessionId => {\n    try {\n      const response = await api.get(`/sessions/${sessionId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching session:', error);\n      throw error;\n    }\n  },\n  /**\n   * Update session notes (expert only)\n   */\n  updateSessionNotes: async (sessionId, notes, summary) => {\n    try {\n      const response = await api.put(`/sessions/${sessionId}/notes`, {\n        notes,\n        summary\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Error updating session notes:', error);\n      throw error;\n    }\n  },\n  /**\n   * Download session recording\n   */\n  downloadRecording: async sessionId => {\n    try {\n      const response = await api.get(`/sessions/${sessionId}/recording`);\n      return response.data;\n    } catch (error) {\n      console.error('Error downloading recording:', error);\n      throw error;\n    }\n  }\n};\nexport default sessionsApi;", "map": {"version": 3, "names": ["api", "sessionsApi", "getUserSessions", "response", "get", "data", "error", "console", "getSessionById", "sessionId", "updateSessionNotes", "notes", "summary", "put", "downloadRecording"], "sources": ["C:/Projeler/kidgarden/burky_root_web/client/src/services/sessionsApi.js"], "sourcesContent": ["import api from './api';\n\n/**\n * Sessions API service\n */\nconst sessionsApi = {\n  /**\n   * Get sessions for current user\n   */\n  getUserSessions: async () => {\n    try {\n      const response = await api.get('/sessions');\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching user sessions:', error);\n      throw error;\n    }\n  },\n\n  /**\n   * Get session by ID\n   */\n  getSessionById: async (sessionId) => {\n    try {\n      const response = await api.get(`/sessions/${sessionId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching session:', error);\n      throw error;\n    }\n  },\n\n  /**\n   * Update session notes (expert only)\n   */\n  updateSessionNotes: async (sessionId, notes, summary) => {\n    try {\n      const response = await api.put(`/sessions/${sessionId}/notes`, {\n        notes,\n        summary\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Error updating session notes:', error);\n      throw error;\n    }\n  },\n\n  /**\n   * Download session recording\n   */\n  downloadRecording: async (sessionId) => {\n    try {\n      const response = await api.get(`/sessions/${sessionId}/recording`);\n      return response.data;\n    } catch (error) {\n      console.error('Error downloading recording:', error);\n      throw error;\n    }\n  }\n};\n\nexport default sessionsApi;\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;;AAEvB;AACA;AACA;AACA,MAAMC,WAAW,GAAG;EAClB;AACF;AACA;EACEC,eAAe,EAAE,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,CAAC,WAAW,CAAC;MAC3C,OAAOD,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAMA,KAAK;IACb;EACF,CAAC;EAED;AACF;AACA;EACEE,cAAc,EAAE,MAAOC,SAAS,IAAK;IACnC,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,CAAC,aAAaK,SAAS,EAAE,CAAC;MACxD,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF,CAAC;EAED;AACF;AACA;EACEI,kBAAkB,EAAE,MAAAA,CAAOD,SAAS,EAAEE,KAAK,EAAEC,OAAO,KAAK;IACvD,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAMH,GAAG,CAACa,GAAG,CAAC,aAAaJ,SAAS,QAAQ,EAAE;QAC7DE,KAAK;QACLC;MACF,CAAC,CAAC;MACF,OAAOT,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAMA,KAAK;IACb;EACF,CAAC;EAED;AACF;AACA;EACEQ,iBAAiB,EAAE,MAAOL,SAAS,IAAK;IACtC,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,CAAC,aAAaK,SAAS,YAAY,CAAC;MAClE,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF;AACF,CAAC;AAED,eAAeL,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}