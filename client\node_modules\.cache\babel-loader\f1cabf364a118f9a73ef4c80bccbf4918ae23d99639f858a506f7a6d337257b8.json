{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: \"eeee 'la semaine dernière à' p\",\n  yesterday: \"'hier à' p\",\n  today: \"'aujourd’hui à' p\",\n  tomorrow: \"'demain à' p'\",\n  nextWeek: \"eeee 'la semaine prochaine à' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/Projeler/kidgarden/burky_root_web/node_modules/date-fns/esm/locale/fr-CH/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: \"eeee 'la semaine dernière à' p\",\n  yesterday: \"'hier à' p\",\n  today: \"'aujourd’hui à' p\",\n  tomorrow: \"'demain à' p'\",\n  nextWeek: \"eeee 'la semaine prochaine à' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,gCAAgC;EAC1CC,SAAS,EAAE,YAAY;EACvBC,KAAK,EAAE,mBAAmB;EAC1BC,QAAQ,EAAE,eAAe;EACzBC,QAAQ,EAAE,iCAAiC;EAC3CC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EAC9E,OAAOX,oBAAoB,CAACQ,KAAK,CAAC;AACpC,CAAC;AACD,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}