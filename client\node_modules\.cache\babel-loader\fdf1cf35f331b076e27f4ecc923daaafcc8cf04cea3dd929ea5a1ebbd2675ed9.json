{"ast": null, "code": "var _jsxFileName = \"C:\\\\claude\\\\burky_root_web\\\\client\\\\src\\\\components\\\\ui\\\\FormInput.jsx\";\nimport React, { forwardRef } from 'react';\n\n/**\n * Modern, özelleştirilebilir form girişi bi<PERSON>şeni\n * \n * @param {Object} props - Bileşen özellikleri\n * @param {string} props.label - Input etiketi\n * @param {string} props.id - Input ID'si\n * @param {string} props.name - Input adı\n * @param {string} props.type - Input tipi (text, password, email, vb.)\n * @param {string} props.placeholder - Placeholder metni\n * @param {string} props.error - Hata mesajı (varsa)\n * @param {boolean} props.fullWidth - Tam genişlik kullanılsın mı\n * @param {string} props.variant - Stil varyantı ('outlined', 'filled', 'underlined')\n * @param {string} props.size - Boyut ('sm', 'md', 'lg')\n * @param {boolean} props.disabled - Etkin değil mi\n * @param {string} props.helperText - Yardımcı metin\n * @param {React.ReactNode} props.icon - Sol ikon\n * @param {React.ReactNode} props.rightIcon - Sağ ikon\n * @param {Function} props.onIconClick - İkon tıklama işleyicisi\n * @param {string} props.className - Ek sınıflar\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FormInput = /*#__PURE__*/forwardRef(_c = ({\n  label,\n  id,\n  name,\n  type = 'text',\n  placeholder,\n  error,\n  fullWidth = true,\n  variant = 'outlined',\n  size = 'md',\n  disabled = false,\n  helperText,\n  icon,\n  rightIcon,\n  onIconClick,\n  className,\n  ...rest\n}, ref) => {\n  // Boyuta göre padding ve font size\n  const sizeClasses = {\n    sm: 'px-3 py-1.5 text-xs',\n    md: 'px-4 py-2 text-sm',\n    lg: 'px-5 py-2.5 text-base'\n  };\n\n  // Variant'a göre stil sınıfları\n  const getVariantClasses = () => {\n    switch (variant) {\n      case 'filled':\n        return `bg-gray-100 border-transparent hover:bg-gray-200 focus:bg-white ${error ? 'border-red-500' : 'focus:border-primary-500'}`;\n      case 'underlined':\n        return `border-t-0 border-r-0 border-l-0 border-b-2 rounded-none px-0 ${error ? 'border-red-500' : 'border-gray-300 focus:border-primary-500'}`;\n      case 'outlined':\n      default:\n        return `bg-white ${error ? 'border-red-400 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 hover:border-gray-400 focus:ring-primary-500 focus:border-primary-500'}`;\n    }\n  };\n\n  // İkon stilleri\n  const iconClasses = 'absolute inset-y-0 flex items-center pointer-events-none text-gray-500';\n  const iconPadding = icon ? 'pl-10' : '';\n  const rightIconPadding = rightIcon ? 'pr-10' : '';\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `${fullWidth ? 'w-full' : 'w-auto'} ${className || ''}`,\n    children: [label && /*#__PURE__*/_jsxDEV(\"label\", {\n      htmlFor: id,\n      className: \"block text-sm font-medium mb-1.5 text-gray-700\",\n      children: label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative\",\n      children: [icon && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `${iconClasses} left-3`,\n        children: icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        id: id,\n        name: name,\n        type: type,\n        placeholder: placeholder,\n        disabled: disabled,\n        ref: ref,\n        className: `\n            block w-full\n            border rounded-lg\n            focus:outline-none focus:ring-2 focus:ring-opacity-50\n            transition-colors duration-200\n            shadow-sm\n            ${sizeClasses[size]}\n            ${getVariantClasses()}\n            ${iconPadding}\n            ${rightIconPadding}\n            ${disabled ? 'opacity-60 cursor-not-allowed bg-gray-100' : ''}\n          `,\n        ...rest\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this), rightIcon && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `absolute inset-y-0 right-3 flex items-center ${onIconClick ? 'cursor-pointer' : 'pointer-events-none'} text-gray-500`,\n        onClick: onIconClick,\n        children: rightIcon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this), (error || helperText) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-1\",\n      children: error ? /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs text-red-600\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 13\n      }, this) : helperText ? /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs text-gray-500\",\n        children: helperText\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 13\n      }, this) : null\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 75,\n    columnNumber: 5\n  }, this);\n});\n_c2 = FormInput;\nFormInput.displayName = 'FormInput';\nexport default FormInput;\nvar _c, _c2;\n$RefreshReg$(_c, \"FormInput$forwardRef\");\n$RefreshReg$(_c2, \"FormInput\");", "map": {"version": 3, "names": ["React", "forwardRef", "jsxDEV", "_jsxDEV", "FormInput", "_c", "label", "id", "name", "type", "placeholder", "error", "fullWidth", "variant", "size", "disabled", "helperText", "icon", "rightIcon", "onIconClick", "className", "rest", "ref", "sizeClasses", "sm", "md", "lg", "getVariantClasses", "iconClasses", "iconPadding", "rightIconPadding", "children", "htmlFor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c2", "displayName", "$RefreshReg$"], "sources": ["C:/claude/burky_root_web/client/src/components/ui/FormInput.jsx"], "sourcesContent": ["import React, { forwardRef } from 'react';\n\n/**\n * Modern, özelleştirilebilir form girişi bi<PERSON>şeni\n * \n * @param {Object} props - Bileşen özellikleri\n * @param {string} props.label - Input etiketi\n * @param {string} props.id - Input ID'si\n * @param {string} props.name - Input adı\n * @param {string} props.type - Input tipi (text, password, email, vb.)\n * @param {string} props.placeholder - Placeholder metni\n * @param {string} props.error - Hata mesajı (varsa)\n * @param {boolean} props.fullWidth - Tam genişlik kullanılsın mı\n * @param {string} props.variant - Stil varyantı ('outlined', 'filled', 'underlined')\n * @param {string} props.size - Boyut ('sm', 'md', 'lg')\n * @param {boolean} props.disabled - Etkin değil mi\n * @param {string} props.helperText - Yardımcı metin\n * @param {React.ReactNode} props.icon - Sol ikon\n * @param {React.ReactNode} props.rightIcon - Sağ ikon\n * @param {Function} props.onIconClick - İkon tıklama işleyicisi\n * @param {string} props.className - Ek sınıflar\n */\nconst FormInput = forwardRef(({\n  label,\n  id,\n  name,\n  type = 'text',\n  placeholder,\n  error,\n  fullWidth = true,\n  variant = 'outlined',\n  size = 'md',\n  disabled = false,\n  helperText,\n  icon,\n  rightIcon,\n  onIconClick,\n  className,\n  ...rest\n}, ref) => {\n  // Boyuta göre padding ve font size\n  const sizeClasses = {\n    sm: 'px-3 py-1.5 text-xs',\n    md: 'px-4 py-2 text-sm',\n    lg: 'px-5 py-2.5 text-base'\n  };\n\n  // Variant'a göre stil sınıfları\n  const getVariantClasses = () => {\n    switch (variant) {\n      case 'filled':\n        return `bg-gray-100 border-transparent hover:bg-gray-200 focus:bg-white ${\n          error ? 'border-red-500' : 'focus:border-primary-500'\n        }`;\n      case 'underlined':\n        return `border-t-0 border-r-0 border-l-0 border-b-2 rounded-none px-0 ${\n          error ? 'border-red-500' : 'border-gray-300 focus:border-primary-500'\n        }`;\n      case 'outlined':\n      default:\n        return `bg-white ${\n          error \n            ? 'border-red-400 focus:ring-red-500 focus:border-red-500' \n            : 'border-gray-300 hover:border-gray-400 focus:ring-primary-500 focus:border-primary-500'\n        }`;\n    }\n  };\n\n  // İkon stilleri\n  const iconClasses = 'absolute inset-y-0 flex items-center pointer-events-none text-gray-500';\n  const iconPadding = icon ? 'pl-10' : '';\n  const rightIconPadding = rightIcon ? 'pr-10' : '';\n\n  return (\n    <div className={`${fullWidth ? 'w-full' : 'w-auto'} ${className || ''}`}>\n      {label && (\n        <label \n          htmlFor={id}\n          className=\"block text-sm font-medium mb-1.5 text-gray-700\"\n        >\n          {label}\n        </label>\n      )}\n      \n      <div className=\"relative\">\n        {icon && (\n          <div className={`${iconClasses} left-3`}>\n            {icon}\n          </div>\n        )}\n        \n        <input\n          id={id}\n          name={name}\n          type={type}\n          placeholder={placeholder}\n          disabled={disabled}\n          ref={ref}\n          className={`\n            block w-full\n            border rounded-lg\n            focus:outline-none focus:ring-2 focus:ring-opacity-50\n            transition-colors duration-200\n            shadow-sm\n            ${sizeClasses[size]}\n            ${getVariantClasses()}\n            ${iconPadding}\n            ${rightIconPadding}\n            ${disabled ? 'opacity-60 cursor-not-allowed bg-gray-100' : ''}\n          `}\n          {...rest}\n        />\n        \n        {rightIcon && (\n          <div \n            className={`absolute inset-y-0 right-3 flex items-center ${onIconClick ? 'cursor-pointer' : 'pointer-events-none'} text-gray-500`}\n            onClick={onIconClick}\n          >\n            {rightIcon}\n          </div>\n        )}\n      </div>\n      \n      {(error || helperText) && (\n        <div className=\"mt-1\">\n          {error ? (\n            <p className=\"text-xs text-red-600\">{error}</p>\n          ) : helperText ? (\n            <p className=\"text-xs text-gray-500\">{helperText}</p>\n          ) : null}\n        </div>\n      )}\n    </div>\n  );\n});\n\nFormInput.displayName = 'FormInput';\n\nexport default FormInput;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAnBA,SAAAC,MAAA,IAAAC,OAAA;AAoBA,MAAMC,SAAS,gBAAGH,UAAU,CAAAI,EAAA,GAACA,CAAC;EAC5BC,KAAK;EACLC,EAAE;EACFC,IAAI;EACJC,IAAI,GAAG,MAAM;EACbC,WAAW;EACXC,KAAK;EACLC,SAAS,GAAG,IAAI;EAChBC,OAAO,GAAG,UAAU;EACpBC,IAAI,GAAG,IAAI;EACXC,QAAQ,GAAG,KAAK;EAChBC,UAAU;EACVC,IAAI;EACJC,SAAS;EACTC,WAAW;EACXC,SAAS;EACT,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT;EACA,MAAMC,WAAW,GAAG;IAClBC,EAAE,EAAE,qBAAqB;IACzBC,EAAE,EAAE,mBAAmB;IACvBC,EAAE,EAAE;EACN,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQd,OAAO;MACb,KAAK,QAAQ;QACX,OAAO,mEACLF,KAAK,GAAG,gBAAgB,GAAG,0BAA0B,EACrD;MACJ,KAAK,YAAY;QACf,OAAO,iEACLA,KAAK,GAAG,gBAAgB,GAAG,0CAA0C,EACrE;MACJ,KAAK,UAAU;MACf;QACE,OAAO,YACLA,KAAK,GACD,wDAAwD,GACxD,uFAAuF,EAC3F;IACN;EACF,CAAC;;EAED;EACA,MAAMiB,WAAW,GAAG,wEAAwE;EAC5F,MAAMC,WAAW,GAAGZ,IAAI,GAAG,OAAO,GAAG,EAAE;EACvC,MAAMa,gBAAgB,GAAGZ,SAAS,GAAG,OAAO,GAAG,EAAE;EAEjD,oBACEf,OAAA;IAAKiB,SAAS,EAAE,GAAGR,SAAS,GAAG,QAAQ,GAAG,QAAQ,IAAIQ,SAAS,IAAI,EAAE,EAAG;IAAAW,QAAA,GACrEzB,KAAK,iBACJH,OAAA;MACE6B,OAAO,EAAEzB,EAAG;MACZa,SAAS,EAAC,gDAAgD;MAAAW,QAAA,EAEzDzB;IAAK;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAEDjC,OAAA;MAAKiB,SAAS,EAAC,UAAU;MAAAW,QAAA,GACtBd,IAAI,iBACHd,OAAA;QAAKiB,SAAS,EAAE,GAAGQ,WAAW,SAAU;QAAAG,QAAA,EACrCd;MAAI;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACN,eAEDjC,OAAA;QACEI,EAAE,EAAEA,EAAG;QACPC,IAAI,EAAEA,IAAK;QACXC,IAAI,EAAEA,IAAK;QACXC,WAAW,EAAEA,WAAY;QACzBK,QAAQ,EAAEA,QAAS;QACnBO,GAAG,EAAEA,GAAI;QACTF,SAAS,EAAE;AACrB;AACA;AACA;AACA;AACA;AACA,cAAcG,WAAW,CAACT,IAAI,CAAC;AAC/B,cAAca,iBAAiB,CAAC,CAAC;AACjC,cAAcE,WAAW;AACzB,cAAcC,gBAAgB;AAC9B,cAAcf,QAAQ,GAAG,2CAA2C,GAAG,EAAE;AACzE,WAAY;QAAA,GACEM;MAAI;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,EAEDlB,SAAS,iBACRf,OAAA;QACEiB,SAAS,EAAE,gDAAgDD,WAAW,GAAG,gBAAgB,GAAG,qBAAqB,gBAAiB;QAClIkB,OAAO,EAAElB,WAAY;QAAAY,QAAA,EAEpBb;MAAS;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAEL,CAACzB,KAAK,IAAIK,UAAU,kBACnBb,OAAA;MAAKiB,SAAS,EAAC,MAAM;MAAAW,QAAA,EAClBpB,KAAK,gBACJR,OAAA;QAAGiB,SAAS,EAAC,sBAAsB;QAAAW,QAAA,EAAEpB;MAAK;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,GAC7CpB,UAAU,gBACZb,OAAA;QAAGiB,SAAS,EAAC,uBAAuB;QAAAW,QAAA,EAAEf;MAAU;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,GACnD;IAAI;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC,CAAC;AAACE,GAAA,GAhHGlC,SAAS;AAkHfA,SAAS,CAACmC,WAAW,GAAG,WAAW;AAEnC,eAAenC,SAAS;AAAC,IAAAC,EAAA,EAAAiC,GAAA;AAAAE,YAAA,CAAAnC,EAAA;AAAAmC,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}