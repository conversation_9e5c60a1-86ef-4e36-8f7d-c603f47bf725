{"ast": null, "code": "var _jsxFileName = \"C:\\\\claude\\\\burky_root_web\\\\client\\\\src\\\\pages\\\\admin\\\\roles\\\\RoleFormPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate, Link } from 'react-router-dom';\nimport { useForm } from 'react-hook-form';\nimport toast from 'react-hot-toast';\nimport api from '../../../services/api';\nimport { useAuth } from '../../../hooks/useAuth';\nimport { FormInput, FormTextarea, FormCheckbox } from '../../../components/ui';\n\n// İkon importları\nimport { TagIcon, DocumentTextIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RoleFormPage = () => {\n  _s();\n  var _errors$name;\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const isEditMode = !!id;\n  const [isLoading, setIsLoading] = useState(false);\n  const [isSaving, setIsSaving] = useState(false);\n  const [isSavingPermissions, setIsSavingPermissions] = useState(false);\n  const [permissionMatrix, setPermissionMatrix] = useState([]);\n  const [selectedPermissions, setSelectedPermissions] = useState([]);\n  const {\n    refreshPermissions\n  } = useAuth();\n  const {\n    register,\n    handleSubmit,\n    setValue,\n    formState: {\n      errors\n    }\n  } = useForm();\n  useEffect(() => {\n    const fetchRoleData = async () => {\n      if (isEditMode) {\n        try {\n          setIsLoading(true);\n\n          // Rol bilgilerini al\n          const {\n            data\n          } = await api.get(`/roles/${id}`);\n          setValue('name', data.name);\n          setValue('description', data.description);\n          setValue('isActive', data.isActive);\n          setValue('isDefault', data.isDefault);\n\n          // İzin matrisini al\n          const matrixResponse = await api.get(`/roles/${id}/permissions-matrix`);\n          setPermissionMatrix(matrixResponse.data || []);\n\n          // Seçili izinleri belirle\n          const initialPermissions = matrixResponse.data.flatMap(page => page.permissions).filter(permission => permission.isGranted).map(permission => permission.id);\n          setSelectedPermissions(initialPermissions);\n        } catch (error) {\n          console.error('Error loading role data:', error);\n          toast.error('Rol bilgileri yüklenirken bir hata oluştu');\n        } finally {\n          setIsLoading(false);\n        }\n      }\n    };\n    fetchRoleData();\n  }, [id, isEditMode, setValue]);\n  const onSubmit = async data => {\n    try {\n      setIsSaving(true);\n      const roleData = {\n        name: data.name,\n        description: data.description,\n        isActive: data.isActive,\n        isDefault: data.isDefault\n      };\n      let response;\n      if (isEditMode) {\n        response = await api.put(`/roles/${id}`, roleData);\n        toast.success('Rol bilgileri güncellendi');\n      } else {\n        var _response$data;\n        response = await api.post('/roles', roleData);\n        const newRoleId = (_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.id;\n        if (newRoleId) {\n          // Yeni oluşturulan rol için yönlendirme\n          toast.success('Rol oluşturuldu, şimdi izinleri düzenleyebilirsiniz');\n          navigate(`/admin/roles/${newRoleId}`);\n        } else {\n          toast.success('Rol oluşturuldu');\n          navigate('/admin/roles');\n        }\n      }\n    } catch (error) {\n      console.error('Error saving role:', error);\n      toast.error('Rol kaydedilirken bir hata oluştu');\n    } finally {\n      setIsSaving(false);\n    }\n  };\n  const handlePermissionChange = permissionId => {\n    setSelectedPermissions(prev => {\n      if (prev.includes(permissionId)) {\n        return prev.filter(id => id !== permissionId);\n      } else {\n        return [...prev, permissionId];\n      }\n    });\n  };\n  const handleSelectAllPage = (pageId, isSelected) => {\n    var _permissionMatrix$fin;\n    const pagePermissions = ((_permissionMatrix$fin = permissionMatrix.find(page => page.id === pageId)) === null || _permissionMatrix$fin === void 0 ? void 0 : _permissionMatrix$fin.permissions.map(p => p.id)) || [];\n    setSelectedPermissions(prev => {\n      if (isSelected) {\n        return [...new Set([...prev, ...pagePermissions])];\n      } else {\n        return prev.filter(id => !pagePermissions.includes(id));\n      }\n    });\n  };\n  const handleSelectAllPermissionType = permissionKey => {\n    const permissionsOfType = permissionMatrix.flatMap(page => page.permissions).filter(p => p.key === permissionKey).map(p => p.id);\n    const allSelected = permissionsOfType.every(id => selectedPermissions.includes(id));\n    setSelectedPermissions(prev => {\n      if (allSelected) {\n        return prev.filter(id => !permissionsOfType.includes(id));\n      } else {\n        return [...new Set([...prev, ...permissionsOfType])];\n      }\n    });\n  };\n  const savePermissions = async () => {\n    if (!isEditMode) return;\n    try {\n      setIsSavingPermissions(true);\n      console.log('Saving permissions:', selectedPermissions);\n\n      // İzinleri kaydet\n      await api.post(`/roles/${id}/permissions`, {\n        permissionIds: selectedPermissions\n      });\n\n      // Kullanıcı izinlerini yenile - bu kullanıcının rolü değiştiyse etkili olur\n      await refreshPermissions();\n      toast.success('İzinler güncellendi, yetki değişikliklerinin etkili olması için sayfayı yenileyin');\n    } catch (error) {\n      console.error('Error saving permissions:', error);\n      toast.error('İzinler kaydedilirken bir hata oluştu');\n    } finally {\n      setIsSavingPermissions(false);\n    }\n  };\n\n  // Benzersiz izin tiplerini (CREATE, READ, vb.) al\n  const permissionKeys = [...new Set(permissionMatrix.flatMap(page => page.permissions).map(permission => permission.key))];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-900\",\n          children: isEditMode ? 'Rol Düzenle' : 'Yeni Rol'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-gray-500\",\n          children: isEditMode ? 'Rol bilgilerini ve izinlerini güncelleyin' : 'Sisteme yeni bir rol ekleyin'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/admin/roles\",\n        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n        children: \"Geri D\\xF6n\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this), isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center items-center p-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-12 w-12 animate-spin rounded-full border-b-2 border-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"ml-2\",\n        children: \"Y\\xFCkleniyor...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-md p-6 mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit(onSubmit),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(FormInput, {\n              id: \"name\",\n              name: \"name\",\n              label: \"Rol Ad\\u0131\",\n              placeholder: \"Rol ad\\u0131n\\u0131 girin\",\n              icon: /*#__PURE__*/_jsxDEV(TagIcon, {\n                className: \"h-5 w-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 25\n              }, this),\n              error: (_errors$name = errors.name) === null || _errors$name === void 0 ? void 0 : _errors$name.message,\n              variant: \"filled\",\n              ...register('name', {\n                required: 'Rol adı gereklidir',\n                minLength: {\n                  value: 2,\n                  message: 'Rol adı en az 2 karakter olmalıdır'\n                }\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(FormCheckbox, {\n                id: \"isActive\",\n                name: \"isActive\",\n                label: \"Aktif\",\n                helperText: \"Rol\\xFC aktif veya pasif yap\\u0131n\",\n                defaultChecked: true,\n                ...register('isActive')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(FormCheckbox, {\n                id: \"isDefault\",\n                name: \"isDefault\",\n                label: \"Varsay\\u0131lan Rol\",\n                helperText: \"Bu rol yeni kullan\\u0131c\\u0131lara otomatik olarak atan\\u0131r\",\n                ...register('isDefault')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FormTextarea, {\n            id: \"description\",\n            name: \"description\",\n            label: \"A\\xE7\\u0131klama\",\n            placeholder: \"Rol a\\xE7\\u0131klamas\\u0131 girin\",\n            icon: /*#__PURE__*/_jsxDEV(DocumentTextIcon, {\n              className: \"h-5 w-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 23\n            }, this),\n            variant: \"filled\",\n            rows: 3,\n            ...register('description')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 flex justify-end\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: isSaving,\n              className: \"inline-flex justify-center rounded-lg bg-primary-600 px-4 py-2.5 text-sm font-semibold text-white shadow-md hover:bg-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors duration-200 disabled:opacity-70 disabled:cursor-not-allowed\",\n              children: isSaving ? 'Kaydediliyor...' : 'Rol Bilgilerini Kaydet'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 11\n      }, this), isEditMode && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium leading-6 text-gray-900\",\n            children: \"Rol \\u0130zinleri\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white shadow-md rounded-lg overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-4 py-5 sm:px-6 bg-gray-50 border-b border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 max-w-2xl text-sm text-gray-500\",\n              children: \"Bu rol\\xFCn sayfalara g\\xF6re izinlerini d\\xFCzenleyin. \\u0130zinleri g\\xFCncelledikten sonra \\\"\\u0130zinleri Kaydet\\\" butonuna t\\u0131klay\\u0131n.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 max-w-2xl text-sm text-gray-500 font-medium text-yellow-600\",\n              children: \"Not: \\u0130zin de\\u011Fi\\u015Fiklikleri sayfay\\u0131 yeniledi\\u011Finizde etkili olacakt\\u0131r.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border-t border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"min-w-full divide-y divide-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                className: \"bg-gray-50\",\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    scope: \"col\",\n                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                    children: \"Sayfa / \\u0130zin\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 25\n                  }, this), permissionKeys.map(key => /*#__PURE__*/_jsxDEV(\"th\", {\n                    scope: \"col\",\n                    className: \"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex flex-col items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: key\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 291,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        type: \"button\",\n                        onClick: () => handleSelectAllPermissionType(key),\n                        className: \"mt-1 inline-flex items-center px-2.5 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                        children: \"T\\xFCm\\xFCn\\xFC Se\\xE7/Kald\\u0131r\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 292,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 290,\n                      columnNumber: 29\n                    }, this)\n                  }, key, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 285,\n                    columnNumber: 27\n                  }, this)), /*#__PURE__*/_jsxDEV(\"th\", {\n                    scope: \"col\",\n                    className: \"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                    children: \"T\\xFCm\\xFC\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 302,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                className: \"bg-white divide-y divide-gray-200\",\n                children: permissionMatrix.length > 0 ? permissionMatrix.map(page => {\n                  const pagePermissions = page.permissions.map(p => p.id);\n                  const isAllPageSelected = pagePermissions.length > 0 && pagePermissions.every(id => selectedPermissions.includes(id));\n                  return /*#__PURE__*/_jsxDEV(\"tr\", {\n                    className: \"hover:bg-gray-50\",\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                      children: page.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 316,\n                      columnNumber: 31\n                    }, this), permissionKeys.map(key => {\n                      const permission = page.permissions.find(p => p.key === key);\n                      if (!permission) {\n                        return /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500\",\n                          children: \"-\"\n                        }, `${page.id}-${key}`, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 325,\n                          columnNumber: 37\n                        }, this);\n                      }\n                      return /*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500\",\n                        children: /*#__PURE__*/_jsxDEV(\"input\", {\n                          type: \"checkbox\",\n                          className: \"h-4 w-4 rounded text-primary-600 focus:ring-primary-500 border-gray-300\",\n                          checked: selectedPermissions.includes(permission.id),\n                          onChange: () => handlePermissionChange(permission.id)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 333,\n                          columnNumber: 37\n                        }, this)\n                      }, `${page.id}-${key}`, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 332,\n                        columnNumber: 35\n                      }, this);\n                    }), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500\",\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"checkbox\",\n                        className: \"h-4 w-4 rounded text-primary-600 focus:ring-primary-500 border-gray-300\",\n                        checked: isAllPageSelected,\n                        onChange: () => handleSelectAllPage(page.id, !isAllPageSelected)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 344,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 343,\n                      columnNumber: 31\n                    }, this)]\n                  }, page.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 29\n                  }, this);\n                }) : /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: permissionKeys.length + 2,\n                    className: \"px-6 py-4 text-center text-sm text-gray-500\",\n                    children: \"Sayfa veya izin bulunamad\\u0131.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-4 py-3 bg-gray-50 text-right border-t border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: savePermissions,\n              disabled: isSavingPermissions,\n              className: \"inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\",\n              children: isSavingPermissions ? 'Kaydediliyor...' : 'İzinleri Kaydet'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 176,\n    columnNumber: 5\n  }, this);\n};\n_s(RoleFormPage, \"JvwREvLlgEXNGYcwEZi6FVfT8iA=\", false, function () {\n  return [useParams, useNavigate, useAuth, useForm];\n});\n_c = RoleFormPage;\nexport default RoleFormPage;\nvar _c;\n$RefreshReg$(_c, \"RoleFormPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "Link", "useForm", "toast", "api", "useAuth", "FormInput", "FormTextarea", "FormCheckbox", "TagIcon", "DocumentTextIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RoleFormPage", "_s", "_errors$name", "id", "navigate", "isEditMode", "isLoading", "setIsLoading", "isSaving", "setIsSaving", "isSavingPermissions", "setIsSavingPermissions", "permissionMatrix", "setPermissionMatrix", "selectedPermissions", "setSelectedPermissions", "refreshPermissions", "register", "handleSubmit", "setValue", "formState", "errors", "fetchRoleData", "data", "get", "name", "description", "isActive", "isDefault", "matrixResponse", "initialPermissions", "flatMap", "page", "permissions", "filter", "permission", "isGranted", "map", "error", "console", "onSubmit", "roleData", "response", "put", "success", "_response$data", "post", "newRoleId", "handlePermissionChange", "permissionId", "prev", "includes", "handleSelectAllPage", "pageId", "isSelected", "_permissionMatrix$fin", "pagePermissions", "find", "p", "Set", "handleSelectAllPermissionType", "<PERSON><PERSON><PERSON>", "permissionsOfType", "key", "allSelected", "every", "savePermissions", "log", "permissionIds", "<PERSON><PERSON><PERSON><PERSON>", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "label", "placeholder", "icon", "message", "variant", "required", "<PERSON><PERSON><PERSON><PERSON>", "value", "helperText", "defaultChecked", "rows", "type", "disabled", "scope", "onClick", "length", "isAllPageSelected", "checked", "onChange", "colSpan", "_c", "$RefreshReg$"], "sources": ["C:/claude/burky_root_web/client/src/pages/admin/roles/RoleFormPage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate, Link } from 'react-router-dom';\nimport { useForm } from 'react-hook-form';\nimport toast from 'react-hot-toast';\nimport api from '../../../services/api';\nimport { useAuth } from '../../../hooks/useAuth';\nimport { FormInput, FormTextarea, FormCheckbox } from '../../../components/ui';\n\n// İkon importları\nimport { TagIcon, DocumentTextIcon } from '@heroicons/react/24/outline';\n\nconst RoleFormPage = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const isEditMode = !!id;\n  const [isLoading, setIsLoading] = useState(false);\n  const [isSaving, setIsSaving] = useState(false);\n  const [isSavingPermissions, setIsSavingPermissions] = useState(false);\n  const [permissionMatrix, setPermissionMatrix] = useState([]);\n  const [selectedPermissions, setSelectedPermissions] = useState([]);\n  const { refreshPermissions } = useAuth();\n  \n  const {\n    register,\n    handleSubmit,\n    setValue,\n    formState: { errors }\n  } = useForm();\n  \n  useEffect(() => {\n    const fetchRoleData = async () => {\n      if (isEditMode) {\n        try {\n          setIsLoading(true);\n          \n          // Rol bilgilerini al\n          const { data } = await api.get(`/roles/${id}`);\n          setValue('name', data.name);\n          setValue('description', data.description);\n          setValue('isActive', data.isActive);\n          setValue('isDefault', data.isDefault);\n          \n          // İzin matrisini al\n          const matrixResponse = await api.get(`/roles/${id}/permissions-matrix`);\n          setPermissionMatrix(matrixResponse.data || []);\n          \n          // Seçili izinleri belirle\n          const initialPermissions = matrixResponse.data\n            .flatMap(page => page.permissions)\n            .filter(permission => permission.isGranted)\n            .map(permission => permission.id);\n          \n          setSelectedPermissions(initialPermissions);\n        } catch (error) {\n          console.error('Error loading role data:', error);\n          toast.error('Rol bilgileri yüklenirken bir hata oluştu');\n        } finally {\n          setIsLoading(false);\n        }\n      }\n    };\n\n    fetchRoleData();\n  }, [id, isEditMode, setValue]);\n  \n  const onSubmit = async (data) => {\n    try {\n      setIsSaving(true);\n      \n      const roleData = {\n        name: data.name,\n        description: data.description,\n        isActive: data.isActive,\n        isDefault: data.isDefault\n      };\n      \n      let response;\n      if (isEditMode) {\n        response = await api.put(`/roles/${id}`, roleData);\n        toast.success('Rol bilgileri güncellendi');\n      } else {\n        response = await api.post('/roles', roleData);\n        const newRoleId = response.data?.id;\n        \n        if (newRoleId) {\n          // Yeni oluşturulan rol için yönlendirme\n          toast.success('Rol oluşturuldu, şimdi izinleri düzenleyebilirsiniz');\n          navigate(`/admin/roles/${newRoleId}`);\n        } else {\n          toast.success('Rol oluşturuldu');\n          navigate('/admin/roles');\n        }\n      }\n    } catch (error) {\n      console.error('Error saving role:', error);\n      toast.error('Rol kaydedilirken bir hata oluştu');\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const handlePermissionChange = (permissionId) => {\n    setSelectedPermissions(prev => {\n      if (prev.includes(permissionId)) {\n        return prev.filter(id => id !== permissionId);\n      } else {\n        return [...prev, permissionId];\n      }\n    });\n  };\n\n  const handleSelectAllPage = (pageId, isSelected) => {\n    const pagePermissions = permissionMatrix\n      .find(page => page.id === pageId)\n      ?.permissions.map(p => p.id) || [];\n    \n    setSelectedPermissions(prev => {\n      if (isSelected) {\n        return [...new Set([...prev, ...pagePermissions])];\n      } else {\n        return prev.filter(id => !pagePermissions.includes(id));\n      }\n    });\n  };\n\n  const handleSelectAllPermissionType = (permissionKey) => {\n    const permissionsOfType = permissionMatrix\n      .flatMap(page => page.permissions)\n      .filter(p => p.key === permissionKey)\n      .map(p => p.id);\n    \n    const allSelected = permissionsOfType.every(id => selectedPermissions.includes(id));\n    \n    setSelectedPermissions(prev => {\n      if (allSelected) {\n        return prev.filter(id => !permissionsOfType.includes(id));\n      } else {\n        return [...new Set([...prev, ...permissionsOfType])];\n      }\n    });\n  };\n\n  const savePermissions = async () => {\n    if (!isEditMode) return;\n    \n    try {\n      setIsSavingPermissions(true);\n      \n      console.log('Saving permissions:', selectedPermissions);\n      \n      // İzinleri kaydet\n      await api.post(`/roles/${id}/permissions`, {\n        permissionIds: selectedPermissions\n      });\n      \n      // Kullanıcı izinlerini yenile - bu kullanıcının rolü değiştiyse etkili olur\n      await refreshPermissions();\n      \n      toast.success('İzinler güncellendi, yetki değişikliklerinin etkili olması için sayfayı yenileyin');\n    } catch (error) {\n      console.error('Error saving permissions:', error);\n      toast.error('İzinler kaydedilirken bir hata oluştu');\n    } finally {\n      setIsSavingPermissions(false);\n    }\n  };\n  \n  // Benzersiz izin tiplerini (CREATE, READ, vb.) al\n  const permissionKeys = [...new Set(\n    permissionMatrix\n      .flatMap(page => page.permissions)\n      .map(permission => permission.key)\n  )];\n  \n  return (\n    <div>\n      <div className=\"flex justify-between items-center mb-4\">\n        <div>\n          <h2 className=\"text-xl font-semibold text-gray-900\">\n            {isEditMode ? 'Rol Düzenle' : 'Yeni Rol'}\n          </h2>\n          <p className=\"mt-1 text-sm text-gray-500\">\n            {isEditMode ? 'Rol bilgilerini ve izinlerini güncelleyin' : 'Sisteme yeni bir rol ekleyin'}\n          </p>\n        </div>\n        <Link\n          to=\"/admin/roles\"\n          className=\"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n        >\n          Geri Dön\n        </Link>\n      </div>\n      \n      {isLoading ? (\n        <div className=\"flex justify-center items-center p-8\">\n          <div className=\"h-12 w-12 animate-spin rounded-full border-b-2 border-primary-600\"></div>\n          <p className=\"ml-2\">Yükleniyor...</p>\n        </div>\n      ) : (\n        <>\n          <div className=\"bg-white rounded-lg shadow-md p-6 mb-8\">\n            <form onSubmit={handleSubmit(onSubmit)}>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n                <FormInput\n                  id=\"name\"\n                  name=\"name\"\n                  label=\"Rol Adı\"\n                  placeholder=\"Rol adını girin\"\n                  icon={<TagIcon className=\"h-5 w-5\" />}\n                  error={errors.name?.message}\n                  variant=\"filled\"\n                  {...register('name', { \n                    required: 'Rol adı gereklidir',\n                    minLength: { value: 2, message: 'Rol adı en az 2 karakter olmalıdır' }\n                  })}\n                />\n\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <FormCheckbox\n                    id=\"isActive\"\n                    name=\"isActive\"\n                    label=\"Aktif\"\n                    helperText=\"Rolü aktif veya pasif yapın\"\n                    defaultChecked={true}\n                    {...register('isActive')}\n                  />\n                  \n                  <FormCheckbox\n                    id=\"isDefault\"\n                    name=\"isDefault\"\n                    label=\"Varsayılan Rol\"\n                    helperText=\"Bu rol yeni kullanıcılara otomatik olarak atanır\"\n                    {...register('isDefault')}\n                  />\n                </div>\n              </div>\n              \n              <FormTextarea\n                id=\"description\"\n                name=\"description\"\n                label=\"Açıklama\"\n                placeholder=\"Rol açıklaması girin\"\n                icon={<DocumentTextIcon className=\"h-5 w-5\" />}\n                variant=\"filled\"\n                rows={3}\n                {...register('description')}\n              />\n              \n              <div className=\"mt-6 flex justify-end\">\n                <button\n                  type=\"submit\"\n                  disabled={isSaving}\n                  className=\"inline-flex justify-center rounded-lg bg-primary-600 px-4 py-2.5 text-sm font-semibold text-white shadow-md hover:bg-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors duration-200 disabled:opacity-70 disabled:cursor-not-allowed\"\n                >\n                  {isSaving ? 'Kaydediliyor...' : 'Rol Bilgilerini Kaydet'}\n                </button>\n              </div>\n            </form>\n          </div>\n\n          {isEditMode && (\n            <div className=\"mt-8\">\n              <div className=\"flex justify-between items-center mb-4\">\n                <h3 className=\"text-lg font-medium leading-6 text-gray-900\">Rol İzinleri</h3>\n              </div>\n              \n              <div className=\"bg-white shadow-md rounded-lg overflow-hidden\">\n                <div className=\"px-4 py-5 sm:px-6 bg-gray-50 border-b border-gray-200\">\n                  <p className=\"mt-1 max-w-2xl text-sm text-gray-500\">\n                    Bu rolün sayfalara göre izinlerini düzenleyin. İzinleri güncelledikten sonra \"İzinleri Kaydet\" butonuna tıklayın.\n                  </p>\n                  <p className=\"mt-1 max-w-2xl text-sm text-gray-500 font-medium text-yellow-600\">\n                    Not: İzin değişiklikleri sayfayı yenilediğinizde etkili olacaktır.\n                  </p>\n                </div>\n\n                <div className=\"border-t border-gray-200\">\n                  <table className=\"min-w-full divide-y divide-gray-200\">\n                    <thead className=\"bg-gray-50\">\n                      <tr>\n                        <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Sayfa / İzin\n                        </th>\n                        {permissionKeys.map(key => (\n                          <th \n                            key={key}\n                            scope=\"col\" \n                            className=\"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider\"\n                          >\n                            <div className=\"flex flex-col items-center\">\n                              <span>{key}</span>\n                              <button\n                                type=\"button\"\n                                onClick={() => handleSelectAllPermissionType(key)}\n                                className=\"mt-1 inline-flex items-center px-2.5 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n                              >\n                                Tümünü Seç/Kaldır\n                              </button>\n                            </div>\n                          </th>\n                        ))}\n                        <th scope=\"col\" className=\"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Tümü\n                        </th>\n                      </tr>\n                    </thead>\n                    <tbody className=\"bg-white divide-y divide-gray-200\">\n                      {permissionMatrix.length > 0 ? (\n                        permissionMatrix.map(page => {\n                          const pagePermissions = page.permissions.map(p => p.id);\n                          const isAllPageSelected = pagePermissions.length > 0 && \n                            pagePermissions.every(id => selectedPermissions.includes(id));\n                          \n                          return (\n                            <tr key={page.id} className=\"hover:bg-gray-50\">\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                                {page.name}\n                              </td>\n                              \n                              {permissionKeys.map(key => {\n                                const permission = page.permissions.find(p => p.key === key);\n                                \n                                if (!permission) {\n                                  return (\n                                    <td key={`${page.id}-${key}`} className=\"px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500\">\n                                      -\n                                    </td>\n                                  );\n                                }\n                                \n                                return (\n                                  <td key={`${page.id}-${key}`} className=\"px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500\">\n                                    <input\n                                      type=\"checkbox\"\n                                      className=\"h-4 w-4 rounded text-primary-600 focus:ring-primary-500 border-gray-300\"\n                                      checked={selectedPermissions.includes(permission.id)}\n                                      onChange={() => handlePermissionChange(permission.id)}\n                                    />\n                                  </td>\n                                );\n                              })}\n                              \n                              <td className=\"px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500\">\n                                <input\n                                  type=\"checkbox\"\n                                  className=\"h-4 w-4 rounded text-primary-600 focus:ring-primary-500 border-gray-300\"\n                                  checked={isAllPageSelected}\n                                  onChange={() => handleSelectAllPage(page.id, !isAllPageSelected)}\n                                />\n                              </td>\n                            </tr>\n                          );\n                        })\n                      ) : (\n                        <tr>\n                          <td colSpan={permissionKeys.length + 2} className=\"px-6 py-4 text-center text-sm text-gray-500\">\n                            Sayfa veya izin bulunamadı.\n                          </td>\n                        </tr>\n                      )}\n                    </tbody>\n                  </table>\n                </div>\n                \n                <div className=\"px-4 py-3 bg-gray-50 text-right border-t border-gray-200\">\n                  <button\n                    type=\"button\"\n                    onClick={savePermissions}\n                    disabled={isSavingPermissions}\n                    className=\"inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\"\n                  >\n                    {isSavingPermissions ? 'Kaydediliyor...' : 'İzinleri Kaydet'}\n                  </button>\n                </div>\n              </div>\n            </div>\n          )}\n        </>\n      )}\n    </div>\n  );\n};\n\nexport default RoleFormPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AAC/D,SAASC,OAAO,QAAQ,iBAAiB;AACzC,OAAOC,KAAK,MAAM,iBAAiB;AACnC,OAAOC,GAAG,MAAM,uBAAuB;AACvC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,SAAS,EAAEC,YAAY,EAAEC,YAAY,QAAQ,wBAAwB;;AAE9E;AACA,SAASC,OAAO,EAAEC,gBAAgB,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExE,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,YAAA;EACzB,MAAM;IAAEC;EAAG,CAAC,GAAGnB,SAAS,CAAC,CAAC;EAC1B,MAAMoB,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAMoB,UAAU,GAAG,CAAC,CAACF,EAAE;EACvB,MAAM,CAACG,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC0B,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC4B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC8B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACgC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM;IAAEkC;EAAmB,CAAC,GAAG1B,OAAO,CAAC,CAAC;EAExC,MAAM;IACJ2B,QAAQ;IACRC,YAAY;IACZC,QAAQ;IACRC,SAAS,EAAE;MAAEC;IAAO;EACtB,CAAC,GAAGlC,OAAO,CAAC,CAAC;EAEbJ,SAAS,CAAC,MAAM;IACd,MAAMuC,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAIjB,UAAU,EAAE;QACd,IAAI;UACFE,YAAY,CAAC,IAAI,CAAC;;UAElB;UACA,MAAM;YAAEgB;UAAK,CAAC,GAAG,MAAMlC,GAAG,CAACmC,GAAG,CAAC,UAAUrB,EAAE,EAAE,CAAC;UAC9CgB,QAAQ,CAAC,MAAM,EAAEI,IAAI,CAACE,IAAI,CAAC;UAC3BN,QAAQ,CAAC,aAAa,EAAEI,IAAI,CAACG,WAAW,CAAC;UACzCP,QAAQ,CAAC,UAAU,EAAEI,IAAI,CAACI,QAAQ,CAAC;UACnCR,QAAQ,CAAC,WAAW,EAAEI,IAAI,CAACK,SAAS,CAAC;;UAErC;UACA,MAAMC,cAAc,GAAG,MAAMxC,GAAG,CAACmC,GAAG,CAAC,UAAUrB,EAAE,qBAAqB,CAAC;UACvEU,mBAAmB,CAACgB,cAAc,CAACN,IAAI,IAAI,EAAE,CAAC;;UAE9C;UACA,MAAMO,kBAAkB,GAAGD,cAAc,CAACN,IAAI,CAC3CQ,OAAO,CAACC,IAAI,IAAIA,IAAI,CAACC,WAAW,CAAC,CACjCC,MAAM,CAACC,UAAU,IAAIA,UAAU,CAACC,SAAS,CAAC,CAC1CC,GAAG,CAACF,UAAU,IAAIA,UAAU,CAAChC,EAAE,CAAC;UAEnCY,sBAAsB,CAACe,kBAAkB,CAAC;QAC5C,CAAC,CAAC,OAAOQ,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;UAChDlD,KAAK,CAACkD,KAAK,CAAC,2CAA2C,CAAC;QAC1D,CAAC,SAAS;UACR/B,YAAY,CAAC,KAAK,CAAC;QACrB;MACF;IACF,CAAC;IAEDe,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACnB,EAAE,EAAEE,UAAU,EAAEc,QAAQ,CAAC,CAAC;EAE9B,MAAMqB,QAAQ,GAAG,MAAOjB,IAAI,IAAK;IAC/B,IAAI;MACFd,WAAW,CAAC,IAAI,CAAC;MAEjB,MAAMgC,QAAQ,GAAG;QACfhB,IAAI,EAAEF,IAAI,CAACE,IAAI;QACfC,WAAW,EAAEH,IAAI,CAACG,WAAW;QAC7BC,QAAQ,EAAEJ,IAAI,CAACI,QAAQ;QACvBC,SAAS,EAAEL,IAAI,CAACK;MAClB,CAAC;MAED,IAAIc,QAAQ;MACZ,IAAIrC,UAAU,EAAE;QACdqC,QAAQ,GAAG,MAAMrD,GAAG,CAACsD,GAAG,CAAC,UAAUxC,EAAE,EAAE,EAAEsC,QAAQ,CAAC;QAClDrD,KAAK,CAACwD,OAAO,CAAC,2BAA2B,CAAC;MAC5C,CAAC,MAAM;QAAA,IAAAC,cAAA;QACLH,QAAQ,GAAG,MAAMrD,GAAG,CAACyD,IAAI,CAAC,QAAQ,EAAEL,QAAQ,CAAC;QAC7C,MAAMM,SAAS,IAAAF,cAAA,GAAGH,QAAQ,CAACnB,IAAI,cAAAsB,cAAA,uBAAbA,cAAA,CAAe1C,EAAE;QAEnC,IAAI4C,SAAS,EAAE;UACb;UACA3D,KAAK,CAACwD,OAAO,CAAC,qDAAqD,CAAC;UACpExC,QAAQ,CAAC,gBAAgB2C,SAAS,EAAE,CAAC;QACvC,CAAC,MAAM;UACL3D,KAAK,CAACwD,OAAO,CAAC,iBAAiB,CAAC;UAChCxC,QAAQ,CAAC,cAAc,CAAC;QAC1B;MACF;IACF,CAAC,CAAC,OAAOkC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1ClD,KAAK,CAACkD,KAAK,CAAC,mCAAmC,CAAC;IAClD,CAAC,SAAS;MACR7B,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAMuC,sBAAsB,GAAIC,YAAY,IAAK;IAC/ClC,sBAAsB,CAACmC,IAAI,IAAI;MAC7B,IAAIA,IAAI,CAACC,QAAQ,CAACF,YAAY,CAAC,EAAE;QAC/B,OAAOC,IAAI,CAAChB,MAAM,CAAC/B,EAAE,IAAIA,EAAE,KAAK8C,YAAY,CAAC;MAC/C,CAAC,MAAM;QACL,OAAO,CAAC,GAAGC,IAAI,EAAED,YAAY,CAAC;MAChC;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMG,mBAAmB,GAAGA,CAACC,MAAM,EAAEC,UAAU,KAAK;IAAA,IAAAC,qBAAA;IAClD,MAAMC,eAAe,GAAG,EAAAD,qBAAA,GAAA3C,gBAAgB,CACrC6C,IAAI,CAACzB,IAAI,IAAIA,IAAI,CAAC7B,EAAE,KAAKkD,MAAM,CAAC,cAAAE,qBAAA,uBADXA,qBAAA,CAEpBtB,WAAW,CAACI,GAAG,CAACqB,CAAC,IAAIA,CAAC,CAACvD,EAAE,CAAC,KAAI,EAAE;IAEpCY,sBAAsB,CAACmC,IAAI,IAAI;MAC7B,IAAII,UAAU,EAAE;QACd,OAAO,CAAC,GAAG,IAAIK,GAAG,CAAC,CAAC,GAAGT,IAAI,EAAE,GAAGM,eAAe,CAAC,CAAC,CAAC;MACpD,CAAC,MAAM;QACL,OAAON,IAAI,CAAChB,MAAM,CAAC/B,EAAE,IAAI,CAACqD,eAAe,CAACL,QAAQ,CAAChD,EAAE,CAAC,CAAC;MACzD;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMyD,6BAA6B,GAAIC,aAAa,IAAK;IACvD,MAAMC,iBAAiB,GAAGlD,gBAAgB,CACvCmB,OAAO,CAACC,IAAI,IAAIA,IAAI,CAACC,WAAW,CAAC,CACjCC,MAAM,CAACwB,CAAC,IAAIA,CAAC,CAACK,GAAG,KAAKF,aAAa,CAAC,CACpCxB,GAAG,CAACqB,CAAC,IAAIA,CAAC,CAACvD,EAAE,CAAC;IAEjB,MAAM6D,WAAW,GAAGF,iBAAiB,CAACG,KAAK,CAAC9D,EAAE,IAAIW,mBAAmB,CAACqC,QAAQ,CAAChD,EAAE,CAAC,CAAC;IAEnFY,sBAAsB,CAACmC,IAAI,IAAI;MAC7B,IAAIc,WAAW,EAAE;QACf,OAAOd,IAAI,CAAChB,MAAM,CAAC/B,EAAE,IAAI,CAAC2D,iBAAiB,CAACX,QAAQ,CAAChD,EAAE,CAAC,CAAC;MAC3D,CAAC,MAAM;QACL,OAAO,CAAC,GAAG,IAAIwD,GAAG,CAAC,CAAC,GAAGT,IAAI,EAAE,GAAGY,iBAAiB,CAAC,CAAC,CAAC;MACtD;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMI,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAAC7D,UAAU,EAAE;IAEjB,IAAI;MACFM,sBAAsB,CAAC,IAAI,CAAC;MAE5B4B,OAAO,CAAC4B,GAAG,CAAC,qBAAqB,EAAErD,mBAAmB,CAAC;;MAEvD;MACA,MAAMzB,GAAG,CAACyD,IAAI,CAAC,UAAU3C,EAAE,cAAc,EAAE;QACzCiE,aAAa,EAAEtD;MACjB,CAAC,CAAC;;MAEF;MACA,MAAME,kBAAkB,CAAC,CAAC;MAE1B5B,KAAK,CAACwD,OAAO,CAAC,mFAAmF,CAAC;IACpG,CAAC,CAAC,OAAON,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDlD,KAAK,CAACkD,KAAK,CAAC,uCAAuC,CAAC;IACtD,CAAC,SAAS;MACR3B,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAM0D,cAAc,GAAG,CAAC,GAAG,IAAIV,GAAG,CAChC/C,gBAAgB,CACbmB,OAAO,CAACC,IAAI,IAAIA,IAAI,CAACC,WAAW,CAAC,CACjCI,GAAG,CAACF,UAAU,IAAIA,UAAU,CAAC4B,GAAG,CACrC,CAAC,CAAC;EAEF,oBACElE,OAAA;IAAAyE,QAAA,gBACEzE,OAAA;MAAK0E,SAAS,EAAC,wCAAwC;MAAAD,QAAA,gBACrDzE,OAAA;QAAAyE,QAAA,gBACEzE,OAAA;UAAI0E,SAAS,EAAC,qCAAqC;UAAAD,QAAA,EAChDjE,UAAU,GAAG,aAAa,GAAG;QAAU;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eACL9E,OAAA;UAAG0E,SAAS,EAAC,4BAA4B;UAAAD,QAAA,EACtCjE,UAAU,GAAG,2CAA2C,GAAG;QAA8B;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACN9E,OAAA,CAACX,IAAI;QACH0F,EAAE,EAAC,cAAc;QACjBL,SAAS,EAAC,uNAAuN;QAAAD,QAAA,EAClO;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAELrE,SAAS,gBACRT,OAAA;MAAK0E,SAAS,EAAC,sCAAsC;MAAAD,QAAA,gBACnDzE,OAAA;QAAK0E,SAAS,EAAC;MAAmE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACzF9E,OAAA;QAAG0E,SAAS,EAAC,MAAM;QAAAD,QAAA,EAAC;MAAa;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC,gBAEN9E,OAAA,CAAAE,SAAA;MAAAuE,QAAA,gBACEzE,OAAA;QAAK0E,SAAS,EAAC,wCAAwC;QAAAD,QAAA,eACrDzE,OAAA;UAAM2C,QAAQ,EAAEtB,YAAY,CAACsB,QAAQ,CAAE;UAAA8B,QAAA,gBACrCzE,OAAA;YAAK0E,SAAS,EAAC,4CAA4C;YAAAD,QAAA,gBACzDzE,OAAA,CAACN,SAAS;cACRY,EAAE,EAAC,MAAM;cACTsB,IAAI,EAAC,MAAM;cACXoD,KAAK,EAAC,cAAS;cACfC,WAAW,EAAC,2BAAiB;cAC7BC,IAAI,eAAElF,OAAA,CAACH,OAAO;gBAAC6E,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACtCrC,KAAK,GAAApC,YAAA,GAAEmB,MAAM,CAACI,IAAI,cAAAvB,YAAA,uBAAXA,YAAA,CAAa8E,OAAQ;cAC5BC,OAAO,EAAC,QAAQ;cAAA,GACZhE,QAAQ,CAAC,MAAM,EAAE;gBACnBiE,QAAQ,EAAE,oBAAoB;gBAC9BC,SAAS,EAAE;kBAAEC,KAAK,EAAE,CAAC;kBAAEJ,OAAO,EAAE;gBAAqC;cACvE,CAAC;YAAC;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEF9E,OAAA;cAAK0E,SAAS,EAAC,wBAAwB;cAAAD,QAAA,gBACrCzE,OAAA,CAACJ,YAAY;gBACXU,EAAE,EAAC,UAAU;gBACbsB,IAAI,EAAC,UAAU;gBACfoD,KAAK,EAAC,OAAO;gBACbQ,UAAU,EAAC,qCAA6B;gBACxCC,cAAc,EAAE,IAAK;gBAAA,GACjBrE,QAAQ,CAAC,UAAU;cAAC;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eAEF9E,OAAA,CAACJ,YAAY;gBACXU,EAAE,EAAC,WAAW;gBACdsB,IAAI,EAAC,WAAW;gBAChBoD,KAAK,EAAC,qBAAgB;gBACtBQ,UAAU,EAAC,iEAAkD;gBAAA,GACzDpE,QAAQ,CAAC,WAAW;cAAC;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9E,OAAA,CAACL,YAAY;YACXW,EAAE,EAAC,aAAa;YAChBsB,IAAI,EAAC,aAAa;YAClBoD,KAAK,EAAC,kBAAU;YAChBC,WAAW,EAAC,mCAAsB;YAClCC,IAAI,eAAElF,OAAA,CAACF,gBAAgB;cAAC4E,SAAS,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC/CM,OAAO,EAAC,QAAQ;YAChBM,IAAI,EAAE,CAAE;YAAA,GACJtE,QAAQ,CAAC,aAAa;UAAC;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eAEF9E,OAAA;YAAK0E,SAAS,EAAC,uBAAuB;YAAAD,QAAA,eACpCzE,OAAA;cACE2F,IAAI,EAAC,QAAQ;cACbC,QAAQ,EAAEjF,QAAS;cACnB+D,SAAS,EAAC,4RAA4R;cAAAD,QAAA,EAErS9D,QAAQ,GAAG,iBAAiB,GAAG;YAAwB;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EAELtE,UAAU,iBACTR,OAAA;QAAK0E,SAAS,EAAC,MAAM;QAAAD,QAAA,gBACnBzE,OAAA;UAAK0E,SAAS,EAAC,wCAAwC;UAAAD,QAAA,eACrDzE,OAAA;YAAI0E,SAAS,EAAC,6CAA6C;YAAAD,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC,eAEN9E,OAAA;UAAK0E,SAAS,EAAC,+CAA+C;UAAAD,QAAA,gBAC5DzE,OAAA;YAAK0E,SAAS,EAAC,uDAAuD;YAAAD,QAAA,gBACpEzE,OAAA;cAAG0E,SAAS,EAAC,sCAAsC;cAAAD,QAAA,EAAC;YAEpD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ9E,OAAA;cAAG0E,SAAS,EAAC,kEAAkE;cAAAD,QAAA,EAAC;YAEhF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAEN9E,OAAA;YAAK0E,SAAS,EAAC,0BAA0B;YAAAD,QAAA,eACvCzE,OAAA;cAAO0E,SAAS,EAAC,qCAAqC;cAAAD,QAAA,gBACpDzE,OAAA;gBAAO0E,SAAS,EAAC,YAAY;gBAAAD,QAAA,eAC3BzE,OAAA;kBAAAyE,QAAA,gBACEzE,OAAA;oBAAI6F,KAAK,EAAC,KAAK;oBAACnB,SAAS,EAAC,gFAAgF;oBAAAD,QAAA,EAAC;kBAE3G;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACJN,cAAc,CAAChC,GAAG,CAAC0B,GAAG,iBACrBlE,OAAA;oBAEE6F,KAAK,EAAC,KAAK;oBACXnB,SAAS,EAAC,kFAAkF;oBAAAD,QAAA,eAE5FzE,OAAA;sBAAK0E,SAAS,EAAC,4BAA4B;sBAAAD,QAAA,gBACzCzE,OAAA;wBAAAyE,QAAA,EAAOP;sBAAG;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAClB9E,OAAA;wBACE2F,IAAI,EAAC,QAAQ;wBACbG,OAAO,EAAEA,CAAA,KAAM/B,6BAA6B,CAACG,GAAG,CAAE;wBAClDQ,SAAS,EAAC,6NAA6N;wBAAAD,QAAA,EACxO;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC,GAbDZ,GAAG;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAcN,CACL,CAAC,eACF9E,OAAA;oBAAI6F,KAAK,EAAC,KAAK;oBAACnB,SAAS,EAAC,kFAAkF;oBAAAD,QAAA,EAAC;kBAE7G;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACR9E,OAAA;gBAAO0E,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,EACjD1D,gBAAgB,CAACgF,MAAM,GAAG,CAAC,GAC1BhF,gBAAgB,CAACyB,GAAG,CAACL,IAAI,IAAI;kBAC3B,MAAMwB,eAAe,GAAGxB,IAAI,CAACC,WAAW,CAACI,GAAG,CAACqB,CAAC,IAAIA,CAAC,CAACvD,EAAE,CAAC;kBACvD,MAAM0F,iBAAiB,GAAGrC,eAAe,CAACoC,MAAM,GAAG,CAAC,IAClDpC,eAAe,CAACS,KAAK,CAAC9D,EAAE,IAAIW,mBAAmB,CAACqC,QAAQ,CAAChD,EAAE,CAAC,CAAC;kBAE/D,oBACEN,OAAA;oBAAkB0E,SAAS,EAAC,kBAAkB;oBAAAD,QAAA,gBAC5CzE,OAAA;sBAAI0E,SAAS,EAAC,+DAA+D;sBAAAD,QAAA,EAC1EtC,IAAI,CAACP;oBAAI;sBAAA+C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC,EAEJN,cAAc,CAAChC,GAAG,CAAC0B,GAAG,IAAI;sBACzB,MAAM5B,UAAU,GAAGH,IAAI,CAACC,WAAW,CAACwB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,GAAG,KAAKA,GAAG,CAAC;sBAE5D,IAAI,CAAC5B,UAAU,EAAE;wBACf,oBACEtC,OAAA;0BAA8B0E,SAAS,EAAC,+DAA+D;0BAAAD,QAAA,EAAC;wBAExG,GAFS,GAAGtC,IAAI,CAAC7B,EAAE,IAAI4D,GAAG,EAAE;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAExB,CAAC;sBAET;sBAEA,oBACE9E,OAAA;wBAA8B0E,SAAS,EAAC,+DAA+D;wBAAAD,QAAA,eACrGzE,OAAA;0BACE2F,IAAI,EAAC,UAAU;0BACfjB,SAAS,EAAC,yEAAyE;0BACnFuB,OAAO,EAAEhF,mBAAmB,CAACqC,QAAQ,CAAChB,UAAU,CAAChC,EAAE,CAAE;0BACrD4F,QAAQ,EAAEA,CAAA,KAAM/C,sBAAsB,CAACb,UAAU,CAAChC,EAAE;wBAAE;0BAAAqE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvD;sBAAC,GANK,GAAG3C,IAAI,CAAC7B,EAAE,IAAI4D,GAAG,EAAE;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAOxB,CAAC;oBAET,CAAC,CAAC,eAEF9E,OAAA;sBAAI0E,SAAS,EAAC,+DAA+D;sBAAAD,QAAA,eAC3EzE,OAAA;wBACE2F,IAAI,EAAC,UAAU;wBACfjB,SAAS,EAAC,yEAAyE;wBACnFuB,OAAO,EAAED,iBAAkB;wBAC3BE,QAAQ,EAAEA,CAAA,KAAM3C,mBAAmB,CAACpB,IAAI,CAAC7B,EAAE,EAAE,CAAC0F,iBAAiB;sBAAE;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA,GAnCE3C,IAAI,CAAC7B,EAAE;oBAAAqE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAoCZ,CAAC;gBAET,CAAC,CAAC,gBAEF9E,OAAA;kBAAAyE,QAAA,eACEzE,OAAA;oBAAImG,OAAO,EAAE3B,cAAc,CAACuB,MAAM,GAAG,CAAE;oBAACrB,SAAS,EAAC,6CAA6C;oBAAAD,QAAA,EAAC;kBAEhG;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACL;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEN9E,OAAA;YAAK0E,SAAS,EAAC,0DAA0D;YAAAD,QAAA,eACvEzE,OAAA;cACE2F,IAAI,EAAC,QAAQ;cACbG,OAAO,EAAEzB,eAAgB;cACzBuB,QAAQ,EAAE/E,mBAAoB;cAC9B6D,SAAS,EAAC,uPAAuP;cAAAD,QAAA,EAEhQ5D,mBAAmB,GAAG,iBAAiB,GAAG;YAAiB;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA,eACD,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC1E,EAAA,CAlXID,YAAY;EAAA,QACDhB,SAAS,EACPC,WAAW,EAOGK,OAAO,EAOlCH,OAAO;AAAA;AAAA8G,EAAA,GAhBPjG,YAAY;AAoXlB,eAAeA,YAAY;AAAC,IAAAiG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}