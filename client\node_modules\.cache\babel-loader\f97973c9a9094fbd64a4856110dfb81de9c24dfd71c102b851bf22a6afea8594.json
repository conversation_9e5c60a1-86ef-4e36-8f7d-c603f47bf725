{"ast": null, "code": "// src/queriesObserver.ts\nimport { notifyManager } from \"./notifyManager.js\";\nimport { QueryObserver } from \"./queryObserver.js\";\nimport { Subscribable } from \"./subscribable.js\";\nimport { replaceEqualDeep } from \"./utils.js\";\nfunction difference(array1, array2) {\n  return array1.filter(x => !array2.includes(x));\n}\nfunction replaceAt(array, index, value) {\n  const copy = array.slice(0);\n  copy[index] = value;\n  return copy;\n}\nvar QueriesObserver = class extends Subscribable {\n  #client;\n  #result;\n  #queries;\n  #options;\n  #observers;\n  #combinedResult;\n  #lastCombine;\n  #lastResult;\n  #observerMatches = [];\n  constructor(client, queries, options) {\n    super();\n    this.#client = client;\n    this.#options = options;\n    this.#queries = [];\n    this.#observers = [];\n    this.#result = [];\n    this.setQueries(queries);\n  }\n  onSubscribe() {\n    if (this.listeners.size === 1) {\n      this.#observers.forEach(observer => {\n        observer.subscribe(result => {\n          this.#onUpdate(observer, result);\n        });\n      });\n    }\n  }\n  onUnsubscribe() {\n    if (!this.listeners.size) {\n      this.destroy();\n    }\n  }\n  destroy() {\n    this.listeners = /* @__PURE__ */new Set();\n    this.#observers.forEach(observer => {\n      observer.destroy();\n    });\n  }\n  setQueries(queries, options, notifyOptions) {\n    this.#queries = queries;\n    this.#options = options;\n    if (process.env.NODE_ENV !== \"production\") {\n      const queryHashes = queries.map(query => this.#client.defaultQueryOptions(query).queryHash);\n      if (new Set(queryHashes).size !== queryHashes.length) {\n        console.warn(\"[QueriesObserver]: Duplicate Queries found. This might result in unexpected behavior.\");\n      }\n    }\n    notifyManager.batch(() => {\n      const prevObservers = this.#observers;\n      const newObserverMatches = this.#findMatchingObservers(this.#queries);\n      this.#observerMatches = newObserverMatches;\n      newObserverMatches.forEach(match => match.observer.setOptions(match.defaultedQueryOptions, notifyOptions));\n      const newObservers = newObserverMatches.map(match => match.observer);\n      const newResult = newObservers.map(observer => observer.getCurrentResult());\n      const hasIndexChange = newObservers.some((observer, index) => observer !== prevObservers[index]);\n      if (prevObservers.length === newObservers.length && !hasIndexChange) {\n        return;\n      }\n      this.#observers = newObservers;\n      this.#result = newResult;\n      if (!this.hasListeners()) {\n        return;\n      }\n      difference(prevObservers, newObservers).forEach(observer => {\n        observer.destroy();\n      });\n      difference(newObservers, prevObservers).forEach(observer => {\n        observer.subscribe(result => {\n          this.#onUpdate(observer, result);\n        });\n      });\n      this.#notify();\n    });\n  }\n  getCurrentResult() {\n    return this.#result;\n  }\n  getQueries() {\n    return this.#observers.map(observer => observer.getCurrentQuery());\n  }\n  getObservers() {\n    return this.#observers;\n  }\n  getOptimisticResult(queries, combine) {\n    const matches = this.#findMatchingObservers(queries);\n    const result = matches.map(match => match.observer.getOptimisticResult(match.defaultedQueryOptions));\n    return [result, r => {\n      return this.#combineResult(r ?? result, combine);\n    }, () => {\n      return this.#trackResult(result, matches);\n    }];\n  }\n  #trackResult(result, matches) {\n    return matches.map((match, index) => {\n      const observerResult = result[index];\n      return !match.defaultedQueryOptions.notifyOnChangeProps ? match.observer.trackResult(observerResult, accessedProp => {\n        matches.forEach(m => {\n          m.observer.trackProp(accessedProp);\n        });\n      }) : observerResult;\n    });\n  }\n  #combineResult(input, combine) {\n    if (combine) {\n      if (!this.#combinedResult || this.#result !== this.#lastResult || combine !== this.#lastCombine) {\n        this.#lastCombine = combine;\n        this.#lastResult = this.#result;\n        this.#combinedResult = replaceEqualDeep(this.#combinedResult, combine(input));\n      }\n      return this.#combinedResult;\n    }\n    return input;\n  }\n  #findMatchingObservers(queries) {\n    const prevObserversMap = new Map(this.#observers.map(observer => [observer.options.queryHash, observer]));\n    const observers = [];\n    queries.forEach(options => {\n      const defaultedOptions = this.#client.defaultQueryOptions(options);\n      const match = prevObserversMap.get(defaultedOptions.queryHash);\n      if (match) {\n        observers.push({\n          defaultedQueryOptions: defaultedOptions,\n          observer: match\n        });\n      } else {\n        observers.push({\n          defaultedQueryOptions: defaultedOptions,\n          observer: new QueryObserver(this.#client, defaultedOptions)\n        });\n      }\n    });\n    return observers;\n  }\n  #onUpdate(observer, result) {\n    const index = this.#observers.indexOf(observer);\n    if (index !== -1) {\n      this.#result = replaceAt(this.#result, index, result);\n      this.#notify();\n    }\n  }\n  #notify() {\n    if (this.hasListeners()) {\n      const previousResult = this.#combinedResult;\n      const newTracked = this.#trackResult(this.#result, this.#observerMatches);\n      const newResult = this.#combineResult(newTracked, this.#options?.combine);\n      if (previousResult !== newResult) {\n        notifyManager.batch(() => {\n          this.listeners.forEach(listener => {\n            listener(this.#result);\n          });\n        });\n      }\n    }\n  }\n};\nexport { QueriesObserver };", "map": {"version": 3, "names": ["notify<PERSON><PERSON>ger", "QueryObserver", "Subscribable", "replaceEqualDeep", "difference", "array1", "array2", "filter", "x", "includes", "replaceAt", "array", "index", "value", "copy", "slice", "QueriesObserver", "client", "result", "queries", "options", "observers", "combinedResult", "lastCombine", "lastResult", "observerMatches", "constructor", "setQueries", "onSubscribe", "listeners", "size", "for<PERSON>ach", "observer", "subscribe", "onUpdate", "onUnsubscribe", "destroy", "Set", "notifyOptions", "process", "env", "NODE_ENV", "queryHashes", "map", "query", "defaultQueryOptions", "queryHash", "length", "console", "warn", "batch", "prevObservers", "newObserverMatches", "findMatchingObservers", "match", "setOptions", "defaultedQueryOptions", "newObservers", "newResult", "getCurrentResult", "hasIndexChange", "some", "hasListeners", "notify", "getQueries", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getObservers", "getOptimisticResult", "combine", "matches", "r", "combineResult", "trackResult", "#trackResult", "observerResult", "notifyOnChangeProps", "accessedProp", "m", "trackProp", "#combineResult", "input", "#findMatchingObservers", "prevObserversMap", "Map", "defaultedOptions", "get", "push", "#onUpdate", "indexOf", "#notify", "previousResult", "newTracked", "listener"], "sources": ["C:\\claude\\burky_root_web\\client\\node_modules\\@tanstack\\query-core\\src\\queriesObserver.ts"], "sourcesContent": ["import { notify<PERSON>ana<PERSON> } from './notifyManager'\nimport { QueryObserver } from './queryObserver'\nimport { Subscribable } from './subscribable'\nimport { replaceEqualDeep } from './utils'\nimport type {\n  DefaultedQueryObserverOptions,\n  QueryObserverOptions,\n  QueryObserverResult,\n} from './types'\nimport type { QueryClient } from './queryClient'\nimport type { NotifyOptions } from './queryObserver'\n\nfunction difference<T>(array1: Array<T>, array2: Array<T>): Array<T> {\n  return array1.filter((x) => !array2.includes(x))\n}\n\nfunction replaceAt<T>(array: Array<T>, index: number, value: T): Array<T> {\n  const copy = array.slice(0)\n  copy[index] = value\n  return copy\n}\n\ntype QueriesObserverListener = (result: Array<QueryObserverResult>) => void\n\ntype CombineFn<TCombinedResult> = (\n  result: Array<QueryObserverResult>,\n) => TCombinedResult\n\nexport interface QueriesObserverOptions<\n  TCombinedResult = Array<QueryObserverResult>,\n> {\n  combine?: CombineFn<TCombinedResult>\n}\n\nexport class QueriesObserver<\n  TCombinedResult = Array<QueryObserverResult>,\n> extends Subscribable<QueriesObserverListener> {\n  #client: QueryClient\n  #result!: Array<QueryObserverResult>\n  #queries: Array<QueryObserverOptions>\n  #options?: QueriesObserverOptions<TCombinedResult>\n  #observers: Array<QueryObserver>\n  #combinedResult?: TCombinedResult\n  #lastCombine?: CombineFn<TCombinedResult>\n  #lastResult?: Array<QueryObserverResult>\n  #observerMatches: Array<QueryObserverMatch> = []\n\n  constructor(\n    client: QueryClient,\n    queries: Array<QueryObserverOptions<any, any, any, any, any>>,\n    options?: QueriesObserverOptions<TCombinedResult>,\n  ) {\n    super()\n\n    this.#client = client\n    this.#options = options\n    this.#queries = []\n    this.#observers = []\n    this.#result = []\n\n    this.setQueries(queries)\n  }\n\n  protected onSubscribe(): void {\n    if (this.listeners.size === 1) {\n      this.#observers.forEach((observer) => {\n        observer.subscribe((result) => {\n          this.#onUpdate(observer, result)\n        })\n      })\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.listeners.size) {\n      this.destroy()\n    }\n  }\n\n  destroy(): void {\n    this.listeners = new Set()\n    this.#observers.forEach((observer) => {\n      observer.destroy()\n    })\n  }\n\n  setQueries(\n    queries: Array<QueryObserverOptions>,\n    options?: QueriesObserverOptions<TCombinedResult>,\n    notifyOptions?: NotifyOptions,\n  ): void {\n    this.#queries = queries\n    this.#options = options\n\n    if (process.env.NODE_ENV !== 'production') {\n      const queryHashes = queries.map(\n        (query) => this.#client.defaultQueryOptions(query).queryHash,\n      )\n      if (new Set(queryHashes).size !== queryHashes.length) {\n        console.warn(\n          '[QueriesObserver]: Duplicate Queries found. This might result in unexpected behavior.',\n        )\n      }\n    }\n\n    notifyManager.batch(() => {\n      const prevObservers = this.#observers\n\n      const newObserverMatches = this.#findMatchingObservers(this.#queries)\n      this.#observerMatches = newObserverMatches\n\n      // set options for the new observers to notify of changes\n      newObserverMatches.forEach((match) =>\n        match.observer.setOptions(match.defaultedQueryOptions, notifyOptions),\n      )\n\n      const newObservers = newObserverMatches.map((match) => match.observer)\n      const newResult = newObservers.map((observer) =>\n        observer.getCurrentResult(),\n      )\n\n      const hasIndexChange = newObservers.some(\n        (observer, index) => observer !== prevObservers[index],\n      )\n\n      if (prevObservers.length === newObservers.length && !hasIndexChange) {\n        return\n      }\n\n      this.#observers = newObservers\n      this.#result = newResult\n\n      if (!this.hasListeners()) {\n        return\n      }\n\n      difference(prevObservers, newObservers).forEach((observer) => {\n        observer.destroy()\n      })\n\n      difference(newObservers, prevObservers).forEach((observer) => {\n        observer.subscribe((result) => {\n          this.#onUpdate(observer, result)\n        })\n      })\n\n      this.#notify()\n    })\n  }\n\n  getCurrentResult(): Array<QueryObserverResult> {\n    return this.#result\n  }\n\n  getQueries() {\n    return this.#observers.map((observer) => observer.getCurrentQuery())\n  }\n\n  getObservers() {\n    return this.#observers\n  }\n\n  getOptimisticResult(\n    queries: Array<QueryObserverOptions>,\n    combine: CombineFn<TCombinedResult> | undefined,\n  ): [\n    rawResult: Array<QueryObserverResult>,\n    combineResult: (r?: Array<QueryObserverResult>) => TCombinedResult,\n    trackResult: () => Array<QueryObserverResult>,\n  ] {\n    const matches = this.#findMatchingObservers(queries)\n    const result = matches.map((match) =>\n      match.observer.getOptimisticResult(match.defaultedQueryOptions),\n    )\n\n    return [\n      result,\n      (r?: Array<QueryObserverResult>) => {\n        return this.#combineResult(r ?? result, combine)\n      },\n      () => {\n        return this.#trackResult(result, matches)\n      },\n    ]\n  }\n\n  #trackResult(\n    result: Array<QueryObserverResult>,\n    matches: Array<QueryObserverMatch>,\n  ) {\n    return matches.map((match, index) => {\n      const observerResult = result[index]!\n      return !match.defaultedQueryOptions.notifyOnChangeProps\n        ? match.observer.trackResult(observerResult, (accessedProp) => {\n            // track property on all observers to ensure proper (synchronized) tracking (#7000)\n            matches.forEach((m) => {\n              m.observer.trackProp(accessedProp)\n            })\n          })\n        : observerResult\n    })\n  }\n\n  #combineResult(\n    input: Array<QueryObserverResult>,\n    combine: CombineFn<TCombinedResult> | undefined,\n  ): TCombinedResult {\n    if (combine) {\n      if (\n        !this.#combinedResult ||\n        this.#result !== this.#lastResult ||\n        combine !== this.#lastCombine\n      ) {\n        this.#lastCombine = combine\n        this.#lastResult = this.#result\n        this.#combinedResult = replaceEqualDeep(\n          this.#combinedResult,\n          combine(input),\n        )\n      }\n\n      return this.#combinedResult\n    }\n    return input as any\n  }\n\n  #findMatchingObservers(\n    queries: Array<QueryObserverOptions>,\n  ): Array<QueryObserverMatch> {\n    const prevObserversMap = new Map(\n      this.#observers.map((observer) => [observer.options.queryHash, observer]),\n    )\n\n    const observers: Array<QueryObserverMatch> = []\n\n    queries.forEach((options) => {\n      const defaultedOptions = this.#client.defaultQueryOptions(options)\n      const match = prevObserversMap.get(defaultedOptions.queryHash)\n      if (match) {\n        observers.push({\n          defaultedQueryOptions: defaultedOptions,\n          observer: match,\n        })\n      } else {\n        observers.push({\n          defaultedQueryOptions: defaultedOptions,\n          observer: new QueryObserver(this.#client, defaultedOptions),\n        })\n      }\n    })\n\n    return observers\n  }\n\n  #onUpdate(observer: QueryObserver, result: QueryObserverResult): void {\n    const index = this.#observers.indexOf(observer)\n    if (index !== -1) {\n      this.#result = replaceAt(this.#result, index, result)\n      this.#notify()\n    }\n  }\n\n  #notify(): void {\n    if (this.hasListeners()) {\n      const previousResult = this.#combinedResult\n      const newTracked = this.#trackResult(this.#result, this.#observerMatches)\n      const newResult = this.#combineResult(newTracked, this.#options?.combine)\n\n      if (previousResult !== newResult) {\n        notifyManager.batch(() => {\n          this.listeners.forEach((listener) => {\n            listener(this.#result)\n          })\n        })\n      }\n    }\n  }\n}\n\ntype QueryObserverMatch = {\n  defaultedQueryOptions: DefaultedQueryObserverOptions\n  observer: QueryObserver\n}\n"], "mappings": ";AAAA,SAASA,aAAA,QAAqB;AAC9B,SAASC,aAAA,QAAqB;AAC9B,SAASC,YAAA,QAAoB;AAC7B,SAASC,gBAAA,QAAwB;AASjC,SAASC,WAAcC,MAAA,EAAkBC,MAAA,EAA4B;EACnE,OAAOD,MAAA,CAAOE,MAAA,CAAQC,CAAA,IAAM,CAACF,MAAA,CAAOG,QAAA,CAASD,CAAC,CAAC;AACjD;AAEA,SAASE,UAAaC,KAAA,EAAiBC,KAAA,EAAeC,KAAA,EAAoB;EACxE,MAAMC,IAAA,GAAOH,KAAA,CAAMI,KAAA,CAAM,CAAC;EAC1BD,IAAA,CAAKF,KAAK,IAAIC,KAAA;EACd,OAAOC,IAAA;AACT;AAcO,IAAME,eAAA,GAAN,cAEGd,YAAA,CAAsC;EAC9C,CAAAe,MAAA;EACA,CAAAC,MAAA;EACA,CAAAC,OAAA;EACA,CAAAC,OAAA;EACA,CAAAC,SAAA;EACA,CAAAC,cAAA;EACA,CAAAC,WAAA;EACA,CAAAC,UAAA;EACA,CAAAC,eAAA,GAA8C,EAAC;EAE/CC,YACET,MAAA,EACAE,OAAA,EACAC,OAAA,EACA;IACA,MAAM;IAEN,KAAK,CAAAH,MAAA,GAAUA,MAAA;IACf,KAAK,CAAAG,OAAA,GAAWA,OAAA;IAChB,KAAK,CAAAD,OAAA,GAAW,EAAC;IACjB,KAAK,CAAAE,SAAA,GAAa,EAAC;IACnB,KAAK,CAAAH,MAAA,GAAU,EAAC;IAEhB,KAAKS,UAAA,CAAWR,OAAO;EACzB;EAEUS,YAAA,EAAoB;IAC5B,IAAI,KAAKC,SAAA,CAAUC,IAAA,KAAS,GAAG;MAC7B,KAAK,CAAAT,SAAA,CAAWU,OAAA,CAASC,QAAA,IAAa;QACpCA,QAAA,CAASC,SAAA,CAAWf,MAAA,IAAW;UAC7B,KAAK,CAAAgB,QAAA,CAAUF,QAAA,EAAUd,MAAM;QACjC,CAAC;MACH,CAAC;IACH;EACF;EAEUiB,cAAA,EAAsB;IAC9B,IAAI,CAAC,KAAKN,SAAA,CAAUC,IAAA,EAAM;MACxB,KAAKM,OAAA,CAAQ;IACf;EACF;EAEAA,QAAA,EAAgB;IACd,KAAKP,SAAA,GAAY,mBAAIQ,GAAA,CAAI;IACzB,KAAK,CAAAhB,SAAA,CAAWU,OAAA,CAASC,QAAA,IAAa;MACpCA,QAAA,CAASI,OAAA,CAAQ;IACnB,CAAC;EACH;EAEAT,WACER,OAAA,EACAC,OAAA,EACAkB,aAAA,EACM;IACN,KAAK,CAAAnB,OAAA,GAAWA,OAAA;IAChB,KAAK,CAAAC,OAAA,GAAWA,OAAA;IAEhB,IAAImB,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;MACzC,MAAMC,WAAA,GAAcvB,OAAA,CAAQwB,GAAA,CACzBC,KAAA,IAAU,KAAK,CAAA3B,MAAA,CAAQ4B,mBAAA,CAAoBD,KAAK,EAAEE,SACrD;MACA,IAAI,IAAIT,GAAA,CAAIK,WAAW,EAAEZ,IAAA,KAASY,WAAA,CAAYK,MAAA,EAAQ;QACpDC,OAAA,CAAQC,IAAA,CACN,uFACF;MACF;IACF;IAEAjD,aAAA,CAAckD,KAAA,CAAM,MAAM;MACxB,MAAMC,aAAA,GAAgB,KAAK,CAAA9B,SAAA;MAE3B,MAAM+B,kBAAA,GAAqB,KAAK,CAAAC,qBAAA,CAAuB,KAAK,CAAAlC,OAAQ;MACpE,KAAK,CAAAM,eAAA,GAAmB2B,kBAAA;MAGxBA,kBAAA,CAAmBrB,OAAA,CAASuB,KAAA,IAC1BA,KAAA,CAAMtB,QAAA,CAASuB,UAAA,CAAWD,KAAA,CAAME,qBAAA,EAAuBlB,aAAa,CACtE;MAEA,MAAMmB,YAAA,GAAeL,kBAAA,CAAmBT,GAAA,CAAKW,KAAA,IAAUA,KAAA,CAAMtB,QAAQ;MACrE,MAAM0B,SAAA,GAAYD,YAAA,CAAad,GAAA,CAAKX,QAAA,IAClCA,QAAA,CAAS2B,gBAAA,CAAiB,CAC5B;MAEA,MAAMC,cAAA,GAAiBH,YAAA,CAAaI,IAAA,CAClC,CAAC7B,QAAA,EAAUpB,KAAA,KAAUoB,QAAA,KAAamB,aAAA,CAAcvC,KAAK,CACvD;MAEA,IAAIuC,aAAA,CAAcJ,MAAA,KAAWU,YAAA,CAAaV,MAAA,IAAU,CAACa,cAAA,EAAgB;QACnE;MACF;MAEA,KAAK,CAAAvC,SAAA,GAAaoC,YAAA;MAClB,KAAK,CAAAvC,MAAA,GAAUwC,SAAA;MAEf,IAAI,CAAC,KAAKI,YAAA,CAAa,GAAG;QACxB;MACF;MAEA1D,UAAA,CAAW+C,aAAA,EAAeM,YAAY,EAAE1B,OAAA,CAASC,QAAA,IAAa;QAC5DA,QAAA,CAASI,OAAA,CAAQ;MACnB,CAAC;MAEDhC,UAAA,CAAWqD,YAAA,EAAcN,aAAa,EAAEpB,OAAA,CAASC,QAAA,IAAa;QAC5DA,QAAA,CAASC,SAAA,CAAWf,MAAA,IAAW;UAC7B,KAAK,CAAAgB,QAAA,CAAUF,QAAA,EAAUd,MAAM;QACjC,CAAC;MACH,CAAC;MAED,KAAK,CAAA6C,MAAA,CAAQ;IACf,CAAC;EACH;EAEAJ,iBAAA,EAA+C;IAC7C,OAAO,KAAK,CAAAzC,MAAA;EACd;EAEA8C,WAAA,EAAa;IACX,OAAO,KAAK,CAAA3C,SAAA,CAAWsB,GAAA,CAAKX,QAAA,IAAaA,QAAA,CAASiC,eAAA,CAAgB,CAAC;EACrE;EAEAC,aAAA,EAAe;IACb,OAAO,KAAK,CAAA7C,SAAA;EACd;EAEA8C,oBACEhD,OAAA,EACAiD,OAAA,EAKA;IACA,MAAMC,OAAA,GAAU,KAAK,CAAAhB,qBAAA,CAAuBlC,OAAO;IACnD,MAAMD,MAAA,GAASmD,OAAA,CAAQ1B,GAAA,CAAKW,KAAA,IAC1BA,KAAA,CAAMtB,QAAA,CAASmC,mBAAA,CAAoBb,KAAA,CAAME,qBAAqB,CAChE;IAEA,OAAO,CACLtC,MAAA,EACCoD,CAAA,IAAmC;MAClC,OAAO,KAAK,CAAAC,aAAA,CAAeD,CAAA,IAAKpD,MAAA,EAAQkD,OAAO;IACjD,GACA,MAAM;MACJ,OAAO,KAAK,CAAAI,WAAA,CAAatD,MAAA,EAAQmD,OAAO;IAC1C,EACF;EACF;EAEA,CAAAG,WAAAC,CACEvD,MAAA,EACAmD,OAAA,EACA;IACA,OAAOA,OAAA,CAAQ1B,GAAA,CAAI,CAACW,KAAA,EAAO1C,KAAA,KAAU;MACnC,MAAM8D,cAAA,GAAiBxD,MAAA,CAAON,KAAK;MACnC,OAAO,CAAC0C,KAAA,CAAME,qBAAA,CAAsBmB,mBAAA,GAChCrB,KAAA,CAAMtB,QAAA,CAASwC,WAAA,CAAYE,cAAA,EAAiBE,YAAA,IAAiB;QAE3DP,OAAA,CAAQtC,OAAA,CAAS8C,CAAA,IAAM;UACrBA,CAAA,CAAE7C,QAAA,CAAS8C,SAAA,CAAUF,YAAY;QACnC,CAAC;MACH,CAAC,IACDF,cAAA;IACN,CAAC;EACH;EAEA,CAAAH,aAAAQ,CACEC,KAAA,EACAZ,OAAA,EACiB;IACjB,IAAIA,OAAA,EAAS;MACX,IACE,CAAC,KAAK,CAAA9C,cAAA,IACN,KAAK,CAAAJ,MAAA,KAAY,KAAK,CAAAM,UAAA,IACtB4C,OAAA,KAAY,KAAK,CAAA7C,WAAA,EACjB;QACA,KAAK,CAAAA,WAAA,GAAe6C,OAAA;QACpB,KAAK,CAAA5C,UAAA,GAAc,KAAK,CAAAN,MAAA;QACxB,KAAK,CAAAI,cAAA,GAAkBnB,gBAAA,CACrB,KAAK,CAAAmB,cAAA,EACL8C,OAAA,CAAQY,KAAK,CACf;MACF;MAEA,OAAO,KAAK,CAAA1D,cAAA;IACd;IACA,OAAO0D,KAAA;EACT;EAEA,CAAA3B,qBAAA4B,CACE9D,OAAA,EAC2B;IAC3B,MAAM+D,gBAAA,GAAmB,IAAIC,GAAA,CAC3B,KAAK,CAAA9D,SAAA,CAAWsB,GAAA,CAAKX,QAAA,IAAa,CAACA,QAAA,CAASZ,OAAA,CAAQ0B,SAAA,EAAWd,QAAQ,CAAC,CAC1E;IAEA,MAAMX,SAAA,GAAuC,EAAC;IAE9CF,OAAA,CAAQY,OAAA,CAASX,OAAA,IAAY;MAC3B,MAAMgE,gBAAA,GAAmB,KAAK,CAAAnE,MAAA,CAAQ4B,mBAAA,CAAoBzB,OAAO;MACjE,MAAMkC,KAAA,GAAQ4B,gBAAA,CAAiBG,GAAA,CAAID,gBAAA,CAAiBtC,SAAS;MAC7D,IAAIQ,KAAA,EAAO;QACTjC,SAAA,CAAUiE,IAAA,CAAK;UACb9B,qBAAA,EAAuB4B,gBAAA;UACvBpD,QAAA,EAAUsB;QACZ,CAAC;MACH,OAAO;QACLjC,SAAA,CAAUiE,IAAA,CAAK;UACb9B,qBAAA,EAAuB4B,gBAAA;UACvBpD,QAAA,EAAU,IAAI/B,aAAA,CAAc,KAAK,CAAAgB,MAAA,EAASmE,gBAAgB;QAC5D,CAAC;MACH;IACF,CAAC;IAED,OAAO/D,SAAA;EACT;EAEA,CAAAa,QAAAqD,CAAUvD,QAAA,EAAyBd,MAAA,EAAmC;IACpE,MAAMN,KAAA,GAAQ,KAAK,CAAAS,SAAA,CAAWmE,OAAA,CAAQxD,QAAQ;IAC9C,IAAIpB,KAAA,KAAU,IAAI;MAChB,KAAK,CAAAM,MAAA,GAAUR,SAAA,CAAU,KAAK,CAAAQ,MAAA,EAASN,KAAA,EAAOM,MAAM;MACpD,KAAK,CAAA6C,MAAA,CAAQ;IACf;EACF;EAEA,CAAAA,MAAA0B,CAAA,EAAgB;IACd,IAAI,KAAK3B,YAAA,CAAa,GAAG;MACvB,MAAM4B,cAAA,GAAiB,KAAK,CAAApE,cAAA;MAC5B,MAAMqE,UAAA,GAAa,KAAK,CAAAnB,WAAA,CAAa,KAAK,CAAAtD,MAAA,EAAS,KAAK,CAAAO,eAAgB;MACxE,MAAMiC,SAAA,GAAY,KAAK,CAAAa,aAAA,CAAeoB,UAAA,EAAY,KAAK,CAAAvE,OAAA,EAAUgD,OAAO;MAExE,IAAIsB,cAAA,KAAmBhC,SAAA,EAAW;QAChC1D,aAAA,CAAckD,KAAA,CAAM,MAAM;UACxB,KAAKrB,SAAA,CAAUE,OAAA,CAAS6D,QAAA,IAAa;YACnCA,QAAA,CAAS,KAAK,CAAA1E,MAAO;UACvB,CAAC;QACH,CAAC;MACH;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}