/**
 * Clients Service
 * Handles business logic for client-related operations
 */

const { pool, sql } = require('../../config/database');
const logger = require('../../utils/logger');

/**
 * Get all clients
 * @returns {Promise<Array>} Array of clients
 */
const getAllClients = async () => {
  try {
    const result = await pool.request()
      .query(`
        SELECT c.ClientID, c.UserID, u.FirstName, u.LastName, u.Email, 
               u.IsActive, u.IsEmailVerified
        FROM Clients c
        INNER JOIN Users u ON c.UserID = u.UserID
        WHERE u.IsActive = 1 AND u.IsEmailVerified = 1
      `);
    
    return result.recordset;
  } catch (error) {
    logger.error('Error in getAllClients:', error);
    throw error;
  }
};

/**
 * Get client by ID
 * @param {number} clientId - Client ID
 * @returns {Promise<Object>} Client data
 */
const getClientById = async (clientId) => {
  try {
    const result = await pool.request()
      .input('clientId', sql.Int, clientId)
      .query(`
        SELECT c.ClientID, c.UserID, u.FirstName, u.LastName, u.Email, 
               u.PhoneNumber, u.IsActive, u.IsEmailVerified, u.IsPhoneVerified,
               u.CreatedAt, c.Address, c.City, c.Country, c.DateOfBirth, c.ChildrenInfo
        FROM Clients c
        INNER JOIN Users u ON c.UserID = u.UserID
        WHERE c.ClientID = @clientId
      `);
    
    return result.recordset[0];
  } catch (error) {
    logger.error('Error in getClientById:', error);
    throw error;
  }
};

/**
 * Get client by user ID
 * @param {number} userId - User ID
 * @returns {Promise<Object>} Client data
 */
const getClientByUserId = async (userId) => {
  try {
    const result = await pool.request()
      .input('userId', sql.Int, userId)
      .query(`
        SELECT c.ClientID, c.UserID, u.FirstName, u.LastName, u.Email, 
               u.PhoneNumber, u.IsActive, u.IsEmailVerified, u.IsPhoneVerified,
               u.CreatedAt, c.Address, c.City, c.Country, c.DateOfBirth, c.ChildrenInfo
        FROM Clients c
        INNER JOIN Users u ON c.UserID = u.UserID
        WHERE c.UserID = @userId
      `);
    
    return result.recordset[0];
  } catch (error) {
    logger.error('Error getting client by user ID:', error);
    throw error;
  }
};

/**
 * Update client profile
 * @param {number} clientId - Client ID
 * @param {Object} clientData - Client data to update
 * @returns {Promise<boolean>} True if successful
 */
const updateClientProfile = async (clientId, clientData) => {
  const transaction = new sql.Transaction(pool);
  
  try {
    await transaction.begin();
    
    const client = await pool.request()
      .input('clientId', sql.Int, clientId)
      .query('SELECT UserID FROM Clients WHERE ClientID = @clientId');
    
    if (client.recordset.length === 0) {
      throw new Error('Client not found');
    }
    
    const userId = client.recordset[0].UserID;
    
    // Update user table
    if (clientData.firstName || clientData.lastName || clientData.phoneNumber) {
      const userRequest = new sql.Request(transaction);
      userRequest.input('userId', sql.Int, userId);
      
      if (clientData.firstName) {
        userRequest.input('firstName', sql.NVarChar(50), clientData.firstName);
      }
      
      if (clientData.lastName) {
        userRequest.input('lastName', sql.NVarChar(50), clientData.lastName);
      }
      
      if (clientData.phoneNumber) {
        userRequest.input('phoneNumber', sql.VarChar(20), clientData.phoneNumber);
      }
      
      let updateUserQuery = 'UPDATE Users SET ';
      const updates = [];
      
      if (clientData.firstName) updates.push('FirstName = @firstName');
      if (clientData.lastName) updates.push('LastName = @lastName');
      if (clientData.phoneNumber) updates.push('PhoneNumber = @phoneNumber');
      
      updateUserQuery += updates.join(', ') + ' WHERE UserID = @userId';
      
      await userRequest.query(updateUserQuery);
    }
    
    // Update client table
    if (clientData.address || clientData.city || clientData.country || 
        clientData.dateOfBirth || clientData.childrenInfo) {
      const clientRequest = new sql.Request(transaction);
      clientRequest.input('clientId', sql.Int, clientId);
      
      if (clientData.address) {
        clientRequest.input('address', sql.NVarChar(255), clientData.address);
      }
      
      if (clientData.city) {
        clientRequest.input('city', sql.NVarChar(50), clientData.city);
      }
      
      if (clientData.country) {
        clientRequest.input('country', sql.NVarChar(50), clientData.country);
      }
      
      if (clientData.dateOfBirth) {
        clientRequest.input('dateOfBirth', sql.Date, new Date(clientData.dateOfBirth));
      }
      
      if (clientData.childrenInfo) {
        clientRequest.input('childrenInfo', sql.NVarChar(sql.MAX), clientData.childrenInfo);
      }
      
      let updateClientQuery = 'UPDATE Clients SET ';
      const updates = [];
      
      if (clientData.address) updates.push('Address = @address');
      if (clientData.city) updates.push('City = @city');
      if (clientData.country) updates.push('Country = @country');
      if (clientData.dateOfBirth) updates.push('DateOfBirth = @dateOfBirth');
      if (clientData.childrenInfo) updates.push('ChildrenInfo = @childrenInfo');
      
      updateClientQuery += updates.join(', ') + ', UpdatedAt = GETDATE() WHERE ClientID = @clientId';
      
      await clientRequest.query(updateClientQuery);
    }
    
    await transaction.commit();
    return true;
  } catch (error) {
    await transaction.rollback();
    logger.error('Error in updateClientProfile:', error);
    throw error;
  }
};

/**
 * Get client appointments
 * @param {number} clientId - Client ID
 * @returns {Promise<Array>} Array of appointments
 */
const getClientAppointments = async (clientId) => {
  try {
    const result = await pool.request()
      .input('clientId', sql.Int, clientId)
      .query(`
        SELECT a.AppointmentID, a.ExpertID, a.ClientID, a.AppointmentDate as StartTime, a.EndTime,
               a.Status, a.Notes, a.CreatedAt, a.UpdatedAt,
               e.ExpertID as 'Expert.ExpertID', u.FirstName as 'Expert.FirstName', 
               u.LastName as 'Expert.LastName', e.Specialty as 'Expert.Specialty'
        FROM Appointments a
        INNER JOIN Experts e ON a.ExpertID = e.ExpertID
        INNER JOIN Users u ON e.UserID = u.UserID
        WHERE a.ClientID = @clientId
        ORDER BY a.AppointmentDate DESC
      `);
    
    // Transform the flat results into nested objects
    const appointments = result.recordset.map(row => {
      const appointment = {
        id: row.AppointmentID,
        expertId: row.ExpertID,
        clientId: row.ClientID,
        startTime: row.StartTime,
        endTime: row.EndTime,
        status: row.Status,
        notes: row.Notes,
        createdAt: row.CreatedAt,
        updatedAt: row.UpdatedAt,
        expert: {
          id: row['Expert.ExpertID'],
          firstName: row['Expert.FirstName'],
          lastName: row['Expert.LastName'],
          specialty: row['Expert.Specialty']
        }
      };
      
      return appointment;
    });
    
    return appointments;
  } catch (error) {
    logger.error('Error in getClientAppointments:', error);
    throw error;
  }
};

/**
 * Book an appointment with an expert
 * @param {Object} appointmentData - Appointment data
 * @returns {Promise<number>} ID of created appointment
 */
const bookAppointment = async (appointmentData) => {
  try {
    const result = await pool.request()
      .input('expertId', sql.Int, appointmentData.expertId)
      .input('clientId', sql.Int, appointmentData.clientId)
      .input('startTime', sql.DateTime, new Date(appointmentData.startTime))
      .input('endTime', sql.DateTime, new Date(appointmentData.endTime))
      .input('status', sql.VarChar(20), 'Pending')
      .input('notes', sql.NVarChar(500), appointmentData.notes || null)
      .query(`
        INSERT INTO Appointments (ExpertID, ClientID, AppointmentDate, EndTime, Status, Notes, CreatedAt, UpdatedAt)
        OUTPUT INSERTED.AppointmentID
        VALUES (@expertId, @clientId, @startTime, @endTime, @status, @notes, GETDATE(), GETDATE())
      `);
    
    return result.recordset[0].AppointmentID;
  } catch (error) {
    logger.error('Error in bookAppointment:', error);
    throw error;
  }
};

/**
 * Cancel an appointment
 * @param {number} appointmentId - Appointment ID
 * @param {number} clientId - Client ID
 * @returns {Promise<boolean>} True if successful
 */
const cancelAppointment = async (appointmentId, clientId) => {
  try {
    const result = await pool.request()
      .input('appointmentId', sql.Int, appointmentId)
      .input('clientId', sql.Int, clientId)
      .input('status', sql.VarChar(20), 'Cancelled')
      .query(`
        UPDATE Appointments
        SET Status = @status, UpdatedAt = GETDATE()
        OUTPUT INSERTED.AppointmentID
        WHERE AppointmentID = @appointmentId AND ClientID = @clientId
      `);
    
    return result.recordset.length > 0;
  } catch (error) {
    logger.error('Error in cancelAppointment:', error);
    throw error;
  }
};

module.exports = {
  getAllClients,
  getClientById,
  getClientByUserId,
  updateClientProfile,
  getClientAppointments,
  bookAppointment,
  cancelAppointment
}; 