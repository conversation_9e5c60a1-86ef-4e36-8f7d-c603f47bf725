{"ast": null, "code": "import ClientAppointmentsPage from './ClientAppointmentsPage';\nimport BookAppointmentPage from './BookAppointmentPage';\nexport default ClientAppointmentsPage;\nexport { BookAppointmentPage };", "map": {"version": 3, "names": ["ClientAppointmentsPage", "BookAppointmentPage"], "sources": ["C:/Projeler/kidgarden/burky_root_web/client/src/pages/client/appointments/index.js"], "sourcesContent": ["import ClientAppointmentsPage from './ClientAppointmentsPage';\r\nimport BookAppointmentPage from './BookAppointmentPage';\r\n\r\nexport default ClientAppointmentsPage;\r\nexport { BookAppointmentPage };"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,mBAAmB,MAAM,uBAAuB;AAEvD,eAAeD,sBAAsB;AACrC,SAASC,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}