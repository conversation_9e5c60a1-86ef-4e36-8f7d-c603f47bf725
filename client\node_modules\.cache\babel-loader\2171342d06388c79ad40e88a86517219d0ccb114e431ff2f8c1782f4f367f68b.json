{"ast": null, "code": "var _jsxFileName = \"C:\\\\Projeler\\\\kidgarden\\\\burky_root_web\\\\client\\\\src\\\\pages\\\\client\\\\messages\\\\ClientMessagesPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '../../../hooks/useAuth';\nimport api from '../../../services/api';\nimport { toast } from 'react-hot-toast';\nimport io from 'socket.io-client';\nimport { ChatBubbleLeftEllipsisIcon, MagnifyingGlassIcon, UserIcon, PaperAirplaneIcon, PaperClipIcon, FaceSmileIcon, EllipsisHorizontalIcon, VideoCameraIcon, CheckCircleIcon, ArchiveBoxIcon, UserGroupIcon, CalendarIcon } from '@heroicons/react/24/outline';\nimport { format } from 'date-fns';\nimport { tr } from 'date-fns/locale';\nimport { Link } from 'react-router-dom';\n\n/**\n * <PERSON><PERSON><PERSON><PERSON> sayfası\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ClientMessagesPage = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [isLoading, setIsLoading] = useState(true);\n  const [conversations, setConversations] = useState([]);\n  const [selectedConversation, setSelectedConversation] = useState(null);\n  const [messages, setMessages] = useState([]);\n  const [messageText, setMessageText] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filter, setFilter] = useState('all'); // all, unread, archived, starred\n  const [socket, setSocket] = useState(null);\n  const [onlineUsers, setOnlineUsers] = useState(new Set());\n  const messagesEndRef = useRef(null);\n  const conversationsRef = useRef(null);\n  const messagesContainerRef = useRef(null);\n\n  // Socket.IO connection - ayrı useEffect\n  useEffect(() => {\n    const token = localStorage.getItem('accessToken');\n    if (token && user !== null && user !== void 0 && user.id) {\n      console.log('🔌 CLIENT: Socket.IO bağlantısı kuruluyor... User ID:', user.id);\n      const socketConnection = io(process.env.REACT_APP_API_URL || 'http://localhost:5000', {\n        auth: {\n          token\n        },\n        transports: ['websocket', 'polling'],\n        reconnection: true,\n        reconnectionDelay: 1000,\n        reconnectionAttempts: 5,\n        timeout: 20000\n      });\n      setSocket(socketConnection);\n\n      // Connection events\n      socketConnection.on('connect', () => {\n        console.log('✅ CLIENT: Socket.IO bağlantısı kuruldu:', socketConnection.id);\n        console.log('🔗 CLIENT: Socket connected:', socketConnection.connected);\n        console.log('🏠 CLIENT: Socket rooms:', socketConnection.rooms);\n        // Kullanıcının online olduğunu bildir\n        socketConnection.emit('user_online');\n        // Online kullanıcı listesini al\n        socketConnection.emit('get_online_users');\n      });\n      socketConnection.on('disconnect', reason => {\n        console.log('❌ CLIENT: Socket.IO bağlantısı kesildi:', reason);\n      });\n      socketConnection.on('connect_error', error => {\n        console.error('🔥 CLIENT: Socket.IO bağlantı hatası:', error);\n      });\n      socketConnection.on('reconnect', attemptNumber => {\n        console.log('🔄 CLIENT: Socket.IO yeniden bağlandı, deneme:', attemptNumber);\n      });\n      socketConnection.on('reconnect_error', error => {\n        console.error('🔥 CLIENT: Socket.IO yeniden bağlanma hatası:', error);\n      });\n\n      // Heartbeat mekanizması\n      const heartbeatInterval = setInterval(() => {\n        if (socketConnection.connected) {\n          socketConnection.emit('ping', response => {\n            if (response === 'pong') {\n              console.log('💓 CLIENT: Heartbeat OK');\n            }\n          });\n        }\n      }, 30000); // 30 saniyede bir ping gönder\n\n      return () => {\n        clearInterval(heartbeatInterval);\n        socketConnection.disconnect();\n      };\n    } else {\n      console.log('❌ CLIENT: Socket bağlantısı kurulamıyor - token veya user eksik:', {\n        hasToken: !!token,\n        hasUser: !!(user !== null && user !== void 0 && user.id)\n      });\n    }\n  }, [user === null || user === void 0 ? void 0 : user.id]);\n\n  // Socket event listeners - ayrı useEffect\n  useEffect(() => {\n    if (socket && user !== null && user !== void 0 && user.id) {\n      console.log('🎧 Socket event listeners kuruluyor...');\n\n      // Sayfa yüklendiğinde online users listesini al\n      if (socket.connected) {\n        console.log('🔄 CLIENT: Requesting online users list on page load');\n        socket.emit('get_online_users');\n      }\n\n      // Socket event listeners\n      const handleNewMessage = message => {\n        console.log('📨 CLIENT: Yeni mesaj alındı:', message);\n        console.log('👤 CLIENT: Current user ID:', user.id);\n        console.log('💬 CLIENT: Message sender ID:', message.senderId);\n        console.log('🏠 CLIENT: Message conversation ID:', message.conversationId);\n\n        // Conversation listesini ÖNCE güncelle (her zaman)\n        setConversations(prev => {\n          console.log('📋 CLIENT: Updating conversations list');\n          return prev.map(conv => conv.id === message.conversationId ? {\n            ...conv,\n            lastMessage: message.content,\n            timestamp: message.createdAt,\n            unread: message.senderId !== user.id\n          } : conv);\n        });\n\n        // Eğer şu anda bu conversation açıksa, mesajı da ekle\n        setSelectedConversation(currentSelected => {\n          console.log('🎯 CLIENT: Current selected conversation:', currentSelected === null || currentSelected === void 0 ? void 0 : currentSelected.id);\n          if (currentSelected && message.conversationId === currentSelected.id) {\n            console.log('✅ CLIENT: Adding message to current conversation');\n            setMessages(prev => {\n              // Mesajın zaten var olup olmadığını kontrol et\n              const messageExists = prev.some(msg => msg.id === message.id);\n              if (messageExists) {\n                console.log('⚠️ CLIENT: Message already exists, skipping:', message.id);\n                return prev;\n              }\n              return [...prev, {\n                id: message.id,\n                senderId: message.senderId,\n                senderName: message.sender.name,\n                senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n                text: message.content,\n                timestamp: message.createdAt,\n                read: message.isRead,\n                delivered: true // Socket'ten gelen mesajlar delivered olarak kabul edilir\n              }];\n            });\n\n            // Eğer mesaj başka birinden geliyorsa ve conversation açıksa, otomatik okundu işaretle\n            if (message.senderId !== user.id) {\n              console.log('📖 CLIENT: Auto-marking message as read since conversation is open');\n              setTimeout(() => {\n                markConversationAsRead(message.conversationId);\n              }, 500); // Kısa bir gecikme ile\n            }\n\n            // Scroll to bottom\n            setTimeout(() => {\n              var _messagesEndRef$curre;\n              (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n                behavior: 'smooth'\n              });\n            }, 100);\n          } else {\n            console.log('❌ CLIENT: Not adding message - conversation not selected or different conversation');\n          }\n          return currentSelected;\n        });\n      };\n      const handleMessageSent = message => {\n        console.log('✅ Mesaj gönderildi onayı:', message);\n      };\n      const handleUserStatusChange = data => {\n        console.log('🔄 CLIENT: User status change:', data);\n        setOnlineUsers(prev => {\n          const newSet = new Set(prev);\n          if (data.status === 'online') {\n            newSet.add(data.userId);\n          } else {\n            newSet.delete(data.userId);\n          }\n          console.log('🟢 CLIENT: Online users updated:', Array.from(newSet));\n          return newSet;\n        });\n      };\n      const handleOnlineUsersList = userIds => {\n        console.log('📋 CLIENT: Online users list received:', userIds);\n        setOnlineUsers(new Set(userIds));\n      };\n      const handleUserTyping = data => {\n        setSelectedConversation(currentSelected => {\n          if (currentSelected && data.conversationId === currentSelected.id) {\n            setTypingUsers(prev => new Set([...prev, data.userId]));\n          }\n          return currentSelected;\n        });\n      };\n      const handleUserStoppedTyping = data => {\n        setTypingUsers(prev => {\n          const newSet = new Set(prev);\n          newSet.delete(data.userId);\n          return newSet;\n        });\n      };\n      const handleMessagesRead = data => {\n        console.log('👁️ CLIENT: Messages read event:', data);\n        console.log('👁️ CLIENT: Read by user:', data.readBy, 'Current user:', user.id);\n        console.log('👁️ CLIENT: Message IDs marked as read:', data.messageIds);\n\n        // Eğer başka bir kullanıcı mesajları okudu ise, kendi gönderdiğim mesajları okundu olarak işaretle\n        if (data.readBy !== user.id) {\n          setMessages(prev => prev.map(msg => {\n            // Kendi gönderdiğim mesajları ve conversation'daki tüm mesajları güncelle\n            if (msg.senderId === user.id && msg.conversationId === data.conversationId) {\n              console.log('👁️ CLIENT: Marking my message as read:', msg.id);\n              return {\n                ...msg,\n                read: true\n              };\n            }\n            // Ayrıca messageIds listesindeki mesajları da güncelle\n            if (data.messageIds.includes(msg.id)) {\n              console.log('👁️ CLIENT: Marking specific message as read:', msg.id);\n              return {\n                ...msg,\n                read: true\n              };\n            }\n            return msg;\n          }));\n\n          // Conversation listesindeki unread durumunu da güncelle\n          setConversations(prev => prev.map(conv => conv.id === data.conversationId ? {\n            ...conv,\n            unread: false\n          } : conv));\n        }\n      };\n\n      // Event listener'ları ekle\n      console.log('🎧 CLIENT: Adding socket event listeners...');\n      socket.on('new_message', handleNewMessage);\n      socket.on('message_sent', handleMessageSent);\n      socket.on('user_status_change', handleUserStatusChange);\n      socket.on('user_typing', handleUserTyping);\n      socket.on('user_stopped_typing', handleUserStoppedTyping);\n      socket.on('messages_read', handleMessagesRead);\n      socket.on('online_users_list', handleOnlineUsersList);\n      console.log('✅ CLIENT: Socket event listeners added');\n\n      // Cleanup function\n      return () => {\n        socket.off('new_message', handleNewMessage);\n        socket.off('message_sent', handleMessageSent);\n        socket.off('user_status_change', handleUserStatusChange);\n        socket.off('user_typing', handleUserTyping);\n        socket.off('user_stopped_typing', handleUserStoppedTyping);\n        socket.off('messages_read', handleMessagesRead);\n        socket.off('online_users_list', handleOnlineUsersList);\n      };\n    }\n  }, [socket, user.id]);\n\n  // Conversations yükleme - ayrı useEffect\n  useEffect(() => {\n    loadConversations();\n  }, []);\n\n  // Socket bağlandığında tüm conversation'lara join ol - sadece bir kez\n  const [joinedConversations, setJoinedConversations] = useState(new Set());\n  useEffect(() => {\n    if (socket && conversations.length > 0) {\n      console.log('🏠 CLIENT: Tüm conversation\\'lara katılıyor...', conversations.length, 'conversation');\n      conversations.forEach(conversation => {\n        if (!joinedConversations.has(conversation.id)) {\n          socket.emit('join_conversation', conversation.id);\n          console.log(`🏠 CLIENT: Conversation ${conversation.id} katıldı`);\n          setJoinedConversations(prev => new Set([...prev, conversation.id]));\n        }\n      });\n    }\n  }, [socket, conversations, joinedConversations]);\n\n  // Conversations yükleme\n  const loadConversations = async () => {\n    try {\n      setIsLoading(true);\n      const response = await api.get('/messages/conversations');\n\n      // API verisini frontend formatına çevir\n      const formattedConversations = response.data.conversations.map(conversation => {\n        var _conversation$lastMes, _conversation$lastMes2;\n        return {\n          id: conversation.id,\n          expertId: conversation.otherUser.id,\n          expertName: conversation.otherUser.name,\n          expertTitle: conversation.otherUser.role === 'Expert' ? 'Uzman' : conversation.otherUser.role,\n          lastMessage: ((_conversation$lastMes = conversation.lastMessage) === null || _conversation$lastMes === void 0 ? void 0 : _conversation$lastMes.content) || 'Henüz mesaj yok',\n          timestamp: ((_conversation$lastMes2 = conversation.lastMessage) === null || _conversation$lastMes2 === void 0 ? void 0 : _conversation$lastMes2.timestamp) || conversation.createdAt,\n          unread: conversation.lastMessage ? !conversation.lastMessage.isRead && conversation.lastMessage.senderId !== user.id : false,\n          avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(conversation.otherUser.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n          status: onlineUsers.has(conversation.otherUser.id) ? 'online' : 'offline',\n          starred: false,\n          archived: false\n        };\n      });\n      setConversations(formattedConversations);\n    } catch (error) {\n      console.error('Konuşmalar yüklenirken hata:', error);\n      toast.error('Konuşmalar yüklenemedi');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Mesaj yükleme ve okundu işaretleme\n  useEffect(() => {\n    if (selectedConversation) {\n      loadMessages(selectedConversation.id);\n      markConversationAsRead(selectedConversation.id);\n\n      // Conversation'ı seçildiğinde unread durumunu false yap\n      setConversations(prev => prev.map(conv => conv.id === selectedConversation.id ? {\n        ...conv,\n        unread: false\n      } : conv));\n    }\n  }, [selectedConversation, loadMessages, markConversationAsRead]);\n\n  // Conversation'daki mesajları okundu olarak işaretle\n  const markConversationAsRead = async conversationId => {\n    try {\n      // Server'a okundu bilgisi gönder (mesajlar yüklenmeden önce de çalışır)\n      const response = await api.put(`/messages/conversations/${conversationId}/read`);\n      if (response.data.updatedCount > 0) {\n        console.log('📖 CLIENT: Marked', response.data.updatedCount, 'messages as read');\n\n        // Local state'i güncelle\n        setMessages(prev => prev.map(msg => msg.senderId !== user.id ? {\n          ...msg,\n          read: true\n        } : msg));\n\n        // Socket'e okundu bilgisi gönder\n        if (socket) {\n          socket.emit('mark_messages_read', {\n            conversationId,\n            messageIds: [] // Server zaten hangi mesajların okundu olduğunu biliyor\n          });\n        }\n      }\n    } catch (error) {\n      console.error('Mesajları okundu olarak işaretlerken hata:', error);\n    }\n  };\n\n  // Conversations yükleme\n  const loadMessages = async conversationId => {\n    try {\n      const response = await api.get(`/messages/conversations/${conversationId}`);\n\n      // API verisini frontend formatına çevir\n      const formattedMessages = response.data.messages.map(message => ({\n        id: message.id,\n        senderId: message.senderId,\n        senderName: message.sender.name,\n        senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        text: message.content,\n        timestamp: message.createdAt,\n        read: message.isRead,\n        delivered: true,\n        // Veritabanından gelen mesajlar delivered olarak kabul edilir\n        attachments: message.attachments\n      }));\n      setMessages(formattedMessages);\n\n      // Scroll to bottom\n      setTimeout(() => {\n        var _messagesEndRef$curre2;\n        (_messagesEndRef$curre2 = messagesEndRef.current) === null || _messagesEndRef$curre2 === void 0 ? void 0 : _messagesEndRef$curre2.scrollIntoView({\n          behavior: 'smooth'\n        });\n      }, 100);\n    } catch (error) {\n      console.error('Mesajlar yüklenirken hata:', error);\n      toast.error('Mesajlar yüklenemedi');\n    }\n  };\n  // Mesaj gönderme\n  const sendMessage = async () => {\n    if (!messageText.trim() || !selectedConversation) return;\n    console.log('📤 Mesaj gönderiliyor:', {\n      receiverId: selectedConversation.expertId,\n      content: messageText.trim()\n    });\n    try {\n      const response = await api.post('/messages/send', {\n        receiverId: selectedConversation.expertId,\n        content: messageText.trim()\n      });\n      console.log('✅ Mesaj gönderildi:', response.data);\n\n      // Sadece mesaj input'unu temizle - mesaj socket'ten gelecek\n      setMessageText('');\n    } catch (error) {\n      console.error('Mesaj gönderilirken hata:', error);\n      toast.error('Mesaj gönderilemedi');\n    }\n  };\n  useEffect(() => {\n    // Mesajları otomatik kaydır\n    if (messagesEndRef.current && messagesContainerRef.current) {\n      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;\n    }\n  }, [messages]);\n\n  // Mesaj gönderme\n  const handleSendMessage = e => {\n    e.preventDefault();\n    sendMessage();\n  };\n\n  // Konuşma seçme\n  const handleSelectConversation = conversation => {\n    setSelectedConversation(conversation);\n  };\n\n  // Tarih formatı\n  const formatMessageDate = dateString => {\n    const date = new Date(dateString);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    if (date.toDateString() === today.toDateString()) {\n      return format(date, 'HH:mm', {\n        locale: tr\n      });\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Dün ' + format(date, 'HH:mm', {\n        locale: tr\n      });\n    } else {\n      return format(date, 'dd MMM HH:mm', {\n        locale: tr\n      });\n    }\n  };\n\n  // Konuşmaları filtrele\n  const filteredConversations = conversations.filter(conv => {\n    // Arama filtresi\n    const matchesSearch = conv.expertName.toLowerCase().includes(searchTerm.toLowerCase()) || conv.lastMessage.toLowerCase().includes(searchTerm.toLowerCase());\n\n    // Durum filtresi\n    const matchesFilter = filter === 'all' || filter === 'unread' && conv.unread || filter === 'archived' && conv.archived || filter === 'starred' && conv.starred;\n    return matchesSearch && matchesFilter;\n  });\n\n  // Konuşmayı yıldızla\n  const toggleStar = id => {\n    setConversations(prevConversations => prevConversations.map(conv => conv.id === id ? {\n      ...conv,\n      starred: !conv.starred\n    } : conv));\n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({\n        ...prev,\n        starred: !prev.starred\n      }));\n    }\n  };\n\n  // Konuşmayı arşivle\n  const toggleArchive = id => {\n    setConversations(prevConversations => prevConversations.map(conv => conv.id === id ? {\n      ...conv,\n      archived: !conv.archived\n    } : conv));\n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({\n        ...prev,\n        archived: !prev.archived\n      }));\n    }\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 502,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 501,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-gray-50 min-h-screen pb-12\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-pink-500 to-pink-600 shadow-lg rounded-lg p-6 mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-white\",\n              children: \"Mesajlar\\u0131m\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-pink-100\",\n              children: \"Uzmanlar\\u0131n\\u0131zla g\\xFCvenli bir \\u015Fekilde ileti\\u015Fim kurun\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 513,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-3 sm:mt-0 flex space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/client/experts\",\n              className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-pink-600 bg-white hover:bg-pink-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-colors duration-150\",\n              children: [/*#__PURE__*/_jsxDEV(UserGroupIcon, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 17\n              }, this), \"Uzmanlar\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/client/appointments\",\n              className: \"inline-flex items-center px-4 py-2 border border-white text-sm font-medium rounded-md shadow-sm text-white hover:bg-pink-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-colors duration-150\",\n              children: [/*#__PURE__*/_jsxDEV(CalendarIcon, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 531,\n                columnNumber: 17\n              }, this), \"Randevular\\u0131m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 519,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 512,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 511,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-md overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-12 h-[75vh]\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-span-12 md:col-span-4 border-r border-gray-200 flex flex-col\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 border-b border-gray-200 bg-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-xl font-semibold text-gray-800 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(ChatBubbleLeftEllipsisIcon, {\n                  className: \"h-6 w-6 text-teal-600 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 545,\n                  columnNumber: 19\n                }, this), \"Mesajlar\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-3 relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Konu\\u015Fmalarda ara...\",\n                  className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500\",\n                  value: searchTerm,\n                  onChange: e => setSearchTerm(e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 549,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                  className: \"h-5 w-5 text-gray-400 absolute left-3 top-2.5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 556,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 548,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-3 flex space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `px-3 py-1 text-sm rounded-full ${filter === 'all' ? 'bg-teal-100 text-teal-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                  onClick: () => setFilter('all'),\n                  children: \"T\\xFCm\\xFC\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 559,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `px-3 py-1 text-sm rounded-full ${filter === 'unread' ? 'bg-teal-100 text-teal-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                  onClick: () => setFilter('unread'),\n                  children: \"Okunmam\\u0131\\u015F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `px-3 py-1 text-sm rounded-full ${filter === 'starred' ? 'bg-teal-100 text-teal-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                  onClick: () => setFilter('starred'),\n                  children: \"Y\\u0131ld\\u0131zl\\u0131\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 575,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `px-3 py-1 text-sm rounded-full ${filter === 'archived' ? 'bg-teal-100 text-teal-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                  onClick: () => setFilter('archived'),\n                  children: \"Ar\\u015Fiv\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 583,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: conversationsRef,\n              style: {\n                height: 'calc(75vh - 145px)',\n                overflowY: 'auto',\n                scrollbarWidth: 'thin',\n                scrollbarColor: '#D1D5DB #F3F4F6'\n              },\n              children: filteredConversations.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 text-center text-gray-500\",\n                children: \"Hi\\xE7 mesaj\\u0131n\\u0131z yok\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 603,\n                columnNumber: 19\n              }, this) : filteredConversations.map(conversation => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `p-3 cursor-pointer hover:bg-gray-50 transition-colors duration-200 ${(selectedConversation === null || selectedConversation === void 0 ? void 0 : selectedConversation.id) === conversation.id ? 'bg-teal-50' : ''} ${conversation.unread ? 'bg-teal-50 hover:bg-teal-100' : ''}`,\n                onClick: () => handleSelectConversation(conversation),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative flex-shrink-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: conversation.avatar,\n                      alt: conversation.expertName,\n                      className: `h-10 w-10 rounded-full ${(selectedConversation === null || selectedConversation === void 0 ? void 0 : selectedConversation.id) === conversation.id ? 'ring-2 ring-teal-600' : ''}`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 617,\n                      columnNumber: 27\n                    }, this), conversation.status === 'online' && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 627,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 616,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-start\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: `text-sm font-medium ${conversation.unread ? 'text-gray-900' : 'text-gray-700'}`,\n                        children: conversation.expertName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 632,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [conversation.starred && /*#__PURE__*/_jsxDEV(StarIcon, {\n                          className: \"h-4 w-4 text-yellow-400 fill-current\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 637,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-xs text-gray-500\",\n                          children: formatMessageDate(conversation.timestamp)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 639,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 635,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 631,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500\",\n                      children: conversation.expertTitle\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 644,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: `text-sm truncate mt-1 ${conversation.unread ? 'text-gray-900 font-medium' : 'text-gray-500'}`,\n                      children: conversation.lastMessage\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 645,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-center mt-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-gray-500\",\n                        children: onlineUsers.has(conversation.expertId) ? /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-green-500 font-medium\",\n                          children: \"\\xC7evrimi\\xE7i\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 653,\n                          columnNumber: 35\n                        }, this) : conversation.lastSeen ? `Son görülme: ${conversation.lastSeen}` : ''\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 651,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex space-x-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: e => {\n                            e.stopPropagation();\n                            toggleStar(conversation.id);\n                          },\n                          className: \"text-gray-400 hover:text-yellow-400\",\n                          children: /*#__PURE__*/_jsxDEV(StarIcon, {\n                            className: `h-4 w-4 ${conversation.starred ? 'text-yellow-400 fill-current' : ''}`\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 666,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 659,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: e => {\n                            e.stopPropagation();\n                            toggleArchive(conversation.id);\n                          },\n                          className: \"text-gray-400 hover:text-gray-600\",\n                          children: /*#__PURE__*/_jsxDEV(ArchiveBoxIcon, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 675,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 668,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 658,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 650,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 630,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 615,\n                  columnNumber: 23\n                }, this), conversation.unread && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"inline-flex items-center ml-12 mt-1 px-2 py-0.5 rounded-full text-xs font-medium bg-teal-600 text-white\",\n                  children: \"Yeni\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 682,\n                  columnNumber: 25\n                }, this)]\n              }, conversation.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 608,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 593,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-span-12 md:col-span-8 flex flex-col\",\n            children: selectedConversation ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 border-b border-gray-200 bg-white flex justify-between items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative mr-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: selectedConversation.avatar,\n                      alt: selectedConversation.expertName,\n                      className: \"h-10 w-10 rounded-full\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 699,\n                      columnNumber: 25\n                    }, this), selectedConversation.status === 'online' && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 705,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 698,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-lg font-medium text-gray-800\",\n                      children: selectedConversation.expertName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 709,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500\",\n                      children: [selectedConversation.expertTitle, \" \", ' • ', selectedConversation.status === 'online' ? 'Çevrimiçi' : selectedConversation.lastSeen ? `Son görülme: ${selectedConversation.lastSeen}` : '']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 712,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 708,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 697,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Link, {\n                    to: `/client/experts/${selectedConversation.expertId}`,\n                    className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                    children: /*#__PURE__*/_jsxDEV(UserIcon, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 727,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 723,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Link, {\n                    to: `/client/appointments?expert=${selectedConversation.expertId}`,\n                    className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                    children: /*#__PURE__*/_jsxDEV(ClockIcon, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 733,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 729,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Link, {\n                    to: `/client/sessions?expert=${selectedConversation.expertId}`,\n                    className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                    children: /*#__PURE__*/_jsxDEV(VideoCameraIcon, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 739,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 735,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                    children: /*#__PURE__*/_jsxDEV(EllipsisHorizontalIcon, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 742,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 741,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 722,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 696,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                ref: messagesContainerRef,\n                className: \"p-4 bg-gray-50\",\n                style: {\n                  height: 'calc(75vh - 195px)',\n                  overflowY: 'auto',\n                  scrollbarWidth: 'thin',\n                  scrollbarColor: '#D1D5DB #F3F4F6'\n                },\n                children: [messages.map((message, index) => {\n                  const isSender = message.senderId === user.id;\n                  const showAvatar = index === 0 || messages[index - 1].senderId !== message.senderId;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `flex ${isSender ? 'justify-end' : 'justify-start'} mb-4`,\n                    children: [!isSender && showAvatar && /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: message.senderAvatar,\n                      alt: message.senderName,\n                      className: \"h-8 w-8 rounded-full mr-2 mt-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 765,\n                      columnNumber: 29\n                    }, this), !isSender && !showAvatar && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-8 mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 771,\n                      columnNumber: 56\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `max-w-xs md:max-w-md lg:max-w-lg rounded-lg px-4 py-2 ${isSender ? 'bg-teal-600 text-white rounded-br-none' : 'bg-white text-gray-800 rounded-bl-none border border-gray-200'}`,\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm\",\n                        children: message.text\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 779,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `text-xs mt-1 ${isSender ? 'text-teal-200' : 'text-gray-500'} flex items-center justify-end`,\n                        children: [formatMessageDate(message.timestamp), isSender && /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                          className: `h-3 w-3 ml-1 ${message.read ? 'text-blue-400' : 'text-gray-400'}`,\n                          title: message.read ? 'Okundu' : 'İletildi'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 783,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 780,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 772,\n                      columnNumber: 27\n                    }, this), isSender && showAvatar && /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: message.senderAvatar,\n                      alt: message.senderName,\n                      className: \"h-8 w-8 rounded-full ml-2 mt-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 791,\n                      columnNumber: 29\n                    }, this), isSender && !showAvatar && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-8 ml-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 797,\n                      columnNumber: 55\n                    }, this)]\n                  }, message.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 763,\n                    columnNumber: 25\n                  }, this);\n                }), /*#__PURE__*/_jsxDEV(\"div\", {\n                  ref: messagesEndRef\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 801,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 748,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3 border-t border-gray-200 bg-white\",\n                children: /*#__PURE__*/_jsxDEV(\"form\", {\n                  onSubmit: handleSendMessage,\n                  className: \"flex items-end\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    className: \"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\",\n                    children: /*#__PURE__*/_jsxDEV(PaperClipIcon, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 811,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 807,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n                      className: \"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 resize-none\",\n                      placeholder: \"Mesaj\\u0131n\\u0131z\\u0131 yaz\\u0131n...\",\n                      rows: \"2\",\n                      value: messageText,\n                      onChange: e => setMessageText(e.target.value),\n                      onKeyDown: e => {\n                        if (e.key === 'Enter' && !e.shiftKey) {\n                          e.preventDefault();\n                          sendMessage();\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 814,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 813,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    className: \"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\",\n                    children: /*#__PURE__*/_jsxDEV(FaceSmileIcon, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 832,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 828,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"submit\",\n                    disabled: !messageText.trim(),\n                    className: `ml-2 p-2 rounded-full ${messageText.trim() ? 'bg-teal-600 text-white hover:bg-teal-700' : 'bg-gray-200 text-gray-400 cursor-not-allowed'} focus:outline-none`,\n                    children: /*#__PURE__*/_jsxDEV(PaperAirplaneIcon, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 843,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 834,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 806,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 805,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) :\n            /*#__PURE__*/\n            // Mesaj seçilmediğinde\n            _jsxDEV(\"div\", {\n              className: \"flex-1 flex flex-col items-center justify-center p-6 bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full max-w-md text-center\",\n                children: [/*#__PURE__*/_jsxDEV(ChatBubbleLeftEllipsisIcon, {\n                  className: \"h-16 w-16 text-gray-300 mb-4 mx-auto\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 852,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-medium text-gray-800 mb-2\",\n                  children: \"Mesajlar\\u0131n\\u0131z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 853,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-500 mx-auto\",\n                  children: \"Mesajla\\u015Fmaya ba\\u015Flamak i\\xE7in sol taraftan bir konu\\u015Fma se\\xE7in veya yeni bir uzmanla ileti\\u015Fime ge\\xE7in.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 854,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-6\",\n                  children: /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/client/experts\",\n                    className: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500\",\n                    children: [/*#__PURE__*/_jsxDEV(UserIcon, {\n                      className: \"-ml-1 mr-2 h-5 w-5\",\n                      \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 862,\n                      columnNumber: 25\n                    }, this), \"Uzmanlar\\u0131 Ke\\u015Ffedin\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 858,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 857,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 851,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 850,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 692,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 540,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 539,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 509,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 508,\n    columnNumber: 5\n  }, this);\n};\n_s(ClientMessagesPage, \"rYHyKpN34Dv9IpuQWPT+V9L0EHM=\", false, function () {\n  return [useAuth];\n});\n_c = ClientMessagesPage;\nexport default ClientMessagesPage;\nvar _c;\n$RefreshReg$(_c, \"ClientMessagesPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useAuth", "api", "toast", "io", "ChatBubbleLeftEllipsisIcon", "MagnifyingGlassIcon", "UserIcon", "PaperAirplaneIcon", "PaperClipIcon", "FaceSmileIcon", "EllipsisHorizontalIcon", "VideoCameraIcon", "CheckCircleIcon", "ArchiveBoxIcon", "UserGroupIcon", "CalendarIcon", "format", "tr", "Link", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ClientMessagesPage", "_s", "user", "isLoading", "setIsLoading", "conversations", "setConversations", "selectedConversation", "setSelectedConversation", "messages", "setMessages", "messageText", "setMessageText", "searchTerm", "setSearchTerm", "filter", "setFilter", "socket", "setSocket", "onlineUsers", "setOnlineUsers", "Set", "messagesEndRef", "conversationsRef", "messagesContainerRef", "token", "localStorage", "getItem", "id", "console", "log", "socketConnection", "process", "env", "REACT_APP_API_URL", "auth", "transports", "reconnection", "reconnectionDelay", "reconnectionAttempts", "timeout", "on", "connected", "rooms", "emit", "reason", "error", "attemptNumber", "heartbeatInterval", "setInterval", "response", "clearInterval", "disconnect", "hasToken", "<PERSON><PERSON>ser", "handleNewMessage", "message", "senderId", "conversationId", "prev", "map", "conv", "lastMessage", "content", "timestamp", "createdAt", "unread", "currentSelected", "messageExists", "some", "msg", "sender<PERSON>ame", "sender", "name", "senderAvatar", "encodeURIComponent", "text", "read", "isRead", "delivered", "setTimeout", "markConversationAsRead", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleMessageSent", "handleUserStatusChange", "data", "newSet", "status", "add", "userId", "delete", "Array", "from", "handleOnlineUsersList", "userIds", "handleUserTyping", "setTypingUsers", "handleUserStoppedTyping", "handleMessagesRead", "readBy", "messageIds", "includes", "off", "loadConversations", "joinedConversations", "setJoinedConversations", "length", "for<PERSON>ach", "conversation", "has", "get", "formattedConversations", "_conversation$lastMes", "_conversation$lastMes2", "expertId", "otherUser", "expertName", "expert<PERSON><PERSON>le", "role", "avatar", "starred", "archived", "loadMessages", "put", "updatedCount", "formattedMessages", "attachments", "_messagesEndRef$curre2", "sendMessage", "trim", "receiverId", "post", "scrollTop", "scrollHeight", "handleSendMessage", "e", "preventDefault", "handleSelectConversation", "formatMessageDate", "dateString", "date", "Date", "today", "yesterday", "setDate", "getDate", "toDateString", "locale", "filteredConversations", "matchesSearch", "toLowerCase", "matchesFilter", "toggleStar", "prevConversations", "toggleArchive", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "type", "placeholder", "value", "onChange", "target", "onClick", "ref", "style", "height", "overflowY", "scrollbarWidth", "scrollbarColor", "src", "alt", "StarIcon", "lastSeen", "stopPropagation", "ClockIcon", "index", "isSender", "showAvatar", "title", "onSubmit", "rows", "onKeyDown", "key", "shift<PERSON>ey", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Projeler/kidgarden/burky_root_web/client/src/pages/client/messages/ClientMessagesPage.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '../../../hooks/useAuth';\nimport api from '../../../services/api';\nimport { toast } from 'react-hot-toast';\nimport io from 'socket.io-client';\nimport {\n  ChatBubbleLeftEllipsisIcon,\n  MagnifyingGlassIcon,\n  UserIcon,\n  PaperAirplaneIcon,\n  PaperClipIcon,\n  FaceSmileIcon,\n  EllipsisHorizontalIcon,\n  VideoCameraIcon,\n  CheckCircleIcon,\n  ArchiveBoxIcon,\n  UserGroupIcon,\n  CalendarIcon\n} from '@heroicons/react/24/outline';\nimport { format } from 'date-fns';\nimport { tr } from 'date-fns/locale';\nimport { Link } from 'react-router-dom';\n\n/**\n * Danışan mesajlaş<PERSON> sayfası\n */\nconst ClientMessagesPage = () => {\n  const { user } = useAuth();\n  const [isLoading, setIsLoading] = useState(true);\n  const [conversations, setConversations] = useState([]);\n  const [selectedConversation, setSelectedConversation] = useState(null);\n  const [messages, setMessages] = useState([]);\n  const [messageText, setMessageText] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filter, setFilter] = useState('all'); // all, unread, archived, starred\n  const [socket, setSocket] = useState(null);\n  const [onlineUsers, setOnlineUsers] = useState(new Set());\n\n  const messagesEndRef = useRef(null);\n  const conversationsRef = useRef(null);\n  const messagesContainerRef = useRef(null);\n\n  // Socket.IO connection - ayrı useEffect\n  useEffect(() => {\n    const token = localStorage.getItem('accessToken');\n    if (token && user?.id) {\n      console.log('🔌 CLIENT: Socket.IO bağlantısı kuruluyor... User ID:', user.id);\n      const socketConnection = io(process.env.REACT_APP_API_URL || 'http://localhost:5000', {\n        auth: { token },\n        transports: ['websocket', 'polling'],\n        reconnection: true,\n        reconnectionDelay: 1000,\n        reconnectionAttempts: 5,\n        timeout: 20000\n      });\n\n      setSocket(socketConnection);\n\n      // Connection events\n      socketConnection.on('connect', () => {\n        console.log('✅ CLIENT: Socket.IO bağlantısı kuruldu:', socketConnection.id);\n        console.log('🔗 CLIENT: Socket connected:', socketConnection.connected);\n        console.log('🏠 CLIENT: Socket rooms:', socketConnection.rooms);\n        // Kullanıcının online olduğunu bildir\n        socketConnection.emit('user_online');\n        // Online kullanıcı listesini al\n        socketConnection.emit('get_online_users');\n      });\n\n      socketConnection.on('disconnect', (reason) => {\n        console.log('❌ CLIENT: Socket.IO bağlantısı kesildi:', reason);\n      });\n\n      socketConnection.on('connect_error', (error) => {\n        console.error('🔥 CLIENT: Socket.IO bağlantı hatası:', error);\n      });\n\n      socketConnection.on('reconnect', (attemptNumber) => {\n        console.log('🔄 CLIENT: Socket.IO yeniden bağlandı, deneme:', attemptNumber);\n      });\n\n      socketConnection.on('reconnect_error', (error) => {\n        console.error('🔥 CLIENT: Socket.IO yeniden bağlanma hatası:', error);\n      });\n\n      // Heartbeat mekanizması\n      const heartbeatInterval = setInterval(() => {\n        if (socketConnection.connected) {\n          socketConnection.emit('ping', (response) => {\n            if (response === 'pong') {\n              console.log('💓 CLIENT: Heartbeat OK');\n            }\n          });\n        }\n      }, 30000); // 30 saniyede bir ping gönder\n\n      return () => {\n        clearInterval(heartbeatInterval);\n        socketConnection.disconnect();\n      };\n    } else {\n      console.log('❌ CLIENT: Socket bağlantısı kurulamıyor - token veya user eksik:', {\n        hasToken: !!token,\n        hasUser: !!user?.id\n      });\n    }\n  }, [user?.id]);\n\n  // Socket event listeners - ayrı useEffect\n  useEffect(() => {\n    if (socket && user?.id) {\n      console.log('🎧 Socket event listeners kuruluyor...');\n\n      // Sayfa yüklendiğinde online users listesini al\n      if (socket.connected) {\n        console.log('🔄 CLIENT: Requesting online users list on page load');\n        socket.emit('get_online_users');\n      }\n\n      // Socket event listeners\n      const handleNewMessage = (message) => {\n        console.log('📨 CLIENT: Yeni mesaj alındı:', message);\n        console.log('👤 CLIENT: Current user ID:', user.id);\n        console.log('💬 CLIENT: Message sender ID:', message.senderId);\n        console.log('🏠 CLIENT: Message conversation ID:', message.conversationId);\n\n        // Conversation listesini ÖNCE güncelle (her zaman)\n        setConversations(prev => {\n          console.log('📋 CLIENT: Updating conversations list');\n          return prev.map(conv =>\n            conv.id === message.conversationId\n              ? { ...conv, lastMessage: message.content, timestamp: message.createdAt, unread: message.senderId !== user.id }\n              : conv\n          );\n        });\n\n        // Eğer şu anda bu conversation açıksa, mesajı da ekle\n        setSelectedConversation(currentSelected => {\n          console.log('🎯 CLIENT: Current selected conversation:', currentSelected?.id);\n          if (currentSelected && message.conversationId === currentSelected.id) {\n            console.log('✅ CLIENT: Adding message to current conversation');\n            setMessages(prev => {\n              // Mesajın zaten var olup olmadığını kontrol et\n              const messageExists = prev.some(msg => msg.id === message.id);\n              if (messageExists) {\n                console.log('⚠️ CLIENT: Message already exists, skipping:', message.id);\n                return prev;\n              }\n\n              return [...prev, {\n                id: message.id,\n                senderId: message.senderId,\n                senderName: message.sender.name,\n                senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n                text: message.content,\n                timestamp: message.createdAt,\n                read: message.isRead,\n                delivered: true // Socket'ten gelen mesajlar delivered olarak kabul edilir\n              }];\n            });\n\n            // Eğer mesaj başka birinden geliyorsa ve conversation açıksa, otomatik okundu işaretle\n            if (message.senderId !== user.id) {\n              console.log('📖 CLIENT: Auto-marking message as read since conversation is open');\n              setTimeout(() => {\n                markConversationAsRead(message.conversationId);\n              }, 500); // Kısa bir gecikme ile\n            }\n\n            // Scroll to bottom\n            setTimeout(() => {\n              messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n            }, 100);\n          } else {\n            console.log('❌ CLIENT: Not adding message - conversation not selected or different conversation');\n          }\n          return currentSelected;\n        });\n      };\n\n      const handleMessageSent = (message) => {\n        console.log('✅ Mesaj gönderildi onayı:', message);\n      };\n\n      const handleUserStatusChange = (data) => {\n        console.log('🔄 CLIENT: User status change:', data);\n        setOnlineUsers(prev => {\n          const newSet = new Set(prev);\n          if (data.status === 'online') {\n            newSet.add(data.userId);\n          } else {\n            newSet.delete(data.userId);\n          }\n          console.log('🟢 CLIENT: Online users updated:', Array.from(newSet));\n          return newSet;\n        });\n      };\n\n      const handleOnlineUsersList = (userIds) => {\n        console.log('📋 CLIENT: Online users list received:', userIds);\n        setOnlineUsers(new Set(userIds));\n      };\n\n      const handleUserTyping = (data) => {\n        setSelectedConversation(currentSelected => {\n          if (currentSelected && data.conversationId === currentSelected.id) {\n            setTypingUsers(prev => new Set([...prev, data.userId]));\n          }\n          return currentSelected;\n        });\n      };\n\n      const handleUserStoppedTyping = (data) => {\n        setTypingUsers(prev => {\n          const newSet = new Set(prev);\n          newSet.delete(data.userId);\n          return newSet;\n        });\n      };\n\n      const handleMessagesRead = (data) => {\n        console.log('👁️ CLIENT: Messages read event:', data);\n        console.log('👁️ CLIENT: Read by user:', data.readBy, 'Current user:', user.id);\n        console.log('👁️ CLIENT: Message IDs marked as read:', data.messageIds);\n\n        // Eğer başka bir kullanıcı mesajları okudu ise, kendi gönderdiğim mesajları okundu olarak işaretle\n        if (data.readBy !== user.id) {\n          setMessages(prev => prev.map(msg => {\n            // Kendi gönderdiğim mesajları ve conversation'daki tüm mesajları güncelle\n            if (msg.senderId === user.id && msg.conversationId === data.conversationId) {\n              console.log('👁️ CLIENT: Marking my message as read:', msg.id);\n              return { ...msg, read: true };\n            }\n            // Ayrıca messageIds listesindeki mesajları da güncelle\n            if (data.messageIds.includes(msg.id)) {\n              console.log('👁️ CLIENT: Marking specific message as read:', msg.id);\n              return { ...msg, read: true };\n            }\n            return msg;\n          }));\n\n          // Conversation listesindeki unread durumunu da güncelle\n          setConversations(prev => prev.map(conv =>\n            conv.id === data.conversationId\n              ? { ...conv, unread: false }\n              : conv\n          ));\n        }\n      };\n\n      // Event listener'ları ekle\n      console.log('🎧 CLIENT: Adding socket event listeners...');\n      socket.on('new_message', handleNewMessage);\n      socket.on('message_sent', handleMessageSent);\n      socket.on('user_status_change', handleUserStatusChange);\n      socket.on('user_typing', handleUserTyping);\n      socket.on('user_stopped_typing', handleUserStoppedTyping);\n      socket.on('messages_read', handleMessagesRead);\n      socket.on('online_users_list', handleOnlineUsersList);\n      console.log('✅ CLIENT: Socket event listeners added');\n\n      // Cleanup function\n      return () => {\n        socket.off('new_message', handleNewMessage);\n        socket.off('message_sent', handleMessageSent);\n        socket.off('user_status_change', handleUserStatusChange);\n        socket.off('user_typing', handleUserTyping);\n        socket.off('user_stopped_typing', handleUserStoppedTyping);\n        socket.off('messages_read', handleMessagesRead);\n        socket.off('online_users_list', handleOnlineUsersList);\n      };\n    }\n  }, [socket, user.id]);\n\n  // Conversations yükleme - ayrı useEffect\n  useEffect(() => {\n    loadConversations();\n  }, []);\n\n  // Socket bağlandığında tüm conversation'lara join ol - sadece bir kez\n  const [joinedConversations, setJoinedConversations] = useState(new Set());\n\n  useEffect(() => {\n    if (socket && conversations.length > 0) {\n      console.log('🏠 CLIENT: Tüm conversation\\'lara katılıyor...', conversations.length, 'conversation');\n\n      conversations.forEach(conversation => {\n        if (!joinedConversations.has(conversation.id)) {\n          socket.emit('join_conversation', conversation.id);\n          console.log(`🏠 CLIENT: Conversation ${conversation.id} katıldı`);\n          setJoinedConversations(prev => new Set([...prev, conversation.id]));\n        }\n      });\n    }\n  }, [socket, conversations, joinedConversations]);\n\n  // Conversations yükleme\n  const loadConversations = async () => {\n    try {\n      setIsLoading(true);\n      const response = await api.get('/messages/conversations');\n\n      // API verisini frontend formatına çevir\n      const formattedConversations = response.data.conversations.map(conversation => ({\n        id: conversation.id,\n        expertId: conversation.otherUser.id,\n        expertName: conversation.otherUser.name,\n        expertTitle: conversation.otherUser.role === 'Expert' ? 'Uzman' : conversation.otherUser.role,\n        lastMessage: conversation.lastMessage?.content || 'Henüz mesaj yok',\n        timestamp: conversation.lastMessage?.timestamp || conversation.createdAt,\n        unread: conversation.lastMessage ? !conversation.lastMessage.isRead && conversation.lastMessage.senderId !== user.id : false,\n        avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(conversation.otherUser.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        status: onlineUsers.has(conversation.otherUser.id) ? 'online' : 'offline',\n        starred: false,\n        archived: false\n      }));\n\n      setConversations(formattedConversations);\n    } catch (error) {\n      console.error('Konuşmalar yüklenirken hata:', error);\n      toast.error('Konuşmalar yüklenemedi');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Mesaj yükleme ve okundu işaretleme\n  useEffect(() => {\n    if (selectedConversation) {\n      loadMessages(selectedConversation.id);\n      markConversationAsRead(selectedConversation.id);\n\n      // Conversation'ı seçildiğinde unread durumunu false yap\n      setConversations(prev => prev.map(conv =>\n        conv.id === selectedConversation.id\n          ? { ...conv, unread: false }\n          : conv\n      ));\n    }\n  }, [selectedConversation, loadMessages, markConversationAsRead]);\n\n  // Conversation'daki mesajları okundu olarak işaretle\n  const markConversationAsRead = async (conversationId) => {\n    try {\n      // Server'a okundu bilgisi gönder (mesajlar yüklenmeden önce de çalışır)\n      const response = await api.put(`/messages/conversations/${conversationId}/read`);\n\n      if (response.data.updatedCount > 0) {\n        console.log('📖 CLIENT: Marked', response.data.updatedCount, 'messages as read');\n\n        // Local state'i güncelle\n        setMessages(prev => prev.map(msg =>\n          msg.senderId !== user.id ? { ...msg, read: true } : msg\n        ));\n\n        // Socket'e okundu bilgisi gönder\n        if (socket) {\n          socket.emit('mark_messages_read', {\n            conversationId,\n            messageIds: [] // Server zaten hangi mesajların okundu olduğunu biliyor\n          });\n        }\n      }\n    } catch (error) {\n      console.error('Mesajları okundu olarak işaretlerken hata:', error);\n    }\n  };\n\n  // Conversations yükleme\n  const loadMessages = async (conversationId) => {\n    try {\n      const response = await api.get(`/messages/conversations/${conversationId}`);\n\n      // API verisini frontend formatına çevir\n      const formattedMessages = response.data.messages.map(message => ({\n        id: message.id,\n        senderId: message.senderId,\n        senderName: message.sender.name,\n        senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        text: message.content,\n        timestamp: message.createdAt,\n        read: message.isRead,\n        delivered: true, // Veritabanından gelen mesajlar delivered olarak kabul edilir\n        attachments: message.attachments\n      }));\n\n      setMessages(formattedMessages);\n\n      // Scroll to bottom\n      setTimeout(() => {\n        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n      }, 100);\n    } catch (error) {\n      console.error('Mesajlar yüklenirken hata:', error);\n      toast.error('Mesajlar yüklenemedi');\n    }\n  };\n  // Mesaj gönderme\n  const sendMessage = async () => {\n    if (!messageText.trim() || !selectedConversation) return;\n\n    console.log('📤 Mesaj gönderiliyor:', {\n      receiverId: selectedConversation.expertId,\n      content: messageText.trim()\n    });\n\n    try {\n      const response = await api.post('/messages/send', {\n        receiverId: selectedConversation.expertId,\n        content: messageText.trim()\n      });\n\n      console.log('✅ Mesaj gönderildi:', response.data);\n\n      // Sadece mesaj input'unu temizle - mesaj socket'ten gelecek\n      setMessageText('');\n\n    } catch (error) {\n      console.error('Mesaj gönderilirken hata:', error);\n      toast.error('Mesaj gönderilemedi');\n    }\n  };\n\n  useEffect(() => {\n    // Mesajları otomatik kaydır\n    if (messagesEndRef.current && messagesContainerRef.current) {\n      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;\n    }\n  }, [messages]);\n\n  // Mesaj gönderme\n  const handleSendMessage = (e) => {\n    e.preventDefault();\n    sendMessage();\n  };\n\n  // Konuşma seçme\n  const handleSelectConversation = (conversation) => {\n    setSelectedConversation(conversation);\n  };\n\n  // Tarih formatı\n  const formatMessageDate = (dateString) => {\n    const date = new Date(dateString);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    \n    if (date.toDateString() === today.toDateString()) {\n      return format(date, 'HH:mm', { locale: tr });\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Dün ' + format(date, 'HH:mm', { locale: tr });\n    } else {\n      return format(date, 'dd MMM HH:mm', { locale: tr });\n    }\n  };\n\n  // Konuşmaları filtrele\n  const filteredConversations = conversations.filter(conv => {\n    // Arama filtresi\n    const matchesSearch = conv.expertName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         conv.lastMessage.toLowerCase().includes(searchTerm.toLowerCase());\n    \n    // Durum filtresi\n    const matchesFilter = filter === 'all' ||\n                         (filter === 'unread' && conv.unread) ||\n                         (filter === 'archived' && conv.archived) ||\n                         (filter === 'starred' && conv.starred);\n                        \n    return matchesSearch && matchesFilter;\n  });\n\n  // Konuşmayı yıldızla\n  const toggleStar = (id) => {\n    setConversations(prevConversations =>\n      prevConversations.map(conv =>\n        conv.id === id ? { ...conv, starred: !conv.starred } : conv\n      )\n    );\n    \n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({ ...prev, starred: !prev.starred }));\n    }\n  };\n\n  // Konuşmayı arşivle\n  const toggleArchive = (id) => {\n    setConversations(prevConversations =>\n      prevConversations.map(conv =>\n        conv.id === id ? { ...conv, archived: !conv.archived } : conv\n      )\n    );\n    \n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({ ...prev, archived: !prev.archived }));\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-gray-50 min-h-screen pb-12\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-8\">\n        {/* Başlık ve Üst Kısım */}\n        <div className=\"bg-gradient-to-r from-pink-500 to-pink-600 shadow-lg rounded-lg p-6 mb-6\">\n          <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-white\">Mesajlarım</h1>\n              <p className=\"mt-1 text-pink-100\">\n                Uzmanlarınızla güvenli bir şekilde iletişim kurun\n              </p>\n            </div>\n            <div className=\"mt-3 sm:mt-0 flex space-x-2\">\n              <Link\n                to=\"/client/experts\"\n                className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-pink-600 bg-white hover:bg-pink-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-colors duration-150\"\n              >\n                <UserGroupIcon className=\"h-4 w-4 mr-2\" />\n                Uzmanlar\n              </Link>\n              <Link\n                to=\"/client/appointments\"\n                className=\"inline-flex items-center px-4 py-2 border border-white text-sm font-medium rounded-md shadow-sm text-white hover:bg-pink-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-colors duration-150\"\n              >\n                <CalendarIcon className=\"h-4 w-4 mr-2\" />\n                Randevularım\n              </Link>\n            </div>\n          </div>\n        </div>\n        \n        {/* Mesajlaşma arayüzü */}\n        <div className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n          <div className=\"grid grid-cols-12 h-[75vh]\">\n            {/* Sol Kenar - Konuşma Listesi */}\n            <div className=\"col-span-12 md:col-span-4 border-r border-gray-200 flex flex-col\">\n              <div className=\"p-4 border-b border-gray-200 bg-white\">\n                <h1 className=\"text-xl font-semibold text-gray-800 flex items-center\">\n                  <ChatBubbleLeftEllipsisIcon className=\"h-6 w-6 text-teal-600 mr-2\" />\n                  Mesajlar\n                </h1>\n                <div className=\"mt-3 relative\">\n                  <input\n                    type=\"text\"\n                    placeholder=\"Konuşmalarda ara...\"\n                    className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                  />\n                  <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400 absolute left-3 top-2.5\" />\n                </div>\n                <div className=\"mt-3 flex space-x-2\">\n                  <button\n                    className={`px-3 py-1 text-sm rounded-full ${filter === 'all' \n                      ? 'bg-teal-100 text-teal-800' \n                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                    onClick={() => setFilter('all')}\n                  >\n                    Tümü\n                  </button>\n                  <button\n                    className={`px-3 py-1 text-sm rounded-full ${filter === 'unread' \n                      ? 'bg-teal-100 text-teal-800' \n                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                    onClick={() => setFilter('unread')}\n                  >\n                    Okunmamış\n                  </button>\n                  <button\n                    className={`px-3 py-1 text-sm rounded-full ${filter === 'starred' \n                      ? 'bg-teal-100 text-teal-800' \n                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                    onClick={() => setFilter('starred')}\n                  >\n                    Yıldızlı\n                  </button>\n                  <button\n                    className={`px-3 py-1 text-sm rounded-full ${filter === 'archived' \n                      ? 'bg-teal-100 text-teal-800' \n                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                    onClick={() => setFilter('archived')}\n                  >\n                    Arşiv\n                  </button>\n                </div>\n              </div>\n              <div \n                ref={conversationsRef}\n                style={{\n                  height: 'calc(75vh - 145px)',\n                  overflowY: 'auto',\n                  scrollbarWidth: 'thin',\n                  scrollbarColor: '#D1D5DB #F3F4F6'\n                }}\n              >\n                {filteredConversations.length === 0 ? (\n                  <div className=\"p-4 text-center text-gray-500\">\n                    Hiç mesajınız yok\n                  </div>\n                ) : (\n                  filteredConversations.map(conversation => (\n                    <div\n                      key={conversation.id}\n                      className={`p-3 cursor-pointer hover:bg-gray-50 transition-colors duration-200 ${\n                        selectedConversation?.id === conversation.id ? 'bg-teal-50' : ''\n                      } ${conversation.unread ? 'bg-teal-50 hover:bg-teal-100' : ''}`}\n                      onClick={() => handleSelectConversation(conversation)}\n                    >\n                      <div className=\"flex items-start space-x-3\">\n                        <div className=\"relative flex-shrink-0\">\n                          <img\n                            src={conversation.avatar}\n                            alt={conversation.expertName}\n                            className={`h-10 w-10 rounded-full ${\n                              selectedConversation?.id === conversation.id \n                                ? 'ring-2 ring-teal-600' \n                                : ''\n                            }`}\n                          />\n                          {conversation.status === 'online' && (\n                            <span className=\"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"></span>\n                          )}\n                        </div>\n                        <div className=\"flex-1 min-w-0\">\n                          <div className=\"flex justify-between items-start\">\n                            <h3 className={`text-sm font-medium ${conversation.unread ? 'text-gray-900' : 'text-gray-700'}`}>\n                              {conversation.expertName}\n                            </h3>\n                            <div className=\"flex items-center space-x-1\">\n                              {conversation.starred && (\n                                <StarIcon className=\"h-4 w-4 text-yellow-400 fill-current\" />\n                              )}\n                              <span className=\"text-xs text-gray-500\">\n                                {formatMessageDate(conversation.timestamp)}\n                              </span>\n                            </div>\n                          </div>\n                          <p className=\"text-xs text-gray-500\">{conversation.expertTitle}</p>\n                          <p className={`text-sm truncate mt-1 ${\n                            conversation.unread ? 'text-gray-900 font-medium' : 'text-gray-500'\n                          }`}>\n                            {conversation.lastMessage}\n                          </p>\n                          <div className=\"flex justify-between items-center mt-1\">\n                            <span className=\"text-xs text-gray-500\">\n                              {onlineUsers.has(conversation.expertId)\n                                ? <span className=\"text-green-500 font-medium\">Çevrimiçi</span>\n                                : conversation.lastSeen\n                                  ? `Son görülme: ${conversation.lastSeen}`\n                                  : ''}\n                            </span>\n                            <div className=\"flex space-x-1\">\n                              <button \n                                onClick={(e) => {\n                                  e.stopPropagation();\n                                  toggleStar(conversation.id);\n                                }}\n                                className=\"text-gray-400 hover:text-yellow-400\"\n                              >\n                                <StarIcon className={`h-4 w-4 ${conversation.starred ? 'text-yellow-400 fill-current' : ''}`} />\n                              </button>\n                              <button \n                                onClick={(e) => {\n                                  e.stopPropagation();\n                                  toggleArchive(conversation.id);\n                                }}\n                                className=\"text-gray-400 hover:text-gray-600\"\n                              >\n                                <ArchiveBoxIcon className=\"h-4 w-4\" />\n                              </button>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                      {conversation.unread && (\n                        <span className=\"inline-flex items-center ml-12 mt-1 px-2 py-0.5 rounded-full text-xs font-medium bg-teal-600 text-white\">\n                          Yeni\n                        </span>\n                      )}\n                    </div>\n                  ))\n                )}\n              </div>\n            </div>\n            {/* Sağ Taraf - Mesaj Alanı */}\n            <div className=\"col-span-12 md:col-span-8 flex flex-col\">\n              {selectedConversation ? (\n                <>\n                  {/* Mesajlaşma Başlığı */}\n                  <div className=\"p-4 border-b border-gray-200 bg-white flex justify-between items-center\">\n                    <div className=\"flex items-center\">\n                      <div className=\"relative mr-3\">\n                        <img\n                          src={selectedConversation.avatar}\n                          alt={selectedConversation.expertName}\n                          className=\"h-10 w-10 rounded-full\"\n                        />\n                        {selectedConversation.status === 'online' && (\n                          <span className=\"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"></span>\n                        )}\n                      </div>\n                      <div>\n                        <h2 className=\"text-lg font-medium text-gray-800\">\n                          {selectedConversation.expertName}\n                        </h2>\n                        <p className=\"text-xs text-gray-500\">\n                          {selectedConversation.expertTitle} {' • '}\n                          {selectedConversation.status === 'online' \n                            ? 'Çevrimiçi' \n                            : selectedConversation.lastSeen \n                              ? `Son görülme: ${selectedConversation.lastSeen}` \n                              : ''}\n                        </p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center space-x-3\">\n                      <Link \n                        to={`/client/experts/${selectedConversation.expertId}`}\n                        className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\"\n                      >\n                        <UserIcon className=\"h-5 w-5\" />\n                      </Link>\n                      <Link \n                        to={`/client/appointments?expert=${selectedConversation.expertId}`}\n                        className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\"\n                      >\n                        <ClockIcon className=\"h-5 w-5\" />\n                      </Link>\n                      <Link \n                        to={`/client/sessions?expert=${selectedConversation.expertId}`}\n                        className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\"\n                      >\n                        <VideoCameraIcon className=\"h-5 w-5\" />\n                      </Link>\n                      <button className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\">\n                        <EllipsisHorizontalIcon className=\"h-5 w-5\" />\n                      </button>\n                    </div>\n                  </div>\n\n                  {/* Mesaj Alanı */}\n                  <div \n                    ref={messagesContainerRef}\n                    className=\"p-4 bg-gray-50\"\n                    style={{\n                      height: 'calc(75vh - 195px)',\n                      overflowY: 'auto',\n                      scrollbarWidth: 'thin',\n                      scrollbarColor: '#D1D5DB #F3F4F6'\n                    }}\n                  >\n                    {messages.map((message, index) => {\n                      const isSender = message.senderId === user.id;\n                      const showAvatar = index === 0 || messages[index - 1].senderId !== message.senderId;\n                      \n                      return (\n                        <div key={message.id} className={`flex ${isSender ? 'justify-end' : 'justify-start'} mb-4`}>\n                          {!isSender && showAvatar && (\n                            <img \n                              src={message.senderAvatar} \n                              alt={message.senderName}\n                              className=\"h-8 w-8 rounded-full mr-2 mt-1\"\n                            />\n                          )}\n                          {!isSender && !showAvatar && <div className=\"w-8 mr-2\"></div>}\n                          <div \n                            className={`max-w-xs md:max-w-md lg:max-w-lg rounded-lg px-4 py-2 ${\n                              isSender \n                                ? 'bg-teal-600 text-white rounded-br-none' \n                                : 'bg-white text-gray-800 rounded-bl-none border border-gray-200'\n                            }`}\n                          >\n                            <p className=\"text-sm\">{message.text}</p>\n                            <div className={`text-xs mt-1 ${isSender ? 'text-teal-200' : 'text-gray-500'} flex items-center justify-end`}>\n                              {formatMessageDate(message.timestamp)}\n                              {isSender && (\n                                <CheckCircleIcon\n                                  className={`h-3 w-3 ml-1 ${message.read ? 'text-blue-400' : 'text-gray-400'}`}\n                                  title={message.read ? 'Okundu' : 'İletildi'}\n                                />\n                              )}\n                            </div>\n                          </div>\n                          {isSender && showAvatar && (\n                            <img \n                              src={message.senderAvatar}\n                              alt={message.senderName}\n                              className=\"h-8 w-8 rounded-full ml-2 mt-1\"\n                            />\n                          )}\n                          {isSender && !showAvatar && <div className=\"w-8 ml-2\"></div>}\n                        </div>\n                      );\n                    })}\n                    <div ref={messagesEndRef} />\n                  </div>\n\n                  {/* Mesaj Giriş Alanı */}\n                  <div className=\"p-3 border-t border-gray-200 bg-white\">\n                    <form onSubmit={handleSendMessage} className=\"flex items-end\">\n                      <button\n                        type=\"button\"\n                        className=\"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\"\n                      >\n                        <PaperClipIcon className=\"h-5 w-5\" />\n                      </button>\n                      <div className=\"flex-1 mx-2\">\n                        <textarea\n                          className=\"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 resize-none\"\n                          placeholder=\"Mesajınızı yazın...\"\n                          rows=\"2\"\n                          value={messageText}\n                          onChange={(e) => setMessageText(e.target.value)}\n                          onKeyDown={(e) => {\n                            if (e.key === 'Enter' && !e.shiftKey) {\n                              e.preventDefault();\n                              sendMessage();\n                            }\n                          }}\n                        ></textarea>\n                      </div>\n                      <button\n                        type=\"button\"\n                        className=\"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\"\n                      >\n                        <FaceSmileIcon className=\"h-5 w-5\" />\n                      </button>\n                      <button\n                        type=\"submit\"\n                        disabled={!messageText.trim()}\n                        className={`ml-2 p-2 rounded-full ${\n                          messageText.trim() \n                            ? 'bg-teal-600 text-white hover:bg-teal-700' \n                            : 'bg-gray-200 text-gray-400 cursor-not-allowed'\n                        } focus:outline-none`}\n                      >\n                        <PaperAirplaneIcon className=\"h-5 w-5\" />\n                      </button>\n                    </form>\n                  </div>\n                </>\n              ) : (\n                // Mesaj seçilmediğinde\n                <div className=\"flex-1 flex flex-col items-center justify-center p-6 bg-gray-50\">\n                  <div className=\"w-full max-w-md text-center\">\n                    <ChatBubbleLeftEllipsisIcon className=\"h-16 w-16 text-gray-300 mb-4 mx-auto\" />\n                    <h3 className=\"text-xl font-medium text-gray-800 mb-2\">Mesajlarınız</h3>\n                    <p className=\"text-gray-500 mx-auto\">\n                      Mesajlaşmaya başlamak için sol taraftan bir konuşma seçin veya yeni bir uzmanla iletişime geçin.\n                    </p>\n                    <div className=\"mt-6\">\n                      <Link\n                        to=\"/client/experts\"\n                        className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500\"\n                      >\n                        <UserIcon className=\"-ml-1 mr-2 h-5 w-5\" aria-hidden=\"true\" />\n                        Uzmanları Keşfedin\n                      </Link>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n} \n\nexport default ClientMessagesPage; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,GAAG,MAAM,uBAAuB;AACvC,SAASC,KAAK,QAAQ,iBAAiB;AACvC,OAAOC,EAAE,MAAM,kBAAkB;AACjC,SACEC,0BAA0B,EAC1BC,mBAAmB,EACnBC,QAAQ,EACRC,iBAAiB,EACjBC,aAAa,EACbC,aAAa,EACbC,sBAAsB,EACtBC,eAAe,EACfC,eAAe,EACfC,cAAc,EACdC,aAAa,EACbC,YAAY,QACP,6BAA6B;AACpC,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,EAAE,QAAQ,iBAAiB;AACpC,SAASC,IAAI,QAAQ,kBAAkB;;AAEvC;AACA;AACA;AAFA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM;IAAEC;EAAK,CAAC,GAAGzB,OAAO,CAAC,CAAC;EAC1B,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC+B,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqC,WAAW,EAAEC,cAAc,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuC,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyC,MAAM,EAAEC,SAAS,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC2C,MAAM,EAAEC,SAAS,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAAC6C,WAAW,EAAEC,cAAc,CAAC,GAAG9C,QAAQ,CAAC,IAAI+C,GAAG,CAAC,CAAC,CAAC;EAEzD,MAAMC,cAAc,GAAG9C,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM+C,gBAAgB,GAAG/C,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMgD,oBAAoB,GAAGhD,MAAM,CAAC,IAAI,CAAC;;EAEzC;EACAD,SAAS,CAAC,MAAM;IACd,MAAMkD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACjD,IAAIF,KAAK,IAAIvB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0B,EAAE,EAAE;MACrBC,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAE5B,IAAI,CAAC0B,EAAE,CAAC;MAC7E,MAAMG,gBAAgB,GAAGnD,EAAE,CAACoD,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB,EAAE;QACpFC,IAAI,EAAE;UAAEV;QAAM,CAAC;QACfW,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;QACpCC,YAAY,EAAE,IAAI;QAClBC,iBAAiB,EAAE,IAAI;QACvBC,oBAAoB,EAAE,CAAC;QACvBC,OAAO,EAAE;MACX,CAAC,CAAC;MAEFtB,SAAS,CAACa,gBAAgB,CAAC;;MAE3B;MACAA,gBAAgB,CAACU,EAAE,CAAC,SAAS,EAAE,MAAM;QACnCZ,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEC,gBAAgB,CAACH,EAAE,CAAC;QAC3EC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEC,gBAAgB,CAACW,SAAS,CAAC;QACvEb,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEC,gBAAgB,CAACY,KAAK,CAAC;QAC/D;QACAZ,gBAAgB,CAACa,IAAI,CAAC,aAAa,CAAC;QACpC;QACAb,gBAAgB,CAACa,IAAI,CAAC,kBAAkB,CAAC;MAC3C,CAAC,CAAC;MAEFb,gBAAgB,CAACU,EAAE,CAAC,YAAY,EAAGI,MAAM,IAAK;QAC5ChB,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEe,MAAM,CAAC;MAChE,CAAC,CAAC;MAEFd,gBAAgB,CAACU,EAAE,CAAC,eAAe,EAAGK,KAAK,IAAK;QAC9CjB,OAAO,CAACiB,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC/D,CAAC,CAAC;MAEFf,gBAAgB,CAACU,EAAE,CAAC,WAAW,EAAGM,aAAa,IAAK;QAClDlB,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEiB,aAAa,CAAC;MAC9E,CAAC,CAAC;MAEFhB,gBAAgB,CAACU,EAAE,CAAC,iBAAiB,EAAGK,KAAK,IAAK;QAChDjB,OAAO,CAACiB,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACvE,CAAC,CAAC;;MAEF;MACA,MAAME,iBAAiB,GAAGC,WAAW,CAAC,MAAM;QAC1C,IAAIlB,gBAAgB,CAACW,SAAS,EAAE;UAC9BX,gBAAgB,CAACa,IAAI,CAAC,MAAM,EAAGM,QAAQ,IAAK;YAC1C,IAAIA,QAAQ,KAAK,MAAM,EAAE;cACvBrB,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;YACxC;UACF,CAAC,CAAC;QACJ;MACF,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;MAEX,OAAO,MAAM;QACXqB,aAAa,CAACH,iBAAiB,CAAC;QAChCjB,gBAAgB,CAACqB,UAAU,CAAC,CAAC;MAC/B,CAAC;IACH,CAAC,MAAM;MACLvB,OAAO,CAACC,GAAG,CAAC,kEAAkE,EAAE;QAC9EuB,QAAQ,EAAE,CAAC,CAAC5B,KAAK;QACjB6B,OAAO,EAAE,CAAC,EAACpD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0B,EAAE;MACrB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC1B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0B,EAAE,CAAC,CAAC;;EAEd;EACArD,SAAS,CAAC,MAAM;IACd,IAAI0C,MAAM,IAAIf,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0B,EAAE,EAAE;MACtBC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;MAErD;MACA,IAAIb,MAAM,CAACyB,SAAS,EAAE;QACpBb,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;QACnEb,MAAM,CAAC2B,IAAI,CAAC,kBAAkB,CAAC;MACjC;;MAEA;MACA,MAAMW,gBAAgB,GAAIC,OAAO,IAAK;QACpC3B,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE0B,OAAO,CAAC;QACrD3B,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE5B,IAAI,CAAC0B,EAAE,CAAC;QACnDC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE0B,OAAO,CAACC,QAAQ,CAAC;QAC9D5B,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE0B,OAAO,CAACE,cAAc,CAAC;;QAE1E;QACApD,gBAAgB,CAACqD,IAAI,IAAI;UACvB9B,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;UACrD,OAAO6B,IAAI,CAACC,GAAG,CAACC,IAAI,IAClBA,IAAI,CAACjC,EAAE,KAAK4B,OAAO,CAACE,cAAc,GAC9B;YAAE,GAAGG,IAAI;YAAEC,WAAW,EAAEN,OAAO,CAACO,OAAO;YAAEC,SAAS,EAAER,OAAO,CAACS,SAAS;YAAEC,MAAM,EAAEV,OAAO,CAACC,QAAQ,KAAKvD,IAAI,CAAC0B;UAAG,CAAC,GAC7GiC,IACN,CAAC;QACH,CAAC,CAAC;;QAEF;QACArD,uBAAuB,CAAC2D,eAAe,IAAI;UACzCtC,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEqC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEvC,EAAE,CAAC;UAC7E,IAAIuC,eAAe,IAAIX,OAAO,CAACE,cAAc,KAAKS,eAAe,CAACvC,EAAE,EAAE;YACpEC,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;YAC/DpB,WAAW,CAACiD,IAAI,IAAI;cAClB;cACA,MAAMS,aAAa,GAAGT,IAAI,CAACU,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC1C,EAAE,KAAK4B,OAAO,CAAC5B,EAAE,CAAC;cAC7D,IAAIwC,aAAa,EAAE;gBACjBvC,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE0B,OAAO,CAAC5B,EAAE,CAAC;gBACvE,OAAO+B,IAAI;cACb;cAEA,OAAO,CAAC,GAAGA,IAAI,EAAE;gBACf/B,EAAE,EAAE4B,OAAO,CAAC5B,EAAE;gBACd6B,QAAQ,EAAED,OAAO,CAACC,QAAQ;gBAC1Bc,UAAU,EAAEf,OAAO,CAACgB,MAAM,CAACC,IAAI;gBAC/BC,YAAY,EAAE,oCAAoCC,kBAAkB,CAACnB,OAAO,CAACgB,MAAM,CAACC,IAAI,CAAC,qDAAqD;gBAC9IG,IAAI,EAAEpB,OAAO,CAACO,OAAO;gBACrBC,SAAS,EAAER,OAAO,CAACS,SAAS;gBAC5BY,IAAI,EAAErB,OAAO,CAACsB,MAAM;gBACpBC,SAAS,EAAE,IAAI,CAAC;cAClB,CAAC,CAAC;YACJ,CAAC,CAAC;;YAEF;YACA,IAAIvB,OAAO,CAACC,QAAQ,KAAKvD,IAAI,CAAC0B,EAAE,EAAE;cAChCC,OAAO,CAACC,GAAG,CAAC,oEAAoE,CAAC;cACjFkD,UAAU,CAAC,MAAM;gBACfC,sBAAsB,CAACzB,OAAO,CAACE,cAAc,CAAC;cAChD,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;YACX;;YAEA;YACAsB,UAAU,CAAC,MAAM;cAAA,IAAAE,qBAAA;cACf,CAAAA,qBAAA,GAAA5D,cAAc,CAAC6D,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;gBAAEC,QAAQ,EAAE;cAAS,CAAC,CAAC;YAChE,CAAC,EAAE,GAAG,CAAC;UACT,CAAC,MAAM;YACLxD,OAAO,CAACC,GAAG,CAAC,oFAAoF,CAAC;UACnG;UACA,OAAOqC,eAAe;QACxB,CAAC,CAAC;MACJ,CAAC;MAED,MAAMmB,iBAAiB,GAAI9B,OAAO,IAAK;QACrC3B,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE0B,OAAO,CAAC;MACnD,CAAC;MAED,MAAM+B,sBAAsB,GAAIC,IAAI,IAAK;QACvC3D,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE0D,IAAI,CAAC;QACnDpE,cAAc,CAACuC,IAAI,IAAI;UACrB,MAAM8B,MAAM,GAAG,IAAIpE,GAAG,CAACsC,IAAI,CAAC;UAC5B,IAAI6B,IAAI,CAACE,MAAM,KAAK,QAAQ,EAAE;YAC5BD,MAAM,CAACE,GAAG,CAACH,IAAI,CAACI,MAAM,CAAC;UACzB,CAAC,MAAM;YACLH,MAAM,CAACI,MAAM,CAACL,IAAI,CAACI,MAAM,CAAC;UAC5B;UACA/D,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEgE,KAAK,CAACC,IAAI,CAACN,MAAM,CAAC,CAAC;UACnE,OAAOA,MAAM;QACf,CAAC,CAAC;MACJ,CAAC;MAED,MAAMO,qBAAqB,GAAIC,OAAO,IAAK;QACzCpE,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEmE,OAAO,CAAC;QAC9D7E,cAAc,CAAC,IAAIC,GAAG,CAAC4E,OAAO,CAAC,CAAC;MAClC,CAAC;MAED,MAAMC,gBAAgB,GAAIV,IAAI,IAAK;QACjChF,uBAAuB,CAAC2D,eAAe,IAAI;UACzC,IAAIA,eAAe,IAAIqB,IAAI,CAAC9B,cAAc,KAAKS,eAAe,CAACvC,EAAE,EAAE;YACjEuE,cAAc,CAACxC,IAAI,IAAI,IAAItC,GAAG,CAAC,CAAC,GAAGsC,IAAI,EAAE6B,IAAI,CAACI,MAAM,CAAC,CAAC,CAAC;UACzD;UACA,OAAOzB,eAAe;QACxB,CAAC,CAAC;MACJ,CAAC;MAED,MAAMiC,uBAAuB,GAAIZ,IAAI,IAAK;QACxCW,cAAc,CAACxC,IAAI,IAAI;UACrB,MAAM8B,MAAM,GAAG,IAAIpE,GAAG,CAACsC,IAAI,CAAC;UAC5B8B,MAAM,CAACI,MAAM,CAACL,IAAI,CAACI,MAAM,CAAC;UAC1B,OAAOH,MAAM;QACf,CAAC,CAAC;MACJ,CAAC;MAED,MAAMY,kBAAkB,GAAIb,IAAI,IAAK;QACnC3D,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE0D,IAAI,CAAC;QACrD3D,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE0D,IAAI,CAACc,MAAM,EAAE,eAAe,EAAEpG,IAAI,CAAC0B,EAAE,CAAC;QAC/EC,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE0D,IAAI,CAACe,UAAU,CAAC;;QAEvE;QACA,IAAIf,IAAI,CAACc,MAAM,KAAKpG,IAAI,CAAC0B,EAAE,EAAE;UAC3BlB,WAAW,CAACiD,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACU,GAAG,IAAI;YAClC;YACA,IAAIA,GAAG,CAACb,QAAQ,KAAKvD,IAAI,CAAC0B,EAAE,IAAI0C,GAAG,CAACZ,cAAc,KAAK8B,IAAI,CAAC9B,cAAc,EAAE;cAC1E7B,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEwC,GAAG,CAAC1C,EAAE,CAAC;cAC9D,OAAO;gBAAE,GAAG0C,GAAG;gBAAEO,IAAI,EAAE;cAAK,CAAC;YAC/B;YACA;YACA,IAAIW,IAAI,CAACe,UAAU,CAACC,QAAQ,CAAClC,GAAG,CAAC1C,EAAE,CAAC,EAAE;cACpCC,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAEwC,GAAG,CAAC1C,EAAE,CAAC;cACpE,OAAO;gBAAE,GAAG0C,GAAG;gBAAEO,IAAI,EAAE;cAAK,CAAC;YAC/B;YACA,OAAOP,GAAG;UACZ,CAAC,CAAC,CAAC;;UAEH;UACAhE,gBAAgB,CAACqD,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IACpCA,IAAI,CAACjC,EAAE,KAAK4D,IAAI,CAAC9B,cAAc,GAC3B;YAAE,GAAGG,IAAI;YAAEK,MAAM,EAAE;UAAM,CAAC,GAC1BL,IACN,CAAC,CAAC;QACJ;MACF,CAAC;;MAED;MACAhC,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAC1Db,MAAM,CAACwB,EAAE,CAAC,aAAa,EAAEc,gBAAgB,CAAC;MAC1CtC,MAAM,CAACwB,EAAE,CAAC,cAAc,EAAE6C,iBAAiB,CAAC;MAC5CrE,MAAM,CAACwB,EAAE,CAAC,oBAAoB,EAAE8C,sBAAsB,CAAC;MACvDtE,MAAM,CAACwB,EAAE,CAAC,aAAa,EAAEyD,gBAAgB,CAAC;MAC1CjF,MAAM,CAACwB,EAAE,CAAC,qBAAqB,EAAE2D,uBAAuB,CAAC;MACzDnF,MAAM,CAACwB,EAAE,CAAC,eAAe,EAAE4D,kBAAkB,CAAC;MAC9CpF,MAAM,CAACwB,EAAE,CAAC,mBAAmB,EAAEuD,qBAAqB,CAAC;MACrDnE,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;MAErD;MACA,OAAO,MAAM;QACXb,MAAM,CAACwF,GAAG,CAAC,aAAa,EAAElD,gBAAgB,CAAC;QAC3CtC,MAAM,CAACwF,GAAG,CAAC,cAAc,EAAEnB,iBAAiB,CAAC;QAC7CrE,MAAM,CAACwF,GAAG,CAAC,oBAAoB,EAAElB,sBAAsB,CAAC;QACxDtE,MAAM,CAACwF,GAAG,CAAC,aAAa,EAAEP,gBAAgB,CAAC;QAC3CjF,MAAM,CAACwF,GAAG,CAAC,qBAAqB,EAAEL,uBAAuB,CAAC;QAC1DnF,MAAM,CAACwF,GAAG,CAAC,eAAe,EAAEJ,kBAAkB,CAAC;QAC/CpF,MAAM,CAACwF,GAAG,CAAC,mBAAmB,EAAET,qBAAqB,CAAC;MACxD,CAAC;IACH;EACF,CAAC,EAAE,CAAC/E,MAAM,EAAEf,IAAI,CAAC0B,EAAE,CAAC,CAAC;;EAErB;EACArD,SAAS,CAAC,MAAM;IACdmI,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGtI,QAAQ,CAAC,IAAI+C,GAAG,CAAC,CAAC,CAAC;EAEzE9C,SAAS,CAAC,MAAM;IACd,IAAI0C,MAAM,IAAIZ,aAAa,CAACwG,MAAM,GAAG,CAAC,EAAE;MACtChF,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEzB,aAAa,CAACwG,MAAM,EAAE,cAAc,CAAC;MAEnGxG,aAAa,CAACyG,OAAO,CAACC,YAAY,IAAI;QACpC,IAAI,CAACJ,mBAAmB,CAACK,GAAG,CAACD,YAAY,CAACnF,EAAE,CAAC,EAAE;UAC7CX,MAAM,CAAC2B,IAAI,CAAC,mBAAmB,EAAEmE,YAAY,CAACnF,EAAE,CAAC;UACjDC,OAAO,CAACC,GAAG,CAAC,2BAA2BiF,YAAY,CAACnF,EAAE,UAAU,CAAC;UACjEgF,sBAAsB,CAACjD,IAAI,IAAI,IAAItC,GAAG,CAAC,CAAC,GAAGsC,IAAI,EAAEoD,YAAY,CAACnF,EAAE,CAAC,CAAC,CAAC;QACrE;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACX,MAAM,EAAEZ,aAAa,EAAEsG,mBAAmB,CAAC,CAAC;;EAEhD;EACA,MAAMD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFtG,YAAY,CAAC,IAAI,CAAC;MAClB,MAAM8C,QAAQ,GAAG,MAAMxE,GAAG,CAACuI,GAAG,CAAC,yBAAyB,CAAC;;MAEzD;MACA,MAAMC,sBAAsB,GAAGhE,QAAQ,CAACsC,IAAI,CAACnF,aAAa,CAACuD,GAAG,CAACmD,YAAY;QAAA,IAAAI,qBAAA,EAAAC,sBAAA;QAAA,OAAK;UAC9ExF,EAAE,EAAEmF,YAAY,CAACnF,EAAE;UACnByF,QAAQ,EAAEN,YAAY,CAACO,SAAS,CAAC1F,EAAE;UACnC2F,UAAU,EAAER,YAAY,CAACO,SAAS,CAAC7C,IAAI;UACvC+C,WAAW,EAAET,YAAY,CAACO,SAAS,CAACG,IAAI,KAAK,QAAQ,GAAG,OAAO,GAAGV,YAAY,CAACO,SAAS,CAACG,IAAI;UAC7F3D,WAAW,EAAE,EAAAqD,qBAAA,GAAAJ,YAAY,CAACjD,WAAW,cAAAqD,qBAAA,uBAAxBA,qBAAA,CAA0BpD,OAAO,KAAI,iBAAiB;UACnEC,SAAS,EAAE,EAAAoD,sBAAA,GAAAL,YAAY,CAACjD,WAAW,cAAAsD,sBAAA,uBAAxBA,sBAAA,CAA0BpD,SAAS,KAAI+C,YAAY,CAAC9C,SAAS;UACxEC,MAAM,EAAE6C,YAAY,CAACjD,WAAW,GAAG,CAACiD,YAAY,CAACjD,WAAW,CAACgB,MAAM,IAAIiC,YAAY,CAACjD,WAAW,CAACL,QAAQ,KAAKvD,IAAI,CAAC0B,EAAE,GAAG,KAAK;UAC5H8F,MAAM,EAAE,oCAAoC/C,kBAAkB,CAACoC,YAAY,CAACO,SAAS,CAAC7C,IAAI,CAAC,qDAAqD;UAChJiB,MAAM,EAAEvE,WAAW,CAAC6F,GAAG,CAACD,YAAY,CAACO,SAAS,CAAC1F,EAAE,CAAC,GAAG,QAAQ,GAAG,SAAS;UACzE+F,OAAO,EAAE,KAAK;UACdC,QAAQ,EAAE;QACZ,CAAC;MAAA,CAAC,CAAC;MAEHtH,gBAAgB,CAAC4G,sBAAsB,CAAC;IAC1C,CAAC,CAAC,OAAOpE,KAAK,EAAE;MACdjB,OAAO,CAACiB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDnE,KAAK,CAACmE,KAAK,CAAC,wBAAwB,CAAC;IACvC,CAAC,SAAS;MACR1C,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA7B,SAAS,CAAC,MAAM;IACd,IAAIgC,oBAAoB,EAAE;MACxBsH,YAAY,CAACtH,oBAAoB,CAACqB,EAAE,CAAC;MACrCqD,sBAAsB,CAAC1E,oBAAoB,CAACqB,EAAE,CAAC;;MAE/C;MACAtB,gBAAgB,CAACqD,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IACpCA,IAAI,CAACjC,EAAE,KAAKrB,oBAAoB,CAACqB,EAAE,GAC/B;QAAE,GAAGiC,IAAI;QAAEK,MAAM,EAAE;MAAM,CAAC,GAC1BL,IACN,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACtD,oBAAoB,EAAEsH,YAAY,EAAE5C,sBAAsB,CAAC,CAAC;;EAEhE;EACA,MAAMA,sBAAsB,GAAG,MAAOvB,cAAc,IAAK;IACvD,IAAI;MACF;MACA,MAAMR,QAAQ,GAAG,MAAMxE,GAAG,CAACoJ,GAAG,CAAC,2BAA2BpE,cAAc,OAAO,CAAC;MAEhF,IAAIR,QAAQ,CAACsC,IAAI,CAACuC,YAAY,GAAG,CAAC,EAAE;QAClClG,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEoB,QAAQ,CAACsC,IAAI,CAACuC,YAAY,EAAE,kBAAkB,CAAC;;QAEhF;QACArH,WAAW,CAACiD,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACU,GAAG,IAC9BA,GAAG,CAACb,QAAQ,KAAKvD,IAAI,CAAC0B,EAAE,GAAG;UAAE,GAAG0C,GAAG;UAAEO,IAAI,EAAE;QAAK,CAAC,GAAGP,GACtD,CAAC,CAAC;;QAEF;QACA,IAAIrD,MAAM,EAAE;UACVA,MAAM,CAAC2B,IAAI,CAAC,oBAAoB,EAAE;YAChCc,cAAc;YACd6C,UAAU,EAAE,EAAE,CAAC;UACjB,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC,OAAOzD,KAAK,EAAE;MACdjB,OAAO,CAACiB,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;IACpE;EACF,CAAC;;EAED;EACA,MAAM+E,YAAY,GAAG,MAAOnE,cAAc,IAAK;IAC7C,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMxE,GAAG,CAACuI,GAAG,CAAC,2BAA2BvD,cAAc,EAAE,CAAC;;MAE3E;MACA,MAAMsE,iBAAiB,GAAG9E,QAAQ,CAACsC,IAAI,CAAC/E,QAAQ,CAACmD,GAAG,CAACJ,OAAO,KAAK;QAC/D5B,EAAE,EAAE4B,OAAO,CAAC5B,EAAE;QACd6B,QAAQ,EAAED,OAAO,CAACC,QAAQ;QAC1Bc,UAAU,EAAEf,OAAO,CAACgB,MAAM,CAACC,IAAI;QAC/BC,YAAY,EAAE,oCAAoCC,kBAAkB,CAACnB,OAAO,CAACgB,MAAM,CAACC,IAAI,CAAC,qDAAqD;QAC9IG,IAAI,EAAEpB,OAAO,CAACO,OAAO;QACrBC,SAAS,EAAER,OAAO,CAACS,SAAS;QAC5BY,IAAI,EAAErB,OAAO,CAACsB,MAAM;QACpBC,SAAS,EAAE,IAAI;QAAE;QACjBkD,WAAW,EAAEzE,OAAO,CAACyE;MACvB,CAAC,CAAC,CAAC;MAEHvH,WAAW,CAACsH,iBAAiB,CAAC;;MAE9B;MACAhD,UAAU,CAAC,MAAM;QAAA,IAAAkD,sBAAA;QACf,CAAAA,sBAAA,GAAA5G,cAAc,CAAC6D,OAAO,cAAA+C,sBAAA,uBAAtBA,sBAAA,CAAwB9C,cAAc,CAAC;UAAEC,QAAQ,EAAE;QAAS,CAAC,CAAC;MAChE,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAOvC,KAAK,EAAE;MACdjB,OAAO,CAACiB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDnE,KAAK,CAACmE,KAAK,CAAC,sBAAsB,CAAC;IACrC;EACF,CAAC;EACD;EACA,MAAMqF,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAACxH,WAAW,CAACyH,IAAI,CAAC,CAAC,IAAI,CAAC7H,oBAAoB,EAAE;IAElDsB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;MACpCuG,UAAU,EAAE9H,oBAAoB,CAAC8G,QAAQ;MACzCtD,OAAO,EAAEpD,WAAW,CAACyH,IAAI,CAAC;IAC5B,CAAC,CAAC;IAEF,IAAI;MACF,MAAMlF,QAAQ,GAAG,MAAMxE,GAAG,CAAC4J,IAAI,CAAC,gBAAgB,EAAE;QAChDD,UAAU,EAAE9H,oBAAoB,CAAC8G,QAAQ;QACzCtD,OAAO,EAAEpD,WAAW,CAACyH,IAAI,CAAC;MAC5B,CAAC,CAAC;MAEFvG,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEoB,QAAQ,CAACsC,IAAI,CAAC;;MAEjD;MACA5E,cAAc,CAAC,EAAE,CAAC;IAEpB,CAAC,CAAC,OAAOkC,KAAK,EAAE;MACdjB,OAAO,CAACiB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDnE,KAAK,CAACmE,KAAK,CAAC,qBAAqB,CAAC;IACpC;EACF,CAAC;EAEDvE,SAAS,CAAC,MAAM;IACd;IACA,IAAI+C,cAAc,CAAC6D,OAAO,IAAI3D,oBAAoB,CAAC2D,OAAO,EAAE;MAC1D3D,oBAAoB,CAAC2D,OAAO,CAACoD,SAAS,GAAG/G,oBAAoB,CAAC2D,OAAO,CAACqD,YAAY;IACpF;EACF,CAAC,EAAE,CAAC/H,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMgI,iBAAiB,GAAIC,CAAC,IAAK;IAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBR,WAAW,CAAC,CAAC;EACf,CAAC;;EAED;EACA,MAAMS,wBAAwB,GAAI7B,YAAY,IAAK;IACjDvG,uBAAuB,CAACuG,YAAY,CAAC;EACvC,CAAC;;EAED;EACA,MAAM8B,iBAAiB,GAAIC,UAAU,IAAK;IACxC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,KAAK,GAAG,IAAID,IAAI,CAAC,CAAC;IACxB,MAAME,SAAS,GAAG,IAAIF,IAAI,CAACC,KAAK,CAAC;IACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IAE1C,IAAIL,IAAI,CAACM,YAAY,CAAC,CAAC,KAAKJ,KAAK,CAACI,YAAY,CAAC,CAAC,EAAE;MAChD,OAAO5J,MAAM,CAACsJ,IAAI,EAAE,OAAO,EAAE;QAAEO,MAAM,EAAE5J;MAAG,CAAC,CAAC;IAC9C,CAAC,MAAM,IAAIqJ,IAAI,CAACM,YAAY,CAAC,CAAC,KAAKH,SAAS,CAACG,YAAY,CAAC,CAAC,EAAE;MAC3D,OAAO,MAAM,GAAG5J,MAAM,CAACsJ,IAAI,EAAE,OAAO,EAAE;QAAEO,MAAM,EAAE5J;MAAG,CAAC,CAAC;IACvD,CAAC,MAAM;MACL,OAAOD,MAAM,CAACsJ,IAAI,EAAE,cAAc,EAAE;QAAEO,MAAM,EAAE5J;MAAG,CAAC,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAM6J,qBAAqB,GAAGlJ,aAAa,CAACU,MAAM,CAAC8C,IAAI,IAAI;IACzD;IACA,MAAM2F,aAAa,GAAG3F,IAAI,CAAC0D,UAAU,CAACkC,WAAW,CAAC,CAAC,CAACjD,QAAQ,CAAC3F,UAAU,CAAC4I,WAAW,CAAC,CAAC,CAAC,IACjE5F,IAAI,CAACC,WAAW,CAAC2F,WAAW,CAAC,CAAC,CAACjD,QAAQ,CAAC3F,UAAU,CAAC4I,WAAW,CAAC,CAAC,CAAC;;IAEtF;IACA,MAAMC,aAAa,GAAG3I,MAAM,KAAK,KAAK,IAChBA,MAAM,KAAK,QAAQ,IAAI8C,IAAI,CAACK,MAAO,IACnCnD,MAAM,KAAK,UAAU,IAAI8C,IAAI,CAAC+D,QAAS,IACvC7G,MAAM,KAAK,SAAS,IAAI8C,IAAI,CAAC8D,OAAQ;IAE3D,OAAO6B,aAAa,IAAIE,aAAa;EACvC,CAAC,CAAC;;EAEF;EACA,MAAMC,UAAU,GAAI/H,EAAE,IAAK;IACzBtB,gBAAgB,CAACsJ,iBAAiB,IAChCA,iBAAiB,CAAChG,GAAG,CAACC,IAAI,IACxBA,IAAI,CAACjC,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGiC,IAAI;MAAE8D,OAAO,EAAE,CAAC9D,IAAI,CAAC8D;IAAQ,CAAC,GAAG9D,IACzD,CACF,CAAC;IAED,IAAItD,oBAAoB,IAAIA,oBAAoB,CAACqB,EAAE,KAAKA,EAAE,EAAE;MAC1DpB,uBAAuB,CAACmD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEgE,OAAO,EAAE,CAAChE,IAAI,CAACgE;MAAQ,CAAC,CAAC,CAAC;IACxE;EACF,CAAC;;EAED;EACA,MAAMkC,aAAa,GAAIjI,EAAE,IAAK;IAC5BtB,gBAAgB,CAACsJ,iBAAiB,IAChCA,iBAAiB,CAAChG,GAAG,CAACC,IAAI,IACxBA,IAAI,CAACjC,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGiC,IAAI;MAAE+D,QAAQ,EAAE,CAAC/D,IAAI,CAAC+D;IAAS,CAAC,GAAG/D,IAC3D,CACF,CAAC;IAED,IAAItD,oBAAoB,IAAIA,oBAAoB,CAACqB,EAAE,KAAKA,EAAE,EAAE;MAC1DpB,uBAAuB,CAACmD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEiE,QAAQ,EAAE,CAACjE,IAAI,CAACiE;MAAS,CAAC,CAAC,CAAC;IAC1E;EACF,CAAC;EAED,IAAIzH,SAAS,EAAE;IACb,oBACEN,OAAA;MAAKiK,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5DlK,OAAA;QAAKiK,SAAS,EAAC;MAA2E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9F,CAAC;EAEV;EAEA,oBACEtK,OAAA;IAAKiK,SAAS,EAAC,+BAA+B;IAAAC,QAAA,eAC5ClK,OAAA;MAAKiK,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1DlK,OAAA;QAAKiK,SAAS,EAAC,0EAA0E;QAAAC,QAAA,eACvFlK,OAAA;UAAKiK,SAAS,EAAC,uEAAuE;UAAAC,QAAA,gBACpFlK,OAAA;YAAAkK,QAAA,gBACElK,OAAA;cAAIiK,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7DtK,OAAA;cAAGiK,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAElC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNtK,OAAA;YAAKiK,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1ClK,OAAA,CAACF,IAAI;cACHyK,EAAE,EAAC,iBAAiB;cACpBN,SAAS,EAAC,sPAAsP;cAAAC,QAAA,gBAEhQlK,OAAA,CAACN,aAAa;gBAACuK,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAE5C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPtK,OAAA,CAACF,IAAI;cACHyK,EAAE,EAAC,sBAAsB;cACzBN,SAAS,EAAC,qOAAqO;cAAAC,QAAA,gBAE/OlK,OAAA,CAACL,YAAY;gBAACsK,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,qBAE3C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtK,OAAA;QAAKiK,SAAS,EAAC,+CAA+C;QAAAC,QAAA,eAC5DlK,OAAA;UAAKiK,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBAEzClK,OAAA;YAAKiK,SAAS,EAAC,kEAAkE;YAAAC,QAAA,gBAC/ElK,OAAA;cAAKiK,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDlK,OAAA;gBAAIiK,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,gBACnElK,OAAA,CAAChB,0BAA0B;kBAACiL,SAAS,EAAC;gBAA4B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,YAEvE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLtK,OAAA;gBAAKiK,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BlK,OAAA;kBACEwK,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,0BAAqB;kBACjCR,SAAS,EAAC,iHAAiH;kBAC3HS,KAAK,EAAE1J,UAAW;kBAClB2J,QAAQ,EAAG9B,CAAC,IAAK5H,aAAa,CAAC4H,CAAC,CAAC+B,MAAM,CAACF,KAAK;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACFtK,OAAA,CAACf,mBAAmB;kBAACgL,SAAS,EAAC;gBAA+C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC,eACNtK,OAAA;gBAAKiK,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAClClK,OAAA;kBACEiK,SAAS,EAAE,kCAAkC/I,MAAM,KAAK,KAAK,GACzD,2BAA2B,GAC3B,6CAA6C,EAAG;kBACpD2J,OAAO,EAAEA,CAAA,KAAM1J,SAAS,CAAC,KAAK,CAAE;kBAAA+I,QAAA,EACjC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTtK,OAAA;kBACEiK,SAAS,EAAE,kCAAkC/I,MAAM,KAAK,QAAQ,GAC5D,2BAA2B,GAC3B,6CAA6C,EAAG;kBACpD2J,OAAO,EAAEA,CAAA,KAAM1J,SAAS,CAAC,QAAQ,CAAE;kBAAA+I,QAAA,EACpC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTtK,OAAA;kBACEiK,SAAS,EAAE,kCAAkC/I,MAAM,KAAK,SAAS,GAC7D,2BAA2B,GAC3B,6CAA6C,EAAG;kBACpD2J,OAAO,EAAEA,CAAA,KAAM1J,SAAS,CAAC,SAAS,CAAE;kBAAA+I,QAAA,EACrC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTtK,OAAA;kBACEiK,SAAS,EAAE,kCAAkC/I,MAAM,KAAK,UAAU,GAC9D,2BAA2B,GAC3B,6CAA6C,EAAG;kBACpD2J,OAAO,EAAEA,CAAA,KAAM1J,SAAS,CAAC,UAAU,CAAE;kBAAA+I,QAAA,EACtC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNtK,OAAA;cACE8K,GAAG,EAAEpJ,gBAAiB;cACtBqJ,KAAK,EAAE;gBACLC,MAAM,EAAE,oBAAoB;gBAC5BC,SAAS,EAAE,MAAM;gBACjBC,cAAc,EAAE,MAAM;gBACtBC,cAAc,EAAE;cAClB,CAAE;cAAAjB,QAAA,EAEDR,qBAAqB,CAAC1C,MAAM,KAAK,CAAC,gBACjChH,OAAA;gBAAKiK,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,GAENZ,qBAAqB,CAAC3F,GAAG,CAACmD,YAAY,iBACpClH,OAAA;gBAEEiK,SAAS,EAAE,sEACT,CAAAvJ,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEqB,EAAE,MAAKmF,YAAY,CAACnF,EAAE,GAAG,YAAY,GAAG,EAAE,IAC9DmF,YAAY,CAAC7C,MAAM,GAAG,8BAA8B,GAAG,EAAE,EAAG;gBAChEwG,OAAO,EAAEA,CAAA,KAAM9B,wBAAwB,CAAC7B,YAAY,CAAE;gBAAAgD,QAAA,gBAEtDlK,OAAA;kBAAKiK,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzClK,OAAA;oBAAKiK,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrClK,OAAA;sBACEoL,GAAG,EAAElE,YAAY,CAACW,MAAO;sBACzBwD,GAAG,EAAEnE,YAAY,CAACQ,UAAW;sBAC7BuC,SAAS,EAAE,0BACT,CAAAvJ,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEqB,EAAE,MAAKmF,YAAY,CAACnF,EAAE,GACxC,sBAAsB,GACtB,EAAE;oBACL;sBAAAoI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,EACDpD,YAAY,CAACrB,MAAM,KAAK,QAAQ,iBAC/B7F,OAAA;sBAAMiK,SAAS,EAAC;oBAAyF;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CACjH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACNtK,OAAA;oBAAKiK,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC7BlK,OAAA;sBAAKiK,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,gBAC/ClK,OAAA;wBAAIiK,SAAS,EAAE,uBAAuB/C,YAAY,CAAC7C,MAAM,GAAG,eAAe,GAAG,eAAe,EAAG;wBAAA6F,QAAA,EAC7FhD,YAAY,CAACQ;sBAAU;wBAAAyC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtB,CAAC,eACLtK,OAAA;wBAAKiK,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,GACzChD,YAAY,CAACY,OAAO,iBACnB9H,OAAA,CAACsL,QAAQ;0BAACrB,SAAS,EAAC;wBAAsC;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAC7D,eACDtK,OAAA;0BAAMiK,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,EACpClB,iBAAiB,CAAC9B,YAAY,CAAC/C,SAAS;wBAAC;0BAAAgG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNtK,OAAA;sBAAGiK,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEhD,YAAY,CAACS;oBAAW;sBAAAwC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnEtK,OAAA;sBAAGiK,SAAS,EAAE,yBACZ/C,YAAY,CAAC7C,MAAM,GAAG,2BAA2B,GAAG,eAAe,EAClE;sBAAA6F,QAAA,EACAhD,YAAY,CAACjD;oBAAW;sBAAAkG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC,eACJtK,OAAA;sBAAKiK,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrDlK,OAAA;wBAAMiK,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EACpC5I,WAAW,CAAC6F,GAAG,CAACD,YAAY,CAACM,QAAQ,CAAC,gBACnCxH,OAAA;0BAAMiK,SAAS,EAAC,4BAA4B;0BAAAC,QAAA,EAAC;wBAAS;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,GAC7DpD,YAAY,CAACqE,QAAQ,GACnB,gBAAgBrE,YAAY,CAACqE,QAAQ,EAAE,GACvC;sBAAE;wBAAApB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACPtK,OAAA;wBAAKiK,SAAS,EAAC,gBAAgB;wBAAAC,QAAA,gBAC7BlK,OAAA;0BACE6K,OAAO,EAAGhC,CAAC,IAAK;4BACdA,CAAC,CAAC2C,eAAe,CAAC,CAAC;4BACnB1B,UAAU,CAAC5C,YAAY,CAACnF,EAAE,CAAC;0BAC7B,CAAE;0BACFkI,SAAS,EAAC,qCAAqC;0BAAAC,QAAA,eAE/ClK,OAAA,CAACsL,QAAQ;4BAACrB,SAAS,EAAE,WAAW/C,YAAY,CAACY,OAAO,GAAG,8BAA8B,GAAG,EAAE;0BAAG;4BAAAqC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1F,CAAC,eACTtK,OAAA;0BACE6K,OAAO,EAAGhC,CAAC,IAAK;4BACdA,CAAC,CAAC2C,eAAe,CAAC,CAAC;4BACnBxB,aAAa,CAAC9C,YAAY,CAACnF,EAAE,CAAC;0BAChC,CAAE;0BACFkI,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,eAE7ClK,OAAA,CAACP,cAAc;4BAACwK,SAAS,EAAC;0BAAS;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EACLpD,YAAY,CAAC7C,MAAM,iBAClBrE,OAAA;kBAAMiK,SAAS,EAAC,yGAAyG;kBAAAC,QAAA,EAAC;gBAE1H;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACP;cAAA,GA5EIpD,YAAY,CAACnF,EAAE;gBAAAoI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6EjB,CACN;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtK,OAAA;YAAKiK,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EACrDxJ,oBAAoB,gBACnBV,OAAA,CAAAE,SAAA;cAAAgK,QAAA,gBAEElK,OAAA;gBAAKiK,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,gBACtFlK,OAAA;kBAAKiK,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChClK,OAAA;oBAAKiK,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5BlK,OAAA;sBACEoL,GAAG,EAAE1K,oBAAoB,CAACmH,MAAO;sBACjCwD,GAAG,EAAE3K,oBAAoB,CAACgH,UAAW;sBACrCuC,SAAS,EAAC;oBAAwB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC,EACD5J,oBAAoB,CAACmF,MAAM,KAAK,QAAQ,iBACvC7F,OAAA;sBAAMiK,SAAS,EAAC;oBAAyF;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CACjH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACNtK,OAAA;oBAAAkK,QAAA,gBACElK,OAAA;sBAAIiK,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAC9CxJ,oBAAoB,CAACgH;oBAAU;sBAAAyC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9B,CAAC,eACLtK,OAAA;sBAAGiK,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GACjCxJ,oBAAoB,CAACiH,WAAW,EAAC,GAAC,EAAC,KAAK,EACxCjH,oBAAoB,CAACmF,MAAM,KAAK,QAAQ,GACrC,WAAW,GACXnF,oBAAoB,CAAC6K,QAAQ,GAC3B,gBAAgB7K,oBAAoB,CAAC6K,QAAQ,EAAE,GAC/C,EAAE;oBAAA;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNtK,OAAA;kBAAKiK,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1ClK,OAAA,CAACF,IAAI;oBACHyK,EAAE,EAAE,mBAAmB7J,oBAAoB,CAAC8G,QAAQ,EAAG;oBACvDyC,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,eAE5DlK,OAAA,CAACd,QAAQ;sBAAC+K,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACPtK,OAAA,CAACF,IAAI;oBACHyK,EAAE,EAAE,+BAA+B7J,oBAAoB,CAAC8G,QAAQ,EAAG;oBACnEyC,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,eAE5DlK,OAAA,CAACyL,SAAS;sBAACxB,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC,eACPtK,OAAA,CAACF,IAAI;oBACHyK,EAAE,EAAE,2BAA2B7J,oBAAoB,CAAC8G,QAAQ,EAAG;oBAC/DyC,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,eAE5DlK,OAAA,CAACT,eAAe;sBAAC0K,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC,eACPtK,OAAA;oBAAQiK,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,eAClElK,OAAA,CAACV,sBAAsB;sBAAC2K,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNtK,OAAA;gBACE8K,GAAG,EAAEnJ,oBAAqB;gBAC1BsI,SAAS,EAAC,gBAAgB;gBAC1Bc,KAAK,EAAE;kBACLC,MAAM,EAAE,oBAAoB;kBAC5BC,SAAS,EAAE,MAAM;kBACjBC,cAAc,EAAE,MAAM;kBACtBC,cAAc,EAAE;gBAClB,CAAE;gBAAAjB,QAAA,GAEDtJ,QAAQ,CAACmD,GAAG,CAAC,CAACJ,OAAO,EAAE+H,KAAK,KAAK;kBAChC,MAAMC,QAAQ,GAAGhI,OAAO,CAACC,QAAQ,KAAKvD,IAAI,CAAC0B,EAAE;kBAC7C,MAAM6J,UAAU,GAAGF,KAAK,KAAK,CAAC,IAAI9K,QAAQ,CAAC8K,KAAK,GAAG,CAAC,CAAC,CAAC9H,QAAQ,KAAKD,OAAO,CAACC,QAAQ;kBAEnF,oBACE5D,OAAA;oBAAsBiK,SAAS,EAAE,QAAQ0B,QAAQ,GAAG,aAAa,GAAG,eAAe,OAAQ;oBAAAzB,QAAA,GACxF,CAACyB,QAAQ,IAAIC,UAAU,iBACtB5L,OAAA;sBACEoL,GAAG,EAAEzH,OAAO,CAACkB,YAAa;sBAC1BwG,GAAG,EAAE1H,OAAO,CAACe,UAAW;sBACxBuF,SAAS,EAAC;oBAAgC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C,CACF,EACA,CAACqB,QAAQ,IAAI,CAACC,UAAU,iBAAI5L,OAAA;sBAAKiK,SAAS,EAAC;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC7DtK,OAAA;sBACEiK,SAAS,EAAE,yDACT0B,QAAQ,GACJ,wCAAwC,GACxC,+DAA+D,EAClE;sBAAAzB,QAAA,gBAEHlK,OAAA;wBAAGiK,SAAS,EAAC,SAAS;wBAAAC,QAAA,EAAEvG,OAAO,CAACoB;sBAAI;wBAAAoF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACzCtK,OAAA;wBAAKiK,SAAS,EAAE,gBAAgB0B,QAAQ,GAAG,eAAe,GAAG,eAAe,gCAAiC;wBAAAzB,QAAA,GAC1GlB,iBAAiB,CAACrF,OAAO,CAACQ,SAAS,CAAC,EACpCwH,QAAQ,iBACP3L,OAAA,CAACR,eAAe;0BACdyK,SAAS,EAAE,gBAAgBtG,OAAO,CAACqB,IAAI,GAAG,eAAe,GAAG,eAAe,EAAG;0BAC9E6G,KAAK,EAAElI,OAAO,CAACqB,IAAI,GAAG,QAAQ,GAAG;wBAAW;0BAAAmF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC7C,CACF;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,EACLqB,QAAQ,IAAIC,UAAU,iBACrB5L,OAAA;sBACEoL,GAAG,EAAEzH,OAAO,CAACkB,YAAa;sBAC1BwG,GAAG,EAAE1H,OAAO,CAACe,UAAW;sBACxBuF,SAAS,EAAC;oBAAgC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C,CACF,EACAqB,QAAQ,IAAI,CAACC,UAAU,iBAAI5L,OAAA;sBAAKiK,SAAS,EAAC;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA,GAlCpD3G,OAAO,CAAC5B,EAAE;oBAAAoI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAmCf,CAAC;gBAEV,CAAC,CAAC,eACFtK,OAAA;kBAAK8K,GAAG,EAAErJ;gBAAe;kBAAA0I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eAGNtK,OAAA;gBAAKiK,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,eACpDlK,OAAA;kBAAM8L,QAAQ,EAAElD,iBAAkB;kBAACqB,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC3DlK,OAAA;oBACEwK,IAAI,EAAC,QAAQ;oBACbP,SAAS,EAAC,uEAAuE;oBAAAC,QAAA,eAEjFlK,OAAA,CAACZ,aAAa;sBAAC6K,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACTtK,OAAA;oBAAKiK,SAAS,EAAC,aAAa;oBAAAC,QAAA,eAC1BlK,OAAA;sBACEiK,SAAS,EAAC,iHAAiH;sBAC3HQ,WAAW,EAAC,yCAAqB;sBACjCsB,IAAI,EAAC,GAAG;sBACRrB,KAAK,EAAE5J,WAAY;sBACnB6J,QAAQ,EAAG9B,CAAC,IAAK9H,cAAc,CAAC8H,CAAC,CAAC+B,MAAM,CAACF,KAAK,CAAE;sBAChDsB,SAAS,EAAGnD,CAAC,IAAK;wBAChB,IAAIA,CAAC,CAACoD,GAAG,KAAK,OAAO,IAAI,CAACpD,CAAC,CAACqD,QAAQ,EAAE;0BACpCrD,CAAC,CAACC,cAAc,CAAC,CAAC;0BAClBR,WAAW,CAAC,CAAC;wBACf;sBACF;oBAAE;sBAAA6B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACNtK,OAAA;oBACEwK,IAAI,EAAC,QAAQ;oBACbP,SAAS,EAAC,uEAAuE;oBAAAC,QAAA,eAEjFlK,OAAA,CAACX,aAAa;sBAAC4K,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACTtK,OAAA;oBACEwK,IAAI,EAAC,QAAQ;oBACb2B,QAAQ,EAAE,CAACrL,WAAW,CAACyH,IAAI,CAAC,CAAE;oBAC9B0B,SAAS,EAAE,yBACTnJ,WAAW,CAACyH,IAAI,CAAC,CAAC,GACd,0CAA0C,GAC1C,8CAA8C,qBAC9B;oBAAA2B,QAAA,eAEtBlK,OAAA,CAACb,iBAAiB;sBAAC8K,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,eACN,CAAC;YAAA;YAEH;YACAtK,OAAA;cAAKiK,SAAS,EAAC,iEAAiE;cAAAC,QAAA,eAC9ElK,OAAA;gBAAKiK,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1ClK,OAAA,CAAChB,0BAA0B;kBAACiL,SAAS,EAAC;gBAAsC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/EtK,OAAA;kBAAIiK,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxEtK,OAAA;kBAAGiK,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAErC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJtK,OAAA;kBAAKiK,SAAS,EAAC,MAAM;kBAAAC,QAAA,eACnBlK,OAAA,CAACF,IAAI;oBACHyK,EAAE,EAAC,iBAAiB;oBACpBN,SAAS,EAAC,wNAAwN;oBAAAC,QAAA,gBAElOlK,OAAA,CAACd,QAAQ;sBAAC+K,SAAS,EAAC,oBAAoB;sBAAC,eAAY;oBAAM;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gCAEhE;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAAlK,EAAA,CAh1BKD,kBAAkB;EAAA,QACLvB,OAAO;AAAA;AAAAwN,EAAA,GADpBjM,kBAAkB;AAk1BxB,eAAeA,kBAAkB;AAAC,IAAAiM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}