/**
 * Sessions Controller
 * Handles HTTP requests for sessions
 */

const sessionsService = require('./sessions.service');
const clientsService = require('../clients/clients.service');
const expertsService = require('../experts/experts.service');
const logger = require('../../utils/logger');

/**
 * Get client sessions
 * @route GET /api/sessions/client
 * @access Private (Client only)
 */
const getClientSessions = async (req, res) => {
  try {
    const userId = req.user.id;
    
    // Get client ID from user ID
    const client = await clientsService.getClientByUserId(userId);
    if (!client) {
      return res.status(404).json({ message: 'Client profile not found' });
    }

    // Extract filters from query parameters
    const filters = {
      status: req.query.status || 'all',
      search: req.query.search || ''
    };

    const sessions = await sessionsService.getClientSessions(client.ClientID, filters);

    res.json({
      success: true,
      data: sessions,
      count: sessions.length
    });
  } catch (error) {
    logger.error('Error getting client sessions:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Error retrieving sessions' 
    });
  }
};

/**
 * Get expert sessions
 * @route GET /api/sessions/expert
 * @access Private (Expert only)
 */
const getExpertSessions = async (req, res) => {
  try {
    const userId = req.user.id;
    
    // Get expert ID from user ID
    const expert = await expertsService.getExpertByUserId(userId);
    if (!expert) {
      return res.status(404).json({ message: 'Expert profile not found' });
    }

    // Extract filters from query parameters
    const filters = {
      status: req.query.status || 'all',
      search: req.query.search || ''
    };

    const sessions = await sessionsService.getExpertSessions(expert.ExpertID, filters);

    res.json({
      success: true,
      data: sessions,
      count: sessions.length
    });
  } catch (error) {
    logger.error('Error getting expert sessions:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Error retrieving sessions' 
    });
  }
};

/**
 * Get session by ID
 * @route GET /api/sessions/:id
 * @access Private
 */
const getSessionById = async (req, res) => {
  try {
    const sessionId = parseInt(req.params.id);
    const userId = req.user.id;

    if (isNaN(sessionId)) {
      return res.status(400).json({ message: 'Invalid session ID' });
    }

    const session = await sessionsService.getSessionById(sessionId);
    if (!session) {
      return res.status(404).json({ message: 'Session not found' });
    }

    // Check if user has access to this session
    const client = await clientsService.getClientByUserId(userId);
    const expert = await expertsService.getExpertByUserId(userId);
    
    const hasAccess = (client && session.ClientID === client.ClientID) || 
                     (expert && session.ExpertID === expert.ExpertID) ||
                     req.user.roleName === 'Admin';

    if (!hasAccess) {
      return res.status(403).json({ message: 'Access denied' });
    }

    res.json({
      success: true,
      data: session
    });
  } catch (error) {
    logger.error('Error getting session by ID:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Error retrieving session' 
    });
  }
};

/**
 * Update session notes
 * @route PUT /api/sessions/:id/notes
 * @access Private (Expert only)
 */
const updateSessionNotes = async (req, res) => {
  try {
    const sessionId = parseInt(req.params.id);
    const userId = req.user.id;
    const { notes } = req.body;

    if (isNaN(sessionId)) {
      return res.status(400).json({ message: 'Invalid session ID' });
    }

    if (!notes || typeof notes !== 'string') {
      return res.status(400).json({ message: 'Notes are required' });
    }

    // Check if user is an expert and has access to this session
    const expert = await expertsService.getExpertByUserId(userId);
    if (!expert) {
      return res.status(403).json({ message: 'Only experts can update session notes' });
    }

    const session = await sessionsService.getSessionById(sessionId);
    if (!session) {
      return res.status(404).json({ message: 'Session not found' });
    }

    if (session.ExpertID !== expert.ExpertID) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const updatedSession = await sessionsService.updateSessionNotes(sessionId, notes, userId);

    res.json({
      success: true,
      data: updatedSession,
      message: 'Session notes updated successfully'
    });
  } catch (error) {
    logger.error('Error updating session notes:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Error updating session notes' 
    });
  }
};

/**
 * Update session status
 * @route PUT /api/sessions/:id/status
 * @access Private (Expert only)
 */
const updateSessionStatus = async (req, res) => {
  try {
    const sessionId = parseInt(req.params.id);
    const userId = req.user.id;
    const { status } = req.body;

    if (isNaN(sessionId)) {
      return res.status(400).json({ message: 'Invalid session ID' });
    }

    const validStatuses = ['scheduled', 'completed', 'cancelled', 'missed'];
    if (!status || !validStatuses.includes(status)) {
      return res.status(400).json({ 
        message: 'Invalid status. Must be one of: ' + validStatuses.join(', ') 
      });
    }

    // Check if user is an expert and has access to this session
    const expert = await expertsService.getExpertByUserId(userId);
    if (!expert) {
      return res.status(403).json({ message: 'Only experts can update session status' });
    }

    const session = await sessionsService.getSessionById(sessionId);
    if (!session) {
      return res.status(404).json({ message: 'Session not found' });
    }

    if (session.ExpertID !== expert.ExpertID) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const updatedSession = await sessionsService.updateSessionStatus(sessionId, status, userId);

    res.json({
      success: true,
      data: updatedSession,
      message: 'Session status updated successfully'
    });
  } catch (error) {
    logger.error('Error updating session status:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Error updating session status' 
    });
  }
};

module.exports = {
  getClientSessions,
  getExpertSessions,
  getSessionById,
  updateSessionNotes,
  updateSessionStatus
};
