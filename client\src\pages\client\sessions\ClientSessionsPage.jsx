import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../hooks/useAuth';
import { sessionsApi } from '../../../services/api';
import {
  VideoCameraIcon,
  ClockIcon,
  CalendarIcon,
  UserIcon,
  CheckCircleIcon,
  XCircleIcon,
  BellIcon,
  ChartBarIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  ArrowDownTrayIcon,
  StarIcon,
  PlayCircleIcon,
  PaperClipIcon,
  DocumentArrowDownIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';
import { format, parseISO } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Link } from 'react-router-dom';
import toast from 'react-hot-toast';

/**
 * <PERSON><PERSON><PERSON><PERSON> görüşmeleri sayfası
 */
const ClientSessionsPage = () => {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [sessions, setSessions] = useState([]);
  const [activeTab, setActiveTab] = useState('upcoming'); // upcoming, past, all
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  // Mock görüşme durumları
  const sessionStatuses = {
    scheduled: "Planlandı",
    inProgress: "Devam Ediyor",
    completed: "Tamamlandı",
    missed: "Kaçırıldı",
    cancelled: "İptal Edildi",
  };

  // Fetch sessions from API
  const fetchSessions = async () => {
    try {
      setIsLoading(true);
      const filters = {
        status: filterStatus,
        search: searchTerm
      };

      const response = await sessionsApi.getClientSessions(filters);

      if (response.data.success) {
        setSessions(response.data.data || []);
      } else {
        toast.error('Seanslar yüklenirken hata oluştu');
        setSessions([]);
      }
    } catch (error) {
      console.error('Error fetching sessions:', error);
      toast.error('Seanslar yüklenirken hata oluştu');
      setSessions([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchSessions();
  }, [filterStatus, searchTerm]);

  // Mock data removed - now using real API data
  /*
  useEffect(() => {
    // Gerçek uygulamada API'den veri çekeceğiz
    // Bu mockup veri sadece gösterim amaçlıdır
    const mockSessions = [
      {
        id: 1,
        expertId: 101,
        expertName: 'Dr. Mehmet Yılmaz',
        expertTitle: 'Klinik Psikolog',
        date: '2025-03-25',
        startTime: '14:00',
        endTime: '14:50',
        duration: 50,
        status: 'scheduled',
        type: 'video',
        notes: 'Anksiyete terapisi - devam seansı',
        recordingAvailable: false,
        sessionsCompleted: 3,
        expertAvatar: 'https://randomuser.me/api/portraits/men/32.jpg',
        packageName: 'Anksiyete Terapisi Paketi'
      },
    ];

    setTimeout(() => {
      setSessions(mockSessions);
      setIsLoading(false);
    }, 1000);
  }, []);
  */

  // İstatistik hesaplamaları
  const stats = {
    total: sessions.length,
    upcoming: sessions.filter(s => s.status === 'scheduled').length,
    completed: sessions.filter(s => s.status === 'completed').length,
    missed: sessions.filter(s => s.status === 'missed').length,
    cancelled: sessions.filter(s => s.status === 'cancelled').length
  };

  // Bugünün tarihi
  const today = new Date();

  // Görüşmeleri filtrele - API'den filtrelenmiş veri geldiği için basit filtreleme
  const filteredSessions = sessions.filter(session => {
    // API'den gelen tarih formatını kontrol et
    let sessionDate;
    try {
      // API'den gelen date formatı 'yyyy-MM-dd' şeklinde
      sessionDate = session.date ? parseISO(session.date) : new Date(session.StartTime);
    } catch (error) {
      console.warn('Date parsing error:', error);
      sessionDate = new Date();
    }

    // Tab filtresi
    if (activeTab === 'upcoming' && sessionDate >= today && session.status === 'scheduled') {
      // Gelecek görüşmeler
    } else if (activeTab === 'past' && (sessionDate < today || session.status === 'completed' || session.status === 'missed' || session.status === 'cancelled')) {
      // Geçmiş görüşmeler
    } else if (activeTab === 'all') {
      // Tüm görüşmeler
    } else if (activeTab !== 'all') {
      return false;
    }

    // Durum filtresi - API'den zaten filtrelenmiş veri geldiği için bu kontrol gereksiz olabilir
    // Ancak client-side ek filtreleme için bırakıyoruz
    if (filterStatus !== 'all' && session.status !== filterStatus) {
      return false;
    }

    // Arama filtresi - API'den zaten filtrelenmiş veri geldiği için bu kontrol gereksiz olabilir
    // Ancak client-side ek filtreleme için bırakıyoruz
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      const expertName = (session.expertName || '').toLowerCase();
      const notes = (session.notes || '').toLowerCase();
      const packageName = (session.packageName || '').toLowerCase();

      if (!expertName.includes(searchLower) &&
          !notes.includes(searchLower) &&
          !packageName.includes(searchLower)) {
        return false;
      }
    }

    return true;
  });

  // Tarihe göre sırala
  const sortedSessions = [...filteredSessions].sort((a, b) => {
    // API'den gelen StartTime'ı kullan
    const dateA = new Date(a.StartTime || a.date);
    const dateB = new Date(b.StartTime || b.date);

    // Yeni tarihler önce gelsin (DESC order)
    return dateB - dateA;
  });

  // Durum badge renkleri
  const getStatusBadge = (status) => {
    switch (status) {
      case 'scheduled':
        return 'bg-teal-100 text-teal-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'missed':
        return 'bg-amber-100 text-amber-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Border renkleri
  const getStatusBorder = (status) => {
    switch (status) {
      case 'scheduled':
        return 'border-teal-500';
      case 'completed':
        return 'border-green-500';
      case 'missed':
        return 'border-amber-500';
      case 'cancelled':
        return 'border-red-500';
      default:
        return 'border-gray-500';
    }
  };

  // Yükleniyor durumu
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-600"></div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen pb-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-8">
        {/* Başlık ve Üst Kısım */}
        <div className="bg-gradient-to-r from-purple-500 to-purple-600 shadow-lg rounded-lg p-6 mb-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
            <div>
              <h1 className="text-2xl font-bold text-white">Seanslarım</h1>
              <p className="mt-1 text-purple-100">
                Tamamlanan seanslarınızı ve notlarınızı buradan görüntüleyebilirsiniz
              </p>
            </div>
            <div className="mt-3 sm:mt-0 flex space-x-2">
              <Link
                to="/client/experts"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-colors duration-150 whitespace-nowrap"
              >
                <UserIcon className="h-4 w-4 mr-2" />
                Yeni Seans
              </Link>
              <Link
                to="/client/appointments"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-purple-600 bg-white hover:bg-purple-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-150 whitespace-nowrap"
              >
                <CalendarIcon className="h-4 w-4 mr-2" />
                Randevularım
              </Link>
              <Link
                to="/client/messages"
                className="inline-flex items-center px-4 py-2 border border-white text-sm font-medium rounded-md shadow-sm text-white hover:bg-purple-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-150 whitespace-nowrap"
              >
                <ChatBubbleLeftRightIcon className="h-4 w-4 mr-2" />
                Mesajlarım
              </Link>
            </div>
          </div>
        </div>

        {/* Özet İstatistikler */}
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-4 mb-6">
          <div 
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-teal-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'all' && filterStatus === 'all' ? 'ring-2 ring-teal-500' : ''}`}
            onClick={() => {
              setActiveTab('all');
              setFilterStatus('all');
            }}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Toplam</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.total}</span>
          </div>
          
          <div 
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-teal-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'upcoming' && filterStatus === 'scheduled' ? 'ring-2 ring-teal-500' : ''}`}
            onClick={() => {
              setActiveTab('upcoming');
              setFilterStatus('scheduled');
            }}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Planlanan</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.upcoming}</span>
          </div>
          
          <div 
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-green-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'all' && filterStatus === 'completed' ? 'ring-2 ring-green-500' : ''}`}
            onClick={() => {
              setActiveTab('all');
              setFilterStatus('completed');
            }}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Tamamlanan</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.completed}</span>
          </div>
          
          <div 
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-amber-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'all' && filterStatus === 'missed' ? 'ring-2 ring-amber-500' : ''}`}
            onClick={() => {
              setActiveTab('all');
              setFilterStatus('missed');
            }}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Kaçırılan</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.missed}</span>
          </div>
          
          <div 
            className={`bg-white shadow-md rounded-lg p-4 flex flex-col items-center border-t-4 border-red-500 hover:shadow-lg transition-shadow duration-300 cursor-pointer ${activeTab === 'all' && filterStatus === 'cancelled' ? 'ring-2 ring-red-500' : ''}`}
            onClick={() => {
              setActiveTab('all');
              setFilterStatus('cancelled');
            }}
          >
            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">İptal Edilen</span>
            <span className="mt-1 text-2xl font-bold text-gray-900">{stats.cancelled}</span>
          </div>
        </div>

        {/* Ana Sekme Navigasyonu */}
        <div className="flex border-b border-gray-200 mb-6">
          <button
            onClick={() => {
              setActiveTab('upcoming');
              setFilterStatus('scheduled');
            }}
            className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
              activeTab === 'upcoming'
                ? 'border-teal-500 text-teal-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center">
              <CalendarIcon className="h-5 w-5 mr-2" />
              <span>Yaklaşan Seanslar</span>
            </div>
          </button>
          <button
            onClick={() => setActiveTab('past')}
            className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
              activeTab === 'past'
                ? 'border-teal-500 text-teal-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center">
              <CheckCircleIcon className="h-5 w-5 mr-2" />
              <span>Geçmiş Seanslar</span>
            </div>
          </button>
          <button
            onClick={() => {
              setActiveTab('all');
              setFilterStatus('all');
            }}
            className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
              activeTab === 'all'
                ? 'border-teal-500 text-teal-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center">
              <DocumentTextIcon className="h-5 w-5 mr-2" />
              <span>Tüm Seanslar</span>
            </div>
          </button>
        </div>

        {/* Arama */}
        <div className="bg-white shadow rounded-lg mb-6">
          <div className="px-4 py-5 sm:p-6">
            <div className="max-w-lg">
              <div className="relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                  placeholder="Uzman adına göre ara..."
                />
              </div>
            </div>
          </div>
        </div>

        {/* Görüşmeler Listesi */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">
              {activeTab === 'upcoming' ? 'Yaklaşan Seanslar' :
               activeTab === 'past' ? 'Geçmiş Seanslar' : 'Tüm Seanslar'}
              {filterStatus !== 'all' && ` - ${sessionStatuses[filterStatus]}`}
            </h2>
          </div>

          {sortedSessions.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {sortedSessions.map((session) => (
                <div key={session.id} className="p-6 hover:bg-gray-50 transition duration-150">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <img
                          className="h-10 w-10 rounded-full border border-gray-200"
                          src={session.expertAvatar || '/uploads/default-avatar.png'}
                          alt={session.expertName}
                          onError={(e) => {
                            e.target.src = '/uploads/default-avatar.png';
                          }}
                        />
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-900">{session.expertName}</h3>
                        <p className="text-xs text-gray-500">{session.expertTitle || session.Specialization}</p>
                        <div className="flex space-x-2 text-xs text-gray-500 mt-1">
                          {session.date && (
                            <>
                              <span>{format(parseISO(session.date), 'EEEE', { locale: tr })}</span>
                              <span>•</span>
                              <span>{format(parseISO(session.date), 'd MMMM yyyy', { locale: tr })}</span>
                            </>
                          )}
                          {!session.date && session.StartTime && (
                            <>
                              <span>{format(new Date(session.StartTime), 'EEEE', { locale: tr })}</span>
                              <span>•</span>
                              <span>{format(new Date(session.StartTime), 'd MMMM yyyy', { locale: tr })}</span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadge(session.status)}`}>
                        {sessionStatuses[session.status]}
                      </span>
                      <span className="text-xs text-gray-500">{session.packageName}</span>
                    </div>
                  </div>

                  <div className="mt-4 flex items-center justify-between">
                    <div className="flex space-x-6 text-sm text-gray-500">
                      <div className="flex items-center">
                        <ClockIcon className="h-4 w-4 text-gray-400 mr-1.5" />
                        <span>
                          {session.startTime && session.endTime
                            ? `${session.startTime} - ${session.endTime}`
                            : session.StartTime && session.EndTime
                            ? `${format(new Date(session.StartTime), 'HH:mm')} - ${format(new Date(session.EndTime), 'HH:mm')}`
                            : 'Saat belirtilmemiş'
                          }
                        </span>
                      </div>
                      <div className="flex items-center">
                        <UserIcon className="h-4 w-4 text-gray-400 mr-1.5" />
                        <span>Seans #{(session.sessionsCompleted || 0) + 1}</span>
                      </div>
                      <div className="flex items-center">
                        <VideoCameraIcon className="h-4 w-4 text-gray-400 mr-1.5" />
                        <span>Video Görüşme</span>
                      </div>
                    </div>

                    <div className="flex space-x-2">
                      {session.status === 'scheduled' && (() => {
                        const sessionDateTime = session.date
                          ? parseISO(session.date)
                          : session.StartTime
                          ? new Date(session.StartTime)
                          : null;
                        return sessionDateTime && sessionDateTime <= new Date(Date.now() + 15 * 60 * 1000);
                      })() && (
                        <Link
                          to={`/client/sessions/${session.id}/meeting`}
                          className="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
                        >
                          <PlayCircleIcon className="-ml-0.5 mr-1 h-4 w-4" />
                          Görüşmeye Katıl
                        </Link>
                      )}
                      
                      {session.recordingAvailable && (
                        <button
                          type="button"
                          className="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-teal-700 bg-teal-100 hover:bg-teal-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
                        >
                          <DocumentArrowDownIcon className="-ml-0.5 mr-1 h-4 w-4" />
                          Kaydı İndir
                        </button>
                      )}
                      
                      {(session.status === 'completed' || session.status === 'missed') && (
                        <Link
                          to={`/client/sessions/${session.id}/notes`}
                          className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                        >
                          <DocumentTextIcon className="-ml-0.5 mr-1 h-4 w-4" />
                          Seans Notları
                        </Link>
                      )}
                      
                      <Link
                        to={`/client/messages?expert=${session.ExpertID || session.expertId}`}
                        className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                      >
                        <ChatBubbleLeftRightIcon className="-ml-0.5 mr-1 h-4 w-4" />
                        Uzmana Mesaj
                      </Link>

                      <Link
                        to={`/client/experts/${session.ExpertID || session.expertId}`}
                        className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                      >
                        <UserIcon className="-ml-0.5 mr-1 h-4 w-4" />
                        Uzman Profili
                      </Link>
                    </div>
                  </div>

                  {session.notes && (
                    <div className="mt-2 text-xs text-gray-500 bg-gray-50 px-3 py-1.5 rounded-md">
                      <span className="font-medium">Not:</span> {session.notes}
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="py-12 text-center">
              <CalendarIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Seans Bulunamadı</h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm || filterStatus !== 'all'
                  ? 'Arama kriterlerinize uygun seans bulunamadı.'
                  : 'Henüz bir seansınız bulunmuyor.'}
              </p>
              <div className="mt-6">
                <Link
                  to="/client/experts"
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-teal-600 hover:bg-teal-700 focus:outline-none"
                >
                  <UserIcon className="h-4 w-4 mr-2" />
                  Uzman Ara
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ClientSessionsPage; 